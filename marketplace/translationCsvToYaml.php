<?php

use Symfony\Component\Yaml\Yaml;

include 'vendor/autoload.php';

$fileFr = __DIR__ . '/src/AppBundle/Resources/translations/AppBundle.fr.yml';
$fileEn = __DIR__ . '/src/AppBundle/Resources/translations/AppBundle.en.yml';
$fileEs = __DIR__ . '/src/AppBundle/Resources/translations/AppBundle.es.yml';
$fileIt = __DIR__ . '/src/AppBundle/Resources/translations/AppBundle.it.yml';
$fileDe = __DIR__ . '/src/AppBundle/Resources/translations/AppBundle.de.yml';
$fileNl = __DIR__ . '/src/AppBundle/Resources/translations/AppBundle.nl.yml';

$dataFr = [];
$dataEn = [];
$dataEs = [];
$dataIt = [];
$dataDe = [];
$dataNl = [];

if (($handle = fopen("translation_nl.csv", "r")) !== false) {
    $first = true;
    while (($data = fgetcsv($handle, 1000, ";")) !== false) {
        $data = array_map("utf8_encode", $data);
        $dataNl = array_merge_recursive($dataNl, setData($data[0], $data[1]));
        $first = false;
    }
    fclose($handle);
}

//file_put_contents($fileFr, \Symfony\Component\Yaml\Yaml::dump($dataFr,10));
//file_put_contents($fileEn, \Symfony\Component\Yaml\Yaml::dump($dataEn, 10));
//file_put_contents($fileEs, \Symfony\Component\Yaml\Yaml::dump($dataEs, 10));
//file_put_contents($fileIt, \Symfony\Component\Yaml\Yaml::dump($dataIt, 10));
//file_put_contents($fileDe, Yaml::dump($dataDe, 10));
file_put_contents($fileNl, Yaml::dump($dataNl, 10));

function setData($key, $value) {
    $data = [];
    $fullPath = explode('.', $key);

    $first = true;
    foreach(array_reverse($fullPath) as $path) {
        $tmpData = [];
        $tmpData[$path] = ($first) ? $value : $data;
        $data = $tmpData;
        $first = false;
    }

    return $data;
}

echo "DONE"."\n";
