{"benmajor/exchange-rates-api": {"version": "4.1.1"}, "composer/package-versions-deprecated": {"version": "*********"}, "ddeboer/vatin": {"version": "2.2.2"}, "ddeboer/vatin-bundle": {"version": "2.3.0"}, "doctrine/annotations": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "a2759dd6123694c8d901d0ec80006e044c2e6457"}, "files": ["config/routes/annotations.yaml"]}, "doctrine/cache": {"version": "2.1.1"}, "doctrine/collections": {"version": "1.6.8"}, "doctrine/common": {"version": "3.1.2"}, "doctrine/dbal": {"version": "2.13.3"}, "doctrine/deprecations": {"version": "v0.5.3"}, "doctrine/doctrine-bundle": {"version": "2.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "2.4", "ref": "bac5c852ff628886de2753215fe5eb1f9ce980fb"}, "files": ["config/packages/doctrine.yaml", "config/packages/prod/doctrine.yaml", "config/packages/test/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.1", "ref": "ee609429c9ee23e22d6fa5728211768f51ed2818"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "doctrine/event-manager": {"version": "1.1.1"}, "doctrine/inflector": {"version": "2.0.3"}, "doctrine/instantiator": {"version": "1.4.0"}, "doctrine/lexer": {"version": "1.2.1"}, "doctrine/migrations": {"version": "3.2.1"}, "doctrine/orm": {"version": "2.9.5"}, "doctrine/persistence": {"version": "2.2.2"}, "doctrine/sql-formatter": {"version": "1.1.1"}, "egulias/email-validator": {"version": "3.1.1"}, "elasticsearch/elasticsearch": {"version": "v7.15.0"}, "ezimuel/guzzlestreams": {"version": "3.0.1"}, "ezimuel/ringphp": {"version": "1.2.0"}, "firebase/php-jwt": {"version": "v5.5.1"}, "friendsofphp/proxy-manager-lts": {"version": "v1.0.5"}, "friendsofsymfony/ckeditor-bundle": {"version": "2.3", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "2.0", "ref": "f5ad42002183a6881962683e6d84bbb25cdfce5d"}, "files": ["config/packages/fos_ckeditor.yaml"]}, "friendsofsymfony/jsrouting-bundle": {"version": "2.7", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "2.3", "ref": "a9f2e49180f75cdc71ae279a929c4b2e0638de84"}, "files": ["config/routes/fos_js_routing.yaml"]}, "friendsofsymfony/rest-bundle": {"version": "3.1", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "2.2", "ref": "fa845143b7e0a4c70aedd1a88c549e6d977e9ae5"}, "files": ["config/packages/fos_rest.yaml"]}, "guzzlehttp/guzzle": {"version": "7.4.0"}, "guzzlehttp/promises": {"version": "1.5.1"}, "guzzlehttp/psr7": {"version": "2.1.0"}, "halaxa/json-machine": {"version": "1.1.1"}, "helios-ag/fm-elfinder-bundle": {"version": "10.1", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "10.0", "ref": "addd79a9903ee92664f4cff61eb6cdd1b95945e7"}, "files": ["config/packages/fm_elfinder.yaml", "config/routes/fm_elfinder.yaml"]}, "html2text/html2text": {"version": "4.3.1"}, "illuminate/collections": {"version": "v8.73.2"}, "illuminate/contracts": {"version": "v8.73.2"}, "illuminate/encryption": {"version": "v8.73.2"}, "illuminate/macroable": {"version": "v8.73.2"}, "illuminate/support": {"version": "v8.73.2"}, "jasny/twig-extensions": {"version": "v1.3.0"}, "jms/i18n-routing-bundle": {"version": "3.1.0"}, "jms/metadata": {"version": "2.6.1"}, "jms/serializer": {"version": "3.16.0"}, "jms/serializer-bundle": {"version": "4.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "3.0", "ref": "384cec52df45f3bfd46a09930d6960a58872b268"}, "files": ["config/packages/dev/jms_serializer.yaml", "config/packages/jms_serializer.yaml", "config/packages/prod/jms_serializer.yaml"]}, "knplabs/knp-components": {"version": "v3.3.0"}, "knplabs/knp-menu": {"version": "v3.3.0"}, "knplabs/knp-menu-bundle": {"version": "v3.2.0"}, "knplabs/knp-paginator-bundle": {"version": "v5.7.0"}, "knpuniversity/oauth2-client-bundle": {"version": "2.8", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "1.20", "ref": "1ff300d8c030f55c99219cc55050b97a695af3f6"}, "files": ["config/packages/knpu_oauth2_client.yaml"]}, "laminas/laminas-code": {"version": "4.4.3"}, "league/csv": {"version": "9.8.0"}, "league/oauth2-client": {"version": "2.6.0"}, "mashape/unirest-php": {"version": "v3.0.4"}, "moneyphp/money": {"version": "v3.3.1"}, "monolog/monolog": {"version": "2.3.4"}, "mpdf/mpdf": {"version": "v8.0.15"}, "myclabs/deep-copy": {"version": "1.10.2"}, "nelmio/cors-bundle": {"version": "2.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.5", "ref": "6bea22e6c564fba3a1391615cada1437d0bde39c"}, "files": ["config/packages/nelmio_cors.yaml"]}, "nesbot/carbon": {"version": "2.54.0"}, "nikic/php-parser": {"version": "v4.13.0"}, "paragonie/constant_time_encoding": {"version": "v2.4.0"}, "paragonie/random_compat": {"version": "v9.99.100"}, "phar-io/manifest": {"version": "2.0.3"}, "phar-io/version": {"version": "3.1.0"}, "php-amqplib/php-amqplib": {"version": "v3.1.0"}, "phpdocumentor/reflection-common": {"version": "2.2.0"}, "phpdocumentor/reflection-docblock": {"version": "5.2.2"}, "phpdocumentor/type-resolver": {"version": "1.5.0"}, "phpseclib/phpseclib": {"version": "3.0.11"}, "phpspec/prophecy": {"version": "1.14.0"}, "phpstan/phpdoc-parser": {"version": "1.2.0"}, "phpunit/php-code-coverage": {"version": "9.2.7"}, "phpunit/php-file-iterator": {"version": "3.0.5"}, "phpunit/php-invoker": {"version": "3.1.1"}, "phpunit/php-text-template": {"version": "2.0.4"}, "phpunit/php-timer": {"version": "5.0.3"}, "phpunit/phpunit": {"version": "9.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "9.3", "ref": "a6249a6c4392e9169b87abf93225f7f9f59025e6"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "predis/predis": {"version": "v1.1.9"}, "psr/cache": {"version": "2.0.0"}, "psr/container": {"version": "1.1.1"}, "psr/event-dispatcher": {"version": "1.0.0"}, "psr/http-client": {"version": "1.0.1"}, "psr/http-factory": {"version": "1.0.1"}, "psr/http-message": {"version": "1.0.1"}, "psr/link": {"version": "1.1.1"}, "psr/log": {"version": "2.0.0"}, "psr/simple-cache": {"version": "1.0.1"}, "ralouphie/getallheaders": {"version": "3.0.3"}, "react/promise": {"version": "v2.8.0"}, "sebastian/cli-parser": {"version": "1.0.1"}, "sebastian/code-unit": {"version": "1.0.8"}, "sebastian/code-unit-reverse-lookup": {"version": "2.0.3"}, "sebastian/comparator": {"version": "4.0.6"}, "sebastian/complexity": {"version": "2.0.2"}, "sebastian/diff": {"version": "4.0.4"}, "sebastian/environment": {"version": "5.1.3"}, "sebastian/exporter": {"version": "4.0.3"}, "sebastian/global-state": {"version": "5.0.3"}, "sebastian/lines-of-code": {"version": "1.0.3"}, "sebastian/object-enumerator": {"version": "4.0.4"}, "sebastian/object-reflector": {"version": "2.0.4"}, "sebastian/recursion-context": {"version": "4.0.4"}, "sebastian/resource-operations": {"version": "3.0.3"}, "sebastian/type": {"version": "2.3.4"}, "sebastian/version": {"version": "3.0.2"}, "sensio/framework-extra-bundle": {"version": "5.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.2", "ref": "fb7e19da7f013d0d422fa9bce16f5c510e27609b"}, "files": ["config/packages/sensio_framework_extra.yaml"]}, "setasign/fpdi": {"version": "v2.3.6"}, "studio-42/elfinder": {"version": "2.1.60"}, "swiftmailer/swiftmailer": {"version": "v6.3.0"}, "symfony/asset": {"version": "v5.3.4"}, "symfony/browser-kit": {"version": "v5.3.4"}, "symfony/cache": {"version": "v5.3.7"}, "symfony/cache-contracts": {"version": "v2.4.0"}, "symfony/config": {"version": "v5.3.4"}, "symfony/console": {"version": "5.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.3", "ref": "da0c8be8157600ad34f10ff0c9cc91232522e047"}, "files": ["bin/console"]}, "symfony/css-selector": {"version": "v5.3.4"}, "symfony/debug-bundle": {"version": "4.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.1", "ref": "0ce7a032d344fb7b661cd25d31914cd703ad445b"}, "files": ["config/packages/dev/debug.yaml"]}, "symfony/dependency-injection": {"version": "v5.3.7"}, "symfony/deprecation-contracts": {"version": "v2.4.0"}, "symfony/doctrine-bridge": {"version": "v5.3.7"}, "symfony/dom-crawler": {"version": "v5.3.7"}, "symfony/dotenv": {"version": "v5.3.7"}, "symfony/error-handler": {"version": "v5.3.7"}, "symfony/event-dispatcher": {"version": "v5.3.7"}, "symfony/event-dispatcher-contracts": {"version": "v2.4.0"}, "symfony/expression-language": {"version": "v5.3.7"}, "symfony/filesystem": {"version": "v5.3.4"}, "symfony/finder": {"version": "v5.3.7"}, "symfony/flex": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "c0eeb50665f0f77226616b6038a9b06c03752d8e"}, "files": [".env"]}, "symfony/form": {"version": "v5.3.7"}, "symfony/framework-bundle": {"version": "5.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.3", "ref": "414ba00ad43fa71be42c7906a551f1831716b03c"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/http-client": {"version": "v5.3.7"}, "symfony/http-client-contracts": {"version": "v2.4.0"}, "symfony/http-foundation": {"version": "v5.3.7"}, "symfony/http-kernel": {"version": "v5.3.7"}, "symfony/intl": {"version": "v5.3.7"}, "symfony/mailer": {"version": "4.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.3", "ref": "bbfc7e27257d3a3f12a6fb0a42540a42d9623a37"}, "files": ["config/packages/mailer.yaml"]}, "symfony/maker-bundle": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/mime": {"version": "v5.3.7"}, "symfony/monolog-bridge": {"version": "v5.3.7"}, "symfony/monolog-bundle": {"version": "3.7", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.7", "ref": "a7bace7dbc5a7ed5608dbe2165e0774c87175fe6"}, "files": ["config/packages/dev/monolog.yaml", "config/packages/prod/deprecations.yaml", "config/packages/prod/monolog.yaml", "config/packages/test/monolog.yaml"]}, "symfony/notifier": {"version": "5.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.0", "ref": "c31585e252b32fe0e1f30b1f256af553f4a06eb9"}, "files": ["config/packages/notifier.yaml"]}, "symfony/options-resolver": {"version": "v5.3.7"}, "symfony/password-hasher": {"version": "v5.3.7"}, "symfony/phpunit-bridge": {"version": "5.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.3", "ref": "97cb3dc7b0f39c7cfc4b7553504c9d7b7795de96"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/polyfill-intl-grapheme": {"version": "v1.23.1"}, "symfony/polyfill-intl-icu": {"version": "v1.23.0"}, "symfony/polyfill-intl-idn": {"version": "v1.23.0"}, "symfony/polyfill-intl-normalizer": {"version": "v1.23.0"}, "symfony/polyfill-mbstring": {"version": "v1.23.1"}, "symfony/polyfill-php73": {"version": "v1.23.0"}, "symfony/polyfill-php80": {"version": "v1.23.1"}, "symfony/polyfill-php81": {"version": "v1.23.0"}, "symfony/process": {"version": "v5.3.7"}, "symfony/property-access": {"version": "v5.3.7"}, "symfony/property-info": {"version": "v5.3.7"}, "symfony/proxy-manager-bridge": {"version": "v5.3.4"}, "symfony/routing": {"version": "5.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.3", "ref": "44633353926a0382d7dfb0530922c5c0b30fae11"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/runtime": {"version": "v5.3.4"}, "symfony/security-bundle": {"version": "5.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.3", "ref": "9c4fcf79873f7400c885b90935f7163233615d6f"}, "files": ["config/packages/security.yaml"]}, "symfony/security-core": {"version": "v5.3.7"}, "symfony/security-csrf": {"version": "v5.3.4"}, "symfony/security-guard": {"version": "v5.3.7"}, "symfony/security-http": {"version": "v5.3.7"}, "symfony/serializer": {"version": "v5.3.4"}, "symfony/service-contracts": {"version": "v2.4.0"}, "symfony/stopwatch": {"version": "v5.3.4"}, "symfony/string": {"version": "v5.3.7"}, "symfony/swiftmailer-bundle": {"version": "3.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "2.5", "ref": "f0b2fccdca2dfd97dc2fd5ad216d5e27c4f895ac"}, "files": ["config/packages/swiftmailer.yaml", "config/packages/dev/swiftmailer.yaml", "config/packages/test/swiftmailer.yaml"]}, "symfony/translation": {"version": "5.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.3", "ref": "da64f5a2b6d96f5dc24914517c0350a5f91dee43"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/translation-contracts": {"version": "v2.4.0"}, "symfony/twig-bridge": {"version": "v5.3.7"}, "symfony/twig-bundle": {"version": "5.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.3", "ref": "3dd530739a4284e3272274c128dbb7a8140a66f1"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/validator": {"version": "4.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.3", "ref": "3eb8df139ec05414489d55b97603c5f6ca0c44cb"}, "files": ["config/packages/test/validator.yaml", "config/packages/validator.yaml"]}, "symfony/var-dumper": {"version": "v5.3.7"}, "symfony/var-exporter": {"version": "v5.3.7"}, "symfony/web-link": {"version": "v5.3.4"}, "symfony/web-profiler-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "6bdfa1a95f6b2e677ab985cd1af2eae35d62e0f6"}, "files": ["config/packages/dev/web_profiler.yaml", "config/packages/test/web_profiler.yaml", "config/routes/dev/web_profiler.yaml"]}, "symfony/webpack-encore-bundle": {"version": "1.13", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.9", "ref": "0f274572ea315eb3b5884518a50ca43f211b4534"}, "files": ["assets/app.js", "assets/bootstrap.js", "assets/controllers.json", "assets/controllers/hello_controller.js", "assets/styles/app.css", "config/packages/assets.yaml", "config/packages/prod/webpack_encore.yaml", "config/packages/test/webpack_encore.yaml", "config/packages/webpack_encore.yaml", "package.json", "webpack.config.js"]}, "symfony/yaml": {"version": "v5.3.6"}, "theseer/tokenizer": {"version": "1.2.1"}, "twbs/bootstrap": {"version": "v5.1.3"}, "twig/extensions": {"version": "1.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "a86723ee8d8b2f9437c8ce60a5546a1c267da5ed"}, "files": ["config/packages/twig_extensions.yaml"]}, "twig/extra-bundle": {"version": "v3.3.3"}, "twig/intl-extra": {"version": "v3.3.3"}, "twig/twig": {"version": "v3.3.3"}, "voku/portable-ascii": {"version": "1.5.6"}, "webmozart/assert": {"version": "1.10.0"}, "willdurand/jsonp-callback-validator": {"version": "v1.1.0"}, "willdurand/negotiation": {"version": "3.0.0"}}