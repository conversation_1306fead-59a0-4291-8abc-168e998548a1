{"license": "click-and-buy", "private": true, "scripts": {"dev-server": "encore dev-server", "dev": "encore dev", "watch": "encore dev --watch", "build": "encore production --progress"}, "devDependencies": {"@babel/preset-env": "^7.10.4", "@babel/preset-react": "^7.0.0", "@symfony/webpack-encore": "^0.30.2", "del": "^3.0.0", "node-sass": "^4.14.1", "promise": "^7.1.1", "requirejs": "^2.3.2", "sass-loader": "^8.0.0", "webpack-notifier": "^1.6.0", "webpack-stream": "^3.2.0", "yargs": "^6.4.0"}, "dependencies": {"@izberg/izberg-ui-beta": "1.0.0-alpha.5", "@material-ui/core": "^3.9.3", "@material-ui/icons": "^3.0.2", "@total/messager": "file:../messager", "axios": "^0.19.2", "bazinga-translator": "^2.6.4", "blueimp-file-upload": "^9.25.0", "bootstrap-tagsinput": "^0.7.1", "breakpoint-sass": "^2.7.1", "datatables.net": "^1.10.21", "datatables.net-responsive": "^2.2.5", "expose-loader": "^1.0.0", "handlebars": "^4.0.6", "i18next": "^17.0.16", "imports-loader": "^1.1.0", "jquery": "^3.5.1", "jquery-timepicker": "^1.3.3", "jquery-ui": "^1.12.1", "jquery-ui-dist": "^1.12.1", "jquery-validation": "^1.19.0", "js-cookie": "^2.2.0", "modernizr": "^3.11.3", "mui-datatables": "^2.14.0", "normalize.css": "^6.0.0", "popper.js": "^1.16.1", "prop-types": "^15.8.1", "react": "^16.9.0", "react-dom": "^16.9.0", "react-fontawesome": "^1.7.1", "react-i18next": "^10.13.0", "react-nl2br": "^0.6.0", "react-number-format": "^4.4.1", "react-router-dom": "^5.1.0", "react-spinners": "^0.8.0", "react-toasts": "^3.0.6", "slider-pro": "^1.3.0", "typeface-roboto": "0.0.75"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"]}}