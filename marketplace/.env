# Symfony app setting vars

# 2 values possible dev -> developpement | prod -> production
APP_ENV=dev
# 1 to activate debug | 0 -> to desactivate debug
APP_DEBUG=1
APP_SECRET=fee27ce5fa9c5948f45c4c5e08d85962
APP_DEFAULT_LOCALE="fr"
APP_LOGS_DIR="/tmp/log"

# Total app params vars
CAPTCHA_PUBLIC=null
CAPTCHA_SECRET=''
CAPTCHA_ENABLED=false
MAILER_FROM_EMAIL="<EMAIL>"
MAILER_FROM_NAME="Support"
SUPPORT_OPERATOR_EMAIL="<EMAIL>"
DOMAIN="total.local"
PROTOCOL="http://"
BUG_IZBERG_LINK_PROTOCOL="http://"
ENVIRONMENT="[DEV] "
CRYPT_KEY="ODkuPMY1OS4zMTUg"
MARKETPLACE_CURRENCY="EUR"
CORS_ALLOW_ORIGIN="^http?://.*?$"

DEFAULT_REDIRECT_TYPE=302
MAX_ITEM_COMPARISON=5
CACHE_PAYMENT_TERMS_TTL=60
APP_SESSION_MAX_IDLE_TIME=1200
GC_MAXLIFETIME=1200
GC_DIVISOR=100
SESSION_MAXIDLETIME=1200
STOCK_MINIMUM=10
REPORTING_MIN_AVERAGE_OFFER_DESCRIPTION_LENGTH=20
REPORTING_EXPORT_TIME_LIMIT=1200

API_VAT_CHECK=false
DISPLAY_FIRST_LEVEL_CATEGORY_ONLY=true

SUPPORTED_CURRENCIES='["EUR","USD"]'
TRUSTED_HOSTS='["total.local"]'
COMMON_EMAIL_PROVIDERS='["gmail.com","outlook.com","free.fr","orange.fr"]'

# TOTAL Open Id Connect vars
TOTAL_OPEN_ID_CONNECT_CLIENT_ID='marketplace'
TOTAL_OPEN_ID_CONNECT_CLIENT_SECRET='13f45f14-6193-4ca4-9318-b2c46f02b89f'
TOTAL_OPEN_ID_CONNECT_URL_AUTHORIZE='http://localhost:8080/auth/realms/total/protocol/openid-connect/auth'
TOTAL_OPEN_ID_CONNECT_URL_ACCESS_TOKEN='http://localhost:8080/auth/realms/total/protocol/openid-connect/token'
TOTAL_OPEN_ID_CONNECT_URL_RESOURCE_OWNER_DETAILS='http://localhost:8080/auth/realms/total/protocol/openid-connect/userinfo'
TOTAL_OPEN_ID_CONNECT_URL_LOGOUT='https://pp-digitalpassport.hubtotal.net/iam/im/total/total/index.jsp?logout=1'

# IZBERG Open Id Connect vars
IZBERG_VENDOR_OPENIDCONNECT_CLIENT_ID="0ac9fc3f09a0269e02aed140187aab98"
IZBERG_VENDOR_OPENIDCONNECT_CLIENT_SECRET="_fz0X4mGkR9L5HNwnRqti7i-_QupKa_ayHmo-D_QfXs"
IZBERG_VENDOR_OPENIDCONNECT_URL_AUTHORIZE="https://izberg.me/auth"
IZBERG_VENDOR_OPENIDCONNECT_URL_ACCESS_TOKEN="https://api.izberg.me/auth/token"
IZBERG_API_DOMAIN="api.sandbox.iceberg.technology"

# Izberg api params vars
IZBERG_API_VERSION=1
IZBERG_API_PROTOCOL="https"
IZBERG_EMAIL="<EMAIL>"
IZBERG_FIRST_NAME="total"
IZBERG_LAST_NAME="Open"
IZBERG_MERCHANT_BASE_URL="https://total-europe.izberg-marketplace.com/"
IZBERG_MERCHANT_BASE_URLS='{"france":"https://total-europe.izberg-marketplace.com/","germany":"https://seller.izberg-marketplace.com/","belgium":"https://seller.izberg-marketplace.com/"}'
IZBERG_EMAIL_SELLER_DOMAIN="mkalstom.com"
IZBERG_CONSOLE_USERNAME="alex_le-bescond1174"
IZBERG_CONSOLE_ACCESS_TOKEN="6fe6f1703a0e33293dbf69173d3ff644acdc4222"
IZBERG_WEBHOOK_USERNAME="alex_le-bescond1174"
IZBERG_WEBHOOK_ACCESS_TOKEN="6fe6f1703a0e33293dbf69173d3ff644acdc4222"
IZBERG_CATEGORY_ICONS_PATH="images/categories"
IZBERG_CATEGORY_ICONS='{"1508":"battery.jpg","1506":"cables.jpg"}'
IZBERG_DATE_PATTERN="Y-m-d\TH:i:s+"
IZBERG_MERCHANT_CARRIER=142
IZBERG_MERCHANT_SHIPPING_PROVIDER=511
IZBERG_MERCHANT_ZONE=371

IZBERG_AUDIENCE="https://api.sandbox.iceberg.technology"
IZBERG_IDENTITY_URL="https://api.izberg.me/v1/"

# Izberg api params vars for france
IZBERG_FRANCE_APPLICATION_ID=2954
IZBERG_FRANCE_APPLICATION_NAMESPACE="total-europe"
IZBERG_FRANCE_ACCESS_TOKEN="b2297a6c9543871e10dd057aab98d9863345635e"
IZBERG_FRANCE_SECRET_KEY="b1b695c6-47f0-4237-ae2e-0db707fc3d42"
IZBERG_FRANCE_USERNAME="open_service-account2884"
IZBERG_FRANCE_CLIENT_ID="m2m-bb01c5c87018bf31771893bb1780e936"
IZBERG_FRANCE_CLIENT_SECRET="ziOkbzrFvz_w9HVlkWPkDxoLbqbYB05uD8RdvCy0ViY"
IZBERG_FRANCE_DOMAIN_ID="4A2CEE19B9"


# Izberg api params vars for germany
IZBERG_GERMANY_APPLICATION_ID=1003484
IZBERG_GERMANY_APPLICATION_NAMESPACE="sand-clickandbuy-02"
IZBERG_GERMANY_ACCESS_TOKEN="ece1cb900817f136d42017c7e7869c49fd0813ce"
IZBERG_GERMANY_SECRET_KEY="24a509ae-2559-4797-a52e-fe3eb1c2769a"
IZBERG_GERMANY_USERNAME="open_service-account2884"
IZBERG_GERMANY_CLIENT_ID="m2m-28c9b682ab0015ff4f6bbdb6a04e8f53"
IZBERG_GERMANY_CLIENT_SECRET="MEW_nEll3mDMiDpM8fHqdQf58M0UYxduLSMakGjUbAY"
IZBERG_GERMANY_DOMAIN_ID="A2B4031F49"

# Izberg api params vars for belgium
IZBERG_BELGIUM_APPLICATION_ID=1004375
IZBERG_BELGIUM_APPLICATION_NAMESPACE="click-buy-be"
IZBERG_BELGIUM_ACCESS_TOKEN="3e31fb22e2f7ee3fc66f564b2a7079e2fa4d1260"
IZBERG_BELGIUM_SECRET_KEY="e9c72225-c0e7-47d7-a432-300a3be257b3"
IZBERG_BELGIUM_USERNAME="open_service-account2884"
IZBERG_BELGIUM_CLIENT_ID="m2m-0635140e3eeafadbd7e94dc0a3c186f7"
IZBERG_BELGIUM_CLIENT_SECRET="jVo2T4cOgn8wp3jm11whIMTDCnUnzhz8TzlKmcmr3GI"
IZBERG_BELGIUM_DOMAIN_ID="E049DF453C"

# Izberg sso params
IZBERG_SSO_VALID_JWT_KEYS_URL="https://api.izberg.me/.well-known/valid-keys.json"
IZBERG_SSO_AUTH_BASE_URL="https://izberg.me/auth"
IZBERG_SSO_AUTH_TOKEN_URL="https://api.izberg.me/auth/token"

### Izberg sso params for france platform
IZBERG_SSO_FRANCE_CLIENT_ID="0ac9fc3f09a0269e02aed140187aab98"
IZBERG_SSO_FRANCE_CLIENT_SECRET="_fz0X4mGkR9L5HNwnRqti7i-_QupKa_ayHmo-D_QfXs"

### Izberg sso params for germany platform
IZBERG_SSO_GERMANY_CLIENT_ID="3b2bcd6f688f9c8eee70c4d41f3fe33b"
IZBERG_SSO_GERMANY_CLIENT_SECRET="6_REypcKpFvz37JunVCTiFYeI6oJs-_0N44NeEAbi1A"

### Izberg sso params for belgium platform
IZBERG_SSO_BELGIUM_CLIENT_ID="990ecb31099727aa2240e934017f7030"
IZBERG_SSO_BELGIUM_CLIENT_SECRET="GnuA_0bUvmwy6hpF5tQFHtqdfmp8ak5XRhNXXlC1rBM"

IDEAL_API_SCHEME=https
IDEAL_API_DOMAIN=pprd.idealv5.totalenergies.com
IDEAL_API_APIM_KEY="Ocp-Apim-Subscription-Key"
IDEAL_API_CLIENT_ID=<EMAIL>
IDEAL_API_CLIENT_SECRET=NoQlcbzyVl8z8zjXQQyoperxaS+NKr8Y
IDEAL_TIMEOUT=300
IDEAL_NB_USER_BY_CALL=10000

# Mailing and Database vars
MAILER_HOST="mailcatcher"
MAILER_PORT=1025

MAILER_URL="smtp://${MAILER_HOST}:${MAILER_PORT}?encryption=&auth_mode="

# Database vars
MYSQL_HOST="maria-db"
MYSQL_PORT=3306
MYSQL_SERVER_VERSION='mariadb-10.4.13'
MYSQL_CHARSET="utf8"
MYSQL_DATABASE="clickandbuy"
MYSQL_USER="clickandbuy"
MYSQL_PASSWORD="password"
MYSQL_ROOT_PASSWORD="password"

DATABASE_URL="mysql://${MYSQL_USER}:${MYSQL_PASSWORD}@${MYSQL_HOST}:${MYSQL_PORT}/${MYSQL_DATABASE}?serverVersion=${MYSQL_SERVER_VERSION}&charset=${MYSQL_CHARSET}"

# RabbitMQ vars
RABBIT_HOST=rabbitmq
RABBIT_USER=guest
RABBIT_PASS=guest

# Elasticsearch vars
ELASTICSEARCH_HOST=elasticsearch
ELASTICSEARCH_PORT=9200
ELASTIC_INDEX="offers"
ELASTIC_SHOW_SCORE=false
ELASTIC_SEARCH_BY_RELEVANCE=false
ELASTIC_SSL=false
ELASTIC_CERTIFICATE_PATH="/var/total-tools/offerIndexing/consumer/ca.crt"
IGNORED_CATEGORIES_ID='[86241]'
HOME_CATEGORY_LIST_FRANCE='[96870,96960,96981,97076,97097,97060]'
HOME_CATEGORY_LIST_GERMANY='[96870,96960,96981,97076,97097,97060]'
HOME_CATEGORY_LIST_BELGIUM='[96870,96960,96981,97076,97097,97060]'
CATEGORIES_PRIORITIES_LIST_FRANCE='[96960,96870,97097,96981,97076,97060,96963]'
CATEGORIES_PRIORITIES_LIST_GERMANY='[1120119,96870,97097,96981,97076,97060,96963]'
CATEGORIES_PRIORITIES_LIST_BELGIUM='[1120119,96870,97097,96981,97076,97060,96963]'
EPI_FILTER_CATEGORY=108045

# CXML and SFTP vars
CXML_BASEDIR="/var/www/total/cxml"
CXML_SENDER_DOMAIN='{"22400":"22400_cxml_sender_domain","1023399":"1023399_cxml_sender_domain","1024133":"1024133_cxml_sender_domain"}'
CXML_SENDER_IDENTITY='{"22400":"22400_cxml_sender_identity","1023399":"1023399_cxml_sender_identity","1024133":"1024133_cxml_sender_identity"}'
CXML_SENDER_SECRET='{"22400":"22400_cxml_sender_secret","1023399":"1023399_cxml_sender_secret","1024133":"1024133_cxml_sender_secret"}'
CXML_MERCHANT_LIST='[22400,1023399,1024133]'
CXML_TO_DOMAIN='{"22400":"22400_cxml_to_domain","1023399":"1023399_cxml_to_domain","1024133":"1024133_cxml_to_domain"}'
CXML_TO_IDENTITY='{"22400":"22400_cxml_to_identity","1023399":"1023399_cxml_to_identity","1024133":"1024133_cxml_to_identity"}'
SFTP_TIMEOUT=0
SFTP_DIR='{"22400":"test","1023399":"test","1024133":"test"}'
SFTP_HOSTNAME='{"22400":"bionicBeaver1","1023399":"bionicBeaver1","1024133":"bionicBeaver1"}'
SFTP_LOGIN='{"22400":"sftpuser","1023399":"sftpuser","1024133":"sftpuser"}'
SFTP_PASSWORD='{"22400":"P@ssw0rd","1023399":"P@ssw0rd","1024133":"P@ssw0rd"}'
SFTP_RSA='{"22400":"/var/www/total/id_rsa"}'
SFTP_RSA_PASSWORD='{"22400":"albin"}'
SFTP_EPSA='{"host":"localhost","user":"test","password":"test","sftp_filepath":"epsa/"}'
SFTP_PARAMS='{"host":"localhost","user":"test","password":"test","sftp_filepath":"sftpuser/buyers.csv","local_filepath":"config/import/total_buyers.csv"}'
# Redis vars
REDIS_HOST=redis
REDIS_PORT=6379
CACHE_DEFAULT_LIFETIME=7776000

# Blackfire
BLACKFIRE_SERVER_ID=c27f5008-9ccb-4256-be0b-fd561c73f72e
BLACKFIRE_SERVER_TOKEN=f983a2997d382114dcee7ca7f94ecbdd62ac909da0fcd49defc8a24ba8151b6c
BLACKFIRE_CLIENT_ID=b16082a3-e5fe-4790-a537-7c7d72234378
BLACKFIRE_CLIENT_TOKEN=d3843b279b93a92e244364d8e5e1b27ccedafa58cee6692352a5c4cca88d80f3
BLACKFIRE_LOG_LEVEL=4
BLACKFIRE_LOG_FILE="/tmp/probe.log"
BLACKFIRE_AGENT_SOCKET="tcp://blackfire:8707"
BLACKFIRE_ENDPOINT="https://blackfire.io"

# NewsUrls
NEWS_URLS='{"france":"https://www.total.local/news/france","germany":"https://www.total.local/news/germany","belgium":"https://www.total.local/news/belgium"}'

# CartLimiDays
CART_LIFETIME_DAYS=15

# RiskCategories
RISK_CATEGORIES='[1157092]'
DEFAULT_RISK_EMAIL='<EMAIL>'
