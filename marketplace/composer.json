{"type": "click-and-buy", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.1", "ext-ctype": "*", "ext-iconv": "*", "ext-json": "*", "benmajor/exchange-rates-api": "^4.1", "composer/package-versions-deprecated": "1.11.99.4", "ddeboer/vatin-bundle": "^2.3", "doctrine/annotations": "^1.0", "doctrine/doctrine-bundle": "^2.4", "doctrine/doctrine-migrations-bundle": "^3.1", "doctrine/orm": "^2.9", "elasticsearch/elasticsearch": "^7.15", "firebase/php-jwt": "^6.4", "friendsofsymfony/ckeditor-bundle": "^2.3", "friendsofsymfony/jsrouting-bundle": "^2.7", "friendsofsymfony/rest-bundle": "^3.1", "halaxa/json-machine": "^1.1", "helios-ag/fm-elfinder-bundle": "^10.1", "html2text/html2text": "^4.3", "illuminate/encryption": "^8.73", "jasny/twig-extensions": "^1.3", "jms/i18n-routing-bundle": "^3.1", "jms/serializer-bundle": "^4.0", "knplabs/knp-menu-bundle": "^3.2", "knplabs/knp-paginator-bundle": "^5.7", "knpuniversity/oauth2-client-bundle": "^2.8", "league/csv": "^9.8", "mashape/unirest-php": "^3.0", "moneyphp/money": "^3.3", "mpdf/mpdf": "^8.0", "nelmio/cors-bundle": "^2.1", "php-amqplib/php-amqplib": "^3.1", "phpdocumentor/reflection-docblock": "^5.2", "predis/predis": "^1.1", "sensio/framework-extra-bundle": "^6.1", "symfony/asset": "5.4.*", "symfony/console": "5.4.*", "symfony/dotenv": "5.4.*", "symfony/expression-language": "5.4.*", "symfony/flex": "^1.3.1", "symfony/form": "5.4.*", "symfony/framework-bundle": "5.4.*", "symfony/http-client": "5.4.*", "symfony/http-kernel": "^5.4", "symfony/intl": "5.4.*", "symfony/mailer": "5.4.*", "symfony/mime": "5.4.*", "symfony/monolog-bundle": "^3.1", "symfony/notifier": "5.4.*", "symfony/process": "5.4.*", "symfony/property-access": "5.4.*", "symfony/property-info": "5.4.*", "symfony/proxy-manager-bridge": "5.4.*", "symfony/runtime": "5.4.*", "symfony/security-bundle": "^5.4", "symfony/security-core": "^5.4", "symfony/security-http": "^5.4", "symfony/serializer": "5.4.*", "symfony/string": "5.4.*", "symfony/swiftmailer-bundle": "^3.5", "symfony/translation": "5.4.*", "symfony/twig-bundle": "5.4.*", "symfony/validator": "5.4.*", "symfony/web-link": "5.4.*", "symfony/webpack-encore-bundle": "^1.12", "symfony/yaml": "5.4.*", "twbs/bootstrap": "^4.0", "twig/extensions": "^1.5", "twig/extra-bundle": "^2.12|^3.0", "twig/intl-extra": "^3.3", "twig/twig": "2.15.3"}, "require-dev": {"phpunit/phpunit": "^9.5", "symfony/browser-kit": "5.4.*", "symfony/css-selector": "5.4.*", "symfony/debug-bundle": "5.4.*", "symfony/maker-bundle": "^1.0", "symfony/phpunit-bridge": "^5.4", "symfony/stopwatch": "5.4.*", "symfony/web-profiler-bundle": "5.4.*"}, "config": {"optimize-autoloader": true, "preferred-install": {"*": "dist"}, "sort-packages": true, "allow-plugins": {"symfony/flex": true, "symfony/runtime": true}}, "autoload": {"psr-4": {"AppBundle\\": "src/AppBundle/", "DomainBundle\\": "src/DomainBundle/", "Open\\": "src/Open/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "ckeditor:install --tag=4.22.1 --clear=drop": "symfony-cmd", "elfinder:install": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "5.4.*"}}}