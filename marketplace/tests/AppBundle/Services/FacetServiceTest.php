<?php

/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 04/07/2019
 * Time: 15:27
 */

namespace Tests\AppBundle\Services;

use AppBundle\Model\Facet;
use AppBundle\Model\FacetValue;
use AppBundle\Services\FacetService;
use AppBundle\Services\IzbergCustomAttributes;
use Open\IzbergBundle\Service\AttributeService;
use Open\IzbergBundle\Service\CategoryService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\DependencyInjection\ContainerInterface;

class FacetServiceTest extends TestCase
{
    use ServiceBuilder;


    private FacetService $facetService;


    public function testPopFacet()
    {
        $facets = [$this->buildFakeFacet()];
        $this->assertEquals($this->buildFakeFacet(), $this->facetService->popFacet($facets, 'toto'));
    }

    /**
     *
     */
    protected function setUp(): void
    {
        parent::setUp();
        $izbergCustomAttributes = $this->createMock(IzbergCustomAttributes::class);
        $AttributeService = $this->createMock(AttributeService::class);
        $categoryService = $this->createMock(CategoryService::class);
        $container = $this->createMock(ContainerInterface::class);
        $this->facetService =  new FacetService([], $izbergCustomAttributes, $AttributeService, $categoryService, $container);
    }

    /**
     * This function will build a fake facet.
     *
     * @return Facet
     */
    private function buildFakeFacet(): Facet
    {
        $facet = new Facet();
        $facet->setFieldName('toto');
        return $facet;
    }
}
