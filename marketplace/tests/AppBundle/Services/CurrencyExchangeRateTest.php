<?php

namespace Tests\AppBundle\Services;

use AppBundle\Provider\ExchangeRates;
use AppBundle\Services\CurrencyExchangeRateService;
use PHPUnit\Framework\TestCase;

class CurrencyExchangeRateTest extends TestCase
{
    public function testComputeExchange()
    {
        $exchangeRatesMock = $this->createMock(ExchangeRates::class);

        $exchangeRatesMock
            ->method('getExchangeRate')
            ->willReturn($this->buildExchangeRate());

        $currencyExchangeRateService = new CurrencyExchangeRateService(['EUR', 'USD'], 'EUR', $exchangeRatesMock);

        $this->assertSame($currencyExchangeRateService->computeExchange(100.00, 'USD'), 125.00);
        $this->assertSame($currencyExchangeRateService->computeExchange(100.25, 'USD'), 125.31);
        $this->assertSame($currencyExchangeRateService->computeExchange(100.2525, 'USD'), 125.31);
    }

    private function buildExchangeRate(): array
    {
        return ['USD' => 1.25];
    }
}
