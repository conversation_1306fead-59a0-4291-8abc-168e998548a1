<?php

namespace Tests\AppBundle\Services;

use AppBundle\Entity\MarketPlace;
use AppBundle\Mapper\ElasticSearchMapper;
use AppBundle\Mapper\IzbergMapper;
use AppBundle\Model\Facet;
use AppBundle\Model\FacetValue;
use AppBundle\Model\InvalidSearchParametersException;
use AppBundle\Model\Offer;
use AppBundle\Model\Search\Filter\SearchFilterQuery;
use AppBundle\Model\SearchResult;
use AppBundle\Model\SortOrderEnum;
use AppBundle\Repository\FavoriRepository;
use AppBundle\Services\CountryService;
use AppBundle\Services\CurrencyExchangeRateService;
use AppBundle\Services\ElasticSearchService;
use AppBundle\Services\FacetService;
use AppBundle\Services\MerchantService;
use AppBundle\Services\OfferService;
use Open\IzbergBundle\Api\ProductOfferApi;
use Open\IzbergBundle\Service\CategoryService;
use Open\LogBundle\Service\LogService;
use PHPUnit\Framework\TestCase;

class OfferServiceTest extends TestCase
{
    use ServiceBuilder;

    private OfferService $offerService;

    private function buildFakeElasticSearchData()
    {
        $data = [];
        $data["hits"]["total"]["value"] = 5;

        $data["hits"]["hits"][0]["_id"] = 30;
        $data["hits"]["hits"][0]["_score"] = 10;


        $offer = [];
        $offer["name"] = "unit test";

        $offer["description"] = "description unit test";

        $offer["price_without_vat"] = 4.32;
        $offer["stock"] = 4.32;

        $offer["merchant"]["id"] = 120;
        $offer["merchant"]["name"] = "merchant name";

        $offer["product"]["id"] = 500;
        $offer["product"]["name"] = "product name";
        $offer["product"]["description"] = "product description";

        $offer["attributes"] = [];

        $data["aggregations"] = [];

        $data["hits"]["hits"][0]["_source"] = $offer;

        $searchResult = (new SearchResult())
            ->setHits($data["hits"]["hits"])
            ->setNbHits($data["hits"]["total"]["value"])
            ->setSize(count($data["hits"]["hits"]))
            ->setNbPages(1);

        return $searchResult;
    }

    public function testSearch()
    {
        $mockSearchService = $this->createMock(ElasticSearchService::class);
        $mockCurrencyExchangeRateService = $this->createMock(CurrencyExchangeRateService::class);

        $mockSearchService->method("search")->willReturn($this->buildFakeElasticSearchData());

        $offerService = new OfferService(
            [],
            new ElasticSearchMapper(
                $this->buildIzbergCustromAttributes(),
                $this->buildAttributeService(),
                $this->buildFavoriRepository(),
                $this->buildCustomsService()
            ),
            $mockSearchService,
            $mockCurrencyExchangeRateService,
            $this->buildCountryService(),
            $this->buildLogService(),
            $this->buildFacetService(),
            $this->buildCategoryService(),
            $this->buildMerchantService(),
            $this->buildProductOfferApi(),
            $this->buildIzbergMapper(),
            $this->buildFavoriRepository()
        );

        //testing incorrect parameter 1/2
        try {
            $result = $offerService->search(
                "",
                $this->buildFakeSearchFilterQuery(),
                30,
                1,
                ElasticSearchService::CUSTOM_ANALYZER,
                "toto",
                null,
                "fr",
                $this->getMarketplace(),
                null
            );
            //must throw an error, so we have nothing to do here
            $this->fail("parameter are invalid (sortDir must not be null), Exception must be thrown");
        } catch (InvalidSearchParametersException $e) {
            $this->assertSame("You must specify sortDir parameter if sortField parameter is specified", $e->getMessage());
        }

        //testing incorrect parameter 2/2
        try {
            $result = $offerService->search(
                "",
                $this->buildFakeSearchFilterQuery(),
                30,
                1,
                ElasticSearchService::CUSTOM_ANALYZER,
                null,
                SortOrderEnum::asc(),
                "fr",
                $this->getMarketplace(),
                null
            );
            //must throw an error, so we have nothing to do here
            $this->fail("parameter are invalid (sortField must not be null), Exception must be thrown");
        } catch (InvalidSearchParametersException $e) {
            $this->assertSame("You must specify sortField parameter if sortOrder parameter is specified", $e->getMessage());
        }

        //testing with good parameters
        try {
            $result = $offerService->search(
                "",
                $this->buildFakeSearchFilterQuery(),
                30,
                1,
                ElasticSearchService::CUSTOM_ANALYZER,
                "price",
                SortOrderEnum::asc(),
                "en",
                $this->getMarketplace(),
                null
            );

            $this->assertSame(5, $result->getNbHits());
            /** @var Offer $offer */
            $offer = $result->getOffers()[0];
            $this->assertSame("unit test", $offer->getName());
            $this->assertSame("description unit test", $offer->getDescription());
            $this->assertSame("product name", $offer->getProduct()->getName());
            $this->assertSame("merchant name", $offer->getMerchant()->getName());
            $this->assertSame(120, $offer->getMerchant()->getId());
        } catch (InvalidSearchParametersException $e) {
            $this->fail("parameters are good => no reason to throw an InvalidParametersException here");
        }
    }

    /**
     * This function will test the buildInfosOffer function.
     */
    public function testBuildInfosOffer()
    {
        $result = $this->offerService->buildInfosOffer([]);
        $this->assertEquals(['message_97076'], $result);
    }


    protected function setUp(): void
    {
        parent::setUp();
        $mockSearchService = $this->createMock(ElasticSearchService::class);
        $mockCurrencyExchangeRateService = $this->createMock(CurrencyExchangeRateService::class);
        $elasticSearchMapper = $this->createMock(ElasticSearchMapper::class);
        $countryService = $this->createMock(CountryService::class);
        $logService = $this->createMock(LogService::class);
        $facetService = $this->createMock(FacetService::class);
        $facetService->method("popFacet")->willReturn($this->buildFakeFacet());
        $categoryService = $this->createMock(CategoryService::class);
        $merchantService = $this->createMock(MerchantService::class);
        $productOfferApi = $this->createMock(ProductOfferApi::class);
        $izbergMapper = $this->createMock(IzbergMapper::class);
        $favoriRepository = $this->createMock(FavoriRepository::class);
        $messageCategories = ['97076' => 'message_97076'];
        $this->offerService = new OfferService(
            $messageCategories,
            $elasticSearchMapper,
            $mockSearchService,
            $mockCurrencyExchangeRateService,
            $countryService,
            $logService,
            $facetService,
            $categoryService,
            $merchantService,
            $productOfferApi,
            $izbergMapper,
            $favoriRepository
        );
    }

    /**
     * This function will build a fake facet.
     *
     * @return Facet
     */
    private function buildFakeFacet(): Facet
    {
        $facet = new Facet();
        $facetValue = new FacetValue();
        $facetValue->setValue(97076);
        $facet->setValues([$facetValue]);

        return $facet;
    }

    private function buildFakeSearchFilterQuery(): SearchFilterQuery
    {
        return $this->createMock(SearchFilterQuery::class);
    }

    private function getMarketplace(): MarketPlace
    {
        return $this->createMock(MarketPlace::class);
    }
}
