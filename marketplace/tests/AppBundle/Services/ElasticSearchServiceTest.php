<?php

namespace Tests\AppBundle\Services;

use AppBundle\Entity\MarketPlace;
use AppBundle\Services\ElasticSearchService;
use Elasticsearch\Client;
use PHPUnit\Framework\TestCase;

class ElasticSearchServiceTest extends TestCase
{
    use ServiceBuilder;

    //empty aggregations
    private function emptyAggregation()
    {
        return [
            'hits' => [
                'total' => [
                    'value' => 0,
                ],
                'hits' => [],
            ],
            'aggregations' => []
        ];
    }

    //no aggregation
    private function noAggregation()
    {
        return [
            'hits' => [
                'total' => [
                    'value' => 0,
                ],
                'hits' => [],
            ],
            'aggregations' => []
        ];
    }

    //with aggregation result
    private function withAggregation()
    {
        $agg = [
            'hits' => [
                'total' => [
                    'value' => 0,
                ],
                'hits' => [],
            ],
            'aggregations' => []
        ];

        $agg["aggregations"]["product.gtin"]["buckets"] = [];
        $agg["aggregations"]["product.gtin"]["buckets"] [] = ["key" => "12341", 'doc_count' => 1];
        $agg["aggregations"]["product.gtin"]["buckets"] [] = ["key" => "12342", 'doc_count' => 1];
        $agg["aggregations"]["product.gtin"]["buckets"] [] = ["key" => "12343", 'doc_count' => 1];
        $agg["aggregations"]["product.gtin"]["buckets"] [] = ["key" => "12344", 'doc_count' => 1];
        $agg["aggregations"]["product.gtin"]["buckets"] [] = ["key" => "12345", 'doc_count' => 1];
        return $agg;
    }


    public function testFindDistinctValuesForField()
    {
        $searchService = new ElasticSearchService(
            "hostname",
            "index",
            false,
            '',
            [],
            [],
            '',
            $this->buildIzbergCustromAttributes(),
            $this->buildFacetService()
        );

        $searchFilterquery = $this->buildSearchFilterQuery();

        //test 1: with no aggregations
        $mockMarketplace = $this->createMock(MarketPlace::class);
        $mockClient = $this->createMock(Client::class);
        $mockClient->method("search")->willReturn($this->noAggregation());
        $searchService->setClient($mockClient);
        $result = $searchService->findDistinctValuesForField("product.gtin", $mockMarketplace, $searchFilterquery);
        $this->assertEmpty($result);


        //test 2: with an empty aggretations
        $mockClient = $this->createMock(Client::class);
        $mockClient->method("search")->willReturn($this->emptyAggregation());
        $searchService->setClient($mockClient);
        $result = $searchService->findDistinctValuesForField("product.gtin", $mockMarketplace, $searchFilterquery);
        $this->assertEmpty($result);

        //test 3: with results
        $mockClient = $this->createMock(Client::class);
        $mockClient->method("search")->willReturn($this->withAggregation());
        $searchService->setClient($mockClient);
        $result = $searchService->findDistinctValuesForField("product.gtin", $mockMarketplace, $searchFilterquery);
        $this->assertNotEmpty($result);
        $this->assertCount(5, $result);
        $this->assertTrue(in_array("12341", $result));
        $this->assertTrue(in_array("12342", $result));
        $this->assertTrue(in_array("12343", $result));
        $this->assertTrue(in_array("12344", $result));
        $this->assertTrue(in_array("12345", $result));
    }
}
