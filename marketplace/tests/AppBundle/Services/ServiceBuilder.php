<?php

namespace Tests\AppBundle\Services;

use AppBundle\Mapper\IzbergMapper;
use AppBundle\Model\Search\Filter\SearchFilterQuery;
use AppBundle\Repository\FavoriRepository;
use AppBundle\Services\CountryService;
use AppBundle\Services\CustomsService;
use AppBundle\Services\FacetService;
use AppBundle\Services\IzbergCustomAttributes;
use AppBundle\Services\MarketPlaceService;
use AppBundle\Services\MerchantService;
use Doctrine\Common\Collections\ArrayCollection;
use Open\IzbergBundle\Api\AsyncAttributeApi;
use Open\IzbergBundle\Api\CategoryApi;
use Open\IzbergBundle\Api\ProductOfferApi;
use Open\IzbergBundle\Service\AttributeService;
use Open\IzbergBundle\Service\CategoryService;
use Open\IzbergBundle\Service\RedisService;
use Open\LogBundle\Service\LogService;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

trait ServiceBuilder
{
    private function buildFacetService(): FacetService
    {
        return new FacetService(
            [],
            $this->buildIzbergCustromAttributes(),
            $this->buildAttributeService(),
            $this->buildCategoryService(),
            $this->createMock(ContainerInterface::class)
        );
    }

    private function buildCategoryService(): CategoryService
    {
        return new CategoryService(
            $this->buildCategoryApi(),
            $this->buildRedisService(),
            $this->buildRequestStack(),
            $this->buildTokenStorageInterface(),
            $this->buildMarketPlaceService()
        );
    }

    private function buildCategoryApi(): CategoryApi
    {
        $categoryApi = $this->createMock(CategoryApi::class);

        $categoryApi->method('getAllCategories')
            ->willReturn(new ArrayCollection());

        return $categoryApi;
    }

    private function buildRedisService(): RedisService
    {
        return $this->createMock(RedisService::class);
    }

    private function buildRequestStack(): RequestStack
    {
        return $this->createMock(RequestStack::class);
    }

    private function buildAttributeService(): AttributeService
    {
        return new AttributeService(
            $this->buildAsyncAttributeApi(),
            $this->buildRedisService(),
            [],
            $this->buildLogService()
        );
    }

    private function buildAsyncAttributeApi(): AsyncAttributeApi
    {
        return $this->createMock(AsyncAttributeApi::class);
    }

    private function buildLogService()
    {
        return $this->createMock(LogService::class);
    }

    private function buildSearchFilterQuery()
    {
        return new SearchFilterQuery();
    }

    private function buildIzbergCustromAttributes(): IzbergCustomAttributes
    {
        $customAttributes = [];
        $customAttributes['country_of_delivery'] = 'test';
        $customAttributes['threshold_1'] = 'test';
        $customAttributes['threshold_1_price'] = 'test';
        $customAttributes['threshold_2'] = 'test';
        $customAttributes['threshold_2_price'] = 'test';
        $customAttributes['threshold_3'] = 'test';
        $customAttributes['threshold_3_price'] = 'test';
        $customAttributes['threshold_4'] = 'test';
        $customAttributes['threshold_4_price'] = 'test';
        $customAttributes['moq'] = 'test';
        $customAttributes['batch_size'] = 'test';
        $customAttributes['stock_management'] = 'test';
        $customAttributes['stock_availability'] = 'test';
        $customAttributes['made_in'] = 'test';
        $customAttributes['zone_restriction'] = 'test';
        $customAttributes['zone_restriction_description'] = 'test';
        $customAttributes['technical_characteristics'] = 'test';
        $customAttributes['vat_rate'] = 'test';
        $customAttributes['company_identification_number'] = 'test';
        $customAttributes['corporate_name'] = 'test';
        $customAttributes['category_list'] = 'test';
        $customAttributes['price_on_quotation'] = 'test';
        $customAttributes['invoice_entity'] = 'test';
        $customAttributes['adapted_company'] = 'test';
        $customAttributes['igg'] = 'test';
        $customAttributes['merchant_comment'] = 'test';
        $customAttributes['merchant_comment_placeholder'] = 'test';
        $customAttributes['recipient_contact'] = 'test';
        $customAttributes['recipient_phone'] = 'test';
        $customAttributes['recipient_comment'] = 'test';
        $customAttributes['offre_secondaire_devis'] = 'test';
        $customAttributes['merchant_branch'] = 'test';
        $customAttributes['notification_order'] = 'test';
        $customAttributes['notification_quote'] = 'test';
        $customAttributes['notification_thread'] = 'test';
        $customAttributes['notification_commercial'] = 'test';
        $customAttributes['epi_inclusive_branch'] = 'test';
        $customAttributes['epi_inclusive_organisation'] = 'test';
        $customAttributes['epi_inclusive_site'] = 'test';
        $customAttributes['epi_inclusive_entity'] = 'test';
        $customAttributes['epi_exclusive_branch'] = 'test';
        $customAttributes['epi_exclusive_organisation'] = 'test';
        $customAttributes['epi_exclusive_site'] = 'test';
        $customAttributes['epi_exclusive_entity'] = 'test';
        $customAttributes['cdat_id'] = 'cdat id';
        $customAttributes['data_sheet'] = '1250_data_sheet';
        $customAttributes['attached_sheet'] = '1255_attached_sheet';
        $customAttributes['merchant_minimum_order_amount'] = '007_minimum_order_amount';
        $customAttributes['color'] = '0460_color';
        return new IzbergCustomAttributes($customAttributes);
    }

    private function buildCountryService(): CountryService
    {
        return $this->createMock(CountryService::class);
    }

    private function buildMerchantService(): MerchantService
    {
        return $this->createMock(MerchantService::class);
    }

    private function buildProductOfferApi(): ProductOfferApi
    {
        return $this->createMock(ProductOfferApi::class);
    }

    private function buildIzbergMapper(): IzbergMapper
    {
        return $this->createMock(IzbergMapper::class);
    }

    private function buildFavoriRepository(): FavoriRepository
    {
        return $this->createMock(FavoriRepository::class);
    }

    private function buildCustomsService(): CustomsService
    {
        return $this->createMock(CustomsService::class);
    }

    private function buildTokenStorageInterface(): TokenStorageInterface
    {
        return $this->createMock(TokenStorageInterface::class);
    }

    private function buildMarketPlaceService(): MarketPlaceService
    {
        return $this->createMock(MarketPlaceService::class);
    }
}
