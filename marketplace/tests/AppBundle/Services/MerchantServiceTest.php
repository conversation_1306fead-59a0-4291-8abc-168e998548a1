<?php

namespace Tests\AppBundle\Services;

use AppBundle\Model\Merchant;
use AppBundle\Repository\MerchantRepository;
use AppBundle\Repository\UserRepository;
use AppBundle\Services\CountryService;
use AppBundle\Services\IzbergCustomAttributes;
use AppBundle\Services\MailService;
use AppBundle\Services\MarketPlaceService;
use AppBundle\Services\MerchantService;
use AppBundle\Services\SearchService;
use AppBundle\Services\SecurityService;
use AppBundle\Services\SerializerService as Serializer;
use AppBundle\Services\TaxService;
use AppBundle\Services\UserBddService;
use Doctrine\ORM\EntityManager;
use DomainBundle\EventDispatcher\MerchantEventDispatcherInterface;
use Illuminate\Encryption\Encrypter;
use Open\IzbergBundle\Api\ApiClientManager;
use Open\IzbergBundle\Api\ApiConfigurator;
use Open\IzbergBundle\Api\AttributeApi;
use Open\IzbergBundle\Api\MerchantApi;
use Open\IzbergBundle\Api\MerchantReviewApi;
use Open\IzbergBundle\Api\PermissionApi;
use Open\IzbergBundle\Factory\OperatorClientFactory;
use Open\IzbergBundle\Service\RedisService;
use Open\LogBundle\Service\LogService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Routing\Router;
use Symfony\Contracts\Translation\TranslatorInterface;

class MerchantServiceTest extends TestCase
{
    /**
     * @var MerchantService
     */
    private $merchantService;
    /**
     * @dataProvider sanatizeSourceDataProvider
     */
    public function testSanatizeSource($source, $expected)
    {
        $sanatizedSource = MerchantService::sanatizeSource($source);

        $this->assertEquals($expected, $sanatizedSource);
    }

    public function sanatizeSourceDataProvider()
    {
        return [
            [
                'total',
                'total',
            ],
            [
                'user-123123',
                'direct',
            ],
            [
                'epsa',
                'direct',
            ],
            [
                'thissourceisnotcorrect',
                'direct',
            ],
            [
                'operator',
                'operator',
            ]
        ];
    }

    /**
     * @param Merchant $merchant
     * @param string   $emailIdentifier
     * @param array    $expected
     * @dataProvider buildNotificationContactsProvider
     */
    public function testBuildNotificationContacts(Merchant $merchant, string $emailIdentifier, array $expected)
    {
        $result = $this->merchantService->buildNotificationContacts($merchant, $emailIdentifier);
        $this->assertEquals($expected, $result);
    }

    /**
     * @return \Generator
     */
    public function buildNotificationContactsProvider()
    {
        $emptyContactMerchant = $this->buildMerchant();
        $merchant = $this->buildMerchant(
            1,
            '<EMAIL>',
            ['<EMAIL>'],
            ['<EMAIL>'],
            ['<EMAIL>'],
            ['<EMAIL>']
        );

        yield [$emptyContactMerchant, MailService::CART_CANCELLED_TO_MERCHANT, ['<EMAIL>']];

        yield [$emptyContactMerchant, "toto", ['<EMAIL>']];

        yield [$merchant, MailService::CART_CANCELLED_TO_MERCHANT, ['<EMAIL>']];

        yield [$merchant, MailService::QUOTE_NEW_QUOTE_TO_VENDOR, ['<EMAIL>']];

        yield [$merchant, MailService::TICKET_THREAD_UPDATED_BY_BUYER_TO_VENDOR, ['<EMAIL>']];

        yield [$merchant, MailService::TICKET_THREAD_UPDATED_BY_BUYER_TO_VENDOR, ['<EMAIL>']];
    }

    /**
     *
     */
    protected function setUp(): void
    {
        $entityManager = $this->createMock(EntityManager::class);
        $permissionApi = $this->createMock(PermissionApi::class);
        $merchantApi = $this->createMock(MerchantApi::class);
        $merchantRepository = $this->createMock(MerchantRepository::class);
        $logger = $this->createMock(LogService::class);
        $mailer = $this->createMock(MailService::class);
        $countryService = $this->createMock(CountryService::class);
        $securityService = $this->createMock(SecurityService::class);
        $middleBaseUrl = [];
        $cache = $this->createMock(RedisService::class);
        $transportAssignments = [];
        $key = "";
        $router = $this->createMock(Router::class);
        $attributeApi = $this->createMock(AttributeApi::class);
        $customAttributes = $this->createMock(IzbergCustomAttributes::class);
        $taxService = $this->createMock(TaxService::class);
        $merchantReviewApi = $this->createMock(MerchantReviewApi::class);
        $serializer = $this->createMock(Serializer::class);
        $userRepository = $this->createMock(UserRepository::class);
        $translator = $this->createMock(TranslatorInterface::class);
        $userService = $this->createMock(UserBddService::class);
        $merchantEventDispatcher = $this->createMock(MerchantEventDispatcherInterface::class);
        $apiClientManager = $this->createMock(ApiClientManager::class);
        $apiConfigurator = $this->createMock(ApiConfigurator::class);
        $marketPlaceService = $this->createMock(MarketPlaceService::class);
        $encrypter = $this->createMock(Encrypter::class);
        $searchService = $this->createMock(SearchService::class);

        $this->merchantService = new MerchantService(
            "<EMAIL>",
            $middleBaseUrl,
            $encrypter,
            $entityManager,
            $permissionApi,
            $merchantApi,
            $merchantRepository,
            $logger,
            $mailer,
            $countryService,
            $marketPlaceService,
            $securityService,
            $cache,
            $router,
            $attributeApi,
            $customAttributes,
            $taxService,
            $merchantReviewApi,
            $serializer,
            $userRepository,
            $translator,
            $userService,
            $merchantEventDispatcher,
            $apiClientManager,
            $apiConfigurator,
            $searchService,
            $this->createMock(OperatorClientFactory::class)
        );
    }

    /**
     * This function will build a dummy merchant.
     *
     * @param int    $id
     * @param string $mainContactEmail
     * @param array  $notificationOrderContacts
     * @param array  $notificationQuoteContacts
     * @param array  $notificationThreadContacts
     * @param array  $notificationCommercialContacts
     *
     * @return Merchant
     */
    private function buildMerchant(
        int $id = 0,
        string $mainContactEmail = "<EMAIL>",
        array $notificationOrderContacts = [],
        array $notificationQuoteContacts = [],
        array $notificationThreadContacts = [],
        array $notificationCommercialContacts = []
    ): Merchant {
        $merchant = new Merchant();
        $merchant->setId($id)
            ->setMainContactEmail($mainContactEmail)
        ->setNotificationOrderContacts($notificationOrderContacts)
        ->setNotificationQuoteContacts($notificationQuoteContacts)
        ->setNotificationThreadContacts($notificationThreadContacts)
        ->setNotificationCommercialContacts($notificationCommercialContacts);

        return $merchant;
    }
}
