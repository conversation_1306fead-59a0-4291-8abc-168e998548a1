{#
/**
 * @file
 * Twitter Bootstrap v4 Sliding pagination control implementation.
 *
 * View that can be used with the pagination module
 * from the Twitter Bootstrap v4 CSS Toolkit
 * https://v4-alpha.getbootstrap.com/components/pagination/
 *
 * <AUTHOR> <<EMAIL>>
 */
#}

{% if pageCount > 1 %}
    <ul class="pagination">
        {% if previous is defined %}
            <li class="page-item previous">
                <a rel="prev" href="{{ path(route, query|merge({(pageParameterName): previous})) }}">
                    <span class="page-link">
                        <i class="arrow left"></i>
                        {{ 'label_previous'|trans({}, 'AppBundle') }}
                    </span>
                </a>
            </li>
        {% else %}
            <li class="page-item disabled previous">
                <span class="page-link">
                    <i class="arrow left"></i>
                    {{ 'label_previous'|trans({}, 'AppBundle') }}</span>
            </li>
        {% endif %}

        {% if startPage > 1 %}
            <li class="page-item">
                <a href="{{ path(route, query|merge({(pageParameterName): 1})) }}" class="page-link">1</a>
            </li>
            {% if startPage == 3 %}
                <li class="page-item">
                    <a href="{{ path(route, query|merge({(pageParameterName): 2})) }}" class="page-link">2</a>
                </li>
            {% elseif startPage != 2 %}
                <li class="page-item disabled">
                    <span class="page-link">&hellip;</span>
                </li>
            {% endif %}
        {% endif %}

        {% for page in pagesInRange %}
            {% if page != current %}
                <li class="page-item">
                    <a href="{{ path(route, query|merge({(pageParameterName): page})) }}" class="page-link">{{ page }}</a>
                </li>
            {% else %}
                <li class="active page-item">
                    <span class="page-link">{{ page }}</span>
                </li>
            {% endif %}

        {% endfor %}

        {% if pageCount > endPage %}
            {% if pageCount > (endPage + 1) %}
                {% if pageCount > (endPage + 2) %}
                    <li class="page-item disabled">
                        <span class="page-link">&hellip;</span>
                    </li>
                {% else %}
                    <li class="page-item">
                        <a href="{{ path(route, query|merge({(pageParameterName): (pageCount - 1)})) }}" class="page-link">{{ pageCount -1 }}</a>
                    </li>
                {% endif %}
            {% endif %}
            <li>
                <a href="{{ path(route, query|merge({(pageParameterName): pageCount})) }}" class="page-link">{{ pageCount }}</a>
            </li>
        {% endif %}

        {% if next is defined %}
            <li class="page-item next">
                <a rel="next" href="{{ path(route, query|merge({(pageParameterName): next})) }}">
                    <span class="page-link">{{ 'label_next'|trans({}, 'AppBundle') }}
                        <i class="arrow right"></i>
                    </span>
                </a>
            </li>
        {% else %}
            <li class="page-item disabled next">
                <span class="page-link">{{ 'label_next'|trans({}, 'AppBundle') }}
                <i class="arrow right"></i>
                </span>
            </li>
        {% endif %}
    </ul>
{% endif %}