{% extends "JMSJobQueueBundle::base.html.twig" %}
{% import "JMSJobQueueBundle:Job:macros.html.twig" as macros %}

{% block title %}Job "{{ job.command }}" (ID: {{ job.id }})" - {{ parent() }}{% endblock %}

{% block content %}
<ul class="breadcrumb">
    <li><a href="{{ path("jms_jobs_overview") }}">Jobs</a> <span class="divider">/</span></li>
    <li class="active">Job "{{ job.command }}"</li>
</ul>

<div class="page-header">
    <h1>Job "{{ job.command }}" <small>(ID: {{ job.id }})</small></h1>
</div>

<table class="table table-bordered table-striped">
    <tr>
        <th width="20%">Command</th>
        <td>{{ macros.command(job, true) }}</td>
    </tr>
    <tr>
        <th>State</th>
        <td>{{ macros.state(job) }}</td>
    </tr>
    <tr>
        <th>Created</th>
        <td>{{ macros.ago(job.createdAt) }}</td>
    </tr>
    {% if job.closedAt %}
    <tr>
        <th>Runtime</th>
        <td>{{ macros.runtime(job) }}</td>
    </tr>
    <tr>
        <th>Closed</th>
        <td>{{ macros.ago(job.closedAt) }}</td>
    </tr>
    {% endif %}
    {% if job.isRetryJob() %}
    <tr>
        <th>Original Job</th>
        <td><a href="{{ path("jms_jobs_details", {"id": job.originalJob.id}) }}">#{{ job.originalJob.id }}</a> {{ macros.state(job.originalJob) }}</td>
    </tr>
    {% endif %}
    {% if relatedEntities|length > 0 %}
    <tr>
        <th>Related Entities</th>
        <td>
            {%- for entity in relatedEntities %}
                {%- if entity.raw is jms_job_queue_linkable -%}
                    <a href="{{ jms_job_queue_path(entity.raw) }}">{{ entity.raw|jms_job_queue_linkname }}</a>
                {%- else -%}
                    {{ entity.class }} ({{ entity.id }})
                {%- endif -%}
                {% if not loop.last %}, {% endif -%}
            {% endfor -%}
        </td>
    </tr>
    {% endif %}
    {% if job.dependencies|length > 0 %}
    <tr>
        <th>Dependencies</th>
        <td>
            {%- for dep in job.dependencies -%}
                <a href="{{ path("jms_jobs_details", {"id": dep.id}) }}" title="{{ dep.command }}{% for arg in dep.args %} {{ arg }}{% endfor %} (ID {{ dep.id }})">{{ dep.command }}</a> {{ macros.state(dep) }}
                {%- if not loop.last %}, {% endif -%}
            {%- endfor -%}
    </tr>
    {% endif %}
    {% if incomingDependencies|length > 0 %}
    <tr>
        <th>Incoming Dependencies</th>
        <td>
            {%- for dep in incomingDependencies -%}
                <a href="{{ path("jms_jobs_details", {"id": dep.id}) }}" title="{{ dep.command }}{% for arg in dep.args %} {{ arg }}{% endfor %} (ID {{ dep.id }})">{{ dep.command }}</a> {{ macros.state(dep) }}
            {%- endfor -%}
        </td>
    </tr>
    {% endif %}
</table>

{% if job.retryJobs|length > 0 %}
<h3>Retry Jobs</h3>

<table class="table table-bordered table-striped table-condensed">
    <thead>
        <tr>
            <th>ID</th>
            <th>Created</th>
            <th>State</th>
        </tr>
    </thead>

    <tbody>
        {% for retryJob in job.retryJobs %}
        <tr>
            <td><a href="{{ path("jms_jobs_details", {"id": retryJob.id}) }}">{{ retryJob.id }}</a></td>
            <td>{{ macros.ago(retryJob.createdAt) }}</td>
            <td>{{ macros.state(retryJob) }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

{% endif %}

{% if job.stackTrace is not empty %}
<h3>Stack Trace</h3>
{% for position, ex in job.stackTrace.toarray %}
    <div class="block">
        <h4>
            {{ ex.class|abbr_class }}: <small>{{ ex.message|nl2br|format_file_from_text }}</small>&nbsp;
            {% spaceless %}
            <a href="#" onclick="toggle('traces_{{ position }}', 'traces'); switchIcons('icon_traces_{{ position }}_open', 'icon_traces_{{ position }}_close'); return false;">
                <img class="toggle" id="icon_traces_{{ position }}_close" alt="-" src="{{ asset('bundles/framework/images/blue_picto_less.gif') }}" style="visibility: hidden;" />
                <img class="toggle" id="icon_traces_{{ position }}_open" alt="+" src="{{ asset('bundles/framework/images/blue_picto_more.gif') }}" style="visibility: display; margin-left: -18px" />
            </a>
            {% endspaceless %}
        </h4>
        <a id="traces_link_{{ position }}"></a>
        <ol class="traces list_ex" id="traces_{{ position }}" style="display: none;">
            {% set _is_first_user_code = true %}
            {% for i, trace in ex.trace %}
                {% set _display_code_snippet = _is_first_user_code and ('/vendor/' not in trace.file) and ('/var/cache/' not in trace.file) and (trace.file is not empty) %}
                {% if _display_code_snippet %}{% set _is_first_user_code = false %}{% endif %}
                <li>
                    {% include 'TwigBundle:Exception:trace.html.twig' with { 'prefix': position, 'i': i, 'trace': trace, '_display_code_snippet': _display_code_snippet } only %}
                </li>
            {% endfor %}
        </ol>
    </div>
{% endfor %}
{% endif %}

{% if job.output is not empty %}
<h3>Output</h3>
<pre style="max-height:300px; overflow:auto;">{{ job.output }}</pre>
{% endif %}

{% if job.errorOutput is not empty %}
<h3>Error Output</h3>
<pre style="max-height:300px; overflow:auto;">{{ job.errorOutput }}</pre>
{% endif %}

{% if job.state == 'failed' or job.errorOutput is not empty %}
<div class="alert alert-warn">
    <a href="{{ path("jms_jobs_retry_job", {"id": job.id}) }}" class="pull-right btn btn-danger">
        <i class="icon-repeat icon-white"></i>
        Retry Job
    </a>
    <p style="margin-bottom: 0; line-height: 30px">Click on the next button to create a new job to retry this failed one.</p>
</div>
{% endif %}

{% if statisticData is not empty %}
<h3>Statistics</h3>
<div id="statistics" style="width:900px; height:300px;"></div>
{% endif %}

{% endblock %}

{% block javascripts %}
    {{ parent() }}

    {% if statisticData is not empty %}
    <script type="text/javascript" src="https://www.google.com/jsapi"></script>
    <script type="text/javascript">
        google.load("visualization", "1", {packages:["corechart"]});
        google.setOnLoadCallback(function() {
            var chart = new google.visualization.LineChart($('#statistics').get(0));
            chart.draw(google.visualization.arrayToDataTable({{ statisticData|json_encode|raw }}), {{ statisticOptions|json_encode|raw }});
        });
    </script>
    {% endif %}

    <script language="javascript" type="text/javascript">//<![CDATA[

        function toggle(id, clazz) {
            var el = document.getElementById(id),
                current = el.style.display,
                i;

            if (clazz) {
                var tags = document.getElementsByTagName('*');
                for (i = tags.length - 1; i >= 0 ; i--) {
                    if (tags[i].className === clazz) {
                        tags[i].style.display = 'none';
                    }
                }
            }

            el.style.display = current === 'none' ? 'block' : 'none';
        }

        function switchIcons(id1, id2) {
            var icon1, icon2, visibility1, visibility2;

            icon1 = document.getElementById(id1);
            icon2 = document.getElementById(id2);

            visibility1 = icon1.style.visibility;
            visibility2 = icon2.style.visibility;

            icon1.style.visibility = visibility2;
            icon2.style.visibility = visibility1;
        }
      //]]></script>
{% endblock %}
