<?php

// This script generates a CSV file to test buyer importation performance

// add csv line to it
// A0000000;<PERSON>;<PERSON><PERSON>;<PERSON><PERSON>;<PERSON><PERSON>;<EMAIL>;+33 1 84 20 10 10;TGS/TGITS/APS/PJA;********;3F1LOG0601;PLD-NEWTON(FRA);
//TOTAL PLD-NEWTON(FRA);9 PLACE DES VOSGES LA DÉFENSE 5;92924;PARIS LA DEFENSE;France;FRA;TOTAL MARKETING FRANCE;
//TOTAL MARKETING FRANCE;Interne;562 AVE DU PARC DE L'ILE;;92000;NANTERRE;France;FRA;29/06/2020;31/12/2099;06/08/2019
$lineType = [
    'A0000000',
    'John',
    'Doe',
    'M.',
    'EN',
    '<EMAIL>',
    '+33 1 84 20 10 10',
    'TGS/TGITS/APS/PJA',
    '********',
    '3F1LOG0601',
    'PLD-NEWTON(FRA)',
    'TOTAL PLD-NEWTON(FRA)',
    '9 PLACE DES VOSGES LA DÉFENSE 5',
    '92924',
    'PARIS LA DEFENSE',
    'France',
    'FRA',
    'TOTAL MARKETING FRANCE',
    'TOTAL MARKETING FRANCE',
    'Interne',
    '562 AVE DU PARC DE L\'ILE',
    '',
    '92000',
    'NANTERRE',
    'France',
    'FRA',
    '29/06/2020',
    '31/12/2099',
    '06/08/2019',
];

$csvFileHandle = fopen(__DIR__ . '/app/import/total_buyers_test.csv', 'a');
fwrite($csvFileHandle,"\n");

for($i = 0; $i < 96000; $i++) {
    $line = $lineType;
    $line[0] = 'TEST'.$i;
    $line[5] = 'test'.$i.'@total.com';

    fputcsv($csvFileHandle, $line, ';');
    unset($line);
}

fclose($csvFileHandle);
