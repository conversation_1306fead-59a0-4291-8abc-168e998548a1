<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240912142924 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Extend length of "title" fields in "quote" table';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql("
            ALTER TABLE `quote` CHANGE `init_offer_title` `init_offer_title` VARCHAR(500)  CHARACTER SET utf8  COLLATE utf8_unicode_ci  NOT NULL  DEFAULT '';
            ALTER TABLE `quote` CHANGE `subject` `subject` VARCHAR(700)  CHARACTER SET utf8  COLLATE utf8_unicode_ci  NULL  DEFAULT NULL;
            ALTER TABLE `quote` CHANGE `title` `title` VARCHAR(500)  CHARACTER SET utf8  COLLATE utf8_unicode_ci  NULL  DEFAULT NULL;
        ");

    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql("
            ALTER TABLE `quote` CHANGE `init_offer_title` `init_offer_title` VARCHAR(150)  CHARACTER SET utf8  COLLATE utf8_unicode_ci  NOT NULL  DEFAULT '';
            ALTER TABLE `quote` CHANGE `subject` `subject` VARCHAR(150)  CHARACTER SET utf8  COLLATE utf8_unicode_ci  NULL  DEFAULT NULL;
            ALTER TABLE `quote` CHANGE `title` `title` VARCHAR(150)  CHARACTER SET utf8  COLLATE utf8_unicode_ci  NULL  DEFAULT NULL;
        ");

    }
}
