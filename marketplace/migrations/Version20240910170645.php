<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240910170645 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Ensure users are disabled if their user_enabler is disabled';
    }

    public function up(Schema $schema): void
    {
      // Ensure users are disabled if their user_enabler is disabled
      $this->addSql("
            UPDATE users u
            SET u.enabled = false
            WHERE u.enabled = true
            AND u.enabler_id IN (
                SELECT u_e.id
                FROM user_enabler u_e
                WHERE u_e.enabled = false
            );
        ");
    }

    public function down(Schema $schema): void {}
}
