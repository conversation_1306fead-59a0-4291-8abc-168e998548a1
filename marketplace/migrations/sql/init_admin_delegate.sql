-- TOTALMP-1716 add delegation admin : here set new colunm values with already configured delegation

-- update delegation type with 'USER'  as already configured delegation are done by user/manager.
update users set users.delegate_user_type = 'USER' where users.id in (select child_user_id from user_to_user_relationship where isDelegate = true) and users.delegate_user_type is null ;

-- update delegation username with user firstname and lastname as already configured delegation are done by user
update users set users.delegate_username = concat(users.firstname,' ',users.lastname)  where users.id in (select child_user_id from user_to_user_relationship where isDelegate = true) and users.delegate_username is null;

-- init delegate_label in user to have the history of changes
CREATE or replace VIEW  delegate AS (
select uc.id,GROUP_CONCAT(up.username SEPARATOR ',') as delegate from user_to_user_relationship uu
join users up on up.id = uu.parent_user_id
join users uc on uc.id = uu.child_user_id
where uu.isDelegate = true
group by uc.id
);
update users set delegate_label = (select delegate from delegate where users.id = delegate.id);
drop VIEW if exists delegate;
