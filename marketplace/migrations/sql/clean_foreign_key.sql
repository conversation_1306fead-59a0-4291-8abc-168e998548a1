delimiter //
drop procedure if exists clean_fk ;
create procedure clean_fk(IN t_name VARCHAR(100),IN c_name VARCHAR(100) )

begin
DECLARE fk_name VARCHAR(100);
    SELECT constraint_name INTO  fk_name FROM information_schema.key_column_usage where TABLE_NAME = t_name and  REFERENCED_TABLE_SCHEMA = 'total'  and column_name  = c_name;

    -- Only insert rows if the platform was found
    if fk_name is not null then
         SELECT CONCAT( 'ALTER TABLE `',t_name,'` DROP FOREIGN KEY `', fk_name,'`') INTO @sqlst;
        PREPARE stmt FROM @sqlst;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        SET @sqlst = NULL;
    end if;

end;

//

delimiter ;

-- Execute the procedure
call clean_fk('users','site_shipping_address_id');
call clean_fk('company','category_id');


-- Drop the procedure
drop procedure clean_fk;
