INSERT INTO marketplace
(id, discriminator, name, conf_key, es_index, locale, billing_address, billing_address2, billing_zip_code, billing_city, billing_country_code, billing_name, languages)
VALUES(1, 'FRA', 'france', 'default', 'offers_france', 'fr', 'Experbuy SAS', '4 Quai des Etroits', '69005', 'Lyon', 'france', 'EPSA', 'a:1:{i:0;s:2:"fr";}');

INSERT INTO marketplace
(id, discriminator, name, conf_key, es_index, locale, billing_address, billing_address2, billing_zip_code, billing_city, billing_country_code, billing_name, languages)
VALUES(2, 'DEU', 'germany', 'sand_clickandbuy_02', 'offers_germany', 'de', '', '', '', '', 'germany', 'EPSA', 'a:1:{i:0;s:2:"de";}');

INSERT INTO marketplace
(id, discriminator, name, conf_key, es_index, locale, billing_address, billing_address2, billing_zip_code, billing_city, billing_country_code, billing_name, languages)
VALUES(3, 'BEL', 'belgium', 'sand_click_and_buy_belgium', 'offers_belgium', 'fr', '', '', '', '', 'belgium', 'EPSA', 'a:2:{i:0;s:2:"fr";i:1;s:2:"nl";}');

alter table country DROP IF EXISTS  market_place;
alter table country DROP IF EXISTS  izberg_name_space;
update merchant set merchant.marketplace_id = (select id from marketplace where name = 'france') where marketplace_id is null;
