ALTER TABLE `users` CHANGE `roles` `roles` LONGTEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:array)';
ALTER TABLE `action_historization` CHANGE `change_set` `change_set` LONGTEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:array)';
ALTER TABLE `jms_jobs` CHANGE `args` `args` LONGTEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:array)';
ALTER TABLE `jms_jobs` CHANGE `stackTrace` `stackTrace` LONGBLOB NULL DEFAULT NULL;
