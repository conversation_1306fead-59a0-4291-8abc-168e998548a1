%!PS-Adobe-3.0
%%Creator: cairo 1.16.0 (https://cairographics.org)
%%CreationDate: Fri Apr 10 10:42:32 2020
%%Pages: 1
%%DocumentData: Clean7Bit
%%LanguageLevel: 2
%%DocumentMedia: 54x11mm 152 31 0 () ()
%%BoundingBox: 0 0 152 31
%%EndComments
%%BeginProlog
/languagelevel where
{ pop languagelevel } { 1 } ifelse
2 lt { /Helvetica findfont 12 scalefont setfont 50 500 moveto
  (This print job requires a PostScript Language Level 2 printer.) show
  showpage quit } if
/q { gsave } bind def
/Q { grestore } bind def
/cm { 6 array astore concat } bind def
/w { setlinewidth } bind def
/J { setlinecap } bind def
/j { setlinejoin } bind def
/M { setmiterlimit } bind def
/d { setdash } bind def
/m { moveto } bind def
/l { lineto } bind def
/c { curveto } bind def
/h { closepath } bind def
/re { exch dup neg 3 1 roll 5 3 roll moveto 0 rlineto
      0 exch rlineto 0 rlineto closepath } bind def
/S { stroke } bind def
/f { fill } bind def
/f* { eofill } bind def
/n { newpath } bind def
/W { clip } bind def
/W* { eoclip } bind def
/BT { } bind def
/ET { } bind def
/BDC { mark 3 1 roll /BDC pdfmark } bind def
/EMC { mark /EMC pdfmark } bind def
/cairo_store_point { /cairo_point_y exch def /cairo_point_x exch def } def
/Tj { show currentpoint cairo_store_point } bind def
/TJ {
  {
    dup
    type /stringtype eq
    { show } { -0.001 mul 0 cairo_font_matrix dtransform rmoveto } ifelse
  } forall
  currentpoint cairo_store_point
} bind def
/cairo_selectfont { cairo_font_matrix aload pop pop pop 0 0 6 array astore
    cairo_font exch selectfont cairo_point_x cairo_point_y moveto } bind def
/Tf { pop /cairo_font exch def /cairo_font_matrix where
      { pop cairo_selectfont } if } bind def
/Td { matrix translate cairo_font_matrix matrix concatmatrix dup
      /cairo_font_matrix exch def dup 4 get exch 5 get cairo_store_point
      /cairo_font where { pop cairo_selectfont } if } bind def
/Tm { 2 copy 8 2 roll 6 array astore /cairo_font_matrix exch def
      cairo_store_point /cairo_font where { pop cairo_selectfont } if } bind def
/g { setgray } bind def
/rg { setrgbcolor } bind def
/d1 { setcachedevice } bind def
/cairo_data_source {
  CairoDataIndex CairoData length lt
    { CairoData CairoDataIndex get /CairoDataIndex CairoDataIndex 1 add def }
    { () } ifelse
} def
/cairo_flush_ascii85_file { cairo_ascii85_file status { cairo_ascii85_file flushfile } if } def
/cairo_image { image cairo_flush_ascii85_file } def
/cairo_imagemask { imagemask cairo_flush_ascii85_file } def
/cairo_set_page_size {
  % Change paper size, but only if different from previous paper size otherwise
  % duplex fails. PLRM specifies a tolerance of 5 pts when matching paper size
  % so we use the same when checking if the size changes.
  /setpagedevice where {
    pop currentpagedevice
    /PageSize known {
      2 copy
      currentpagedevice /PageSize get aload pop
      exch 4 1 roll
      sub abs 5 gt
      3 1 roll
      sub abs 5 gt
      or
    } {
      true
    } ifelse
    {
      2 array astore
      2 dict begin
        /PageSize exch def
        /ImagingBBox null def
      currentdict end
      setpagedevice
    } {
      pop pop
    } ifelse
  } {
    pop
  } ifelse
} def
%%EndProlog
%%BeginSetup
%%EndSetup
%%Page: 1 1
%%BeginPageSetup
%%PageMedia: 54x11mm
%%PageBoundingBox: 0 0 152 31
152 31 cairo_set_page_size
%%EndPageSetup
q 0 0 152 31 rectclip
1 0 0 -1 0 31 cm q
q
0 0 152 31 re W n
[ 1 0 0 1 0 0 ] concat
  q
0.0705882 0.270588 0.560784 rg
2.836 2.52 m 4.48 0.84 6.574 0 9.113 0 c 12.512 0 14.996 1.129 16.566 3.379
 c 17.438 4.645 17.902 5.914 17.965 7.188 c 13.699 7.188 l 13.43 6.211 13.078
 5.473 12.652 4.973 c 11.895 4.086 10.77 3.645 9.277 3.645 c 7.758 3.645
 6.559 4.27 5.68 5.52 c 4.801 6.77 4.363 8.543 4.363 10.832 c 4.363 13.121
 4.824 14.836 5.754 15.977 c 6.68 17.117 7.855 17.688 9.281 17.688 c 10.746
 17.688 11.859 17.199 12.629 16.219 c 13.055 15.695 13.406 14.902 13.684
 13.852 c 17.926 13.852 l 17.559 16.078 16.625 17.887 15.121 19.281 c 13.617
 20.676 11.695 21.371 9.348 21.371 c 6.441 21.371 4.156 20.43 2.492 18.547
 c 0.832 16.652 0 14.059 0 10.762 c 0 7.199 0.945 4.449 2.836 2.52 c f
  Q
Q
0.0705882 0.270588 0.560784 rg
21.137 0.387 3.949 20.418 re f
32.996 3.906 m 28.992 3.906 l 28.992 0.262 l 32.996 0.262 l h
28.992 5.707 m 32.996 5.707 l 32.996 20.805 l 28.992 20.805 l h
28.992 5.707 m f
q
0 0 152 31 re W n
[ 1 0 0 1 0 0 ] concat
  q
0.0705882 0.270588 0.560784 rg
45.848 11.121 m 45.773 10.559 45.586 10.051 45.277 9.598 c 44.836 8.988
 44.145 8.684 43.211 8.684 c 41.879 8.684 40.965 9.344 40.477 10.664 c 40.215
 11.367 40.086 12.297 40.086 13.457 c 40.086 14.563 40.215 15.449 40.477
 16.125 c 40.945 17.379 41.836 18.008 43.141 18.008 c 44.066 18.008 44.723
 17.758 45.113 17.258 c 45.5 16.762 45.738 16.113 45.82 15.32 c 49.852 15.32
 l 49.758 16.52 49.324 17.656 48.547 18.727 c 47.309 20.453 45.473 21.316
 43.043 21.316 c 40.613 21.316 38.824 20.598 37.68 19.156 c 36.531 17.715
 35.957 15.848 35.957 13.555 c 35.957 10.965 36.59 8.949 37.859 7.508 c 
39.125 6.066 40.871 5.348 43.098 5.348 c 44.992 5.348 46.543 5.77 47.75 
6.621 c 48.957 7.469 49.672 8.973 49.895 11.121 c h
45.848 11.121 m f
0.960784 0.6 0.0705882 rg
74.492 12.27 m 75.102 11.395 76.098 10.527 77.484 9.668 c 77.91 9.402 l
 77.32 8.738 76.863 8.059 76.539 7.363 c 76.219 6.672 76.055 5.949 76.055
 5.199 c 76.055 3.711 76.563 2.551 77.57 1.711 c 78.578 0.875 79.887 0.457
 81.496 0.457 c 82.961 0.457 84.176 0.887 85.133 1.742 c 86.09 2.598 86.57
 3.695 86.57 5.027 c 86.57 6.234 86.281 7.211 85.707 7.961 c 85.133 8.711
 84.289 9.406 83.18 10.043 c 86.113 13.602 l 86.449 13.121 86.711 12.602
 86.891 12.035 c 87.074 11.473 87.168 10.879 87.18 10.25 c 90.684 10.25 
l 90.617 11.492 90.32 12.746 89.785 14.016 c 89.488 14.73 89.012 15.516 
88.348 16.375 c 92.055 20.805 l 87.277 20.805 l 85.855 19.074 l 85.164 19.715
 84.52 20.191 83.922 20.496 c 82.859 21.043 81.633 21.316 80.242 21.316 
c 78.148 21.316 76.516 20.707 75.34 19.488 c 74.164 18.27 73.578 16.918 
73.578 15.43 c 73.578 14.203 73.883 13.148 74.492 12.27 c 78.348 17.16 m
 78.906 17.727 79.625 18.008 80.496 18.008 c 81.156 18.008 81.777 17.859
 82.367 17.563 c 82.953 17.266 83.43 16.934 83.797 16.57 c 79.977 11.918
 l 78.977 12.574 78.316 13.152 77.992 13.652 c 77.672 14.152 77.512 14.699
 77.512 15.301 c 77.512 15.977 77.789 16.598 78.348 17.16 c 80.09 6.227 
m 80.262 6.492 80.633 6.969 81.207 7.656 c 81.781 7.266 82.195 6.938 82.445
 6.668 c 82.922 6.168 83.16 5.629 83.16 5.043 c 83.16 4.621 83.02 4.242 
82.73 3.906 c 82.441 3.574 82.008 3.406 81.426 3.406 c 81.059 3.406 80.719
 3.504 80.402 3.699 c 79.926 3.984 79.684 4.438 79.684 5.059 c 79.684 5.43
 79.82 5.816 80.09 6.227 c f
  Q
Q
0.0705882 0.270588 0.560784 rg
52.473 0.457 m 56.352 0.457 l 56.352 11.473 l 61.328 5.777 l 66.238 5.777
 l 58.629 13.891 l 67.102 20.891 l 61.645 20.805 l 56.352 16.055 l 56.352
 20.805 l 52.473 20.805 l h
52.473 0.457 m f
q
0 0 152 31 re W n
[ 1 0 0 1 0 0 ] concat
  q
0.0705882 0.270588 0.560784 rg
115.953 2.563 m 116.578 3.43 116.891 4.469 116.891 5.68 c 116.891 6.926
 116.574 7.926 115.945 8.684 c 115.59 9.109 115.074 9.496 114.387 9.848 
c 115.43 10.227 116.215 10.828 116.746 11.648 c 117.277 12.469 117.539 13.469
 117.539 14.641 c 117.539 15.852 117.238 16.934 116.633 17.895 c 116.246
 18.535 115.766 19.07 115.188 19.504 c 114.535 20 113.766 20.344 112.879
 20.527 c 111.992 20.711 111.031 20.805 109.992 20.805 c 100.793 20.805 
l 100.793 0.387 l 110.66 0.387 l 113.152 0.426 114.914 1.148 115.953 2.563
 c 104.867 3.934 m 104.867 8.434 l 109.828 8.434 l 110.715 8.434 111.434
 8.266 111.988 7.93 c 112.543 7.594 112.816 6.996 112.816 6.137 c 112.816
 5.184 112.453 4.559 111.719 4.254 c 111.09 4.039 110.285 3.934 109.309 
3.934 c h
104.867 11.816 m 104.867 17.258 l 109.824 17.258 l 110.711 17.258 111.398
 17.137 111.891 16.898 c 112.785 16.457 113.234 15.605 113.234 14.348 c 
113.234 13.289 112.801 12.559 111.934 12.16 c 111.449 11.941 110.77 11.824
 109.891 11.816 c h
104.867 11.816 m f
138.949 23.699 m 139.449 23.727 l 139.836 23.746 140.203 23.73 140.555 
23.684 c 140.906 23.641 141.203 23.535 141.441 23.367 c 141.672 23.211 141.887
 22.883 142.086 22.383 c 142.285 21.887 142.371 21.582 142.344 21.469 c 
136.793 5.707 l 141.184 5.707 l 144.484 16.844 l 147.598 5.707 l 151.797
 5.707 l 146.621 20.555 l 145.621 23.418 144.828 25.195 144.246 25.883 c
 143.664 26.57 142.5 26.914 140.75 26.914 c 140.398 26.914 140.117 26.91
 139.906 26.906 c 139.691 26.902 139.375 26.887 138.949 26.855 c h
138.949 23.699 m f
0.878431 0.0196078 0.2 rg
117.813 7.73 m 117.813 11.863 l 120.59 11.863 l 120.59 15.73 l 120.59 15.73
 120.441 20.668 124.457 20.891 c 134.406 20.891 l 134.406 7.73 l 130.383
 7.73 l 130.383 9.059 130.352 10.645 130.352 11.969 c 130.352 12.824 130.355
 13.676 130.313 14.531 c 130.254 15.762 130.039 16.969 129.008 17.535 c 
128.34 17.902 127.512 17.988 126.781 17.852 c 125.832 17.676 125.242 17.309
 125.004 16.254 c 124.766 15.203 124.824 14.008 124.82 12.938 c 124.82 12.059
 124.82 11.176 124.82 10.297 c 124.824 10.164 124.758 7.73 124.762 7.73 
c h
117.813 7.73 m f
125.824 24.965 m 125.824 26.105 124.898 27.027 123.762 27.027 c 122.621
 27.027 121.699 26.105 121.699 24.965 c 121.699 23.828 122.621 22.906 123.762
 22.906 c 124.898 22.906 125.824 23.828 125.824 24.965 c f
134.176 24.965 m 134.176 26.105 133.25 27.027 132.113 27.027 c 130.977 
27.027 130.051 26.105 130.051 24.965 c 130.051 23.828 130.977 22.906 132.113
 22.906 c 133.25 22.906 134.176 23.828 134.176 24.965 c f
  Q
Q
0.878431 0.0196078 0.2 rg
57.887 19.492 m 57.887 30.625 l 61.238 27.367 l 65.906 27.152 l h
57.887 19.492 m f
Q Q
showpage
%%Trailer
%%EOF
