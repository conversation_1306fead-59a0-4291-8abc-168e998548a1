<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="12px" viewBox="0 0 16 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 63.1 (92452) - https://sketch.com -->
    <title></title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="-21" width="271" height="466"></rect>
        <filter x="-3.7%" y="-1.7%" width="107.4%" height="104.3%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.270588235   0 0 0 0 0.28627451   0 0 0 0 0.356862745  0 0 0 0.15 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Compte-Hover" transform="translate(-21.000000, -328.000000)">
            <g id="Rectangle">
                <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-1"></use>
            </g>
            <path d="M36.625,340 C36.7291665,340 36.8177085,339.963542 36.890625,339.890625 C36.9635415,339.817708 37,339.729167 37,339.625 L37,339.625 L37,338.875 C37,338.770833 36.9635415,338.682292 36.890625,338.609375 C36.8177085,338.536458 36.7291665,338.5 36.625,338.5 L36.625,338.5 L22.5,338.5 L22.5,328.375 C22.5,328.270833 22.4635415,328.182292 22.390625,328.109375 C22.3177085,328.036458 22.2291665,328 22.125,328 L22.125,328 L21.375,328 C21.2708335,328 21.1822915,328.036458 21.109375,328.109375 C21.0364585,328.182292 21,328.270833 21,328.375 L21,328.375 L21,339.625 C21,339.729167 21.0364585,339.817708 21.109375,339.890625 C21.1822915,339.963542 21.2708335,340 21.375,340 L21.375,340 L36.625,340 Z M34.125,337 C34.2291665,337 34.3177085,336.963542 34.390625,336.890625 C34.4635415,336.817708 34.5,336.729167 34.5,336.625 L34.5,336.625 L34.5,329.375 C34.5,329.270833 34.4635415,329.182292 34.390625,329.109375 C34.3177085,329.036458 34.2291665,329 34.125,329 L34.125,329 L33.375,329 C33.2708335,329 33.1822915,329.036458 33.109375,329.109375 C33.0364585,329.182292 33,329.270833 33,329.375 L33,329.375 L33,336.625 C33,336.729167 33.0364585,336.817708 33.109375,336.890625 C33.1822915,336.963542 33.2708335,337 33.375,337 L33.375,337 L34.125,337 Z M28.125,337 C28.2291665,337 28.3177085,336.963542 28.390625,336.890625 C28.4635415,336.817708 28.5,336.729167 28.5,336.625 L28.5,336.625 L28.5,330.375 C28.5,330.270833 28.4635415,330.182292 28.390625,330.109375 C28.3177085,330.036458 28.2291665,330 28.125,330 L28.125,330 L27.375,330 C27.2708335,330 27.1822915,330.036458 27.109375,330.109375 C27.0364585,330.182292 27,330.270833 27,330.375 L27,330.375 L27,336.625 C27,336.729167 27.0364585,336.817708 27.109375,336.890625 C27.1822915,336.963542 27.2708335,337 27.375,337 L27.375,337 L28.125,337 Z M31.125,337 C31.2291665,337 31.3177085,336.963542 31.390625,336.890625 C31.4635415,336.817708 31.5,336.729167 31.5,336.625 L31.5,336.625 L31.5,332.375 C31.5,332.270833 31.4635415,332.182292 31.390625,332.109375 C31.3177085,332.036458 31.2291665,332 31.125,332 L31.125,332 L30.375,332 C30.2708335,332 30.1822915,332.036458 30.109375,332.109375 C30.0364585,332.182292 30,332.270833 30,332.375 L30,332.375 L30,336.625 C30,336.729167 30.0364585,336.817708 30.109375,336.890625 C30.1822915,336.963542 30.2708335,337 30.375,337 L30.375,337 L31.125,337 Z M25.125,337 C25.2291665,337 25.3177085,336.963542 25.390625,336.890625 C25.4635415,336.817708 25.5,336.729167 25.5,336.625 L25.5,336.625 L25.5,334.375 C25.5,334.270833 25.4635415,334.182292 25.390625,334.109375 C25.3177085,334.036458 25.2291665,334 25.125,334 L25.125,334 L24.375,334 C24.2708335,334 24.1822915,334.036458 24.109375,334.109375 C24.0364585,334.182292 24,334.270833 24,334.375 L24,334.375 L24,336.625 C24,336.729167 24.0364585,336.817708 24.109375,336.890625 C24.1822915,336.963542 24.2708335,337 24.375,337 L24.375,337 L25.125,337 Z" id="" fill="#000000" fill-rule="nonzero"></path>
        </g>
    </g>
</svg>