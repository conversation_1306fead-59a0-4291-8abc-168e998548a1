.Page .Page-inner {
	margin:0 auto;
	padding: 50px 10px 80px;
	font-size: 18px;
}

.Page .Page-inner h1 {
	padding: 0 0 20px 0 !important;
	font-size: 34px !important;
	color: #ED0000;
	text-align: center;
}

.Modal-content .Modal-body .Page-inner {
	padding: 0;
}

@media (min-width: 960px) {

	.Page .Page-inner {
		padding-left: 0;
		padding-right: 0;
	}

	.Page .Page-inner h1 {
		padding: 0! important;
		margin-bottom: 20px !important;
	}
}

.Page-type--static * {
	font-family: 'OpenSans', 'Poppins', sans-serif !important;
}

@media (max-width: 960px) {
	.Page-type--static .Page-inner {
		padding-top: 10px !important;
	}

	.Page-type--static .Page-content {
		overflow: hidden !important;
		word-break: break-word !important;
	}

	.Page-type--static .Page-content * {
		margin-left: 0 !important;
		margin-right: 0 !important;
	}

	.Page-type--static .Page-content li {
		text-align: initial !important;
	}

	.Page-type--static img {
		max-width: 100% !important;
		height: auto !important;
	}

	.Page-type--static iframe {
		width: 100% !important;
		height: auto !important;
	}

	.Page-type--static .Page-content ul {
		padding-left: 10px !important;
		padding-right: 0 !important;
	}

	.Page-type--static .Page-content {
		padding: 0 15px !important;
	}

	.Page-type--static .Page-content table {
		display: block !important;
		max-width: 100% !important;
	}

	.Page-type--static .Page-content table tbody {
		display: block !important;
	}

	.Page-type--static .Page-content table tr {
		display: flex !important;
		flex-direction: column !important;
	}

	.Page-type--static .Page-content table td {
		margin-bottom: 20px !important;
		display: flex !important;
		flex-direction: column !important;
	}

	.Page-type--static .Page-content table td img {
		margin: auto !important;
	}

	.Page-type--static .Page-content table p {
		margin: 0 !important;
	}
}
