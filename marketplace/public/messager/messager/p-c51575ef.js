let e,t,n=0,l=!1,s=!1;const o="undefined"!=typeof window?window:{},r=o.CSS,i=o.document||{head:{}},c={t:0,l:"",jmp:e=>e(),raf:e=>requestAnimationFrame(e),ael:(e,t,n,l)=>e.addEventListener(t,n,l),rel:(e,t,n,l)=>e.removeEventListener(t,n,l)},a=(()=>(i.head.attachShadow+"").indexOf("[native")>-1)(),f=e=>Promise.resolve(e),u=(()=>{try{return new CSSStyleSheet,!0}catch(e){}return!1})(),p=new WeakMap,m=e=>p.get(e),d=(e,t)=>p.set(t.s=e,t),w=(e,t)=>t in e,$=e=>console.error(e),h=new Map,y=new Map,g=[],b=[],_=[],v=(e,t)=>n=>{e.push(n),l||(l=!0,t&&4&c.t?M(S):c.raf(S))},j=(e,t)=>{let n=0,l=0;for(;n<e.length&&(l=performance.now())<t;)try{e[n++](l)}catch(s){$(s)}n===e.length?e.length=0:0!==n&&e.splice(0,n)},S=()=>{n++,(e=>{for(let n=0;n<e.length;n++)try{e[n](performance.now())}catch(t){$(t)}e.length=0})(g);const e=2==(6&c.t)?performance.now()+10*Math.ceil(n*(1/22)):1/0;j(b,e),j(_,e),b.length>0&&(_.push(...b),b.length=0),(l=g.length+b.length+_.length>0)?c.raf(S):n=0},M=e=>f().then(e),k=v(b,!0),O={},U=e=>"object"==(e=typeof e)||"function"===e,R=()=>r&&r.supports&&r.supports("color","var(--c)")?f():__sc_import_messager("./p-1826e5f0.js").then(()=>(c.o=o.__cssshim)?(!1).i():0),L=()=>{c.o=o.__cssshim;const e=Array.from(i.querySelectorAll("script")).find(e=>new RegExp("/messager(\\.esm)?\\.js($|\\?|#)").test(e.src)||"messager"===e.getAttribute("data-stencil-namespace")),t={};return"onbeforeload"in e&&!history.scrollRestoration?{then(){}}:(t.resourcesUrl=new URL(".",new URL(e.getAttribute("data-resources-url")||e.src,o.location.href)).href,x(t.resourcesUrl,e),o.customElements?f(t):__sc_import_messager("./p-3b66a627.js").then(()=>t))},x=(e,t)=>{const n=`__sc_import_${"messager".replace(/\s|-/g,"_")}`;try{o[n]=new Function("w",`return import(w);//${Math.random()}`)}catch(l){const s=new Map;o[n]=l=>{const r=new URL(l,e).href;let c=s.get(r);if(!c){const e=i.createElement("script");e.type="module",e.crossOrigin=t.crossOrigin,e.src=URL.createObjectURL(new Blob([`import * as m from '${r}'; window.${n}.m = m;`],{type:"application/javascript"})),c=new Promise(t=>{e.onload=()=>{t(o[n].m),e.remove()}}),s.set(r,c),i.head.appendChild(e)}return c}}},P=new WeakMap,C=e=>"sc-"+e,A=(e,t,...n)=>{let l=null,s=null,o=!1,r=!1,i=[];const c=t=>{for(let n=0;n<t.length;n++)l=t[n],Array.isArray(l)?c(l):null!=l&&"boolean"!=typeof l&&((o="function"!=typeof e&&!U(l))&&(l=String(l)),o&&r?i[i.length-1].u+=l:i.push(o?D(null,l):l),r=o)};if(c(n),t){t.key&&(s=t.key);{const e=t.className||t.class;e&&(t.class="object"!=typeof e?e:Object.keys(e).filter(t=>e[t]).join(" "))}}const a=D(e,null);return a.p=t,i.length>0&&(a.$=i),a.h=s,a},D=(e,t)=>({t:0,g:e,u:t,_:null,$:null,p:null,h:null}),E={},F=(e,t,n,l,s,r)=>{if(n!==l){let a=w(e,t),f=t.toLowerCase();if("class"===t){const t=e.classList,s=W(n),o=W(l);t.remove(...s.filter(e=>e&&!o.includes(e))),t.add(...o.filter(e=>e&&!s.includes(e)))}else if("style"===t){for(const t in n)l&&null!=l[t]||(t.includes("-")?e.style.removeProperty(t):e.style[t]="");for(const t in l)n&&l[t]===n[t]||(t.includes("-")?e.style.setProperty(t,l[t]):e.style[t]=l[t])}else if("key"===t);else if(a||"o"!==t[0]||"n"!==t[1]){const o=U(l);if((a||o&&null!==l)&&!s)try{if(e.tagName.includes("-"))e[t]=l;else{let s=null==l?"":l;"list"===t?a=!1:null!=n&&e[t]==s||(e[t]=s)}}catch(i){}null==l||!1===l?e.removeAttribute(t):(!a||4&r||s)&&!o&&e.setAttribute(t,l=!0===l?"":l)}else t="-"===t[2]?t.slice(3):w(o,f)?f.slice(2):f[2]+t.slice(3),n&&c.rel(e,t,n,!1),l&&c.ael(e,t,l,!1)}},T=/\s/,W=e=>e?e.split(T):[],q=(e,t,n,l)=>{const s=11===t._.nodeType&&t._.host?t._.host:t._,o=e&&e.p||O,r=t.p||O;for(l in o)l in r||F(s,l,o[l],void 0,n,t.t);for(l in r)F(s,l,o[l],r[l],n,t.t)},B=(t,n,l)=>{let o,r,c=n.$[l],a=0;if(null!==c.u)o=c._=i.createTextNode(c.u);else{if(s||(s="svg"===c.g),o=c._=i.createElementNS(s?"http://www.w3.org/2000/svg":"http://www.w3.org/1999/xhtml",c.g),s&&"foreignObject"===c.g&&(s=!1),q(null,c,s),null!=e&&o["s-si"]!==e&&o.classList.add(o["s-si"]=e),c.$)for(a=0;a<c.$.length;++a)r=B(t,c,a),r&&o.appendChild(r);"svg"===c.g?s=!1:"foreignObject"===o.tagName&&(s=!0)}return o},H=(e,n,l,s,o,r)=>{let i,c=e;for(c.shadowRoot&&c.tagName===t&&(c=c.shadowRoot);o<=r;++o)s[o]&&(i=B(null,l,o),i&&(s[o]._=i,c.insertBefore(i,n)))},N=(e,t,n,l)=>{for(;t<=n;++t)(l=e[t])&&l._.remove()},V=(e,t)=>e.g===t.g&&e.h===t.h,z=(e,t)=>{const n=t._=e._,l=e.$,o=t.$,r=t.g,i=t.u;null===i?(s="svg"===r||"foreignObject"!==r&&s,q(e,t,s),null!==l&&null!==o?((e,t,n,l)=>{let s,o,r=0,i=0,c=0,a=0,f=t.length-1,u=t[0],p=t[f],m=l.length-1,d=l[0],w=l[m];for(;r<=f&&i<=m;)if(null==u)u=t[++r];else if(null==p)p=t[--f];else if(null==d)d=l[++i];else if(null==w)w=l[--m];else if(V(u,d))z(u,d),u=t[++r],d=l[++i];else if(V(p,w))z(p,w),p=t[--f],w=l[--m];else if(V(u,w))z(u,w),e.insertBefore(u._,p._.nextSibling),u=t[++r],w=l[--m];else if(V(p,d))z(p,d),e.insertBefore(p._,u._),p=t[--f],d=l[++i];else{for(c=-1,a=r;a<=f;++a)if(t[a]&&null!==t[a].h&&t[a].h===d.h){c=a;break}c>=0?(o=t[c],o.g!==d.g?s=B(t&&t[i],n,c):(z(o,d),t[c]=void 0,s=o._),d=l[++i]):(s=B(t&&t[i],n,i),d=l[++i]),s&&u._.parentNode.insertBefore(s,u._)}r>f?H(e,null==l[m+1]?null:l[m+1]._,n,l,i,m):i>m&&N(t,r,f)})(n,l,t,o):null!==o?(null!==e.u&&(n.textContent=""),H(n,null,t,o,0,o.length-1)):null!==l&&N(l,0,l.length-1),s&&"svg"===r&&(s=!1)):e.u!==i&&(n.data=i)},G=(e,t)=>{t&&!e.v&&t["s-p"].push(new Promise(t=>e.v=t))},I=(e,t,n,l)=>{if(t.t|=16,4&t.t)return void(t.t|=512);const s=t.s,o=()=>J(e,t,n,s,l);return G(t,t.j),Z(void 0,()=>k(o))},J=(n,l,s,o,r)=>{const c=n["s-rc"];r&&((e,t)=>{const n=((e,t)=>{let n=C(t.S),l=y.get(n);if(e=11===e.nodeType?e:i,l)if("string"==typeof l){let t,s=P.get(e=e.head||e);s||P.set(e,s=new Set),s.has(n)||(t=i.createElement("style"),t.innerHTML=l,e.insertBefore(t,e.querySelector("link")),s&&s.add(n))}else e.adoptedStyleSheets.includes(l)||(e.adoptedStyleSheets=[...e.adoptedStyleSheets,l]);return n})(a&&e.shadowRoot?e.shadowRoot:e.getRootNode(),t);10&t.t&&(e["s-sc"]=n,e.classList.add(n+"-h"))})(n,s),((n,l,s,o)=>{t=n.tagName;const r=l.M||D(null,null),i=(c=o)&&c.g===E?o:A(null,null,o);var c;i.g=null,i.t|=4,l.M=i,i._=r._=n.shadowRoot||n,e=n["s-sc"],z(r,i)})(n,l,0,K(o)),l.t&=-17,l.t|=2,c&&(c.forEach(e=>e()),n["s-rc"]=void 0);{const e=n["s-p"],t=()=>Q(n,l,s);0===e.length?t():(Promise.all(e).then(t),l.t|=4,e.length=0)}},K=e=>{try{e=e.render()}catch(t){$(t)}return e},Q=(e,t,n)=>{const l=t.s,s=t.j;64&t.t?Y(l,"componentDidUpdate"):(t.t|=64,ee(e),Y(l,"componentDidLoad"),t.k(e),s||X()),t.v&&(t.v(),t.v=void 0),512&t.t&&M(()=>I(e,t,n,!1)),t.t&=-517},X=()=>{ee(i.documentElement),c.t|=2},Y=(e,t,n)=>{if(e&&e[t])try{return e[t](n)}catch(l){$(l)}},Z=(e,t)=>e&&e.then?e.then(t):t(),ee=e=>e.classList.add("hydrated"),te=(e,t,n)=>{if(t.O){const l=Object.entries(t.O),s=e.prototype;if(l.forEach(([e,[l]])=>{(31&l||2&n&&32&l)&&Object.defineProperty(s,e,{get(){return t=e,m(this).U.get(t);var t},set(n){((e,t,n,l)=>{const s=m(this),o=s.R,r=s.U.get(t),i=s.t,c=s.s;var a,f;f=l.O[t][0],(n=null==(a=n)||U(a)?a:4&f?"false"!==a&&(""===a||!!a):2&f?parseFloat(a):1&f?String(a):a)===r||8&i&&void 0!==r||(s.U.set(t,n),c&&2==(18&i)&&I(o,s,l,!1))})(0,e,n,t)},configurable:!0,enumerable:!0})}),1&n){const t=new Map;s.attributeChangedCallback=function(e,n,l){c.jmp(()=>{const n=t.get(e);this[n]=(null!==l||"boolean"!=typeof this[n])&&l})},e.observedAttributes=l.filter(([e,t])=>15&t[0]).map(([e,n])=>{const l=n[1]||e;return t.set(l,e),l})}}return e},ne=(e,t={})=>{const n=[],l=t.exclude||[],s=o.customElements,r=i.head,f=r.querySelector("meta[charset]"),d=i.createElement("style"),w=[];let g,b=!0;Object.assign(c,t),c.l=new URL(t.resourcesUrl||"./",i.baseURI).href,t.syncQueue&&(c.t|=4),e.forEach(e=>e[1].forEach(t=>{const o={t:t[0],S:t[1],O:t[2],L:t[3]};o.O=t[2],!a&&1&o.t&&(o.t|=8);const r=o.S,i=class extends HTMLElement{constructor(e){super(e),(e=>{const t={t:0,R:e,U:new Map};t.P=new Promise(e=>t.k=e),e["s-p"]=[],e["s-rc"]=[],p.set(e,t)})(e=this),1&o.t&&(a?e.attachShadow({mode:"open"}):"shadowRoot"in e||(e.shadowRoot=e))}connectedCallback(){g&&(clearTimeout(g),g=null),b?w.push(this):c.jmp(()=>((e,t)=>{if(0==(1&c.t)){const n=()=>{},l=m(e);if(!(1&l.t)){l.t|=1;{let t=e;for(;t=t.parentNode||t.host;)if(t["s-p"]){G(l,l.j=t);break}}t.O&&Object.entries(t.O).forEach(([t,[n]])=>{if(31&n&&e.hasOwnProperty(t)){const n=e[t];delete e[t],e[t]=n}}),M(()=>(async(e,t,n,l,s)=>{if(0==(32&t.t)){t.t|=32;{if((s=(e=>{const t=e.S.replace(/-/g,"_"),n=e.C,l=h.get(n);return l?l[t]:__sc_import_messager(`./${n}.entry.js`).then(e=>(h.set(n,e),e[t]),$)})(n)).then){const e=()=>{};s=await s,e()}s.isProxied||(te(s,n,2),s.isProxied=!0);const e=()=>{};t.t|=8;try{new s(t)}catch(i){$(i)}t.t&=-9,e()}const e=C(n.S);if(!y.has(e)&&s.style){const t=()=>{};let l=s.style;8&n.t&&(l=await __sc_import_messager("./p-93e23355.js").then(t=>t.scopeCss(l,e,!1))),((e,t,n)=>{let l=y.get(e);u&&n?(l=l||new CSSStyleSheet,l.replace(t)):l=t,y.set(e,l)})(e,l,!!(1&n.t)),t()}}const o=t.j,r=()=>I(e,t,n,!0);o&&o["s-rc"]?o["s-rc"].push(r):r()})(e,l,t))}n()}})(this,o))}disconnectedCallback(){c.jmp(()=>(()=>{0==(1&c.t)&&Y(m(this).s,"componentDidUnload")})())}forceUpdate(){((e,t)=>{{const n=m(e);n.R.isConnected&&2==(18&n.t)&&I(e,n,t,!1)}})(this,o)}componentOnReady(){return m(this).P}};o.C=e[0],l.includes(r)||s.get(r)||(n.push(r),s.define(r,te(i,o,1)))})),d.innerHTML=n+"{visibility:hidden}.hydrated{visibility:inherit}",d.setAttribute("data-styles",""),r.insertBefore(d,f?f.nextSibling:r.firstChild),b=!1,w.length>0?w.forEach(e=>e.connectedCallback()):c.jmp(()=>g=setTimeout(X,30))},le=e=>m(e).R;export{R as a,ne as b,le as g,A as h,L as p,d as r};