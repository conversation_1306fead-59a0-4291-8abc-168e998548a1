var __extends=this&&this.__extends||function(){var e=function(r,t){e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,r){e.__proto__=r}||function(e,r){for(var t in r)if(r.hasOwnProperty(t))e[t]=r[t]};return e(r,t)};return function(r,t){e(r,t);function n(){this.constructor=r}r.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}}();var __awaiter=this&&this.__awaiter||function(e,r,t,n){function a(e){return e instanceof t?e:new t((function(r){r(e)}))}return new(t||(t=Promise))((function(t,i){function s(e){try{o(n.next(e))}catch(r){i(r)}}function l(e){try{o(n["throw"](e))}catch(r){i(r)}}function o(e){e.done?t(e.value):a(e.value).then(s,l)}o((n=n.apply(e,r||[])).next())}))};var __generator=this&&this.__generator||function(e,r){var t={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},n,a,i,s;return s={next:l(0),throw:l(1),return:l(2)},typeof Symbol==="function"&&(s[Symbol.iterator]=function(){return this}),s;function l(e){return function(r){return o([e,r])}}function o(s){if(n)throw new TypeError("Generator is already executing.");while(t)try{if(n=1,a&&(i=s[0]&2?a["return"]:s[0]?a["throw"]||((i=a["return"])&&i.call(a),0):a.next)&&!(i=i.call(a,s[1])).done)return i;if(a=0,i)s=[s[0]&2,i.value];switch(s[0]){case 0:case 1:i=s;break;case 4:t.label++;return{value:s[1],done:false};case 5:t.label++;a=s[1];s=[0];continue;case 7:s=t.ops.pop();t.trys.pop();continue;default:if(!(i=t.trys,i=i.length>0&&i[i.length-1])&&(s[0]===6||s[0]===2)){t=0;continue}if(s[0]===3&&(!i||s[1]>i[0]&&s[1]<i[3])){t.label=s[1];break}if(s[0]===6&&t.label<i[1]){t.label=i[1];i=s;break}if(i&&t.label<i[2]){t.label=i[2];t.ops.push(s);break}if(i[2])t.ops.pop();t.trys.pop();continue}s=r.call(e,t)}catch(l){s=[6,l];a=0}finally{n=i=0}if(s[0]&5)throw s[1];return{value:s[0]?s[1]:void 0,done:true}}};var __spreadArrays=this&&this.__spreadArrays||function(){for(var e=0,r=0,t=arguments.length;r<t;r++)e+=arguments[r].length;for(var n=Array(e),a=0,r=0;r<t;r++)for(var i=arguments[r],s=0,l=i.length;s<l;s++,a++)n[a]=i[s];return n};System.register([],(function(e,r){"use strict";return{execute:function(){var t=this;var n="messager";var a=0;var i=false;var s;var l;var o=false;var f=typeof window!=="undefined"?window:{};var u=f.CSS;var c=f.document||{head:{}};var $={$flags$:0,$resourcesUrl$:"",jmp:function(e){return e()},raf:function(e){return requestAnimationFrame(e)},ael:function(e,r,t,n){return e.addEventListener(r,t,n)},rel:function(e,r,t,n){return e.removeEventListener(r,t,n)}};var v=function(){return(c.head.attachShadow+"").indexOf("[native")>-1}();var h=function(e){return Promise.resolve(e)};var d=function(){try{new CSSStyleSheet;return true}catch(e){}return false}();var m=new WeakMap;var p=function(e){return m.get(e)};var g=e("r",(function(e,r){return m.set(r.$lazyInstance$=e,r)}));var y=function(e){var r={$flags$:0,$hostElement$:e,$instanceValues$:new Map};{r.$onReadyPromise$=new Promise((function(e){return r.$onReadyResolve$=e}));e["s-p"]=[];e["s-rc"]=[]}return m.set(e,r)};var w=function(e,r){return r in e};var b=function(e){return console.error(e)};var S=new Map;var _=function(e,t,n){var a=e.$tagName$.replace(/-/g,"_");var i=e.$lazyBundleIds$;var s=S.get(i);if(s){return s[a]}return r.import("./"+i+".entry.js"+"").then((function(e){{S.set(i,e)}return e[a]}),b)};var R=new Map;var x=[];var j=[];var k=[];var N=function(e,r){return function(t){e.push(t);if(!i){i=true;if(r&&$.$flags$&4){L(O)}else{$.raf(O)}}}};var E=function(e){for(var r=0;r<e.length;r++){try{e[r](performance.now())}catch(t){b(t)}}e.length=0};var C=function(e,r){var t=0;var n=0;while(t<e.length&&(n=performance.now())<r){try{e[t++](n)}catch(a){b(a)}}if(t===e.length){e.length=0}else if(t!==0){e.splice(0,t)}};var O=function(){a++;E(x);var e=($.$flags$&6)===2?performance.now()+10*Math.ceil(a*(1/22)):Infinity;C(j,e);C(k,e);if(j.length>0){k.push.apply(k,j);j.length=0}if(i=x.length+j.length+k.length>0){$.raf(O)}else{a=0}};var L=function(e){return h().then(e)};var U=N(j,true);var A={};var P="http://www.w3.org/2000/svg";var M="http://www.w3.org/1999/xhtml";var I=function(e){return e!=null};var B=function(e){e=typeof e;return e==="object"||e==="function"};var T=function(e){return"__sc_import_"+e.replace(/\s|-/g,"_")};var z=e("a",(function(){if(!(u&&u.supports&&u.supports("color","var(--c)"))){return r.import("./p-dbe40eff.system.js").then((function(){if($.$cssShim$=f.__cssshim){return $.$cssShim$.i()}else{return 0}}))}return h()}));var H=e("p",(function(){{$.$cssShim$=f.__cssshim}var e=Array.from(c.querySelectorAll("script")).find((function(e){return new RegExp("/"+n+"(\\.esm)?\\.js($|\\?|#)").test(e.src)||e.getAttribute("data-stencil-namespace")===n}));var t={};if("onbeforeload"in e&&!history.scrollRestoration&&false){return{then:function(){}}}{t.resourcesUrl=new URL(".",new URL(e.getAttribute("data-resources-url")||e.src,f.location.href)).href;q(t.resourcesUrl,e);if(!f.customElements){return r.import("./p-7f10eb01.system.js").then((function(){return t}))}}return h(t)}));var q=function(e,r){var t=T(n);try{f[t]=new Function("w","return import(w);//"+Math.random())}catch(i){var a=new Map;f[t]=function(n){var i=new URL(n,e).href;var s=a.get(i);if(!s){var l=c.createElement("script");l.type="module";l.crossOrigin=r.crossOrigin;l.src=URL.createObjectURL(new Blob(["import * as m from '"+i+"'; window."+t+".m = m;"],{type:"application/javascript"}));s=new Promise((function(e){l.onload=function(){e(f[t].m);l.remove()}}));a.set(i,s);c.head.appendChild(l)}return s}}};var V="{visibility:hidden}.hydrated{visibility:inherit}";var D=function(e,r){if(r===void 0){r=""}{return function(){return}}};var F=function(e,r){{return function(){return}}};var W=new WeakMap;var G=function(e,r,t){var n=R.get(e);if(d&&t){n=n||new CSSStyleSheet;n.replace(r)}else{n=r}R.set(e,n)};var Q=function(e,r,t,n){var a=K(r.$tagName$);var i=R.get(a);e=e.nodeType===11?e:c;if(i){if(typeof i==="string"){e=e.head||e;var s=W.get(e);var l=void 0;if(!s){W.set(e,s=new Set)}if(!s.has(a)){{if($.$cssShim$){l=$.$cssShim$.createHostStyle(n,a,i,!!(r.$flags$&10));var o=l["s-sc"];if(o){a=o;s=null}}else{l=c.createElement("style");l.innerHTML=i}e.insertBefore(l,e.querySelector("link"))}if(s){s.add(a)}}}else if(!e.adoptedStyleSheets.includes(i)){e.adoptedStyleSheets=__spreadArrays(e.adoptedStyleSheets,[i])}}return a};var J=function(e,r,t){var n=D("attachStyles",r.$tagName$);var a=Q(v&&e.shadowRoot?e.shadowRoot:e.getRootNode(),r,t,e);if(r.$flags$&10){e["s-sc"]=a;e.classList.add(a+"-h")}n()};var K=function(e,r){return"sc-"+e};var X=e("h",(function(e,r){var t=[];for(var n=2;n<arguments.length;n++){t[n-2]=arguments[n]}var a=null;var i=null;var s=false;var l=false;var o=[];var f=function(r){for(var t=0;t<r.length;t++){a=r[t];if(Array.isArray(a)){f(a)}else if(a!=null&&typeof a!=="boolean"){if(s=typeof e!=="function"&&!B(a)){a=String(a)}if(s&&l){o[o.length-1].$text$+=a}else{o.push(s?Y(null,a):a)}l=s}}};f(t);if(r){if(r.key){i=r.key}{var u=r.className||r.class;if(u){r.class=typeof u!=="object"?u:Object.keys(u).filter((function(e){return u[e]})).join(" ")}}}var c=Y(e,null);c.$attrs$=r;if(o.length>0){c.$children$=o}{c.$key$=i}return c}));var Y=function(e,r){var t={$flags$:0,$tag$:e,$text$:r,$elm$:null,$children$:null};{t.$attrs$=null}{t.$key$=null}return t};var Z={};var ee=function(e){return e&&e.$tag$===Z};var re=function(e,r,t,n,a,i){if(t!==n){var s=w(e,r);var l=r.toLowerCase();if(r==="class"){var o=e.classList;var u=ne(t);var c=ne(n);o.remove.apply(o,u.filter((function(e){return e&&!c.includes(e)})));o.add.apply(o,c.filter((function(e){return e&&!u.includes(e)})))}else if(r==="style"){{for(var v in t){if(!n||n[v]==null){if(v.includes("-")){e.style.removeProperty(v)}else{e.style[v]=""}}}}for(var v in n){if(!t||n[v]!==t[v]){if(v.includes("-")){e.style.setProperty(v,n[v])}else{e.style[v]=n[v]}}}}else if(r==="key");else if(!s&&r[0]==="o"&&r[1]==="n"){if(r[2]==="-"){r=r.slice(3)}else if(w(f,l)){r=l.slice(2)}else{r=l[2]+r.slice(3)}if(t){$.rel(e,r,t,false)}if(n){$.ael(e,r,n,false)}}else{var h=B(n);if((s||h&&n!==null)&&!a){try{if(!e.tagName.includes("-")){var d=n==null?"":n;if(r==="list"){s=false}else if(t==null||e[r]!=d){e[r]=d}}else{e[r]=n}}catch(m){}}if(n==null||n===false){{e.removeAttribute(r)}}else if((!s||i&4||a)&&!h){n=n===true?"":n;{e.setAttribute(r,n)}}}}};var te=/\s/;var ne=function(e){return!e?[]:e.split(te)};var ae=function(e,r,t,n){var a=r.$elm$.nodeType===11&&r.$elm$.host?r.$elm$.host:r.$elm$;var i=e&&e.$attrs$||A;var s=r.$attrs$||A;{for(n in i){if(!(n in s)){re(a,n,i[n],undefined,t,r.$flags$)}}}for(n in s){re(a,n,i[n],s[n],t,r.$flags$)}};var ie=function(e,r,t,n){var a=r.$children$[t];var i=0;var l;var f;if(a.$text$!==null){l=a.$elm$=c.createTextNode(a.$text$)}else{if(!o){o=a.$tag$==="svg"}l=a.$elm$=c.createElementNS(o?P:M,a.$tag$);if(o&&a.$tag$==="foreignObject"){o=false}{ae(null,a,o)}if(I(s)&&l["s-si"]!==s){l.classList.add(l["s-si"]=s)}if(a.$children$){for(i=0;i<a.$children$.length;++i){f=ie(e,a,i);if(f){l.appendChild(f)}}}{if(a.$tag$==="svg"){o=false}else if(l.tagName==="foreignObject"){o=true}}}return l};var se=function(e,r,t,n,a,i){var s=e;var o;if(s.shadowRoot&&s.tagName===l){s=s.shadowRoot}for(;a<=i;++a){if(n[a]){o=ie(null,t,a);if(o){n[a].$elm$=o;s.insertBefore(o,r)}}}};var le=function(e,r,t,n,a){for(;r<=t;++r){if(n=e[r]){a=n.$elm$;a.remove()}}};var oe=function(e,r,t,n){var a=0;var i=0;var s=0;var l=0;var o=r.length-1;var f=r[0];var u=r[o];var c=n.length-1;var $=n[0];var v=n[c];var h;var d;while(a<=o&&i<=c){if(f==null){f=r[++a]}else if(u==null){u=r[--o]}else if($==null){$=n[++i]}else if(v==null){v=n[--c]}else if(fe(f,$)){ue(f,$);f=r[++a];$=n[++i]}else if(fe(u,v)){ue(u,v);u=r[--o];v=n[--c]}else if(fe(f,v)){ue(f,v);e.insertBefore(f.$elm$,u.$elm$.nextSibling);f=r[++a];v=n[--c]}else if(fe(u,$)){ue(u,$);e.insertBefore(u.$elm$,f.$elm$);u=r[--o];$=n[++i]}else{s=-1;{for(l=a;l<=o;++l){if(r[l]&&r[l].$key$!==null&&r[l].$key$===$.$key$){s=l;break}}}if(s>=0){d=r[s];if(d.$tag$!==$.$tag$){h=ie(r&&r[i],t,s)}else{ue(d,$);r[s]=undefined;h=d.$elm$}$=n[++i]}else{h=ie(r&&r[i],t,i);$=n[++i]}if(h){{f.$elm$.parentNode.insertBefore(h,f.$elm$)}}}}if(a>o){se(e,n[c+1]==null?null:n[c+1].$elm$,t,n,i,c)}else if(i>c){le(r,a,o)}};var fe=function(e,r){if(e.$tag$===r.$tag$){{return e.$key$===r.$key$}}return false};var ue=function(e,r){var t=r.$elm$=e.$elm$;var n=e.$children$;var a=r.$children$;var i=r.$tag$;var s=r.$text$;if(s===null){{o=i==="svg"?true:i==="foreignObject"?false:o}{{ae(e,r,o)}}if(n!==null&&a!==null){oe(t,n,r,a)}else if(a!==null){if(e.$text$!==null){t.textContent=""}se(t,null,r,a,0,a.length-1)}else if(n!==null){le(n,0,n.length-1)}if(o&&i==="svg"){o=false}}else if(e.$text$!==s){t.data=s}};var ce=function(e,r,t,n){l=e.tagName;var a=r.$vnode$||Y(null,null);var i=ee(n)?n:X(null,null,n);i.$tag$=null;i.$flags$|=4;r.$vnode$=i;i.$elm$=a.$elm$=e.shadowRoot||e;{s=e["s-sc"]}ue(a,i)};var $e=function(e,r){if(r&&!e.$onRenderResolve$){r["s-p"].push(new Promise((function(r){return e.$onRenderResolve$=r})))}};var ve=function(e,r,t,n){{r.$flags$|=16}if(r.$flags$&4){r.$flags$|=512;return}var a=D("scheduleUpdate",t.$tagName$);var i=r.$ancestorComponent$;var s=r.$lazyInstance$;var l=function(){return he(e,r,t,s,n)};$e(r,i);var o;a();return we(o,(function(){return U(l)}))};var he=function(e,r,t,n,a){var i=D("update",t.$tagName$);var s=e["s-rc"];if(a){J(e,t,r.$modeName$)}var l=D("render",t.$tagName$);{{ce(e,r,t,de(n))}}if($.$cssShim$){$.$cssShim$.updateHost(e)}{r.$flags$&=~16}{r.$flags$|=2}if(s){s.forEach((function(e){return e()}));e["s-rc"]=undefined}l();i();{var o=e["s-p"];var f=function(){return me(e,r,t)};if(o.length===0){f()}else{Promise.all(o).then(f);r.$flags$|=4;o.length=0}}};var de=function(e,r){try{e=e.render()}catch(t){b(t)}return e};var me=function(e,r,t){var n=D("postUpdate",t.$tagName$);var a=r.$lazyInstance$;var i=r.$ancestorComponent$;if(!(r.$flags$&64)){r.$flags$|=64;{be(e)}{ye(a,"componentDidLoad")}n();{r.$onReadyResolve$(e);if(!i){ge()}}}else{{ye(a,"componentDidUpdate")}n()}{if(r.$onRenderResolve$){r.$onRenderResolve$();r.$onRenderResolve$=undefined}if(r.$flags$&512){L((function(){return ve(e,r,t,false)}))}r.$flags$&=~(4|512)}};var pe=function(e,r){{var t=p(e);var n=t.$hostElement$.isConnected;if(n&&(t.$flags$&(2|16))===2){ve(e,t,r,false)}return n}};var ge=function(e){{be(c.documentElement)}{$.$flags$|=2}};var ye=function(e,r,t){if(e&&e[r]){try{return e[r](t)}catch(n){b(n)}}return undefined};var we=function(e,r){return e&&e.then?e.then(r):r()};var be=function(e){return e.classList.add("hydrated")};var Se=function(e,r){if(e!=null&&!B(e)){if(r&4){return e==="false"?false:e===""||!!e}if(r&2){return parseFloat(e)}if(r&1){return String(e)}return e}return e};var _e=function(e,r){return p(e).$instanceValues$.get(r)};var Re=function(e,r,t,n){var a=p(e);var i=a.$hostElement$;var s=a.$instanceValues$.get(r);var l=a.$flags$;var o=a.$lazyInstance$;t=Se(t,n.$members$[r][0]);if(t!==s&&(!(l&8)||s===undefined)){a.$instanceValues$.set(r,t);if(o){if((l&(2|16))===2){ve(i,a,n,false)}}}};var xe=function(e,r,t){if(r.$members$){var n=Object.entries(r.$members$);var a=e.prototype;n.forEach((function(e){var n=e[0],i=e[1][0];if(i&31||t&2&&i&32){Object.defineProperty(a,n,{get:function(){return _e(this,n)},set:function(e){Re(this,n,e,r)},configurable:true,enumerable:true})}}));if(t&1){var i=new Map;a.attributeChangedCallback=function(e,r,t){var n=this;$.jmp((function(){var r=i.get(e);n[r]=t===null&&typeof n[r]==="boolean"?false:t}))};e.observedAttributes=n.filter((function(e){var r=e[0],t=e[1];return t[0]&15})).map((function(e){var r=e[0],t=e[1];var n=t[1]||r;i.set(n,r);return n}))}}return e};var je=function(e,n,a,i,s){return __awaiter(t,void 0,void 0,(function(){var t,i,l,o,f,u,c;return __generator(this,(function($){switch($.label){case 0:if(!((n.$flags$&32)===0))return[3,5];n.$flags$|=32;s=_(a);if(!s.then)return[3,2];t=F();return[4,s];case 1:s=$.sent();t();$.label=2;case 2:if(!s.isProxied){xe(s,a,2);s.isProxied=true}i=D("createInstance",a.$tagName$);{n.$flags$|=8}try{new s(n)}catch(v){b(v)}{n.$flags$&=~8}i();l=K(a.$tagName$);if(!(!R.has(l)&&s.style))return[3,5];o=D("registerStyles",a.$tagName$);f=s.style;if(!(a.$flags$&8))return[3,4];return[4,r.import("./p-6cef36c5.system.js").then((function(e){return e.scopeCss(f,l,false)}))];case 3:f=$.sent();$.label=4;case 4:G(l,f,!!(a.$flags$&1));o();$.label=5;case 5:u=n.$ancestorComponent$;c=function(){return ve(e,n,a,true)};if(u&&u["s-rc"]){u["s-rc"].push(c)}else{c()}return[2]}}))}))};var ke=function(e,r){if(($.$flags$&1)===0){var t=D("connectedCallback",r.$tagName$);var n=p(e);if(!(n.$flags$&1)){n.$flags$|=1;{var a=e;while(a=a.parentNode||a.host){if(a["s-p"]){$e(n,n.$ancestorComponent$=a);break}}}if(r.$members$){Object.entries(r.$members$).forEach((function(r){var t=r[0],n=r[1][0];if(n&31&&e.hasOwnProperty(t)){var a=e[t];delete e[t];e[t]=a}}))}{L((function(){return je(e,n,r)}))}}t()}};var Ne=function(e){if(($.$flags$&1)===0){var r=p(e);var t=r.$lazyInstance$;if($.$cssShim$){$.$cssShim$.removeHost(e)}{ye(t,"componentDidUnload")}}};var Ee=e("b",(function(e,r){if(r===void 0){r={}}var t=D();var n=[];var a=r.exclude||[];var i=f.customElements;var s=c.head;var l=s.querySelector("meta[charset]");var o=c.createElement("style");var u=[];var h;var d=true;Object.assign($,r);$.$resourcesUrl$=new URL(r.resourcesUrl||"./",c.baseURI).href;if(r.syncQueue){$.$flags$|=4}e.forEach((function(e){return e[1].forEach((function(r){var t={$flags$:r[0],$tagName$:r[1],$members$:r[2],$listeners$:r[3]};{t.$members$=r[2]}if(!v&&t.$flags$&1){t.$flags$|=8}var s=t.$tagName$;var l=function(e){__extends(r,e);function r(r){var n=e.call(this,r)||this;r=n;y(r);if(t.$flags$&1){if(v){{r.attachShadow({mode:"open"})}}else if(!("shadowRoot"in r)){r.shadowRoot=r}}return n}r.prototype.connectedCallback=function(){var e=this;if(h){clearTimeout(h);h=null}if(d){u.push(this)}else{$.jmp((function(){return ke(e,t)}))}};r.prototype.disconnectedCallback=function(){var e=this;$.jmp((function(){return Ne(e)}))};r.prototype.forceUpdate=function(){pe(this,t)};r.prototype.componentOnReady=function(){return p(this).$onReadyPromise$};return r}(HTMLElement);t.$lazyBundleIds$=e[0];if(!a.includes(s)&&!i.get(s)){n.push(s);i.define(s,xe(l,t,1))}}))}));{o.innerHTML=n+V;o.setAttribute("data-styles","");s.insertBefore(o,l?l.nextSibling:s.firstChild)}d=false;if(u.length>0){u.forEach((function(e){return e.connectedCallback()}))}else{{$.jmp((function(){return h=setTimeout(ge,30)}))}}t()}));var Ce=e("g",(function(e){return p(e).$hostElement$}))}}}));