import{r as i,h as t,g as n}from"./p-c51575ef.js";var a=function(i,t){return function(){for(var n=new Array(arguments.length),a=0;a<n.length;a++)n[a]=arguments[a];return i.apply(t,n)}},e=Object.prototype.toString;function o(i){return"[object Array]"===e.call(i)}function s(i){return void 0===i}function r(i){return null!==i&&"object"==typeof i}function p(i){return"[object Function]"===e.call(i)}function c(i,t){if(null!=i)if("object"!=typeof i&&(i=[i]),o(i))for(var n=0,a=i.length;n<a;n++)t.call(null,i[n],n,i);else for(var e in i)Object.prototype.hasOwnProperty.call(i,e)&&t.call(null,i[e],e,i)}var l={isArray:o,isArrayBuffer:function(i){return"[object ArrayBuffer]"===e.call(i)},isBuffer:function(i){return null!==i&&!s(i)&&null!==i.constructor&&!s(i.constructor)&&"function"==typeof i.constructor.isBuffer&&i.constructor.isBuffer(i)},isFormData:function(i){return"undefined"!=typeof FormData&&i instanceof FormData},isArrayBufferView:function(i){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(i):i&&i.buffer&&i.buffer instanceof ArrayBuffer},isString:function(i){return"string"==typeof i},isNumber:function(i){return"number"==typeof i},isObject:r,isUndefined:s,isDate:function(i){return"[object Date]"===e.call(i)},isFile:function(i){return"[object File]"===e.call(i)},isBlob:function(i){return"[object Blob]"===e.call(i)},isFunction:p,isStream:function(i){return r(i)&&p(i.pipe)},isURLSearchParams:function(i){return"undefined"!=typeof URLSearchParams&&i instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:c,merge:function i(){var t={};function n(n,a){t[a]="object"==typeof t[a]&&"object"==typeof n?i(t[a],n):n}for(var a=0,e=arguments.length;a<e;a++)c(arguments[a],n);return t},deepMerge:function i(){var t={};function n(n,a){t[a]="object"==typeof t[a]&&"object"==typeof n?i(t[a],n):"object"==typeof n?i({},n):n}for(var a=0,e=arguments.length;a<e;a++)c(arguments[a],n);return t},extend:function(i,t,n){return c(t,(function(t,e){i[e]=n&&"function"==typeof t?a(t,n):t})),i},trim:function(i){return i.replace(/^\s*/,"").replace(/\s*$/,"")}};function d(i){return encodeURIComponent(i).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}var u=function(i,t,n){if(!t)return i;var a;if(n)a=n(t);else if(l.isURLSearchParams(t))a=t.toString();else{var e=[];l.forEach(t,(function(i,t){null!=i&&(l.isArray(i)?t+="[]":i=[i],l.forEach(i,(function(i){l.isDate(i)?i=i.toISOString():l.isObject(i)&&(i=JSON.stringify(i)),e.push(d(t)+"="+d(i))})))})),a=e.join("&")}if(a){var o=i.indexOf("#");-1!==o&&(i=i.slice(0,o)),i+=(-1===i.indexOf("?")?"?":"&")+a}return i};function m(){this.handlers=[]}m.prototype.use=function(i,t){return this.handlers.push({fulfilled:i,rejected:t}),this.handlers.length-1},m.prototype.eject=function(i){this.handlers[i]&&(this.handlers[i]=null)},m.prototype.forEach=function(i){l.forEach(this.handlers,(function(t){null!==t&&i(t)}))};var f=m,h=function(i,t,n){return l.forEach(n,(function(n){i=n(i,t)})),i},v=function(i){return!(!i||!i.__CANCEL__)},x=function(i,t){l.forEach(i,(function(n,a){a!==t&&a.toUpperCase()===t.toUpperCase()&&(i[t]=n,delete i[a])}))},g=function(i,t,n,a,e){return function(i,t,n,a,e){return i.config=t,n&&(i.code=n),i.request=a,i.response=e,i.isAxiosError=!0,i.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},i}(new Error(i),t,n,a,e)},b=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"],w=l.isStandardBrowserEnv()?function(){var i,t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function a(i){var a=i;return t&&(n.setAttribute("href",a),a=n.href),n.setAttribute("href",a),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return i=a(window.location.href),function(t){var n=l.isString(t)?a(t):t;return n.protocol===i.protocol&&n.host===i.host}}():function(){return!0},y=l.isStandardBrowserEnv()?{write:function(i,t,n,a,e,o){var s=[];s.push(i+"="+encodeURIComponent(t)),l.isNumber(n)&&s.push("expires="+new Date(n).toGMTString()),l.isString(a)&&s.push("path="+a),l.isString(e)&&s.push("domain="+e),!0===o&&s.push("secure"),document.cookie=s.join("; ")},read:function(i){var t=document.cookie.match(new RegExp("(^|;\\s*)("+i+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(i){this.write(i,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}},k=function(i){return new Promise((function(t,n){var a=i.data,e=i.headers;l.isFormData(a)&&delete e["Content-Type"];var o=new XMLHttpRequest;i.auth&&(e.Authorization="Basic "+btoa((i.auth.username||"")+":"+(i.auth.password||"")));var s,r,p=(r=i.url,(s=i.baseURL)&&!/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(r)?function(i,t){return t?i.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):i}(s,r):r);if(o.open(i.method.toUpperCase(),u(p,i.params,i.paramsSerializer),!0),o.timeout=i.timeout,o.onreadystatechange=function(){if(o&&4===o.readyState&&(0!==o.status||o.responseURL&&0===o.responseURL.indexOf("file:"))){var a,e,s,r,p,c="getAllResponseHeaders"in o?(a=o.getAllResponseHeaders(),p={},a?(l.forEach(a.split("\n"),(function(i){if(r=i.indexOf(":"),e=l.trim(i.substr(0,r)).toLowerCase(),s=l.trim(i.substr(r+1)),e){if(p[e]&&b.indexOf(e)>=0)return;p[e]="set-cookie"===e?(p[e]?p[e]:[]).concat([s]):p[e]?p[e]+", "+s:s}})),p):p):null;!function(i,t,n){var a=n.config.validateStatus;!a||a(n.status)?i(n):t(g("Request failed with status code "+n.status,n.config,null,n.request,n))}(t,n,{data:i.responseType&&"text"!==i.responseType?o.response:o.responseText,status:o.status,statusText:o.statusText,headers:c,config:i,request:o}),o=null}},o.onabort=function(){o&&(n(g("Request aborted",i,"ECONNABORTED",o)),o=null)},o.onerror=function(){n(g("Network Error",i,null,o)),o=null},o.ontimeout=function(){var t="timeout of "+i.timeout+"ms exceeded";i.timeoutErrorMessage&&(t=i.timeoutErrorMessage),n(g(t,i,"ECONNABORTED",o)),o=null},l.isStandardBrowserEnv()){var c=y,d=(i.withCredentials||w(p))&&i.xsrfCookieName?c.read(i.xsrfCookieName):void 0;d&&(e[i.xsrfHeaderName]=d)}if("setRequestHeader"in o&&l.forEach(e,(function(i,t){void 0===a&&"content-type"===t.toLowerCase()?delete e[t]:o.setRequestHeader(t,i)})),l.isUndefined(i.withCredentials)||(o.withCredentials=!!i.withCredentials),i.responseType)try{o.responseType=i.responseType}catch(m){if("json"!==i.responseType)throw m}"function"==typeof i.onDownloadProgress&&o.addEventListener("progress",i.onDownloadProgress),"function"==typeof i.onUploadProgress&&o.upload&&o.upload.addEventListener("progress",i.onUploadProgress),i.cancelToken&&i.cancelToken.promise.then((function(i){o&&(o.abort(),n(i),o=null)})),void 0===a&&(a=null),o.send(a)}))},M={"Content-Type":"application/x-www-form-urlencoded"};function Y(i,t){!l.isUndefined(i)&&l.isUndefined(i["Content-Type"])&&(i["Content-Type"]=t)}var D,j={adapter:("undefined"!=typeof XMLHttpRequest?D=k:"undefined"!=typeof process&&"[object process]"===Object.prototype.toString.call(process)&&(D=k),D),transformRequest:[function(i,t){return x(t,"Accept"),x(t,"Content-Type"),l.isFormData(i)||l.isArrayBuffer(i)||l.isBuffer(i)||l.isStream(i)||l.isFile(i)||l.isBlob(i)?i:l.isArrayBufferView(i)?i.buffer:l.isURLSearchParams(i)?(Y(t,"application/x-www-form-urlencoded;charset=utf-8"),i.toString()):l.isObject(i)?(Y(t,"application/json;charset=utf-8"),JSON.stringify(i)):i}],transformResponse:[function(i){if("string"==typeof i)try{i=JSON.parse(i)}catch(t){}return i}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(i){return i>=200&&i<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};l.forEach(["delete","get","head"],(function(i){j.headers[i]={}})),l.forEach(["post","put","patch"],(function(i){j.headers[i]=l.merge(M)}));var S=j;function _(i){i.cancelToken&&i.cancelToken.throwIfRequested()}var z=function(i){return _(i),i.headers=i.headers||{},i.data=h(i.data,i.headers,i.transformRequest),i.headers=l.merge(i.headers.common||{},i.headers[i.method]||{},i.headers),l.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete i.headers[t]})),(i.adapter||S.adapter)(i).then((function(t){return _(i),t.data=h(t.data,t.headers,i.transformResponse),t}),(function(t){return v(t)||(_(i),t&&t.response&&(t.response.data=h(t.response.data,t.response.headers,i.transformResponse))),Promise.reject(t)}))},T=function(i,t){t=t||{};var n={},a=["url","method","params","data"],e=["headers","auth","proxy"],o=["baseURL","url","transformRequest","transformResponse","paramsSerializer","timeout","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","maxContentLength","validateStatus","maxRedirects","httpAgent","httpsAgent","cancelToken","socketPath"];l.forEach(a,(function(i){void 0!==t[i]&&(n[i]=t[i])})),l.forEach(e,(function(a){l.isObject(t[a])?n[a]=l.deepMerge(i[a],t[a]):void 0!==t[a]?n[a]=t[a]:l.isObject(i[a])?n[a]=l.deepMerge(i[a]):void 0!==i[a]&&(n[a]=i[a])})),l.forEach(o,(function(a){void 0!==t[a]?n[a]=t[a]:void 0!==i[a]&&(n[a]=i[a])}));var s=a.concat(e).concat(o),r=Object.keys(t).filter((function(i){return-1===s.indexOf(i)}));return l.forEach(r,(function(a){void 0!==t[a]?n[a]=t[a]:void 0!==i[a]&&(n[a]=i[a])})),n};function N(i){this.defaults=i,this.interceptors={request:new f,response:new f}}N.prototype.request=function(i){"string"==typeof i?(i=arguments[1]||{}).url=arguments[0]:i=i||{},(i=T(this.defaults,i)).method=i.method?i.method.toLowerCase():this.defaults.method?this.defaults.method.toLowerCase():"get";var t=[z,void 0],n=Promise.resolve(i);for(this.interceptors.request.forEach((function(i){t.unshift(i.fulfilled,i.rejected)})),this.interceptors.response.forEach((function(i){t.push(i.fulfilled,i.rejected)}));t.length;)n=n.then(t.shift(),t.shift());return n},N.prototype.getUri=function(i){return i=T(this.defaults,i),u(i.url,i.params,i.paramsSerializer).replace(/^\?/,"")},l.forEach(["delete","get","head","options"],(function(i){N.prototype[i]=function(t,n){return this.request(l.merge(n||{},{method:i,url:t}))}})),l.forEach(["post","put","patch"],(function(i){N.prototype[i]=function(t,n,a){return this.request(l.merge(a||{},{method:i,url:t,data:n}))}}));var H=N;function O(i){this.message=i}O.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},O.prototype.__CANCEL__=!0;var E=O;function L(i){if("function"!=typeof i)throw new TypeError("executor must be a function.");var t;this.promise=new Promise((function(i){t=i}));var n=this;i((function(i){n.reason||(n.reason=new E(i),t(n.reason))}))}L.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},L.source=function(){var i;return{token:new L((function(t){i=t})),cancel:i}};var q=L;function G(i){var t=new H(i),n=a(H.prototype.request,t);return l.extend(n,H.prototype,t),l.extend(n,t),n}var W=G(S);W.Axios=H,W.create=function(i){return G(T(W.defaults,i))},W.Cancel=E,W.CancelToken=q,W.isCancel=v,W.all=function(i){return Promise.all(i)},W.spread=function(i){return function(t){return i.apply(null,t)}};var A=W;A.default=W;var R,F,C=A;function I(){return R.apply(null,arguments)}function P(i){return i instanceof Array||"[object Array]"===Object.prototype.toString.call(i)}function Z(i){return null!=i&&"[object Object]"===Object.prototype.toString.call(i)}function U(i){return void 0===i}function B(i){return"number"==typeof i||"[object Number]"===Object.prototype.toString.call(i)}function $(i){return i instanceof Date||"[object Date]"===Object.prototype.toString.call(i)}function J(i,t){var n,a=[];for(n=0;n<i.length;++n)a.push(t(i[n],n));return a}function X(i,t){return Object.prototype.hasOwnProperty.call(i,t)}function Q(i,t){for(var n in t)X(t,n)&&(i[n]=t[n]);return X(t,"toString")&&(i.toString=t.toString),X(t,"valueOf")&&(i.valueOf=t.valueOf),i}function V(i,t,n,a){return ln(i,t,n,a,!0).utc()}function K(i){return null==i._pf&&(i._pf={empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],meridiem:null,rfc2822:!1,weekdayMismatch:!1}),i._pf}function ii(i){if(null==i._isValid){var t=K(i),n=F.call(t.parsedDateParts,(function(i){return null!=i})),a=!isNaN(i._d.getTime())&&t.overflow<0&&!t.empty&&!t.invalidMonth&&!t.invalidWeekday&&!t.weekdayMismatch&&!t.nullInput&&!t.invalidFormat&&!t.userInvalidated&&(!t.meridiem||t.meridiem&&n);if(i._strict&&(a=a&&0===t.charsLeftOver&&0===t.unusedTokens.length&&void 0===t.bigHour),null!=Object.isFrozen&&Object.isFrozen(i))return a;i._isValid=a}return i._isValid}function ti(i){var t=V(NaN);return null!=i?Q(K(t),i):K(t).userInvalidated=!0,t}F=Array.prototype.some?Array.prototype.some:function(i){for(var t=Object(this),n=t.length>>>0,a=0;a<n;a++)if(a in t&&i.call(this,t[a],a,t))return!0;return!1};var ni=I.momentProperties=[];function ai(i,t){var n,a,e;if(U(t._isAMomentObject)||(i._isAMomentObject=t._isAMomentObject),U(t._i)||(i._i=t._i),U(t._f)||(i._f=t._f),U(t._l)||(i._l=t._l),U(t._strict)||(i._strict=t._strict),U(t._tzm)||(i._tzm=t._tzm),U(t._isUTC)||(i._isUTC=t._isUTC),U(t._offset)||(i._offset=t._offset),U(t._pf)||(i._pf=K(t)),U(t._locale)||(i._locale=t._locale),ni.length>0)for(n=0;n<ni.length;n++)U(e=t[a=ni[n]])||(i[a]=e);return i}var ei=!1;function oi(i){ai(this,i),this._d=new Date(null!=i._d?i._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),!1===ei&&(ei=!0,I.updateOffset(this),ei=!1)}function si(i){return i instanceof oi||null!=i&&null!=i._isAMomentObject}function ri(i){return i<0?Math.ceil(i)||0:Math.floor(i)}function pi(i){var t=+i,n=0;return 0!==t&&isFinite(t)&&(n=ri(t)),n}function ci(i,t,n){var a,e=Math.min(i.length,t.length),o=Math.abs(i.length-t.length),s=0;for(a=0;a<e;a++)(n&&i[a]!==t[a]||!n&&pi(i[a])!==pi(t[a]))&&s++;return s+o}function li(i){!1===I.suppressDeprecationWarnings&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+i)}function di(i,t){var n=!0;return Q((function(){if(null!=I.deprecationHandler&&I.deprecationHandler(null,i),n){for(var a,e=[],o=0;o<arguments.length;o++){if(a="","object"==typeof arguments[o]){for(var s in a+="\n["+o+"] ",arguments[0])a+=s+": "+arguments[0][s]+", ";a=a.slice(0,-2)}else a=arguments[o];e.push(a)}li(i+"\nArguments: "+Array.prototype.slice.call(e).join("")+"\n"+(new Error).stack),n=!1}return t.apply(this,arguments)}),t)}var ui,mi={};function fi(i,t){null!=I.deprecationHandler&&I.deprecationHandler(i,t),mi[i]||(li(t),mi[i]=!0)}function hi(i){return i instanceof Function||"[object Function]"===Object.prototype.toString.call(i)}function vi(i,t){var n,a=Q({},i);for(n in t)X(t,n)&&(Z(i[n])&&Z(t[n])?(a[n]={},Q(a[n],i[n]),Q(a[n],t[n])):null!=t[n]?a[n]=t[n]:delete a[n]);for(n in i)X(i,n)&&!X(t,n)&&Z(i[n])&&(a[n]=Q({},a[n]));return a}function xi(i){null!=i&&this.set(i)}I.suppressDeprecationWarnings=!1,I.deprecationHandler=null,ui=Object.keys?Object.keys:function(i){var t,n=[];for(t in i)X(i,t)&&n.push(t);return n};var gi={};function bi(i,t){var n=i.toLowerCase();gi[n]=gi[n+"s"]=gi[t]=i}function wi(i){return"string"==typeof i?gi[i]||gi[i.toLowerCase()]:void 0}function yi(i){var t,n,a={};for(n in i)X(i,n)&&(t=wi(n))&&(a[t]=i[n]);return a}var ki={};function Mi(i,t){ki[i]=t}function Yi(i,t,n){var a=""+Math.abs(i);return(i>=0?n?"+":"":"-")+Math.pow(10,Math.max(0,t-a.length)).toString().substr(1)+a}var Di=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|YYYYYY|YYYYY|YYYY|YY|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,ji=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,Si={},_i={};function zi(i,t,n,a){var e=a;"string"==typeof a&&(e=function(){return this[a]()}),i&&(_i[i]=e),t&&(_i[t[0]]=function(){return Yi(e.apply(this,arguments),t[1],t[2])}),n&&(_i[n]=function(){return this.localeData().ordinal(e.apply(this,arguments),i)})}function Ti(i,t){return i.isValid()?(t=Ni(t,i.localeData()),Si[t]=Si[t]||function(i){var t,n,a,e=i.match(Di);for(t=0,n=e.length;t<n;t++)e[t]=_i[e[t]]?_i[e[t]]:(a=e[t]).match(/\[[\s\S]/)?a.replace(/^\[|\]$/g,""):a.replace(/\\/g,"");return function(t){var a,o="";for(a=0;a<n;a++)o+=hi(e[a])?e[a].call(t,i):e[a];return o}}(t),Si[t](i)):i.localeData().invalidDate()}function Ni(i,t){var n=5;function a(i){return t.longDateFormat(i)||i}for(ji.lastIndex=0;n>=0&&ji.test(i);)i=i.replace(ji,a),ji.lastIndex=0,n-=1;return i}var Hi=/\d/,Oi=/\d\d/,Ei=/\d{3}/,Li=/\d{4}/,qi=/[+-]?\d{6}/,Gi=/\d\d?/,Wi=/\d\d\d\d?/,Ai=/\d\d\d\d\d\d?/,Ri=/\d{1,3}/,Fi=/\d{1,4}/,Ci=/[+-]?\d{1,6}/,Ii=/\d+/,Pi=/[+-]?\d+/,Zi=/Z|[+-]\d\d:?\d\d/gi,Ui=/Z|[+-]\d\d(?::?\d\d)?/gi,Bi=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i,$i={};function Ji(i,t,n){$i[i]=hi(t)?t:function(i){return i&&n?n:t}}function Xi(i,t){return X($i,i)?$i[i](t._strict,t._locale):new RegExp(Qi(i.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,(function(i,t,n,a,e){return t||n||a||e}))))}function Qi(i){return i.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}var Vi={};function Ki(i,t){var n,a=t;for("string"==typeof i&&(i=[i]),B(t)&&(a=function(i,n){n[t]=pi(i)}),n=0;n<i.length;n++)Vi[i[n]]=a}function it(i,t){Ki(i,(function(i,n,a,e){a._w=a._w||{},t(i,a._w,a,e)}))}function tt(i,t,n){null!=t&&X(Vi,i)&&Vi[i](t,n._a,n,i)}function nt(i){return at(i)?366:365}function at(i){return i%4==0&&i%100!=0||i%400==0}zi("Y",0,0,(function(){var i=this.year();return i<=9999?""+i:"+"+i})),zi(0,["YY",2],0,(function(){return this.year()%100})),zi(0,["YYYY",4],0,"year"),zi(0,["YYYYY",5],0,"year"),zi(0,["YYYYYY",6,!0],0,"year"),bi("year","y"),Mi("year",1),Ji("Y",Pi),Ji("YY",Gi,Oi),Ji("YYYY",Fi,Li),Ji("YYYYY",Ci,qi),Ji("YYYYYY",Ci,qi),Ki(["YYYYY","YYYYYY"],0),Ki("YYYY",(function(i,t){t[0]=2===i.length?I.parseTwoDigitYear(i):pi(i)})),Ki("YY",(function(i,t){t[0]=I.parseTwoDigitYear(i)})),Ki("Y",(function(i,t){t[0]=parseInt(i,10)})),I.parseTwoDigitYear=function(i){return pi(i)+(pi(i)>68?1900:2e3)};var et,ot=st("FullYear",!0);function st(i,t){return function(n){return null!=n?(pt(this,i,n),I.updateOffset(this,t),this):rt(this,i)}}function rt(i,t){return i.isValid()?i._d["get"+(i._isUTC?"UTC":"")+t]():NaN}function pt(i,t,n){i.isValid()&&!isNaN(n)&&("FullYear"===t&&at(i.year())&&1===i.month()&&29===i.date()?i._d["set"+(i._isUTC?"UTC":"")+t](n,i.month(),ct(n,i.month())):i._d["set"+(i._isUTC?"UTC":"")+t](n))}function ct(i,t){if(isNaN(i)||isNaN(t))return NaN;var n=(t%12+12)%12;return i+=(t-n)/12,1===n?at(i)?29:28:31-n%7%2}et=Array.prototype.indexOf?Array.prototype.indexOf:function(i){var t;for(t=0;t<this.length;++t)if(this[t]===i)return t;return-1},zi("M",["MM",2],"Mo",(function(){return this.month()+1})),zi("MMM",0,0,(function(i){return this.localeData().monthsShort(this,i)})),zi("MMMM",0,0,(function(i){return this.localeData().months(this,i)})),bi("month","M"),Mi("month",8),Ji("M",Gi),Ji("MM",Gi,Oi),Ji("MMM",(function(i,t){return t.monthsShortRegex(i)})),Ji("MMMM",(function(i,t){return t.monthsRegex(i)})),Ki(["M","MM"],(function(i,t){t[1]=pi(i)-1})),Ki(["MMM","MMMM"],(function(i,t,n,a){var e=n._locale.monthsParse(i,a,n._strict);null!=e?t[1]=e:K(n).invalidMonth=i}));var lt=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,dt="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ut="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_");function mt(i,t,n){var a,e,o,s=i.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],a=0;a<12;++a)o=V([2e3,a]),this._shortMonthsParse[a]=this.monthsShort(o,"").toLocaleLowerCase(),this._longMonthsParse[a]=this.months(o,"").toLocaleLowerCase();return n?"MMM"===t?-1!==(e=et.call(this._shortMonthsParse,s))?e:null:-1!==(e=et.call(this._longMonthsParse,s))?e:null:"MMM"===t?-1!==(e=et.call(this._shortMonthsParse,s))?e:-1!==(e=et.call(this._longMonthsParse,s))?e:null:-1!==(e=et.call(this._longMonthsParse,s))?e:-1!==(e=et.call(this._shortMonthsParse,s))?e:null}function ft(i,t){var n;if(!i.isValid())return i;if("string"==typeof t)if(/^\d+$/.test(t))t=pi(t);else if(!B(t=i.localeData().monthsParse(t)))return i;return n=Math.min(i.date(),ct(i.year(),t)),i._d["set"+(i._isUTC?"UTC":"")+"Month"](t,n),i}function ht(i){return null!=i?(ft(this,i),I.updateOffset(this,!0),this):rt(this,"Month")}var vt=Bi,xt=Bi;function gt(){function i(i,t){return t.length-i.length}var t,n,a=[],e=[],o=[];for(t=0;t<12;t++)n=V([2e3,t]),a.push(this.monthsShort(n,"")),e.push(this.months(n,"")),o.push(this.months(n,"")),o.push(this.monthsShort(n,""));for(a.sort(i),e.sort(i),o.sort(i),t=0;t<12;t++)a[t]=Qi(a[t]),e[t]=Qi(e[t]);for(t=0;t<24;t++)o[t]=Qi(o[t]);this._monthsRegex=new RegExp("^("+o.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+e.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+a.join("|")+")","i")}function bt(i,t,n,a,e,o,s){var r;return i<100&&i>=0?(r=new Date(i+400,t,n,a,e,o,s),isFinite(r.getFullYear())&&r.setFullYear(i)):r=new Date(i,t,n,a,e,o,s),r}function wt(i){var t;if(i<100&&i>=0){var n=Array.prototype.slice.call(arguments);n[0]=i+400,t=new Date(Date.UTC.apply(null,n)),isFinite(t.getUTCFullYear())&&t.setUTCFullYear(i)}else t=new Date(Date.UTC.apply(null,arguments));return t}function yt(i,t,n){var a=7+t-n;return-(7+wt(i,0,a).getUTCDay()-t)%7+a-1}function kt(i,t,n,a,e){var o,s,r=1+7*(t-1)+(7+n-a)%7+yt(i,a,e);return r<=0?s=nt(o=i-1)+r:r>nt(i)?(o=i+1,s=r-nt(i)):(o=i,s=r),{year:o,dayOfYear:s}}function Mt(i,t,n){var a,e,o=yt(i.year(),t,n),s=Math.floor((i.dayOfYear()-o-1)/7)+1;return s<1?a=s+Yt(e=i.year()-1,t,n):s>Yt(i.year(),t,n)?(a=s-Yt(i.year(),t,n),e=i.year()+1):(e=i.year(),a=s),{week:a,year:e}}function Yt(i,t,n){var a=yt(i,t,n),e=yt(i+1,t,n);return(nt(i)-a+e)/7}function Dt(i,t){return i.slice(t,7).concat(i.slice(0,t))}zi("w",["ww",2],"wo","week"),zi("W",["WW",2],"Wo","isoWeek"),bi("week","w"),bi("isoWeek","W"),Mi("week",5),Mi("isoWeek",5),Ji("w",Gi),Ji("ww",Gi,Oi),Ji("W",Gi),Ji("WW",Gi,Oi),it(["w","ww","W","WW"],(function(i,t,n,a){t[a.substr(0,1)]=pi(i)})),zi("d",0,"do","day"),zi("dd",0,0,(function(i){return this.localeData().weekdaysMin(this,i)})),zi("ddd",0,0,(function(i){return this.localeData().weekdaysShort(this,i)})),zi("dddd",0,0,(function(i){return this.localeData().weekdays(this,i)})),zi("e",0,0,"weekday"),zi("E",0,0,"isoWeekday"),bi("day","d"),bi("weekday","e"),bi("isoWeekday","E"),Mi("day",11),Mi("weekday",11),Mi("isoWeekday",11),Ji("d",Gi),Ji("e",Gi),Ji("E",Gi),Ji("dd",(function(i,t){return t.weekdaysMinRegex(i)})),Ji("ddd",(function(i,t){return t.weekdaysShortRegex(i)})),Ji("dddd",(function(i,t){return t.weekdaysRegex(i)})),it(["dd","ddd","dddd"],(function(i,t,n,a){var e=n._locale.weekdaysParse(i,a,n._strict);null!=e?t.d=e:K(n).invalidWeekday=i})),it(["d","e","E"],(function(i,t,n,a){t[a]=pi(i)}));var jt="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),St="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),_t="Su_Mo_Tu_We_Th_Fr_Sa".split("_");function zt(i,t,n){var a,e,o,s=i.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],a=0;a<7;++a)o=V([2e3,1]).day(a),this._minWeekdaysParse[a]=this.weekdaysMin(o,"").toLocaleLowerCase(),this._shortWeekdaysParse[a]=this.weekdaysShort(o,"").toLocaleLowerCase(),this._weekdaysParse[a]=this.weekdays(o,"").toLocaleLowerCase();return n?"dddd"===t?-1!==(e=et.call(this._weekdaysParse,s))?e:null:"ddd"===t?-1!==(e=et.call(this._shortWeekdaysParse,s))?e:null:-1!==(e=et.call(this._minWeekdaysParse,s))?e:null:"dddd"===t?-1!==(e=et.call(this._weekdaysParse,s))?e:-1!==(e=et.call(this._shortWeekdaysParse,s))?e:-1!==(e=et.call(this._minWeekdaysParse,s))?e:null:"ddd"===t?-1!==(e=et.call(this._shortWeekdaysParse,s))?e:-1!==(e=et.call(this._weekdaysParse,s))?e:-1!==(e=et.call(this._minWeekdaysParse,s))?e:null:-1!==(e=et.call(this._minWeekdaysParse,s))?e:-1!==(e=et.call(this._weekdaysParse,s))?e:-1!==(e=et.call(this._shortWeekdaysParse,s))?e:null}var Tt=Bi,Nt=Bi,Ht=Bi;function Ot(){function i(i,t){return t.length-i.length}var t,n,a,e,o,s=[],r=[],p=[],c=[];for(t=0;t<7;t++)n=V([2e3,1]).day(t),a=this.weekdaysMin(n,""),e=this.weekdaysShort(n,""),o=this.weekdays(n,""),s.push(a),r.push(e),p.push(o),c.push(a),c.push(e),c.push(o);for(s.sort(i),r.sort(i),p.sort(i),c.sort(i),t=0;t<7;t++)r[t]=Qi(r[t]),p[t]=Qi(p[t]),c[t]=Qi(c[t]);this._weekdaysRegex=new RegExp("^("+c.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+p.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+r.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+s.join("|")+")","i")}function Et(){return this.hours()%12||12}function Lt(i,t){zi(i,0,0,(function(){return this.localeData().meridiem(this.hours(),this.minutes(),t)}))}function qt(i,t){return t._meridiemParse}zi("H",["HH",2],0,"hour"),zi("h",["hh",2],0,Et),zi("k",["kk",2],0,(function(){return this.hours()||24})),zi("hmm",0,0,(function(){return""+Et.apply(this)+Yi(this.minutes(),2)})),zi("hmmss",0,0,(function(){return""+Et.apply(this)+Yi(this.minutes(),2)+Yi(this.seconds(),2)})),zi("Hmm",0,0,(function(){return""+this.hours()+Yi(this.minutes(),2)})),zi("Hmmss",0,0,(function(){return""+this.hours()+Yi(this.minutes(),2)+Yi(this.seconds(),2)})),Lt("a",!0),Lt("A",!1),bi("hour","h"),Mi("hour",13),Ji("a",qt),Ji("A",qt),Ji("H",Gi),Ji("h",Gi),Ji("k",Gi),Ji("HH",Gi,Oi),Ji("hh",Gi,Oi),Ji("kk",Gi,Oi),Ji("hmm",Wi),Ji("hmmss",Ai),Ji("Hmm",Wi),Ji("Hmmss",Ai),Ki(["H","HH"],3),Ki(["k","kk"],(function(i,t){var n=pi(i);t[3]=24===n?0:n})),Ki(["a","A"],(function(i,t,n){n._isPm=n._locale.isPM(i),n._meridiem=i})),Ki(["h","hh"],(function(i,t,n){t[3]=pi(i),K(n).bigHour=!0})),Ki("hmm",(function(i,t,n){var a=i.length-2;t[3]=pi(i.substr(0,a)),t[4]=pi(i.substr(a)),K(n).bigHour=!0})),Ki("hmmss",(function(i,t,n){var a=i.length-4,e=i.length-2;t[3]=pi(i.substr(0,a)),t[4]=pi(i.substr(a,2)),t[5]=pi(i.substr(e)),K(n).bigHour=!0})),Ki("Hmm",(function(i,t){var n=i.length-2;t[3]=pi(i.substr(0,n)),t[4]=pi(i.substr(n))})),Ki("Hmmss",(function(i,t){var n=i.length-4,a=i.length-2;t[3]=pi(i.substr(0,n)),t[4]=pi(i.substr(n,2)),t[5]=pi(i.substr(a))}));var Gt,Wt=st("Hours",!0),At={calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},longDateFormat:{LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},invalidDate:"Invalid date",ordinal:"%d",dayOfMonthOrdinalParse:/\d{1,2}/,relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},months:dt,monthsShort:ut,week:{dow:0,doy:6},weekdays:jt,weekdaysMin:_t,weekdaysShort:St,meridiemParse:/[ap]\.?m?\.?/i},Rt={},Ft={};function Ct(i){return i?i.toLowerCase().replace("_","-"):i}function It(i){var t=null;if(!Rt[i]&&"undefined"!=typeof module&&module&&module.exports)try{t=Gt._abbr,require("./locale/"+i),Pt(t)}catch(n){}return Rt[i]}function Pt(i,t){var n;return i&&((n=U(t)?Ut(i):Zt(i,t))?Gt=n:"undefined"!=typeof console&&console.warn&&console.warn("Locale "+i+" not found. Did you forget to load it?")),Gt._abbr}function Zt(i,t){if(null!==t){var n,a=At;if(t.abbr=i,null!=Rt[i])fi("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),a=Rt[i]._config;else if(null!=t.parentLocale)if(null!=Rt[t.parentLocale])a=Rt[t.parentLocale]._config;else{if(null==(n=It(t.parentLocale)))return Ft[t.parentLocale]||(Ft[t.parentLocale]=[]),Ft[t.parentLocale].push({name:i,config:t}),null;a=n._config}return Rt[i]=new xi(vi(a,t)),Ft[i]&&Ft[i].forEach((function(i){Zt(i.name,i.config)})),Pt(i),Rt[i]}return delete Rt[i],null}function Ut(i){var t;if(i&&i._locale&&i._locale._abbr&&(i=i._locale._abbr),!i)return Gt;if(!P(i)){if(t=It(i))return t;i=[i]}return function(i){for(var t,n,a,e,o=0;o<i.length;){for(t=(e=Ct(i[o]).split("-")).length,n=(n=Ct(i[o+1]))?n.split("-"):null;t>0;){if(a=It(e.slice(0,t).join("-")))return a;if(n&&n.length>=t&&ci(e,n,!0)>=t-1)break;t--}o++}return Gt}(i)}function Bt(i){var t,n=i._a;return n&&-2===K(i).overflow&&(t=n[1]<0||n[1]>11?1:n[2]<1||n[2]>ct(n[0],n[1])?2:n[3]<0||n[3]>24||24===n[3]&&(0!==n[4]||0!==n[5]||0!==n[6])?3:n[4]<0||n[4]>59?4:n[5]<0||n[5]>59?5:n[6]<0||n[6]>999?6:-1,K(i)._overflowDayOfYear&&(t<0||t>2)&&(t=2),K(i)._overflowWeeks&&-1===t&&(t=7),K(i)._overflowWeekday&&-1===t&&(t=8),K(i).overflow=t),i}function $t(i,t,n){return null!=i?i:null!=t?t:n}function Jt(i){var t,n,a,e,o,s=[];if(!i._d){for(a=function(i){var t=new Date(I.now());return i._useUTC?[t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate()]:[t.getFullYear(),t.getMonth(),t.getDate()]}(i),i._w&&null==i._a[2]&&null==i._a[1]&&function(i){var t,n,a,e,o,s,r,p;if(null!=(t=i._w).GG||null!=t.W||null!=t.E)o=1,s=4,n=$t(t.GG,i._a[0],Mt(dn(),1,4).year),a=$t(t.W,1),((e=$t(t.E,1))<1||e>7)&&(p=!0);else{o=i._locale._week.dow,s=i._locale._week.doy;var c=Mt(dn(),o,s);n=$t(t.gg,i._a[0],c.year),a=$t(t.w,c.week),null!=t.d?((e=t.d)<0||e>6)&&(p=!0):null!=t.e?(e=t.e+o,(t.e<0||t.e>6)&&(p=!0)):e=o}a<1||a>Yt(n,o,s)?K(i)._overflowWeeks=!0:null!=p?K(i)._overflowWeekday=!0:(r=kt(n,a,e,o,s),i._a[0]=r.year,i._dayOfYear=r.dayOfYear)}(i),null!=i._dayOfYear&&(o=$t(i._a[0],a[0]),(i._dayOfYear>nt(o)||0===i._dayOfYear)&&(K(i)._overflowDayOfYear=!0),n=wt(o,0,i._dayOfYear),i._a[1]=n.getUTCMonth(),i._a[2]=n.getUTCDate()),t=0;t<3&&null==i._a[t];++t)i._a[t]=s[t]=a[t];for(;t<7;t++)i._a[t]=s[t]=null==i._a[t]?2===t?1:0:i._a[t];24===i._a[3]&&0===i._a[4]&&0===i._a[5]&&0===i._a[6]&&(i._nextDay=!0,i._a[3]=0),i._d=(i._useUTC?wt:bt).apply(null,s),e=i._useUTC?i._d.getUTCDay():i._d.getDay(),null!=i._tzm&&i._d.setUTCMinutes(i._d.getUTCMinutes()-i._tzm),i._nextDay&&(i._a[3]=24),i._w&&void 0!==i._w.d&&i._w.d!==e&&(K(i).weekdayMismatch=!0)}}var Xt=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?$/,Qt=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?$/,Vt=/Z|[+-]\d\d(?::?\d\d)?/,Kt=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/]],tn=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],nn=/^\/?Date\((\-?\d+)/i;function an(i){var t,n,a,e,o,s,r=i._i,p=Xt.exec(r)||Qt.exec(r);if(p){for(K(i).iso=!0,t=0,n=Kt.length;t<n;t++)if(Kt[t][1].exec(p[1])){e=Kt[t][0],a=!1!==Kt[t][2];break}if(null==e)return void(i._isValid=!1);if(p[3]){for(t=0,n=tn.length;t<n;t++)if(tn[t][1].exec(p[3])){o=(p[2]||" ")+tn[t][0];break}if(null==o)return void(i._isValid=!1)}if(!a&&null!=o)return void(i._isValid=!1);if(p[4]){if(!Vt.exec(p[4]))return void(i._isValid=!1);s="Z"}i._f=e+(o||"")+(s||""),pn(i)}else i._isValid=!1}var en=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/;function on(i){var t=parseInt(i,10);return t<=49?2e3+t:t<=999?1900+t:t}var sn={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function rn(i){var t,n,a,e,o,s,r=en.exec(i._i.replace(/\([^)]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,""));if(r){var p=(t=r[3],n=r[2],a=r[5],e=r[6],o=r[7],s=[on(r[4]),ut.indexOf(t),parseInt(n,10),parseInt(a,10),parseInt(e,10)],o&&s.push(parseInt(o,10)),s);if(!function(i,t,n){return!i||St.indexOf(i)===new Date(t[0],t[1],t[2]).getDay()||(K(n).weekdayMismatch=!0,n._isValid=!1,!1)}(r[1],p,i))return;i._a=p,i._tzm=function(i,t,n){if(i)return sn[i];if(t)return 0;var a=parseInt(n,10),e=a%100;return(a-e)/100*60+e}(r[8],r[9],r[10]),i._d=wt.apply(null,i._a),i._d.setUTCMinutes(i._d.getUTCMinutes()-i._tzm),K(i).rfc2822=!0}else i._isValid=!1}function pn(i){if(i._f!==I.ISO_8601)if(i._f!==I.RFC_2822){i._a=[],K(i).empty=!0;var t,n,a,e,o,s=""+i._i,r=s.length,p=0;for(a=Ni(i._f,i._locale).match(Di)||[],t=0;t<a.length;t++)(n=(s.match(Xi(e=a[t],i))||[])[0])&&((o=s.substr(0,s.indexOf(n))).length>0&&K(i).unusedInput.push(o),s=s.slice(s.indexOf(n)+n.length),p+=n.length),_i[e]?(n?K(i).empty=!1:K(i).unusedTokens.push(e),tt(e,n,i)):i._strict&&!n&&K(i).unusedTokens.push(e);K(i).charsLeftOver=r-p,s.length>0&&K(i).unusedInput.push(s),i._a[3]<=12&&!0===K(i).bigHour&&i._a[3]>0&&(K(i).bigHour=void 0),K(i).parsedDateParts=i._a.slice(0),K(i).meridiem=i._meridiem,i._a[3]=function(i,t,n){var a;return null==n?t:null!=i.meridiemHour?i.meridiemHour(t,n):null!=i.isPM?((a=i.isPM(n))&&t<12&&(t+=12),a||12!==t||(t=0),t):t}(i._locale,i._a[3],i._meridiem),Jt(i),Bt(i)}else rn(i);else an(i)}function cn(i){var t=i._i,n=i._f;return i._locale=i._locale||Ut(i._l),null===t||void 0===n&&""===t?ti({nullInput:!0}):("string"==typeof t&&(i._i=t=i._locale.preparse(t)),si(t)?new oi(Bt(t)):($(t)?i._d=t:P(n)?function(i){var t,n,a,e,o;if(0===i._f.length)return K(i).invalidFormat=!0,void(i._d=new Date(NaN));for(e=0;e<i._f.length;e++)o=0,t=ai({},i),null!=i._useUTC&&(t._useUTC=i._useUTC),t._f=i._f[e],pn(t),ii(t)&&(o+=K(t).charsLeftOver,o+=10*K(t).unusedTokens.length,K(t).score=o,(null==a||o<a)&&(a=o,n=t));Q(i,n||t)}(i):n?pn(i):function(i){var t=i._i;U(t)?i._d=new Date(I.now()):$(t)?i._d=new Date(t.valueOf()):"string"==typeof t?function(i){var t=nn.exec(i._i);null===t?(an(i),!1===i._isValid&&(delete i._isValid,rn(i),!1===i._isValid&&(delete i._isValid,I.createFromInputFallback(i)))):i._d=new Date(+t[1])}(i):P(t)?(i._a=J(t.slice(0),(function(i){return parseInt(i,10)})),Jt(i)):Z(t)?function(i){if(!i._d){var t=yi(i._i);i._a=J([t.year,t.month,t.day||t.date,t.hour,t.minute,t.second,t.millisecond],(function(i){return i&&parseInt(i,10)})),Jt(i)}}(i):B(t)?i._d=new Date(t):I.createFromInputFallback(i)}(i),ii(i)||(i._d=null),i))}function ln(i,t,n,a,e){var o,s={};return!0!==n&&!1!==n||(a=n,n=void 0),(Z(i)&&function(i){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(i).length;var t;for(t in i)if(i.hasOwnProperty(t))return!1;return!0}(i)||P(i)&&0===i.length)&&(i=void 0),s._isAMomentObject=!0,s._useUTC=s._isUTC=e,s._l=n,s._i=i,s._f=t,s._strict=a,(o=new oi(Bt(cn(s))))._nextDay&&(o.add(1,"d"),o._nextDay=void 0),o}function dn(i,t,n,a){return ln(i,t,n,a,!1)}I.createFromInputFallback=di("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged and will be removed in an upcoming major release. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",(function(i){i._d=new Date(i._i+(i._useUTC?" UTC":""))})),I.ISO_8601=function(){},I.RFC_2822=function(){};var un=di("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",(function(){var i=dn.apply(null,arguments);return this.isValid()&&i.isValid()?i<this?this:i:ti()})),mn=di("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",(function(){var i=dn.apply(null,arguments);return this.isValid()&&i.isValid()?i>this?this:i:ti()}));function fn(i,t){var n,a;if(1===t.length&&P(t[0])&&(t=t[0]),!t.length)return dn();for(n=t[0],a=1;a<t.length;++a)t[a].isValid()&&!t[a][i](n)||(n=t[a]);return n}var hn=["year","quarter","month","week","day","hour","minute","second","millisecond"];function vn(i){var t=yi(i),n=t.year||0,a=t.quarter||0,e=t.month||0,o=t.week||t.isoWeek||0,s=t.day||0,r=t.hour||0,p=t.minute||0,c=t.second||0,l=t.millisecond||0;this._isValid=function(i){for(var t in i)if(-1===et.call(hn,t)||null!=i[t]&&isNaN(i[t]))return!1;for(var n=!1,a=0;a<hn.length;++a)if(i[hn[a]]){if(n)return!1;parseFloat(i[hn[a]])!==pi(i[hn[a]])&&(n=!0)}return!0}(t),this._milliseconds=+l+1e3*c+6e4*p+1e3*r*60*60,this._days=+s+7*o,this._months=+e+3*a+12*n,this._data={},this._locale=Ut(),this._bubble()}function xn(i){return i instanceof vn}function gn(i){return i<0?-1*Math.round(-1*i):Math.round(i)}function bn(i,t){zi(i,0,0,(function(){var i=this.utcOffset(),n="+";return i<0&&(i=-i,n="-"),n+Yi(~~(i/60),2)+t+Yi(~~i%60,2)}))}bn("Z",":"),bn("ZZ",""),Ji("Z",Ui),Ji("ZZ",Ui),Ki(["Z","ZZ"],(function(i,t,n){n._useUTC=!0,n._tzm=yn(Ui,i)}));var wn=/([\+\-]|\d\d)/gi;function yn(i,t){var n=(t||"").match(i);if(null===n)return null;var a=((n[n.length-1]||[])+"").match(wn)||["-",0,0],e=60*a[1]+pi(a[2]);return 0===e?0:"+"===a[0]?e:-e}function kn(i,t){var n,a;return t._isUTC?(n=t.clone(),a=(si(i)||$(i)?i.valueOf():dn(i).valueOf())-n.valueOf(),n._d.setTime(n._d.valueOf()+a),I.updateOffset(n,!1),n):dn(i).local()}function Mn(i){return 15*-Math.round(i._d.getTimezoneOffset()/15)}function Yn(){return!!this.isValid()&&this._isUTC&&0===this._offset}I.updateOffset=function(){};var Dn=/^(\-|\+)?(?:(\d*)[. ])?(\d+)\:(\d+)(?:\:(\d+)(\.\d*)?)?$/,jn=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function Sn(i,t){var n,a,e,o,s,r,p=i,c=null;return xn(i)?p={ms:i._milliseconds,d:i._days,M:i._months}:B(i)?(p={},t?p[t]=i:p.milliseconds=i):(c=Dn.exec(i))?(n="-"===c[1]?-1:1,p={y:0,d:pi(c[2])*n,h:pi(c[3])*n,m:pi(c[4])*n,s:pi(c[5])*n,ms:pi(gn(1e3*c[6]))*n}):(c=jn.exec(i))?p={y:_n(c[2],n="-"===c[1]?-1:1),M:_n(c[3],n),w:_n(c[4],n),d:_n(c[5],n),h:_n(c[6],n),m:_n(c[7],n),s:_n(c[8],n)}:null==p?p={}:"object"==typeof p&&("from"in p||"to"in p)&&(o=dn(p.from),s=dn(p.to),e=o.isValid()&&s.isValid()?(s=kn(s,o),o.isBefore(s)?r=zn(o,s):((r=zn(s,o)).milliseconds=-r.milliseconds,r.months=-r.months),r):{milliseconds:0,months:0},(p={}).ms=e.milliseconds,p.M=e.months),a=new vn(p),xn(i)&&X(i,"_locale")&&(a._locale=i._locale),a}function _n(i,t){var n=i&&parseFloat(i.replace(",","."));return(isNaN(n)?0:n)*t}function zn(i,t){var n={};return n.months=t.month()-i.month()+12*(t.year()-i.year()),i.clone().add(n.months,"M").isAfter(t)&&--n.months,n.milliseconds=+t-+i.clone().add(n.months,"M"),n}function Tn(i,t){return function(n,a){var e;return null===a||isNaN(+a)||(fi(t,"moment()."+t+"(period, number) is deprecated. Please use moment()."+t+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),e=n,n=a,a=e),Nn(this,Sn(n="string"==typeof n?+n:n,a),i),this}}function Nn(i,t,n,a){var e=t._milliseconds,o=gn(t._days),s=gn(t._months);i.isValid()&&(a=null==a||a,s&&ft(i,rt(i,"Month")+s*n),o&&pt(i,"Date",rt(i,"Date")+o*n),e&&i._d.setTime(i._d.valueOf()+e*n),a&&I.updateOffset(i,o||s))}Sn.fn=vn.prototype,Sn.invalid=function(){return Sn(NaN)};var Hn=Tn(1,"add"),On=Tn(-1,"subtract");function En(i,t){var n=12*(t.year()-i.year())+(t.month()-i.month()),a=i.clone().add(n,"months");return-(n+(t-a<0?(t-a)/(a-i.clone().add(n-1,"months")):(t-a)/(i.clone().add(n+1,"months")-a)))||0}function Ln(i){var t;return void 0===i?this._locale._abbr:(null!=(t=Ut(i))&&(this._locale=t),this)}I.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",I.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";var qn=di("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",(function(i){return void 0===i?this.localeData():this.locale(i)}));function Gn(){return this._locale}function Wn(i,t){return(i%t+t)%t}function An(i,t,n){return i<100&&i>=0?new Date(i+400,t,n)-126227808e5:new Date(i,t,n).valueOf()}function Rn(i,t,n){return i<100&&i>=0?Date.UTC(i+400,t,n)-126227808e5:Date.UTC(i,t,n)}function Fn(i,t){zi(0,[i,i.length],0,t)}function Cn(i,t,n,a,e){var o;return null==i?Mt(this,a,e).year:(t>(o=Yt(i,a,e))&&(t=o),In.call(this,i,t,n,a,e))}function In(i,t,n,a,e){var o=kt(i,t,n,a,e),s=wt(o.year,0,o.dayOfYear);return this.year(s.getUTCFullYear()),this.month(s.getUTCMonth()),this.date(s.getUTCDate()),this}zi(0,["gg",2],0,(function(){return this.weekYear()%100})),zi(0,["GG",2],0,(function(){return this.isoWeekYear()%100})),Fn("gggg","weekYear"),Fn("ggggg","weekYear"),Fn("GGGG","isoWeekYear"),Fn("GGGGG","isoWeekYear"),bi("weekYear","gg"),bi("isoWeekYear","GG"),Mi("weekYear",1),Mi("isoWeekYear",1),Ji("G",Pi),Ji("g",Pi),Ji("GG",Gi,Oi),Ji("gg",Gi,Oi),Ji("GGGG",Fi,Li),Ji("gggg",Fi,Li),Ji("GGGGG",Ci,qi),Ji("ggggg",Ci,qi),it(["gggg","ggggg","GGGG","GGGGG"],(function(i,t,n,a){t[a.substr(0,2)]=pi(i)})),it(["gg","GG"],(function(i,t,n,a){t[a]=I.parseTwoDigitYear(i)})),zi("Q",0,"Qo","quarter"),bi("quarter","Q"),Mi("quarter",7),Ji("Q",Hi),Ki("Q",(function(i,t){t[1]=3*(pi(i)-1)})),zi("D",["DD",2],"Do","date"),bi("date","D"),Mi("date",9),Ji("D",Gi),Ji("DD",Gi,Oi),Ji("Do",(function(i,t){return i?t._dayOfMonthOrdinalParse||t._ordinalParse:t._dayOfMonthOrdinalParseLenient})),Ki(["D","DD"],2),Ki("Do",(function(i,t){t[2]=pi(i.match(Gi)[0])}));var Pn=st("Date",!0);zi("DDD",["DDDD",3],"DDDo","dayOfYear"),bi("dayOfYear","DDD"),Mi("dayOfYear",4),Ji("DDD",Ri),Ji("DDDD",Ei),Ki(["DDD","DDDD"],(function(i,t,n){n._dayOfYear=pi(i)})),zi("m",["mm",2],0,"minute"),bi("minute","m"),Mi("minute",14),Ji("m",Gi),Ji("mm",Gi,Oi),Ki(["m","mm"],4);var Zn=st("Minutes",!1);zi("s",["ss",2],0,"second"),bi("second","s"),Mi("second",15),Ji("s",Gi),Ji("ss",Gi,Oi),Ki(["s","ss"],5);var Un,Bn=st("Seconds",!1);for(zi("S",0,0,(function(){return~~(this.millisecond()/100)})),zi(0,["SS",2],0,(function(){return~~(this.millisecond()/10)})),zi(0,["SSS",3],0,"millisecond"),zi(0,["SSSS",4],0,(function(){return 10*this.millisecond()})),zi(0,["SSSSS",5],0,(function(){return 100*this.millisecond()})),zi(0,["SSSSSS",6],0,(function(){return 1e3*this.millisecond()})),zi(0,["SSSSSSS",7],0,(function(){return 1e4*this.millisecond()})),zi(0,["SSSSSSSS",8],0,(function(){return 1e5*this.millisecond()})),zi(0,["SSSSSSSSS",9],0,(function(){return 1e6*this.millisecond()})),bi("millisecond","ms"),Mi("millisecond",16),Ji("S",Ri,Hi),Ji("SS",Ri,Oi),Ji("SSS",Ri,Ei),Un="SSSS";Un.length<=9;Un+="S")Ji(Un,Ii);function $n(i,t){t[6]=pi(1e3*("0."+i))}for(Un="S";Un.length<=9;Un+="S")Ki(Un,$n);var Jn=st("Milliseconds",!1);zi("z",0,0,"zoneAbbr"),zi("zz",0,0,"zoneName");var Xn=oi.prototype;function Qn(i){return i}Xn.add=Hn,Xn.calendar=function(i,t){var n=i||dn(),a=kn(n,this).startOf("day"),e=I.calendarFormat(this,a)||"sameElse",o=t&&(hi(t[e])?t[e].call(this,n):t[e]);return this.format(o||this.localeData().calendar(e,this,dn(n)))},Xn.clone=function(){return new oi(this)},Xn.diff=function(i,t,n){var a,e,o;if(!this.isValid())return NaN;if(!(a=kn(i,this)).isValid())return NaN;switch(e=6e4*(a.utcOffset()-this.utcOffset()),t=wi(t)){case"year":o=En(this,a)/12;break;case"month":o=En(this,a);break;case"quarter":o=En(this,a)/3;break;case"second":o=(this-a)/1e3;break;case"minute":o=(this-a)/6e4;break;case"hour":o=(this-a)/36e5;break;case"day":o=(this-a-e)/864e5;break;case"week":o=(this-a-e)/6048e5;break;default:o=this-a}return n?o:ri(o)},Xn.endOf=function(i){var t;if(void 0===(i=wi(i))||"millisecond"===i||!this.isValid())return this;var n=this._isUTC?Rn:An;switch(i){case"year":t=n(this.year()+1,0,1)-1;break;case"quarter":t=n(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":t=n(this.year(),this.month()+1,1)-1;break;case"week":t=n(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":t=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":t=n(this.year(),this.month(),this.date()+1)-1;break;case"hour":t=this._d.valueOf(),t+=36e5-Wn(t+(this._isUTC?0:6e4*this.utcOffset()),36e5)-1;break;case"minute":t=this._d.valueOf(),t+=6e4-Wn(t,6e4)-1;break;case"second":t=this._d.valueOf(),t+=1e3-Wn(t,1e3)-1}return this._d.setTime(t),I.updateOffset(this,!0),this},Xn.format=function(i){i||(i=this.isUtc()?I.defaultFormatUtc:I.defaultFormat);var t=Ti(this,i);return this.localeData().postformat(t)},Xn.from=function(i,t){return this.isValid()&&(si(i)&&i.isValid()||dn(i).isValid())?Sn({to:this,from:i}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},Xn.fromNow=function(i){return this.from(dn(),i)},Xn.to=function(i,t){return this.isValid()&&(si(i)&&i.isValid()||dn(i).isValid())?Sn({from:this,to:i}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},Xn.toNow=function(i){return this.to(dn(),i)},Xn.get=function(i){return hi(this[i=wi(i)])?this[i]():this},Xn.invalidAt=function(){return K(this).overflow},Xn.isAfter=function(i,t){var n=si(i)?i:dn(i);return!(!this.isValid()||!n.isValid())&&("millisecond"===(t=wi(t)||"millisecond")?this.valueOf()>n.valueOf():n.valueOf()<this.clone().startOf(t).valueOf())},Xn.isBefore=function(i,t){var n=si(i)?i:dn(i);return!(!this.isValid()||!n.isValid())&&("millisecond"===(t=wi(t)||"millisecond")?this.valueOf()<n.valueOf():this.clone().endOf(t).valueOf()<n.valueOf())},Xn.isBetween=function(i,t,n,a){var e=si(i)?i:dn(i),o=si(t)?t:dn(t);return!!(this.isValid()&&e.isValid()&&o.isValid())&&("("===(a=a||"()")[0]?this.isAfter(e,n):!this.isBefore(e,n))&&(")"===a[1]?this.isBefore(o,n):!this.isAfter(o,n))},Xn.isSame=function(i,t){var n,a=si(i)?i:dn(i);return!(!this.isValid()||!a.isValid())&&("millisecond"===(t=wi(t)||"millisecond")?this.valueOf()===a.valueOf():(n=a.valueOf(),this.clone().startOf(t).valueOf()<=n&&n<=this.clone().endOf(t).valueOf()))},Xn.isSameOrAfter=function(i,t){return this.isSame(i,t)||this.isAfter(i,t)},Xn.isSameOrBefore=function(i,t){return this.isSame(i,t)||this.isBefore(i,t)},Xn.isValid=function(){return ii(this)},Xn.lang=qn,Xn.locale=Ln,Xn.localeData=Gn,Xn.max=mn,Xn.min=un,Xn.parsingFlags=function(){return Q({},K(this))},Xn.set=function(i,t){if("object"==typeof i)for(var n=function(i){var t=[];for(var n in i)t.push({unit:n,priority:ki[n]});return t.sort((function(i,t){return i.priority-t.priority})),t}(i=yi(i)),a=0;a<n.length;a++)this[n[a].unit](i[n[a].unit]);else if(hi(this[i=wi(i)]))return this[i](t);return this},Xn.startOf=function(i){var t;if(void 0===(i=wi(i))||"millisecond"===i||!this.isValid())return this;var n=this._isUTC?Rn:An;switch(i){case"year":t=n(this.year(),0,1);break;case"quarter":t=n(this.year(),this.month()-this.month()%3,1);break;case"month":t=n(this.year(),this.month(),1);break;case"week":t=n(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":t=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":t=n(this.year(),this.month(),this.date());break;case"hour":t=this._d.valueOf(),t-=Wn(t+(this._isUTC?0:6e4*this.utcOffset()),36e5);break;case"minute":t=this._d.valueOf(),t-=Wn(t,6e4);break;case"second":t=this._d.valueOf(),t-=Wn(t,1e3)}return this._d.setTime(t),I.updateOffset(this,!0),this},Xn.subtract=On,Xn.toArray=function(){var i=this;return[i.year(),i.month(),i.date(),i.hour(),i.minute(),i.second(),i.millisecond()]},Xn.toObject=function(){var i=this;return{years:i.year(),months:i.month(),date:i.date(),hours:i.hours(),minutes:i.minutes(),seconds:i.seconds(),milliseconds:i.milliseconds()}},Xn.toDate=function(){return new Date(this.valueOf())},Xn.toISOString=function(i){if(!this.isValid())return null;var t=!0!==i,n=t?this.clone().utc():this;return n.year()<0||n.year()>9999?Ti(n,t?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):hi(Date.prototype.toISOString)?t?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",Ti(n,"Z")):Ti(n,t?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ")},Xn.inspect=function(){if(!this.isValid())return"moment.invalid(/* "+this._i+" */)";var i="moment",t="";this.isLocal()||(i=0===this.utcOffset()?"moment.utc":"moment.parseZone",t="Z");var n="["+i+'("]',a=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY";return this.format(n+a+"-MM-DD[T]HH:mm:ss.SSS"+t+'[")]')},Xn.toJSON=function(){return this.isValid()?this.toISOString():null},Xn.toString=function(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")},Xn.unix=function(){return Math.floor(this.valueOf()/1e3)},Xn.valueOf=function(){return this._d.valueOf()-6e4*(this._offset||0)},Xn.creationData=function(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}},Xn.year=ot,Xn.isLeapYear=function(){return at(this.year())},Xn.weekYear=function(i){return Cn.call(this,i,this.week(),this.weekday(),this.localeData()._week.dow,this.localeData()._week.doy)},Xn.isoWeekYear=function(i){return Cn.call(this,i,this.isoWeek(),this.isoWeekday(),1,4)},Xn.quarter=Xn.quarters=function(i){return null==i?Math.ceil((this.month()+1)/3):this.month(3*(i-1)+this.month()%3)},Xn.month=ht,Xn.daysInMonth=function(){return ct(this.year(),this.month())},Xn.week=Xn.weeks=function(i){var t=this.localeData().week(this);return null==i?t:this.add(7*(i-t),"d")},Xn.isoWeek=Xn.isoWeeks=function(i){var t=Mt(this,1,4).week;return null==i?t:this.add(7*(i-t),"d")},Xn.weeksInYear=function(){var i=this.localeData()._week;return Yt(this.year(),i.dow,i.doy)},Xn.isoWeeksInYear=function(){return Yt(this.year(),1,4)},Xn.date=Pn,Xn.day=Xn.days=function(i){if(!this.isValid())return null!=i?this:NaN;var t=this._isUTC?this._d.getUTCDay():this._d.getDay();return null!=i?(i=function(i,t){return"string"!=typeof i?i:isNaN(i)?"number"==typeof(i=t.weekdaysParse(i))?i:null:parseInt(i,10)}(i,this.localeData()),this.add(i-t,"d")):t},Xn.weekday=function(i){if(!this.isValid())return null!=i?this:NaN;var t=(this.day()+7-this.localeData()._week.dow)%7;return null==i?t:this.add(i-t,"d")},Xn.isoWeekday=function(i){if(!this.isValid())return null!=i?this:NaN;if(null!=i){var t=function(i,t){return"string"==typeof i?t.weekdaysParse(i)%7||7:isNaN(i)?null:i}(i,this.localeData());return this.day(this.day()%7?t:t-7)}return this.day()||7},Xn.dayOfYear=function(i){var t=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==i?t:this.add(i-t,"d")},Xn.hour=Xn.hours=Wt,Xn.minute=Xn.minutes=Zn,Xn.second=Xn.seconds=Bn,Xn.millisecond=Xn.milliseconds=Jn,Xn.utcOffset=function(i,t,n){var a,e=this._offset||0;if(!this.isValid())return null!=i?this:NaN;if(null!=i){if("string"==typeof i){if(null===(i=yn(Ui,i)))return this}else Math.abs(i)<16&&!n&&(i*=60);return!this._isUTC&&t&&(a=Mn(this)),this._offset=i,this._isUTC=!0,null!=a&&this.add(a,"m"),e!==i&&(!t||this._changeInProgress?Nn(this,Sn(i-e,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,I.updateOffset(this,!0),this._changeInProgress=null)),this}return this._isUTC?e:Mn(this)},Xn.utc=function(i){return this.utcOffset(0,i)},Xn.local=function(i){return this._isUTC&&(this.utcOffset(0,i),this._isUTC=!1,i&&this.subtract(Mn(this),"m")),this},Xn.parseZone=function(){if(null!=this._tzm)this.utcOffset(this._tzm,!1,!0);else if("string"==typeof this._i){var i=yn(Zi,this._i);null!=i?this.utcOffset(i):this.utcOffset(0,!0)}return this},Xn.hasAlignedHourOffset=function(i){return!!this.isValid()&&(i=i?dn(i).utcOffset():0,(this.utcOffset()-i)%60==0)},Xn.isDST=function(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()},Xn.isLocal=function(){return!!this.isValid()&&!this._isUTC},Xn.isUtcOffset=function(){return!!this.isValid()&&this._isUTC},Xn.isUtc=Yn,Xn.isUTC=Yn,Xn.zoneAbbr=function(){return this._isUTC?"UTC":""},Xn.zoneName=function(){return this._isUTC?"Coordinated Universal Time":""},Xn.dates=di("dates accessor is deprecated. Use date instead.",Pn),Xn.months=di("months accessor is deprecated. Use month instead",ht),Xn.years=di("years accessor is deprecated. Use year instead",ot),Xn.zone=di("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",(function(i,t){return null!=i?("string"!=typeof i&&(i=-i),this.utcOffset(i,t),this):-this.utcOffset()})),Xn.isDSTShifted=di("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",(function(){if(!U(this._isDSTShifted))return this._isDSTShifted;var i={};if(ai(i,this),(i=cn(i))._a){var t=i._isUTC?V(i._a):dn(i._a);this._isDSTShifted=this.isValid()&&ci(i._a,t.toArray())>0}else this._isDSTShifted=!1;return this._isDSTShifted}));var Vn=xi.prototype;function Kn(i,t,n,a){var e=Ut(),o=V().set(a,t);return e[n](o,i)}function ia(i,t,n){if(B(i)&&(t=i,i=void 0),i=i||"",null!=t)return Kn(i,t,n,"month");var a,e=[];for(a=0;a<12;a++)e[a]=Kn(i,a,n,"month");return e}function ta(i,t,n,a){"boolean"==typeof i?(B(t)&&(n=t,t=void 0),t=t||""):(n=t=i,i=!1,B(t)&&(n=t,t=void 0),t=t||"");var e,o=Ut(),s=i?o._week.dow:0;if(null!=n)return Kn(t,(n+s)%7,a,"day");var r=[];for(e=0;e<7;e++)r[e]=Kn(t,(e+s)%7,a,"day");return r}Vn.calendar=function(i,t,n){var a=this._calendar[i]||this._calendar.sameElse;return hi(a)?a.call(t,n):a},Vn.longDateFormat=function(i){var t=this._longDateFormat[i],n=this._longDateFormat[i.toUpperCase()];return t||!n?t:(this._longDateFormat[i]=n.replace(/MMMM|MM|DD|dddd/g,(function(i){return i.slice(1)})),this._longDateFormat[i])},Vn.invalidDate=function(){return this._invalidDate},Vn.ordinal=function(i){return this._ordinal.replace("%d",i)},Vn.preparse=Qn,Vn.postformat=Qn,Vn.relativeTime=function(i,t,n,a){var e=this._relativeTime[n];return hi(e)?e(i,t,n,a):e.replace(/%d/i,i)},Vn.pastFuture=function(i,t){var n=this._relativeTime[i>0?"future":"past"];return hi(n)?n(t):n.replace(/%s/i,t)},Vn.set=function(i){var t,n;for(n in i)hi(t=i[n])?this[n]=t:this["_"+n]=t;this._config=i,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)},Vn.months=function(i,t){return i?P(this._months)?this._months[i.month()]:this._months[(this._months.isFormat||lt).test(t)?"format":"standalone"][i.month()]:P(this._months)?this._months:this._months.standalone},Vn.monthsShort=function(i,t){return i?P(this._monthsShort)?this._monthsShort[i.month()]:this._monthsShort[lt.test(t)?"format":"standalone"][i.month()]:P(this._monthsShort)?this._monthsShort:this._monthsShort.standalone},Vn.monthsParse=function(i,t,n){var a,e,o;if(this._monthsParseExact)return mt.call(this,i,t,n);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),a=0;a<12;a++){if(e=V([2e3,a]),n&&!this._longMonthsParse[a]&&(this._longMonthsParse[a]=new RegExp("^"+this.months(e,"").replace(".","")+"$","i"),this._shortMonthsParse[a]=new RegExp("^"+this.monthsShort(e,"").replace(".","")+"$","i")),n||this._monthsParse[a]||(o="^"+this.months(e,"")+"|^"+this.monthsShort(e,""),this._monthsParse[a]=new RegExp(o.replace(".",""),"i")),n&&"MMMM"===t&&this._longMonthsParse[a].test(i))return a;if(n&&"MMM"===t&&this._shortMonthsParse[a].test(i))return a;if(!n&&this._monthsParse[a].test(i))return a}},Vn.monthsRegex=function(i){return this._monthsParseExact?(X(this,"_monthsRegex")||gt.call(this),i?this._monthsStrictRegex:this._monthsRegex):(X(this,"_monthsRegex")||(this._monthsRegex=xt),this._monthsStrictRegex&&i?this._monthsStrictRegex:this._monthsRegex)},Vn.monthsShortRegex=function(i){return this._monthsParseExact?(X(this,"_monthsRegex")||gt.call(this),i?this._monthsShortStrictRegex:this._monthsShortRegex):(X(this,"_monthsShortRegex")||(this._monthsShortRegex=vt),this._monthsShortStrictRegex&&i?this._monthsShortStrictRegex:this._monthsShortRegex)},Vn.week=function(i){return Mt(i,this._week.dow,this._week.doy).week},Vn.firstDayOfYear=function(){return this._week.doy},Vn.firstDayOfWeek=function(){return this._week.dow},Vn.weekdays=function(i,t){var n=P(this._weekdays)?this._weekdays:this._weekdays[i&&!0!==i&&this._weekdays.isFormat.test(t)?"format":"standalone"];return!0===i?Dt(n,this._week.dow):i?n[i.day()]:n},Vn.weekdaysMin=function(i){return!0===i?Dt(this._weekdaysMin,this._week.dow):i?this._weekdaysMin[i.day()]:this._weekdaysMin},Vn.weekdaysShort=function(i){return!0===i?Dt(this._weekdaysShort,this._week.dow):i?this._weekdaysShort[i.day()]:this._weekdaysShort},Vn.weekdaysParse=function(i,t,n){var a,e,o;if(this._weekdaysParseExact)return zt.call(this,i,t,n);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),a=0;a<7;a++){if(e=V([2e3,1]).day(a),n&&!this._fullWeekdaysParse[a]&&(this._fullWeekdaysParse[a]=new RegExp("^"+this.weekdays(e,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[a]=new RegExp("^"+this.weekdaysShort(e,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[a]=new RegExp("^"+this.weekdaysMin(e,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[a]||(o="^"+this.weekdays(e,"")+"|^"+this.weekdaysShort(e,"")+"|^"+this.weekdaysMin(e,""),this._weekdaysParse[a]=new RegExp(o.replace(".",""),"i")),n&&"dddd"===t&&this._fullWeekdaysParse[a].test(i))return a;if(n&&"ddd"===t&&this._shortWeekdaysParse[a].test(i))return a;if(n&&"dd"===t&&this._minWeekdaysParse[a].test(i))return a;if(!n&&this._weekdaysParse[a].test(i))return a}},Vn.weekdaysRegex=function(i){return this._weekdaysParseExact?(X(this,"_weekdaysRegex")||Ot.call(this),i?this._weekdaysStrictRegex:this._weekdaysRegex):(X(this,"_weekdaysRegex")||(this._weekdaysRegex=Tt),this._weekdaysStrictRegex&&i?this._weekdaysStrictRegex:this._weekdaysRegex)},Vn.weekdaysShortRegex=function(i){return this._weekdaysParseExact?(X(this,"_weekdaysRegex")||Ot.call(this),i?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(X(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=Nt),this._weekdaysShortStrictRegex&&i?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)},Vn.weekdaysMinRegex=function(i){return this._weekdaysParseExact?(X(this,"_weekdaysRegex")||Ot.call(this),i?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(X(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=Ht),this._weekdaysMinStrictRegex&&i?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)},Vn.isPM=function(i){return"p"===(i+"").toLowerCase().charAt(0)},Vn.meridiem=function(i,t,n){return i>11?n?"pm":"PM":n?"am":"AM"},Pt("en",{dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(i){var t=i%10;return i+(1===pi(i%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")}}),I.lang=di("moment.lang is deprecated. Use moment.locale instead.",Pt),I.langData=di("moment.langData is deprecated. Use moment.localeData instead.",Ut);var na=Math.abs;function aa(i,t,n,a){var e=Sn(t,n);return i._milliseconds+=a*e._milliseconds,i._days+=a*e._days,i._months+=a*e._months,i._bubble()}function ea(i){return i<0?Math.floor(i):Math.ceil(i)}function oa(i){return 4800*i/146097}function sa(i){return 146097*i/4800}function ra(i){return function(){return this.as(i)}}var pa=ra("ms"),ca=ra("s"),la=ra("m"),da=ra("h"),ua=ra("d"),ma=ra("w"),fa=ra("M"),ha=ra("Q"),va=ra("y");function xa(i){return function(){return this.isValid()?this._data[i]:NaN}}var ga=xa("milliseconds"),ba=xa("seconds"),wa=xa("minutes"),ya=xa("hours"),ka=xa("days"),Ma=xa("months"),Ya=xa("years"),Da=Math.round,ja={ss:44,s:45,m:45,h:22,d:26,M:11};function Sa(i,t,n,a,e){return e.relativeTime(t||1,!!n,i,a)}var _a=Math.abs;function za(i){return(i>0)-(i<0)||+i}function Ta(){if(!this.isValid())return this.localeData().invalidDate();var i,t,n=_a(this._milliseconds)/1e3,a=_a(this._days),e=_a(this._months);i=ri(n/60),t=ri(i/60),n%=60,i%=60;var o=ri(e/12),s=e%=12,r=a,p=t,c=i,l=n?n.toFixed(3).replace(/\.?0+$/,""):"",d=this.asSeconds();if(!d)return"P0D";var u=d<0?"-":"",m=za(this._months)!==za(d)?"-":"",f=za(this._days)!==za(d)?"-":"",h=za(this._milliseconds)!==za(d)?"-":"";return u+"P"+(o?m+o+"Y":"")+(s?m+s+"M":"")+(r?f+r+"D":"")+(p||c||l?"T":"")+(p?h+p+"H":"")+(c?h+c+"M":"")+(l?h+l+"S":"")}var Na=vn.prototype;Na.isValid=function(){return this._isValid},Na.abs=function(){var i=this._data;return this._milliseconds=na(this._milliseconds),this._days=na(this._days),this._months=na(this._months),i.milliseconds=na(i.milliseconds),i.seconds=na(i.seconds),i.minutes=na(i.minutes),i.hours=na(i.hours),i.months=na(i.months),i.years=na(i.years),this},Na.add=function(i,t){return aa(this,i,t,1)},Na.subtract=function(i,t){return aa(this,i,t,-1)},Na.as=function(i){if(!this.isValid())return NaN;var t,n,a=this._milliseconds;if("month"===(i=wi(i))||"quarter"===i||"year"===i)switch(n=this._months+oa(t=this._days+a/864e5),i){case"month":return n;case"quarter":return n/3;case"year":return n/12}else switch(t=this._days+Math.round(sa(this._months)),i){case"week":return t/7+a/6048e5;case"day":return t+a/864e5;case"hour":return 24*t+a/36e5;case"minute":return 1440*t+a/6e4;case"second":return 86400*t+a/1e3;case"millisecond":return Math.floor(864e5*t)+a;default:throw new Error("Unknown unit "+i)}},Na.asMilliseconds=pa,Na.asSeconds=ca,Na.asMinutes=la,Na.asHours=da,Na.asDays=ua,Na.asWeeks=ma,Na.asMonths=fa,Na.asQuarters=ha,Na.asYears=va,Na.valueOf=function(){return this.isValid()?this._milliseconds+864e5*this._days+this._months%12*2592e6+31536e6*pi(this._months/12):NaN},Na._bubble=function(){var i,t,n,a,e,o=this._milliseconds,s=this._days,r=this._months,p=this._data;return o>=0&&s>=0&&r>=0||o<=0&&s<=0&&r<=0||(o+=864e5*ea(sa(r)+s),s=0,r=0),p.milliseconds=o%1e3,i=ri(o/1e3),p.seconds=i%60,t=ri(i/60),p.minutes=t%60,n=ri(t/60),p.hours=n%24,s+=ri(n/24),r+=e=ri(oa(s)),s-=ea(sa(e)),a=ri(r/12),r%=12,p.days=s,p.months=r,p.years=a,this},Na.clone=function(){return Sn(this)},Na.get=function(i){return i=wi(i),this.isValid()?this[i+"s"]():NaN},Na.milliseconds=ga,Na.seconds=ba,Na.minutes=wa,Na.hours=ya,Na.days=ka,Na.weeks=function(){return ri(this.days()/7)},Na.months=Ma,Na.years=Ya,Na.humanize=function(i){if(!this.isValid())return this.localeData().invalidDate();var t=this.localeData(),n=function(i,t,n){var a=Sn(i).abs(),e=Da(a.as("s")),o=Da(a.as("m")),s=Da(a.as("h")),r=Da(a.as("d")),p=Da(a.as("M")),c=Da(a.as("y")),l=e<=ja.ss&&["s",e]||e<ja.s&&["ss",e]||o<=1&&["m"]||o<ja.m&&["mm",o]||s<=1&&["h"]||s<ja.h&&["hh",s]||r<=1&&["d"]||r<ja.d&&["dd",r]||p<=1&&["M"]||p<ja.M&&["MM",p]||c<=1&&["y"]||["yy",c];return l[2]=t,l[3]=+i>0,l[4]=n,Sa.apply(null,l)}(this,!i,t);return i&&(n=t.pastFuture(+this,n)),t.postformat(n)},Na.toISOString=Ta,Na.toString=Ta,Na.toJSON=Ta,Na.locale=Ln,Na.localeData=Gn,Na.toIsoString=di("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",Ta),Na.lang=qn,zi("X",0,0,"unix"),zi("x",0,0,"valueOf"),Ji("x",Pi),Ji("X",/[+-]?\d+(\.\d{1,3})?/),Ki("X",(function(i,t,n){n._d=new Date(1e3*parseFloat(i,10))})),Ki("x",(function(i,t,n){n._d=new Date(pi(i))})),I.version="2.24.0",R=dn,I.fn=Xn,I.min=function(){var i=[].slice.call(arguments,0);return fn("isBefore",i)},I.max=function(){var i=[].slice.call(arguments,0);return fn("isAfter",i)},I.now=function(){return Date.now?Date.now():+new Date},I.utc=V,I.unix=function(i){return dn(1e3*i)},I.months=function(i,t){return ia(i,t,"months")},I.isDate=$,I.locale=Pt,I.invalid=ti,I.duration=Sn,I.isMoment=si,I.weekdays=function(i,t,n){return ta(i,t,n,"weekdays")},I.parseZone=function(){return dn.apply(null,arguments).parseZone()},I.localeData=Ut,I.isDuration=xn,I.monthsShort=function(i,t){return ia(i,t,"monthsShort")},I.weekdaysMin=function(i,t,n){return ta(i,t,n,"weekdaysMin")},I.defineLocale=Zt,I.updateLocale=function(i,t){if(null!=t){var n,a,e=At;null!=(a=It(i))&&(e=a._config),(n=new xi(t=vi(e,t))).parentLocale=Rt[i],Rt[i]=n,Pt(i)}else null!=Rt[i]&&(null!=Rt[i].parentLocale?Rt[i]=Rt[i].parentLocale:null!=Rt[i]&&delete Rt[i]);return Rt[i]},I.locales=function(){return ui(Rt)},I.weekdaysShort=function(i,t,n){return ta(i,t,n,"weekdaysShort")},I.normalizeUnits=wi,I.relativeTimeRounding=function(i){return void 0===i?Da:"function"==typeof i&&(Da=i,!0)},I.relativeTimeThreshold=function(i,t){return void 0!==ja[i]&&(void 0===t?ja[i]:(ja[i]=t,"s"===i&&(ja.ss=t-1),!0))},I.calendarFormat=function(i,t){var n=i.diff(t,"days",!0);return n<-6?"sameElse":n<-1?"lastWeek":n<0?"lastDay":n<1?"sameDay":n<2?"nextDay":n<7?"nextWeek":"sameElse"},I.prototype=Xn,I.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"};const Ha={fr:{send:"Soumission",redraft:"Rediscussion",new:"Nouvelle demande",refused:"Refusée",cancelled:"Annulation",validated:"Validation du devis",draft:""},de:{send:"Empfangen",redraft:"Neu diskutiert",new:"Neue Anfrage",refused:"Abgelehnt",cancelled:"Storniert",validated:"Validiert",draft:"In Bearbeitung"},en:{send:"Send",redraft:"Redraft",new:"New",refused:"Refused",cancelled:"Cancelled",validated:"Validated",draft:"Draft"},nl:{send:"Verzenden",redraft:"Herschrijven",new:"nieuw",refused:"Geweigerd",cancelled:"Geannuleerd",validated:"Gevalideerd",draft:"Concept"}};function Oa(){this._types=Object.create(null),this._extensions=Object.create(null);for(var i=0;i<arguments.length;i++)this.define(arguments[i]);this.define=this.define.bind(this),this.getType=this.getType.bind(this),this.getExtension=this.getExtension.bind(this)}I.defineLocale("fr",{months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.".split("_"),monthsParseExact:!0,weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Aujourd’hui à] LT",nextDay:"[Demain à] LT",nextWeek:"dddd [à] LT",lastDay:"[Hier à] LT",lastWeek:"dddd [dernier à] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",ss:"%d secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},dayOfMonthOrdinalParse:/\d{1,2}(er|)/,ordinal:function(i,t){switch(t){case"D":return i+(1===i?"er":"");default:case"M":case"Q":case"DDD":case"d":return i+(1===i?"er":"e");case"w":case"W":return i+(1===i?"re":"e")}},week:{dow:1,doy:4}}),Oa.prototype.define=function(i,t){for(var n in i){var a=i[n].map((function(i){return i.toLowerCase()}));n=n.toLowerCase();for(var e=0;e<a.length;e++){var o;if("*"!=(o=a[e])[0]){if(!t&&o in this._types)throw new Error('Attempt to change mapping for "'+o+'" extension from "'+this._types[o]+'" to "'+n+'". Pass `force=true` to allow this, otherwise remove "'+o+'" from the list of extensions for "'+n+'".');this._types[o]=n}}!t&&this._extensions[n]||(this._extensions[n]="*"!=(o=a[0])[0]?o:o.substr(1))}},Oa.prototype.getType=function(i){var t=(i=String(i)).replace(/^.*[/\\]/,"").toLowerCase(),n=t.replace(/^.*\./,"").toLowerCase();return(n.length<t.length-1||!(t.length<i.length))&&this._types[n]||null},Oa.prototype.getExtension=function(i){return(i=/^\s*([^;\s]*)/.test(i)&&RegExp.$1)&&this._extensions[i.toLowerCase()]||null};var Ea=new Oa({"application/andrew-inset":["ez"],"application/applixware":["aw"],"application/atom+xml":["atom"],"application/atomcat+xml":["atomcat"],"application/atomsvc+xml":["atomsvc"],"application/bdoc":["bdoc"],"application/ccxml+xml":["ccxml"],"application/cdmi-capability":["cdmia"],"application/cdmi-container":["cdmic"],"application/cdmi-domain":["cdmid"],"application/cdmi-object":["cdmio"],"application/cdmi-queue":["cdmiq"],"application/cu-seeme":["cu"],"application/dash+xml":["mpd"],"application/davmount+xml":["davmount"],"application/docbook+xml":["dbk"],"application/dssc+der":["dssc"],"application/dssc+xml":["xdssc"],"application/ecmascript":["ecma","es"],"application/emma+xml":["emma"],"application/epub+zip":["epub"],"application/exi":["exi"],"application/font-tdpfr":["pfr"],"application/geo+json":["geojson"],"application/gml+xml":["gml"],"application/gpx+xml":["gpx"],"application/gxf":["gxf"],"application/gzip":["gz"],"application/hjson":["hjson"],"application/hyperstudio":["stk"],"application/inkml+xml":["ink","inkml"],"application/ipfix":["ipfix"],"application/java-archive":["jar","war","ear"],"application/java-serialized-object":["ser"],"application/java-vm":["class"],"application/javascript":["js","mjs"],"application/json":["json","map"],"application/json5":["json5"],"application/jsonml+json":["jsonml"],"application/ld+json":["jsonld"],"application/lost+xml":["lostxml"],"application/mac-binhex40":["hqx"],"application/mac-compactpro":["cpt"],"application/mads+xml":["mads"],"application/manifest+json":["webmanifest"],"application/marc":["mrc"],"application/marcxml+xml":["mrcx"],"application/mathematica":["ma","nb","mb"],"application/mathml+xml":["mathml"],"application/mbox":["mbox"],"application/mediaservercontrol+xml":["mscml"],"application/metalink+xml":["metalink"],"application/metalink4+xml":["meta4"],"application/mets+xml":["mets"],"application/mods+xml":["mods"],"application/mp21":["m21","mp21"],"application/mp4":["mp4s","m4p"],"application/msword":["doc","dot"],"application/mxf":["mxf"],"application/n-quads":["nq"],"application/n-triples":["nt"],"application/octet-stream":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"],"application/oda":["oda"],"application/oebps-package+xml":["opf"],"application/ogg":["ogx"],"application/omdoc+xml":["omdoc"],"application/onenote":["onetoc","onetoc2","onetmp","onepkg"],"application/oxps":["oxps"],"application/patch-ops-error+xml":["xer"],"application/pdf":["pdf"],"application/pgp-encrypted":["pgp"],"application/pgp-signature":["asc","sig"],"application/pics-rules":["prf"],"application/pkcs10":["p10"],"application/pkcs7-mime":["p7m","p7c"],"application/pkcs7-signature":["p7s"],"application/pkcs8":["p8"],"application/pkix-attr-cert":["ac"],"application/pkix-cert":["cer"],"application/pkix-crl":["crl"],"application/pkix-pkipath":["pkipath"],"application/pkixcmp":["pki"],"application/pls+xml":["pls"],"application/postscript":["ai","eps","ps"],"application/pskc+xml":["pskcxml"],"application/raml+yaml":["raml"],"application/rdf+xml":["rdf","owl"],"application/reginfo+xml":["rif"],"application/relax-ng-compact-syntax":["rnc"],"application/resource-lists+xml":["rl"],"application/resource-lists-diff+xml":["rld"],"application/rls-services+xml":["rs"],"application/rpki-ghostbusters":["gbr"],"application/rpki-manifest":["mft"],"application/rpki-roa":["roa"],"application/rsd+xml":["rsd"],"application/rss+xml":["rss"],"application/rtf":["rtf"],"application/sbml+xml":["sbml"],"application/scvp-cv-request":["scq"],"application/scvp-cv-response":["scs"],"application/scvp-vp-request":["spq"],"application/scvp-vp-response":["spp"],"application/sdp":["sdp"],"application/set-payment-initiation":["setpay"],"application/set-registration-initiation":["setreg"],"application/shf+xml":["shf"],"application/sieve":["siv","sieve"],"application/smil+xml":["smi","smil"],"application/sparql-query":["rq"],"application/sparql-results+xml":["srx"],"application/srgs":["gram"],"application/srgs+xml":["grxml"],"application/sru+xml":["sru"],"application/ssdl+xml":["ssdl"],"application/ssml+xml":["ssml"],"application/tei+xml":["tei","teicorpus"],"application/thraud+xml":["tfi"],"application/timestamped-data":["tsd"],"application/voicexml+xml":["vxml"],"application/wasm":["wasm"],"application/widget":["wgt"],"application/winhlp":["hlp"],"application/wsdl+xml":["wsdl"],"application/wspolicy+xml":["wspolicy"],"application/xaml+xml":["xaml"],"application/xcap-diff+xml":["xdf"],"application/xenc+xml":["xenc"],"application/xhtml+xml":["xhtml","xht"],"application/xml":["xml","xsl","xsd","rng"],"application/xml-dtd":["dtd"],"application/xop+xml":["xop"],"application/xproc+xml":["xpl"],"application/xslt+xml":["xslt"],"application/xspf+xml":["xspf"],"application/xv+xml":["mxml","xhvml","xvml","xvm"],"application/yang":["yang"],"application/yin+xml":["yin"],"application/zip":["zip"],"audio/3gpp":["*3gpp"],"audio/adpcm":["adp"],"audio/basic":["au","snd"],"audio/midi":["mid","midi","kar","rmi"],"audio/mp3":["*mp3"],"audio/mp4":["m4a","mp4a"],"audio/mpeg":["mpga","mp2","mp2a","mp3","m2a","m3a"],"audio/ogg":["oga","ogg","spx"],"audio/s3m":["s3m"],"audio/silk":["sil"],"audio/wav":["wav"],"audio/wave":["*wav"],"audio/webm":["weba"],"audio/xm":["xm"],"font/collection":["ttc"],"font/otf":["otf"],"font/ttf":["ttf"],"font/woff":["woff"],"font/woff2":["woff2"],"image/aces":["exr"],"image/apng":["apng"],"image/bmp":["bmp"],"image/cgm":["cgm"],"image/dicom-rle":["drle"],"image/emf":["emf"],"image/fits":["fits"],"image/g3fax":["g3"],"image/gif":["gif"],"image/heic":["heic"],"image/heic-sequence":["heics"],"image/heif":["heif"],"image/heif-sequence":["heifs"],"image/ief":["ief"],"image/jls":["jls"],"image/jp2":["jp2","jpg2"],"image/jpeg":["jpeg","jpg","jpe"],"image/jpm":["jpm"],"image/jpx":["jpx","jpf"],"image/jxr":["jxr"],"image/ktx":["ktx"],"image/png":["png"],"image/sgi":["sgi"],"image/svg+xml":["svg","svgz"],"image/t38":["t38"],"image/tiff":["tif","tiff"],"image/tiff-fx":["tfx"],"image/webp":["webp"],"image/wmf":["wmf"],"message/disposition-notification":["disposition-notification"],"message/global":["u8msg"],"message/global-delivery-status":["u8dsn"],"message/global-disposition-notification":["u8mdn"],"message/global-headers":["u8hdr"],"message/rfc822":["eml","mime"],"model/3mf":["3mf"],"model/gltf+json":["gltf"],"model/gltf-binary":["glb"],"model/iges":["igs","iges"],"model/mesh":["msh","mesh","silo"],"model/stl":["stl"],"model/vrml":["wrl","vrml"],"model/x3d+binary":["*x3db","x3dbz"],"model/x3d+fastinfoset":["x3db"],"model/x3d+vrml":["*x3dv","x3dvz"],"model/x3d+xml":["x3d","x3dz"],"model/x3d-vrml":["x3dv"],"text/cache-manifest":["appcache","manifest"],"text/calendar":["ics","ifb"],"text/coffeescript":["coffee","litcoffee"],"text/css":["css"],"text/csv":["csv"],"text/html":["html","htm","shtml"],"text/jade":["jade"],"text/jsx":["jsx"],"text/less":["less"],"text/markdown":["markdown","md"],"text/mathml":["mml"],"text/mdx":["mdx"],"text/n3":["n3"],"text/plain":["txt","text","conf","def","list","log","in","ini"],"text/richtext":["rtx"],"text/rtf":["*rtf"],"text/sgml":["sgml","sgm"],"text/shex":["shex"],"text/slim":["slim","slm"],"text/stylus":["stylus","styl"],"text/tab-separated-values":["tsv"],"text/troff":["t","tr","roff","man","me","ms"],"text/turtle":["ttl"],"text/uri-list":["uri","uris","urls"],"text/vcard":["vcard"],"text/vtt":["vtt"],"text/xml":["*xml"],"text/yaml":["yaml","yml"],"video/3gpp":["3gp","3gpp"],"video/3gpp2":["3g2"],"video/h261":["h261"],"video/h263":["h263"],"video/h264":["h264"],"video/jpeg":["jpgv"],"video/jpm":["*jpm","jpgm"],"video/mj2":["mj2","mjp2"],"video/mp2t":["ts"],"video/mp4":["mp4","mp4v","mpg4"],"video/mpeg":["mpeg","mpg","mpe","m1v","m2v"],"video/ogg":["ogv"],"video/quicktime":["qt","mov"],"video/webm":["webm"]},{"application/prs.cww":["cww"],"application/vnd.3gpp.pic-bw-large":["plb"],"application/vnd.3gpp.pic-bw-small":["psb"],"application/vnd.3gpp.pic-bw-var":["pvb"],"application/vnd.3gpp2.tcap":["tcap"],"application/vnd.3m.post-it-notes":["pwn"],"application/vnd.accpac.simply.aso":["aso"],"application/vnd.accpac.simply.imp":["imp"],"application/vnd.acucobol":["acu"],"application/vnd.acucorp":["atc","acutc"],"application/vnd.adobe.air-application-installer-package+zip":["air"],"application/vnd.adobe.formscentral.fcdt":["fcdt"],"application/vnd.adobe.fxp":["fxp","fxpl"],"application/vnd.adobe.xdp+xml":["xdp"],"application/vnd.adobe.xfdf":["xfdf"],"application/vnd.ahead.space":["ahead"],"application/vnd.airzip.filesecure.azf":["azf"],"application/vnd.airzip.filesecure.azs":["azs"],"application/vnd.amazon.ebook":["azw"],"application/vnd.americandynamics.acc":["acc"],"application/vnd.amiga.ami":["ami"],"application/vnd.android.package-archive":["apk"],"application/vnd.anser-web-certificate-issue-initiation":["cii"],"application/vnd.anser-web-funds-transfer-initiation":["fti"],"application/vnd.antix.game-component":["atx"],"application/vnd.apple.installer+xml":["mpkg"],"application/vnd.apple.keynote":["keynote"],"application/vnd.apple.mpegurl":["m3u8"],"application/vnd.apple.numbers":["numbers"],"application/vnd.apple.pages":["pages"],"application/vnd.apple.pkpass":["pkpass"],"application/vnd.aristanetworks.swi":["swi"],"application/vnd.astraea-software.iota":["iota"],"application/vnd.audiograph":["aep"],"application/vnd.blueice.multipass":["mpm"],"application/vnd.bmi":["bmi"],"application/vnd.businessobjects":["rep"],"application/vnd.chemdraw+xml":["cdxml"],"application/vnd.chipnuts.karaoke-mmd":["mmd"],"application/vnd.cinderella":["cdy"],"application/vnd.citationstyles.style+xml":["csl"],"application/vnd.claymore":["cla"],"application/vnd.cloanto.rp9":["rp9"],"application/vnd.clonk.c4group":["c4g","c4d","c4f","c4p","c4u"],"application/vnd.cluetrust.cartomobile-config":["c11amc"],"application/vnd.cluetrust.cartomobile-config-pkg":["c11amz"],"application/vnd.commonspace":["csp"],"application/vnd.contact.cmsg":["cdbcmsg"],"application/vnd.cosmocaller":["cmc"],"application/vnd.crick.clicker":["clkx"],"application/vnd.crick.clicker.keyboard":["clkk"],"application/vnd.crick.clicker.palette":["clkp"],"application/vnd.crick.clicker.template":["clkt"],"application/vnd.crick.clicker.wordbank":["clkw"],"application/vnd.criticaltools.wbs+xml":["wbs"],"application/vnd.ctc-posml":["pml"],"application/vnd.cups-ppd":["ppd"],"application/vnd.curl.car":["car"],"application/vnd.curl.pcurl":["pcurl"],"application/vnd.dart":["dart"],"application/vnd.data-vision.rdz":["rdz"],"application/vnd.dece.data":["uvf","uvvf","uvd","uvvd"],"application/vnd.dece.ttml+xml":["uvt","uvvt"],"application/vnd.dece.unspecified":["uvx","uvvx"],"application/vnd.dece.zip":["uvz","uvvz"],"application/vnd.denovo.fcselayout-link":["fe_launch"],"application/vnd.dna":["dna"],"application/vnd.dolby.mlp":["mlp"],"application/vnd.dpgraph":["dpg"],"application/vnd.dreamfactory":["dfac"],"application/vnd.ds-keypoint":["kpxx"],"application/vnd.dvb.ait":["ait"],"application/vnd.dvb.service":["svc"],"application/vnd.dynageo":["geo"],"application/vnd.ecowin.chart":["mag"],"application/vnd.enliven":["nml"],"application/vnd.epson.esf":["esf"],"application/vnd.epson.msf":["msf"],"application/vnd.epson.quickanime":["qam"],"application/vnd.epson.salt":["slt"],"application/vnd.epson.ssf":["ssf"],"application/vnd.eszigno3+xml":["es3","et3"],"application/vnd.ezpix-album":["ez2"],"application/vnd.ezpix-package":["ez3"],"application/vnd.fdf":["fdf"],"application/vnd.fdsn.mseed":["mseed"],"application/vnd.fdsn.seed":["seed","dataless"],"application/vnd.flographit":["gph"],"application/vnd.fluxtime.clip":["ftc"],"application/vnd.framemaker":["fm","frame","maker","book"],"application/vnd.frogans.fnc":["fnc"],"application/vnd.frogans.ltf":["ltf"],"application/vnd.fsc.weblaunch":["fsc"],"application/vnd.fujitsu.oasys":["oas"],"application/vnd.fujitsu.oasys2":["oa2"],"application/vnd.fujitsu.oasys3":["oa3"],"application/vnd.fujitsu.oasysgp":["fg5"],"application/vnd.fujitsu.oasysprs":["bh2"],"application/vnd.fujixerox.ddd":["ddd"],"application/vnd.fujixerox.docuworks":["xdw"],"application/vnd.fujixerox.docuworks.binder":["xbd"],"application/vnd.fuzzysheet":["fzs"],"application/vnd.genomatix.tuxedo":["txd"],"application/vnd.geogebra.file":["ggb"],"application/vnd.geogebra.tool":["ggt"],"application/vnd.geometry-explorer":["gex","gre"],"application/vnd.geonext":["gxt"],"application/vnd.geoplan":["g2w"],"application/vnd.geospace":["g3w"],"application/vnd.gmx":["gmx"],"application/vnd.google-apps.document":["gdoc"],"application/vnd.google-apps.presentation":["gslides"],"application/vnd.google-apps.spreadsheet":["gsheet"],"application/vnd.google-earth.kml+xml":["kml"],"application/vnd.google-earth.kmz":["kmz"],"application/vnd.grafeq":["gqf","gqs"],"application/vnd.groove-account":["gac"],"application/vnd.groove-help":["ghf"],"application/vnd.groove-identity-message":["gim"],"application/vnd.groove-injector":["grv"],"application/vnd.groove-tool-message":["gtm"],"application/vnd.groove-tool-template":["tpl"],"application/vnd.groove-vcard":["vcg"],"application/vnd.hal+xml":["hal"],"application/vnd.handheld-entertainment+xml":["zmm"],"application/vnd.hbci":["hbci"],"application/vnd.hhe.lesson-player":["les"],"application/vnd.hp-hpgl":["hpgl"],"application/vnd.hp-hpid":["hpid"],"application/vnd.hp-hps":["hps"],"application/vnd.hp-jlyt":["jlt"],"application/vnd.hp-pcl":["pcl"],"application/vnd.hp-pclxl":["pclxl"],"application/vnd.hydrostatix.sof-data":["sfd-hdstx"],"application/vnd.ibm.minipay":["mpy"],"application/vnd.ibm.modcap":["afp","listafp","list3820"],"application/vnd.ibm.rights-management":["irm"],"application/vnd.ibm.secure-container":["sc"],"application/vnd.iccprofile":["icc","icm"],"application/vnd.igloader":["igl"],"application/vnd.immervision-ivp":["ivp"],"application/vnd.immervision-ivu":["ivu"],"application/vnd.insors.igm":["igm"],"application/vnd.intercon.formnet":["xpw","xpx"],"application/vnd.intergeo":["i2g"],"application/vnd.intu.qbo":["qbo"],"application/vnd.intu.qfx":["qfx"],"application/vnd.ipunplugged.rcprofile":["rcprofile"],"application/vnd.irepository.package+xml":["irp"],"application/vnd.is-xpr":["xpr"],"application/vnd.isac.fcs":["fcs"],"application/vnd.jam":["jam"],"application/vnd.jcp.javame.midlet-rms":["rms"],"application/vnd.jisp":["jisp"],"application/vnd.joost.joda-archive":["joda"],"application/vnd.kahootz":["ktz","ktr"],"application/vnd.kde.karbon":["karbon"],"application/vnd.kde.kchart":["chrt"],"application/vnd.kde.kformula":["kfo"],"application/vnd.kde.kivio":["flw"],"application/vnd.kde.kontour":["kon"],"application/vnd.kde.kpresenter":["kpr","kpt"],"application/vnd.kde.kspread":["ksp"],"application/vnd.kde.kword":["kwd","kwt"],"application/vnd.kenameaapp":["htke"],"application/vnd.kidspiration":["kia"],"application/vnd.kinar":["kne","knp"],"application/vnd.koan":["skp","skd","skt","skm"],"application/vnd.kodak-descriptor":["sse"],"application/vnd.las.las+xml":["lasxml"],"application/vnd.llamagraphics.life-balance.desktop":["lbd"],"application/vnd.llamagraphics.life-balance.exchange+xml":["lbe"],"application/vnd.lotus-1-2-3":["123"],"application/vnd.lotus-approach":["apr"],"application/vnd.lotus-freelance":["pre"],"application/vnd.lotus-notes":["nsf"],"application/vnd.lotus-organizer":["org"],"application/vnd.lotus-screencam":["scm"],"application/vnd.lotus-wordpro":["lwp"],"application/vnd.macports.portpkg":["portpkg"],"application/vnd.mcd":["mcd"],"application/vnd.medcalcdata":["mc1"],"application/vnd.mediastation.cdkey":["cdkey"],"application/vnd.mfer":["mwf"],"application/vnd.mfmp":["mfm"],"application/vnd.micrografx.flo":["flo"],"application/vnd.micrografx.igx":["igx"],"application/vnd.mif":["mif"],"application/vnd.mobius.daf":["daf"],"application/vnd.mobius.dis":["dis"],"application/vnd.mobius.mbk":["mbk"],"application/vnd.mobius.mqy":["mqy"],"application/vnd.mobius.msl":["msl"],"application/vnd.mobius.plc":["plc"],"application/vnd.mobius.txf":["txf"],"application/vnd.mophun.application":["mpn"],"application/vnd.mophun.certificate":["mpc"],"application/vnd.mozilla.xul+xml":["xul"],"application/vnd.ms-artgalry":["cil"],"application/vnd.ms-cab-compressed":["cab"],"application/vnd.ms-excel":["xls","xlm","xla","xlc","xlt","xlw"],"application/vnd.ms-excel.addin.macroenabled.12":["xlam"],"application/vnd.ms-excel.sheet.binary.macroenabled.12":["xlsb"],"application/vnd.ms-excel.sheet.macroenabled.12":["xlsm"],"application/vnd.ms-excel.template.macroenabled.12":["xltm"],"application/vnd.ms-fontobject":["eot"],"application/vnd.ms-htmlhelp":["chm"],"application/vnd.ms-ims":["ims"],"application/vnd.ms-lrm":["lrm"],"application/vnd.ms-officetheme":["thmx"],"application/vnd.ms-outlook":["msg"],"application/vnd.ms-pki.seccat":["cat"],"application/vnd.ms-pki.stl":["*stl"],"application/vnd.ms-powerpoint":["ppt","pps","pot"],"application/vnd.ms-powerpoint.addin.macroenabled.12":["ppam"],"application/vnd.ms-powerpoint.presentation.macroenabled.12":["pptm"],"application/vnd.ms-powerpoint.slide.macroenabled.12":["sldm"],"application/vnd.ms-powerpoint.slideshow.macroenabled.12":["ppsm"],"application/vnd.ms-powerpoint.template.macroenabled.12":["potm"],"application/vnd.ms-project":["mpp","mpt"],"application/vnd.ms-word.document.macroenabled.12":["docm"],"application/vnd.ms-word.template.macroenabled.12":["dotm"],"application/vnd.ms-works":["wps","wks","wcm","wdb"],"application/vnd.ms-wpl":["wpl"],"application/vnd.ms-xpsdocument":["xps"],"application/vnd.mseq":["mseq"],"application/vnd.musician":["mus"],"application/vnd.muvee.style":["msty"],"application/vnd.mynfc":["taglet"],"application/vnd.neurolanguage.nlu":["nlu"],"application/vnd.nitf":["ntf","nitf"],"application/vnd.noblenet-directory":["nnd"],"application/vnd.noblenet-sealer":["nns"],"application/vnd.noblenet-web":["nnw"],"application/vnd.nokia.n-gage.data":["ngdat"],"application/vnd.nokia.n-gage.symbian.install":["n-gage"],"application/vnd.nokia.radio-preset":["rpst"],"application/vnd.nokia.radio-presets":["rpss"],"application/vnd.novadigm.edm":["edm"],"application/vnd.novadigm.edx":["edx"],"application/vnd.novadigm.ext":["ext"],"application/vnd.oasis.opendocument.chart":["odc"],"application/vnd.oasis.opendocument.chart-template":["otc"],"application/vnd.oasis.opendocument.database":["odb"],"application/vnd.oasis.opendocument.formula":["odf"],"application/vnd.oasis.opendocument.formula-template":["odft"],"application/vnd.oasis.opendocument.graphics":["odg"],"application/vnd.oasis.opendocument.graphics-template":["otg"],"application/vnd.oasis.opendocument.image":["odi"],"application/vnd.oasis.opendocument.image-template":["oti"],"application/vnd.oasis.opendocument.presentation":["odp"],"application/vnd.oasis.opendocument.presentation-template":["otp"],"application/vnd.oasis.opendocument.spreadsheet":["ods"],"application/vnd.oasis.opendocument.spreadsheet-template":["ots"],"application/vnd.oasis.opendocument.text":["odt"],"application/vnd.oasis.opendocument.text-master":["odm"],"application/vnd.oasis.opendocument.text-template":["ott"],"application/vnd.oasis.opendocument.text-web":["oth"],"application/vnd.olpc-sugar":["xo"],"application/vnd.oma.dd2+xml":["dd2"],"application/vnd.openofficeorg.extension":["oxt"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":["pptx"],"application/vnd.openxmlformats-officedocument.presentationml.slide":["sldx"],"application/vnd.openxmlformats-officedocument.presentationml.slideshow":["ppsx"],"application/vnd.openxmlformats-officedocument.presentationml.template":["potx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":["xlsx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.template":["xltx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":["docx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.template":["dotx"],"application/vnd.osgeo.mapguide.package":["mgp"],"application/vnd.osgi.dp":["dp"],"application/vnd.osgi.subsystem":["esa"],"application/vnd.palm":["pdb","pqa","oprc"],"application/vnd.pawaafile":["paw"],"application/vnd.pg.format":["str"],"application/vnd.pg.osasli":["ei6"],"application/vnd.picsel":["efif"],"application/vnd.pmi.widget":["wg"],"application/vnd.pocketlearn":["plf"],"application/vnd.powerbuilder6":["pbd"],"application/vnd.previewsystems.box":["box"],"application/vnd.proteus.magazine":["mgz"],"application/vnd.publishare-delta-tree":["qps"],"application/vnd.pvi.ptid1":["ptid"],"application/vnd.quark.quarkxpress":["qxd","qxt","qwd","qwt","qxl","qxb"],"application/vnd.realvnc.bed":["bed"],"application/vnd.recordare.musicxml":["mxl"],"application/vnd.recordare.musicxml+xml":["musicxml"],"application/vnd.rig.cryptonote":["cryptonote"],"application/vnd.rim.cod":["cod"],"application/vnd.rn-realmedia":["rm"],"application/vnd.rn-realmedia-vbr":["rmvb"],"application/vnd.route66.link66+xml":["link66"],"application/vnd.sailingtracker.track":["st"],"application/vnd.seemail":["see"],"application/vnd.sema":["sema"],"application/vnd.semd":["semd"],"application/vnd.semf":["semf"],"application/vnd.shana.informed.formdata":["ifm"],"application/vnd.shana.informed.formtemplate":["itp"],"application/vnd.shana.informed.interchange":["iif"],"application/vnd.shana.informed.package":["ipk"],"application/vnd.simtech-mindmapper":["twd","twds"],"application/vnd.smaf":["mmf"],"application/vnd.smart.teacher":["teacher"],"application/vnd.solent.sdkm+xml":["sdkm","sdkd"],"application/vnd.spotfire.dxp":["dxp"],"application/vnd.spotfire.sfs":["sfs"],"application/vnd.stardivision.calc":["sdc"],"application/vnd.stardivision.draw":["sda"],"application/vnd.stardivision.impress":["sdd"],"application/vnd.stardivision.math":["smf"],"application/vnd.stardivision.writer":["sdw","vor"],"application/vnd.stardivision.writer-global":["sgl"],"application/vnd.stepmania.package":["smzip"],"application/vnd.stepmania.stepchart":["sm"],"application/vnd.sun.wadl+xml":["wadl"],"application/vnd.sun.xml.calc":["sxc"],"application/vnd.sun.xml.calc.template":["stc"],"application/vnd.sun.xml.draw":["sxd"],"application/vnd.sun.xml.draw.template":["std"],"application/vnd.sun.xml.impress":["sxi"],"application/vnd.sun.xml.impress.template":["sti"],"application/vnd.sun.xml.math":["sxm"],"application/vnd.sun.xml.writer":["sxw"],"application/vnd.sun.xml.writer.global":["sxg"],"application/vnd.sun.xml.writer.template":["stw"],"application/vnd.sus-calendar":["sus","susp"],"application/vnd.svd":["svd"],"application/vnd.symbian.install":["sis","sisx"],"application/vnd.syncml+xml":["xsm"],"application/vnd.syncml.dm+wbxml":["bdm"],"application/vnd.syncml.dm+xml":["xdm"],"application/vnd.tao.intent-module-archive":["tao"],"application/vnd.tcpdump.pcap":["pcap","cap","dmp"],"application/vnd.tmobile-livetv":["tmo"],"application/vnd.trid.tpt":["tpt"],"application/vnd.triscape.mxs":["mxs"],"application/vnd.trueapp":["tra"],"application/vnd.ufdl":["ufd","ufdl"],"application/vnd.uiq.theme":["utz"],"application/vnd.umajin":["umj"],"application/vnd.unity":["unityweb"],"application/vnd.uoml+xml":["uoml"],"application/vnd.vcx":["vcx"],"application/vnd.visio":["vsd","vst","vss","vsw"],"application/vnd.visionary":["vis"],"application/vnd.vsf":["vsf"],"application/vnd.wap.wbxml":["wbxml"],"application/vnd.wap.wmlc":["wmlc"],"application/vnd.wap.wmlscriptc":["wmlsc"],"application/vnd.webturbo":["wtb"],"application/vnd.wolfram.player":["nbp"],"application/vnd.wordperfect":["wpd"],"application/vnd.wqd":["wqd"],"application/vnd.wt.stf":["stf"],"application/vnd.xara":["xar"],"application/vnd.xfdl":["xfdl"],"application/vnd.yamaha.hv-dic":["hvd"],"application/vnd.yamaha.hv-script":["hvs"],"application/vnd.yamaha.hv-voice":["hvp"],"application/vnd.yamaha.openscoreformat":["osf"],"application/vnd.yamaha.openscoreformat.osfpvg+xml":["osfpvg"],"application/vnd.yamaha.smaf-audio":["saf"],"application/vnd.yamaha.smaf-phrase":["spf"],"application/vnd.yellowriver-custom-menu":["cmp"],"application/vnd.zul":["zir","zirz"],"application/vnd.zzazz.deck+xml":["zaz"],"application/x-7z-compressed":["7z"],"application/x-abiword":["abw"],"application/x-ace-compressed":["ace"],"application/x-apple-diskimage":["*dmg"],"application/x-arj":["arj"],"application/x-authorware-bin":["aab","x32","u32","vox"],"application/x-authorware-map":["aam"],"application/x-authorware-seg":["aas"],"application/x-bcpio":["bcpio"],"application/x-bdoc":["*bdoc"],"application/x-bittorrent":["torrent"],"application/x-blorb":["blb","blorb"],"application/x-bzip":["bz"],"application/x-bzip2":["bz2","boz"],"application/x-cbr":["cbr","cba","cbt","cbz","cb7"],"application/x-cdlink":["vcd"],"application/x-cfs-compressed":["cfs"],"application/x-chat":["chat"],"application/x-chess-pgn":["pgn"],"application/x-chrome-extension":["crx"],"application/x-cocoa":["cco"],"application/x-conference":["nsc"],"application/x-cpio":["cpio"],"application/x-csh":["csh"],"application/x-debian-package":["*deb","udeb"],"application/x-dgc-compressed":["dgc"],"application/x-director":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"],"application/x-doom":["wad"],"application/x-dtbncx+xml":["ncx"],"application/x-dtbook+xml":["dtb"],"application/x-dtbresource+xml":["res"],"application/x-dvi":["dvi"],"application/x-envoy":["evy"],"application/x-eva":["eva"],"application/x-font-bdf":["bdf"],"application/x-font-ghostscript":["gsf"],"application/x-font-linux-psf":["psf"],"application/x-font-pcf":["pcf"],"application/x-font-snf":["snf"],"application/x-font-type1":["pfa","pfb","pfm","afm"],"application/x-freearc":["arc"],"application/x-futuresplash":["spl"],"application/x-gca-compressed":["gca"],"application/x-glulx":["ulx"],"application/x-gnumeric":["gnumeric"],"application/x-gramps-xml":["gramps"],"application/x-gtar":["gtar"],"application/x-hdf":["hdf"],"application/x-httpd-php":["php"],"application/x-install-instructions":["install"],"application/x-iso9660-image":["*iso"],"application/x-java-archive-diff":["jardiff"],"application/x-java-jnlp-file":["jnlp"],"application/x-latex":["latex"],"application/x-lua-bytecode":["luac"],"application/x-lzh-compressed":["lzh","lha"],"application/x-makeself":["run"],"application/x-mie":["mie"],"application/x-mobipocket-ebook":["prc","mobi"],"application/x-ms-application":["application"],"application/x-ms-shortcut":["lnk"],"application/x-ms-wmd":["wmd"],"application/x-ms-wmz":["wmz"],"application/x-ms-xbap":["xbap"],"application/x-msaccess":["mdb"],"application/x-msbinder":["obd"],"application/x-mscardfile":["crd"],"application/x-msclip":["clp"],"application/x-msdos-program":["*exe"],"application/x-msdownload":["*exe","*dll","com","bat","*msi"],"application/x-msmediaview":["mvb","m13","m14"],"application/x-msmetafile":["*wmf","*wmz","*emf","emz"],"application/x-msmoney":["mny"],"application/x-mspublisher":["pub"],"application/x-msschedule":["scd"],"application/x-msterminal":["trm"],"application/x-mswrite":["wri"],"application/x-netcdf":["nc","cdf"],"application/x-ns-proxy-autoconfig":["pac"],"application/x-nzb":["nzb"],"application/x-perl":["pl","pm"],"application/x-pilot":["*prc","*pdb"],"application/x-pkcs12":["p12","pfx"],"application/x-pkcs7-certificates":["p7b","spc"],"application/x-pkcs7-certreqresp":["p7r"],"application/x-rar-compressed":["rar"],"application/x-redhat-package-manager":["rpm"],"application/x-research-info-systems":["ris"],"application/x-sea":["sea"],"application/x-sh":["sh"],"application/x-shar":["shar"],"application/x-shockwave-flash":["swf"],"application/x-silverlight-app":["xap"],"application/x-sql":["sql"],"application/x-stuffit":["sit"],"application/x-stuffitx":["sitx"],"application/x-subrip":["srt"],"application/x-sv4cpio":["sv4cpio"],"application/x-sv4crc":["sv4crc"],"application/x-t3vm-image":["t3"],"application/x-tads":["gam"],"application/x-tar":["tar"],"application/x-tcl":["tcl","tk"],"application/x-tex":["tex"],"application/x-tex-tfm":["tfm"],"application/x-texinfo":["texinfo","texi"],"application/x-tgif":["obj"],"application/x-ustar":["ustar"],"application/x-virtualbox-hdd":["hdd"],"application/x-virtualbox-ova":["ova"],"application/x-virtualbox-ovf":["ovf"],"application/x-virtualbox-vbox":["vbox"],"application/x-virtualbox-vbox-extpack":["vbox-extpack"],"application/x-virtualbox-vdi":["vdi"],"application/x-virtualbox-vhd":["vhd"],"application/x-virtualbox-vmdk":["vmdk"],"application/x-wais-source":["src"],"application/x-web-app-manifest+json":["webapp"],"application/x-x509-ca-cert":["der","crt","pem"],"application/x-xfig":["fig"],"application/x-xliff+xml":["xlf"],"application/x-xpinstall":["xpi"],"application/x-xz":["xz"],"application/x-zmachine":["z1","z2","z3","z4","z5","z6","z7","z8"],"audio/vnd.dece.audio":["uva","uvva"],"audio/vnd.digital-winds":["eol"],"audio/vnd.dra":["dra"],"audio/vnd.dts":["dts"],"audio/vnd.dts.hd":["dtshd"],"audio/vnd.lucent.voice":["lvp"],"audio/vnd.ms-playready.media.pya":["pya"],"audio/vnd.nuera.ecelp4800":["ecelp4800"],"audio/vnd.nuera.ecelp7470":["ecelp7470"],"audio/vnd.nuera.ecelp9600":["ecelp9600"],"audio/vnd.rip":["rip"],"audio/x-aac":["aac"],"audio/x-aiff":["aif","aiff","aifc"],"audio/x-caf":["caf"],"audio/x-flac":["flac"],"audio/x-m4a":["*m4a"],"audio/x-matroska":["mka"],"audio/x-mpegurl":["m3u"],"audio/x-ms-wax":["wax"],"audio/x-ms-wma":["wma"],"audio/x-pn-realaudio":["ram","ra"],"audio/x-pn-realaudio-plugin":["rmp"],"audio/x-realaudio":["*ra"],"audio/x-wav":["*wav"],"chemical/x-cdx":["cdx"],"chemical/x-cif":["cif"],"chemical/x-cmdf":["cmdf"],"chemical/x-cml":["cml"],"chemical/x-csml":["csml"],"chemical/x-xyz":["xyz"],"image/prs.btif":["btif"],"image/prs.pti":["pti"],"image/vnd.adobe.photoshop":["psd"],"image/vnd.airzip.accelerator.azv":["azv"],"image/vnd.dece.graphic":["uvi","uvvi","uvg","uvvg"],"image/vnd.djvu":["djvu","djv"],"image/vnd.dvb.subtitle":["*sub"],"image/vnd.dwg":["dwg"],"image/vnd.dxf":["dxf"],"image/vnd.fastbidsheet":["fbs"],"image/vnd.fpx":["fpx"],"image/vnd.fst":["fst"],"image/vnd.fujixerox.edmics-mmr":["mmr"],"image/vnd.fujixerox.edmics-rlc":["rlc"],"image/vnd.microsoft.icon":["ico"],"image/vnd.ms-modi":["mdi"],"image/vnd.ms-photo":["wdp"],"image/vnd.net-fpx":["npx"],"image/vnd.tencent.tap":["tap"],"image/vnd.valve.source.texture":["vtf"],"image/vnd.wap.wbmp":["wbmp"],"image/vnd.xiff":["xif"],"image/vnd.zbrush.pcx":["pcx"],"image/x-3ds":["3ds"],"image/x-cmu-raster":["ras"],"image/x-cmx":["cmx"],"image/x-freehand":["fh","fhc","fh4","fh5","fh7"],"image/x-icon":["*ico"],"image/x-jng":["jng"],"image/x-mrsid-image":["sid"],"image/x-ms-bmp":["*bmp"],"image/x-pcx":["*pcx"],"image/x-pict":["pic","pct"],"image/x-portable-anymap":["pnm"],"image/x-portable-bitmap":["pbm"],"image/x-portable-graymap":["pgm"],"image/x-portable-pixmap":["ppm"],"image/x-rgb":["rgb"],"image/x-tga":["tga"],"image/x-xbitmap":["xbm"],"image/x-xpixmap":["xpm"],"image/x-xwindowdump":["xwd"],"message/vnd.wfa.wsc":["wsc"],"model/vnd.collada+xml":["dae"],"model/vnd.dwf":["dwf"],"model/vnd.gdl":["gdl"],"model/vnd.gtw":["gtw"],"model/vnd.mts":["mts"],"model/vnd.opengex":["ogex"],"model/vnd.parasolid.transmit.binary":["x_b"],"model/vnd.parasolid.transmit.text":["x_t"],"model/vnd.usdz+zip":["usdz"],"model/vnd.valve.source.compiled-map":["bsp"],"model/vnd.vtu":["vtu"],"text/prs.lines.tag":["dsc"],"text/vnd.curl":["curl"],"text/vnd.curl.dcurl":["dcurl"],"text/vnd.curl.mcurl":["mcurl"],"text/vnd.curl.scurl":["scurl"],"text/vnd.dvb.subtitle":["sub"],"text/vnd.fly":["fly"],"text/vnd.fmi.flexstor":["flx"],"text/vnd.graphviz":["gv"],"text/vnd.in3d.3dml":["3dml"],"text/vnd.in3d.spot":["spot"],"text/vnd.sun.j2me.app-descriptor":["jad"],"text/vnd.wap.wml":["wml"],"text/vnd.wap.wmlscript":["wmls"],"text/x-asm":["s","asm"],"text/x-c":["c","cc","cxx","cpp","h","hh","dic"],"text/x-component":["htc"],"text/x-fortran":["f","for","f77","f90"],"text/x-handlebars-template":["hbs"],"text/x-java-source":["java"],"text/x-lua":["lua"],"text/x-markdown":["mkd"],"text/x-nfo":["nfo"],"text/x-opml":["opml"],"text/x-org":["*org"],"text/x-pascal":["p","pas"],"text/x-processing":["pde"],"text/x-sass":["sass"],"text/x-scss":["scss"],"text/x-setext":["etx"],"text/x-sfv":["sfv"],"text/x-suse-ymp":["ymp"],"text/x-uuencode":["uu"],"text/x-vcalendar":["vcs"],"text/x-vcard":["vcf"],"video/vnd.dece.hd":["uvh","uvvh"],"video/vnd.dece.mobile":["uvm","uvvm"],"video/vnd.dece.pd":["uvp","uvvp"],"video/vnd.dece.sd":["uvs","uvvs"],"video/vnd.dece.video":["uvv","uvvv"],"video/vnd.dvb.file":["dvb"],"video/vnd.fvt":["fvt"],"video/vnd.mpegurl":["mxu","m4u"],"video/vnd.ms-playready.media.pyv":["pyv"],"video/vnd.uvvu.mp4":["uvu","uvvu"],"video/vnd.vivo":["viv"],"video/x-f4v":["f4v"],"video/x-fli":["fli"],"video/x-flv":["flv"],"video/x-m4v":["m4v"],"video/x-matroska":["mkv","mk3d","mks"],"video/x-mng":["mng"],"video/x-ms-asf":["asf","asx"],"video/x-ms-vob":["vob"],"video/x-ms-wm":["wm"],"video/x-ms-wmv":["wmv"],"video/x-ms-wmx":["wmx"],"video/x-ms-wvx":["wvx"],"video/x-msvideo":["avi"],"video/x-sgi-movie":["movie"],"video/x-smv":["smv"],"x-conference/x-cooltalk":["ice"]});I.locale("fr");const La=class{constructor(t){i(this,t),this.req=!1,this.refresh_time=1e4,this.language="fr",this.ismobile=!1,this.state={messages:[],message:"",files:null,loader:!1,showError:!1},this.validMime=["application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/msword","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/pdf","image/jpeg","image/gif","image/png","application/zip","application/x-zip-compressed"],this.imgMime=["image/jpeg","image/gif","image/png"]}handleSubmit(){if(""===this.state.message.trim()&&null===this.state.files)return;let i=this.state.message;""===i.trim()&&(i="Envoyé");var t=new FormData;t.append("message",i),this.state.files&&this.state.files.length>0&&t.append("files[]",this.state.files[0],this.state.files[0].name),this.state=Object.assign(Object.assign({},this.state),{loader:!0}),C.post(this.post_message_url,t,{responseType:"json",headers:{"content-type":"multipart/form-data"}}).then(i=>{let t=this.state.messages;t.push(i.data),this.state=Object.assign(Object.assign({},this.state),{messages:[...t],message:"",files:null,loader:!1}),setTimeout(()=>{this.el.shadowRoot.getElementById("message").style.cssText="height:auto; padding:0"},0)}).catch(()=>this.state=Object.assign(Object.assign({},this.state),{loader:!1}))}fileChange(i){let t=!1;1===i.length?(console.log(i.item(0)),i.item(0).size>=5242880||!this.validMime.includes(i.item(0).type)?t=!0:(this.state.files=i,this.handleSubmit())):this.state.files=null,this.state=Object.assign(Object.assign({},this.state),{showError:t})}scrollToBottom(){const i=this.el.shadowRoot.querySelector(".messager_body");i.scrollTop=i.scrollHeight-i.clientHeight}handleChange(i){this.state=Object.assign(Object.assign({},this.state),{message:i.target.value})}componentDidLoad(){this.state=Object.assign(Object.assign({},this.state),{loader:!0}),C.get(this.get_messages_url).then(i=>{this.state=Object.assign(Object.assign({},this.state),{messages:i.data,loader:!1}),this.refresh_time>0&&(this.timer=window.setInterval(()=>{this.req||(this.req=!0,C.get(this.refresh_message_url).then(i=>{let t=this.state.messages;i.data&&i.data.length>0&&(t=[...t,...i.data],this.state=Object.assign(Object.assign({},this.state),{messages:t})),this.req=!1}).catch(()=>this.req=!1))},this.refresh_time))}).catch(()=>this.state=Object.assign(Object.assign({},this.state),{loader:!1}))}componentDidUnload(){window.clearInterval(this.timer)}componentDidUpdate(){this.scrollToBottom()}autosize(i){if(13==i.keyCode&&!i.shiftKey)return i.preventDefault(),this.handleSubmit(),!1;var t=i.target;setTimeout((function(){t.style.cssText="height:auto; padding:0",t.style.cssText="-moz-box-sizing:content-box",t.style.cssText="height:"+t.scrollHeight+"px"}),0)}messagesList(){return t("div",{class:"messager_body"},this.state.messages.map(i=>t("div",{class:"messager_body_container",key:i.id},i.body&&t("div",{class:i.sender_id!==this.user_id?"messager_body_message messager_body_message_recipient":"messager_body_message messager_body_message_sender"},t("div",{class:"messager_body_message_date"},i&&i.created_at?I(i.created_at).format("LLL"):""),this.eventMessage(i),t("div",{class:"messager_body_message_card"},t("div",{class:"messager_body_message_card_user"},i.sender),t("div",{class:"messager_body_message_card_body"},t("div",{innerHTML:i.body}),i.files.length>0&&this.filesMessageDetail(i.files))),i.files.length>0&&this.filesMessage(i.files)))))}filesMessage(i){return t("div",null,i.map(i=>t("div",null,t("a",{class:"file-link",href:this.file_url+i.id,target:"_blank"},t("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 15 17",height:"16"},t("path",{fill:"#134391","fill-rule":"evenodd",d:"M10.764 3.343c.322-.337-.322-1.01-.645-.674l-7.474 7.81s-1.675 1.75-.129 3.365c1.546 1.616 3.222-.135 3.222-.135l7.861-8.212s2.063-2.154-.128-4.444C11.28-1.236 9.218.92 9.218.92L1.356 9.132s-2.963 3.097-.129 6.06c2.835 2.961 5.8-.136 5.8-.136l7.861-8.213c.322-.336-.322-1.01-.645-.673l-7.86 8.212s-2.32 2.424-4.51.135C-.32 12.228 2 9.805 2 9.805l7.86-8.213s1.418-1.481 2.965.135c1.546 1.616.129 3.097.129 3.097l-7.86 8.212s-1.032 1.078-1.934.135c-.903-.943.129-2.02.129-2.02l7.474-7.808z"})),i.file_name))))}filesMessageDetail(i){return t("div",null,i.map(i=>t("div",{style:{textAlign:"center"}},t("a",{class:"file-link",href:this.file_url+i.id,target:"_blank"},!this.imgMime.includes(Ea.getType(i.file_name))&&t("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 15 17",height:"64"},t("path",{fill:"#134391","fill-rule":"evenodd",d:"M10.764 3.343c.322-.337-.322-1.01-.645-.674l-7.474 7.81s-1.675 1.75-.129 3.365c1.546 1.616 3.222-.135 3.222-.135l7.861-8.212s2.063-2.154-.128-4.444C11.28-1.236 9.218.92 9.218.92L1.356 9.132s-2.963 3.097-.129 6.06c2.835 2.961 5.8-.136 5.8-.136l7.861-8.213c.322-.336-.322-1.01-.645-.673l-7.86 8.212s-2.32 2.424-4.51.135C-.32 12.228 2 9.805 2 9.805l7.86-8.213s1.418-1.481 2.965.135c1.546 1.616.129 3.097.129 3.097l-7.86 8.212s-1.032 1.078-1.934.135c-.903-.943.129-2.02.129-2.02l7.474-7.808z"})),this.imgMime.includes(Ea.getType(i.file_name))&&t("img",{src:this.file_url+i.id,width:128})))))}eventMessage(i){return t("div",null,i.event_name&&t("div",{class:i.event_name+" event"},Ha[this.language][i.event_name]))}render(){return t("div",{class:this.ismobile?"ismobile messager":"messager"},this.state.loader&&t("div",{class:"messager_loader"},t("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100",preserveAspectRatio:"xMidYMid"},t("circle",{cx:"50",cy:"50",fill:"none","stroke-width":"10",stroke:this.headerbordercolor,r:"35","stroke-dasharray":"164.93361431346415 56.97787143782138",transform:"rotate(222.044 50 50)"},t("animateTransform",{attributeName:"transform",type:"rotate",repeatCount:"indefinite",dur:"1s",values:"0 50 50;360 50 50",keyTimes:"0;1"})))),t("div",{class:"messager_header",style:{background:this.headerbackground,borderColor:this.headerbordercolor}},t("div",{class:"messager_header_logo"},t("img",{src:this.logo,height:"30"}))),this.messagesList(),this.state.showError&&t("div",{class:"error",onClick:()=>this.state=Object.assign(Object.assign({},this.state),{showError:!1})},"Erreur d'envoi, les formats autorisés sont doc, docx, xls, xlsx, pdf, jpeg, gif, png, zip et la taille max 5Mo"),t("form",{enctype:"multipart/form-data",class:"messager_footer",onSubmit:i=>{i.preventDefault()}},t("div",{class:"messager_footer_editor"},t("textarea",{name:"message",id:"message",placeholder:"Votre message...",value:this.state.message,onInput:i=>this.handleChange(i),onKeyPress:i=>this.autosize(i)})),t("div",{class:"messager_footer_upload"},t("label",{htmlFor:"messager_footer_attachment"},t("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 15 17",height:"16"},t("path",{fill:"#134391","fill-rule":"evenodd",d:"M10.764 3.343c.322-.337-.322-1.01-.645-.674l-7.474 7.81s-1.675 1.75-.129 3.365c1.546 1.616 3.222-.135 3.222-.135l7.861-8.212s2.063-2.154-.128-4.444C11.28-1.236 9.218.92 9.218.92L1.356 9.132s-2.963 3.097-.129 6.06c2.835 2.961 5.8-.136 5.8-.136l7.861-8.213c.322-.336-.322-1.01-.645-.673l-7.86 8.212s-2.32 2.424-4.51.135C-.32 12.228 2 9.805 2 9.805l7.86-8.213s1.418-1.481 2.965.135c1.546 1.616.129 3.097.129 3.097l-7.86 8.212s-1.032 1.078-1.934.135c-.903-.943.129-2.02.129-2.02l7.474-7.808z"}))),t("input",{style:{display:"none"},type:"file",name:"messager_footer_attachment",id:"messager_footer_attachment",value:this.state.files,onInput:i=>this.fileChange(i.target.files)})),t("div",{class:"messager_footer_send"},t("button",{type:"submit",onClick:()=>this.handleSubmit()},t("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 10.121108 11.034",height:"16"},t("path",{fill:"#134391","fill-rule":"evenodd",d:"m -1.439446,0 v 4.472 l 7.06,0.968 -7.06,0.908 v 4.686 l 13,-5.594 z"}))))))}get el(){return n(this)}static get style(){return"\@-webkit-keyframes rotating{0%{-webkit-transform:rotate(0deg)}to{-webkit-transform:rotate(1turn)}}\@keyframes rotating{0%{-webkit-transform:rotate(0deg)}to{-webkit-transform:rotate(1turn)}}.file-link{color:#134391}.messager{font-family:Arial,Helvetica,sans-serif;font-size:12px;border-radius:17px;-webkit-box-shadow:0 2px 9px 4px rgba(0,0,0,.15);box-shadow:0 2px 9px 4px rgba(0,0,0,.15);display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;overflow:hidden;background:#fff;position:relative}.messager *{-webkit-box-sizing:border-box;box-sizing:border-box}.messager_header{display:-ms-flexbox;display:flex;padding:15px;border-bottom:1px solid}.messager_header_logo{-ms-flex:1;flex:1;text-align:center}.messager_header_logo img{height:30px}.messager_loader{position:absolute;top:0;left:0;right:0;bottom:0;display:-ms-flexbox;display:flex;background:rgba(0,0,0,.3);-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}.messager_loader svg{width:40px;height:40px}.messager_loader svg circle{-webkit-transform-origin:50% 50%;transform-origin:50% 50%;-webkit-animation:rotating 2s linear infinite;animation:rotating 2s linear infinite}.messager_body{overflow:auto;min-height:60px;max-height:328px;display:block;padding:10px}.messager_body_container{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}.messager_body_message{max-width:80%;margin:10px 0}.messager_body_message .event{font-style:italic;font-weight:700;display:inline-block;padding:3px 8px;border-radius:10px}.messager_body_message .draft{display:none}.messager_body_message .redraft{color:#fff;background:#f49812}.messager_body_message .cancelled,.messager_body_message .refused{color:#fff;background:#ed0000}.messager_body_message .new,.messager_body_message .send,.messager_body_message .validated{color:#fff;background:#6b9f33}.messager_body_message_date{-ms-flex-item-align:center;align-self:center;color:#134391}.messager_body_message_card{background:#f0f0f0;padding:10px 20px;border-radius:10px}.messager_body_message_card_user{font-weight:700;margin-bottom:10px}.messager_body_message_recipient{-ms-flex-item-align:start;align-self:flex-start}.messager_body_message_sender{-ms-flex-item-align:end;align-self:flex-end}.messager_body_message_sender .messager_body_message_card{background:#ecf4fb}.messager_footer{display:-ms-flexbox;display:flex;padding:0;margin:0;border-top:1px solid #e2e4ed;padding:15px 30px}.messager_footer_editor{-ms-flex:1;flex:1}.messager_footer_editor textarea{display:block;resize:none;border:none!important;width:100%;margin:0;line-height:20px;height:20px;padding:0;max-height:60px;font-family:Arial,Helvetica,sans-serif;font-size:12px}.messager_footer svg{height:16px;width:16px}.messager_footer_upload label{padding:0 10px;display:-ms-flexbox;display:flex;line-height:20px}.messager_footer_upload label:hover{cursor:pointer}.messager_footer_send button{display:-ms-flexbox;display:flex;border:none;background:none;padding:0 10px;margin:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.messager_footer_send button:hover{cursor:pointer}.messager.ismobile{height:100vh;border-radius:0}.messager.ismobile .messager_body{max-height:none;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column}.error{background:#e10532;color:#fff;padding:5px;text-align:center}"}};export{La as t_messager};