var __spreadArrays=this&&this.__spreadArrays||function(){for(var e=0,t=0,i=arguments.length;t<i;t++)e+=arguments[t].length;for(var a=Array(e),n=0,t=0;t<i;t++)for(var s=arguments[t],r=0,o=s.length;r<o;r++,n++)a[n]=s[r];return a};System.register(["./p-f689ebf0.system.js"],(function(e){"use strict";var t,i,a;return{setters:[function(e){t=e.r;i=e.h;a=e.g}],execute:function(){var n=function e(t,i){return function e(){var a=new Array(arguments.length);for(var n=0;n<a.length;n++){a[n]=arguments[n]}return t.apply(i,a)}};var s=Object.prototype.toString;function r(e){return s.call(e)==="[object Array]"}function o(e){return typeof e==="undefined"}function l(e){return e!==null&&!o(e)&&e.constructor!==null&&!o(e.constructor)&&typeof e.constructor.isBuffer==="function"&&e.constructor.isBuffer(e)}function c(e){return s.call(e)==="[object ArrayBuffer]"}function p(e){return typeof FormData!=="undefined"&&e instanceof FormData}function d(e){var t;if(typeof ArrayBuffer!=="undefined"&&ArrayBuffer.isView){t=ArrayBuffer.isView(e)}else{t=e&&e.buffer&&e.buffer instanceof ArrayBuffer}return t}function u(e){return typeof e==="string"}function f(e){return typeof e==="number"}function m(e){return e!==null&&typeof e==="object"}function h(e){return s.call(e)==="[object Date]"}function v(e){return s.call(e)==="[object File]"}function g(e){return s.call(e)==="[object Blob]"}function x(e){return s.call(e)==="[object Function]"}function y(e){return m(e)&&x(e.pipe)}function _(e){return typeof URLSearchParams!=="undefined"&&e instanceof URLSearchParams}function w(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")}function b(){if(typeof navigator!=="undefined"&&(navigator.product==="ReactNative"||navigator.product==="NativeScript"||navigator.product==="NS")){return false}return typeof window!=="undefined"&&typeof document!=="undefined"}function k(e,t){if(e===null||typeof e==="undefined"){return}if(typeof e!=="object"){e=[e]}if(r(e)){for(var i=0,a=e.length;i<a;i++){t.call(null,e[i],i,e)}}else{for(var n in e){if(Object.prototype.hasOwnProperty.call(e,n)){t.call(null,e[n],n,e)}}}}function M(){var e={};function t(t,i){if(typeof e[i]==="object"&&typeof t==="object"){e[i]=M(e[i],t)}else{e[i]=t}}for(var i=0,a=arguments.length;i<a;i++){k(arguments[i],t)}return e}function S(){var e={};function t(t,i){if(typeof e[i]==="object"&&typeof t==="object"){e[i]=S(e[i],t)}else if(typeof t==="object"){e[i]=S({},t)}else{e[i]=t}}for(var i=0,a=arguments.length;i<a;i++){k(arguments[i],t)}return e}function D(e,t,i){k(t,(function t(a,s){if(i&&typeof a==="function"){e[s]=n(a,i)}else{e[s]=a}}));return e}var Y={isArray:r,isArrayBuffer:c,isBuffer:l,isFormData:p,isArrayBufferView:d,isString:u,isNumber:f,isObject:m,isUndefined:o,isDate:h,isFile:v,isBlob:g,isFunction:x,isStream:y,isURLSearchParams:_,isStandardBrowserEnv:b,forEach:k,merge:M,deepMerge:S,extend:D,trim:w};function O(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}var j=function e(t,i,a){if(!i){return t}var n;if(a){n=a(i)}else if(Y.isURLSearchParams(i)){n=i.toString()}else{var s=[];Y.forEach(i,(function e(t,i){if(t===null||typeof t==="undefined"){return}if(Y.isArray(t)){i=i+"[]"}else{t=[t]}Y.forEach(t,(function e(t){if(Y.isDate(t)){t=t.toISOString()}else if(Y.isObject(t)){t=JSON.stringify(t)}s.push(O(i)+"="+O(t))}))}));n=s.join("&")}if(n){var r=t.indexOf("#");if(r!==-1){t=t.slice(0,r)}t+=(t.indexOf("?")===-1?"?":"&")+n}return t};function T(){this.handlers=[]}T.prototype.use=function e(t,i){this.handlers.push({fulfilled:t,rejected:i});return this.handlers.length-1};T.prototype.eject=function e(t){if(this.handlers[t]){this.handlers[t]=null}};T.prototype.forEach=function e(t){Y.forEach(this.handlers,(function e(i){if(i!==null){t(i)}}))};var C=T;var z=function e(t,i,a){Y.forEach(a,(function e(a){t=a(t,i)}));return t};var P=function e(t){return!!(t&&t.__CANCEL__)};var L=function e(t,i){Y.forEach(t,(function e(a,n){if(n!==i&&n.toUpperCase()===i.toUpperCase()){t[i]=a;delete t[n]}}))};var R=function e(t,i,a,n,s){t.config=i;if(a){t.code=a}t.request=n;t.response=s;t.isAxiosError=true;t.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}};return t};var E=function e(t,i,a,n,s){var r=new Error(t);return R(r,i,a,n,s)};var H=function e(t,i,a){var n=a.config.validateStatus;if(!n||n(a.status)){t(a)}else{i(E("Request failed with status code "+a.status,a.config,null,a.request,a))}};var N=function e(t){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(t)};var U=function e(t,i){return i?t.replace(/\/+$/,"")+"/"+i.replace(/^\/+/,""):t};var W=function e(t,i){if(t&&!N(i)){return U(t,i)}return i};var A=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];var F=function e(t){var i={};var a;var n;var s;if(!t){return i}Y.forEach(t.split("\n"),(function e(t){s=t.indexOf(":");a=Y.trim(t.substr(0,s)).toLowerCase();n=Y.trim(t.substr(s+1));if(a){if(i[a]&&A.indexOf(a)>=0){return}if(a==="set-cookie"){i[a]=(i[a]?i[a]:[]).concat([n])}else{i[a]=i[a]?i[a]+", "+n:n}}}));return i};var q=Y.isStandardBrowserEnv()?function e(){var t=/(msie|trident)/i.test(navigator.userAgent);var i=document.createElement("a");var a;function n(e){var a=e;if(t){i.setAttribute("href",a);a=i.href}i.setAttribute("href",a);return{href:i.href,protocol:i.protocol?i.protocol.replace(/:$/,""):"",host:i.host,search:i.search?i.search.replace(/^\?/,""):"",hash:i.hash?i.hash.replace(/^#/,""):"",hostname:i.hostname,port:i.port,pathname:i.pathname.charAt(0)==="/"?i.pathname:"/"+i.pathname}}a=n(window.location.href);return function e(t){var i=Y.isString(t)?n(t):t;return i.protocol===a.protocol&&i.host===a.host}}():function e(){return function e(){return true}}();var V=Y.isStandardBrowserEnv()?function e(){return{write:function e(t,i,a,n,s,r){var o=[];o.push(t+"="+encodeURIComponent(i));if(Y.isNumber(a)){o.push("expires="+new Date(a).toGMTString())}if(Y.isString(n)){o.push("path="+n)}if(Y.isString(s)){o.push("domain="+s)}if(r===true){o.push("secure")}document.cookie=o.join("; ")},read:function e(t){var i=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return i?decodeURIComponent(i[3]):null},remove:function e(t){this.write(t,"",Date.now()-864e5)}}}():function e(){return{write:function e(){},read:function e(){return null},remove:function e(){}}}();var G=function e(t){return new Promise((function e(i,a){var n=t.data;var s=t.headers;if(Y.isFormData(n)){delete s["Content-Type"]}var r=new XMLHttpRequest;if(t.auth){var o=t.auth.username||"";var l=t.auth.password||"";s.Authorization="Basic "+btoa(o+":"+l)}var c=W(t.baseURL,t.url);r.open(t.method.toUpperCase(),j(c,t.params,t.paramsSerializer),true);r.timeout=t.timeout;r.onreadystatechange=function e(){if(!r||r.readyState!==4){return}if(r.status===0&&!(r.responseURL&&r.responseURL.indexOf("file:")===0)){return}var n="getAllResponseHeaders"in r?F(r.getAllResponseHeaders()):null;var s=!t.responseType||t.responseType==="text"?r.responseText:r.response;var o={data:s,status:r.status,statusText:r.statusText,headers:n,config:t,request:r};H(i,a,o);r=null};r.onabort=function e(){if(!r){return}a(E("Request aborted",t,"ECONNABORTED",r));r=null};r.onerror=function e(){a(E("Network Error",t,null,r));r=null};r.ontimeout=function e(){var i="timeout of "+t.timeout+"ms exceeded";if(t.timeoutErrorMessage){i=t.timeoutErrorMessage}a(E(i,t,"ECONNABORTED",r));r=null};if(Y.isStandardBrowserEnv()){var p=V;var d=(t.withCredentials||q(c))&&t.xsrfCookieName?p.read(t.xsrfCookieName):undefined;if(d){s[t.xsrfHeaderName]=d}}if("setRequestHeader"in r){Y.forEach(s,(function e(t,i){if(typeof n==="undefined"&&i.toLowerCase()==="content-type"){delete s[i]}else{r.setRequestHeader(i,t)}}))}if(!Y.isUndefined(t.withCredentials)){r.withCredentials=!!t.withCredentials}if(t.responseType){try{r.responseType=t.responseType}catch(u){if(t.responseType!=="json"){throw u}}}if(typeof t.onDownloadProgress==="function"){r.addEventListener("progress",t.onDownloadProgress)}if(typeof t.onUploadProgress==="function"&&r.upload){r.upload.addEventListener("progress",t.onUploadProgress)}if(t.cancelToken){t.cancelToken.promise.then((function e(t){if(!r){return}r.abort();a(t);r=null}))}if(n===undefined){n=null}r.send(n)}))};var I={"Content-Type":"application/x-www-form-urlencoded"};function B(e,t){if(!Y.isUndefined(e)&&Y.isUndefined(e["Content-Type"])){e["Content-Type"]=t}}function Z(){var e;if(typeof XMLHttpRequest!=="undefined"){e=G}else if(typeof process!=="undefined"&&Object.prototype.toString.call(process)==="[object process]"){e=G}return e}var $={adapter:Z(),transformRequest:[function e(t,i){L(i,"Accept");L(i,"Content-Type");if(Y.isFormData(t)||Y.isArrayBuffer(t)||Y.isBuffer(t)||Y.isStream(t)||Y.isFile(t)||Y.isBlob(t)){return t}if(Y.isArrayBufferView(t)){return t.buffer}if(Y.isURLSearchParams(t)){B(i,"application/x-www-form-urlencoded;charset=utf-8");return t.toString()}if(Y.isObject(t)){B(i,"application/json;charset=utf-8");return JSON.stringify(t)}return t}],transformResponse:[function e(t){if(typeof t==="string"){try{t=JSON.parse(t)}catch(i){}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function e(t){return t>=200&&t<300}};$.headers={common:{Accept:"application/json, text/plain, */*"}};Y.forEach(["delete","get","head"],(function e(t){$.headers[t]={}}));Y.forEach(["post","put","patch"],(function e(t){$.headers[t]=Y.merge(I)}));var J=$;function X(e){if(e.cancelToken){e.cancelToken.throwIfRequested()}}var Q=function e(t){X(t);t.headers=t.headers||{};t.data=z(t.data,t.headers,t.transformRequest);t.headers=Y.merge(t.headers.common||{},t.headers[t.method]||{},t.headers);Y.forEach(["delete","get","head","post","put","patch","common"],(function e(i){delete t.headers[i]}));var i=t.adapter||J.adapter;return i(t).then((function e(i){X(t);i.data=z(i.data,i.headers,t.transformResponse);return i}),(function e(i){if(!P(i)){X(t);if(i&&i.response){i.response.data=z(i.response.data,i.response.headers,t.transformResponse)}}return Promise.reject(i)}))};var K=function e(t,i){i=i||{};var a={};var n=["url","method","params","data"];var s=["headers","auth","proxy"];var r=["baseURL","url","transformRequest","transformResponse","paramsSerializer","timeout","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","maxContentLength","validateStatus","maxRedirects","httpAgent","httpsAgent","cancelToken","socketPath"];Y.forEach(n,(function e(t){if(typeof i[t]!=="undefined"){a[t]=i[t]}}));Y.forEach(s,(function e(n){if(Y.isObject(i[n])){a[n]=Y.deepMerge(t[n],i[n])}else if(typeof i[n]!=="undefined"){a[n]=i[n]}else if(Y.isObject(t[n])){a[n]=Y.deepMerge(t[n])}else if(typeof t[n]!=="undefined"){a[n]=t[n]}}));Y.forEach(r,(function e(n){if(typeof i[n]!=="undefined"){a[n]=i[n]}else if(typeof t[n]!=="undefined"){a[n]=t[n]}}));var o=n.concat(s).concat(r);var l=Object.keys(i).filter((function e(t){return o.indexOf(t)===-1}));Y.forEach(l,(function e(n){if(typeof i[n]!=="undefined"){a[n]=i[n]}else if(typeof t[n]!=="undefined"){a[n]=t[n]}}));return a};function ee(e){this.defaults=e;this.interceptors={request:new C,response:new C}}ee.prototype.request=function e(t){if(typeof t==="string"){t=arguments[1]||{};t.url=arguments[0]}else{t=t||{}}t=K(this.defaults,t);if(t.method){t.method=t.method.toLowerCase()}else if(this.defaults.method){t.method=this.defaults.method.toLowerCase()}else{t.method="get"}var i=[Q,undefined];var a=Promise.resolve(t);this.interceptors.request.forEach((function e(t){i.unshift(t.fulfilled,t.rejected)}));this.interceptors.response.forEach((function e(t){i.push(t.fulfilled,t.rejected)}));while(i.length){a=a.then(i.shift(),i.shift())}return a};ee.prototype.getUri=function e(t){t=K(this.defaults,t);return j(t.url,t.params,t.paramsSerializer).replace(/^\?/,"")};Y.forEach(["delete","get","head","options"],(function e(t){ee.prototype[t]=function(e,i){return this.request(Y.merge(i||{},{method:t,url:e}))}}));Y.forEach(["post","put","patch"],(function e(t){ee.prototype[t]=function(e,i,a){return this.request(Y.merge(a||{},{method:t,url:e,data:i}))}}));var te=ee;function ie(e){this.message=e}ie.prototype.toString=function e(){return"Cancel"+(this.message?": "+this.message:"")};ie.prototype.__CANCEL__=true;var ae=ie;function ne(e){if(typeof e!=="function"){throw new TypeError("executor must be a function.")}var t;this.promise=new Promise((function e(i){t=i}));var i=this;e((function e(a){if(i.reason){return}i.reason=new ae(a);t(i.reason)}))}ne.prototype.throwIfRequested=function e(){if(this.reason){throw this.reason}};ne.source=function e(){var t;var i=new ne((function e(i){t=i}));return{token:i,cancel:t}};var se=ne;var re=function e(t){return function e(i){return t.apply(null,i)}};function oe(e){var t=new te(e);var i=n(te.prototype.request,t);Y.extend(i,te.prototype,t);Y.extend(i,t);return i}var le=oe(J);le.Axios=te;le.create=function e(t){return oe(K(le.defaults,t))};le.Cancel=ae;le.CancelToken=se;le.isCancel=P;le.all=function e(t){return Promise.all(t)};le.spread=re;var ce=le;var pe=le;ce.default=pe;var de=ce;var ue;function fe(){return ue.apply(null,arguments)}function me(e){ue=e}function he(e){return e instanceof Array||Object.prototype.toString.call(e)==="[object Array]"}function ve(e){return e!=null&&Object.prototype.toString.call(e)==="[object Object]"}function ge(e){if(Object.getOwnPropertyNames){return Object.getOwnPropertyNames(e).length===0}else{var t;for(t in e){if(e.hasOwnProperty(t)){return false}}return true}}function xe(e){return e===void 0}function ye(e){return typeof e==="number"||Object.prototype.toString.call(e)==="[object Number]"}function _e(e){return e instanceof Date||Object.prototype.toString.call(e)==="[object Date]"}function we(e,t){var i=[],a;for(a=0;a<e.length;++a){i.push(t(e[a],a))}return i}function be(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function ke(e,t){for(var i in t){if(be(t,i)){e[i]=t[i]}}if(be(t,"toString")){e.toString=t.toString}if(be(t,"valueOf")){e.valueOf=t.valueOf}return e}function Me(e,t,i,a){return nn(e,t,i,a,true).utc()}function Se(){return{empty:false,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:false,invalidMonth:null,invalidFormat:false,userInvalidated:false,iso:false,parsedDateParts:[],meridiem:null,rfc2822:false,weekdayMismatch:false}}function De(e){if(e._pf==null){e._pf=Se()}return e._pf}var Ye;if(Array.prototype.some){Ye=Array.prototype.some}else{Ye=function(e){var t=Object(this);var i=t.length>>>0;for(var a=0;a<i;a++){if(a in t&&e.call(this,t[a],a,t)){return true}}return false}}function Oe(e){if(e._isValid==null){var t=De(e);var i=Ye.call(t.parsedDateParts,(function(e){return e!=null}));var a=!isNaN(e._d.getTime())&&t.overflow<0&&!t.empty&&!t.invalidMonth&&!t.invalidWeekday&&!t.weekdayMismatch&&!t.nullInput&&!t.invalidFormat&&!t.userInvalidated&&(!t.meridiem||t.meridiem&&i);if(e._strict){a=a&&t.charsLeftOver===0&&t.unusedTokens.length===0&&t.bigHour===undefined}if(Object.isFrozen==null||!Object.isFrozen(e)){e._isValid=a}else{return a}}return e._isValid}function je(e){var t=Me(NaN);if(e!=null){ke(De(t),e)}else{De(t).userInvalidated=true}return t}var Te=fe.momentProperties=[];function Ce(e,t){var i,a,n;if(!xe(t._isAMomentObject)){e._isAMomentObject=t._isAMomentObject}if(!xe(t._i)){e._i=t._i}if(!xe(t._f)){e._f=t._f}if(!xe(t._l)){e._l=t._l}if(!xe(t._strict)){e._strict=t._strict}if(!xe(t._tzm)){e._tzm=t._tzm}if(!xe(t._isUTC)){e._isUTC=t._isUTC}if(!xe(t._offset)){e._offset=t._offset}if(!xe(t._pf)){e._pf=De(t)}if(!xe(t._locale)){e._locale=t._locale}if(Te.length>0){for(i=0;i<Te.length;i++){a=Te[i];n=t[a];if(!xe(n)){e[a]=n}}}return e}var ze=false;function Pe(e){Ce(this,e);this._d=new Date(e._d!=null?e._d.getTime():NaN);if(!this.isValid()){this._d=new Date(NaN)}if(ze===false){ze=true;fe.updateOffset(this);ze=false}}function Le(e){return e instanceof Pe||e!=null&&e._isAMomentObject!=null}function Re(e){if(e<0){return Math.ceil(e)||0}else{return Math.floor(e)}}function Ee(e){var t=+e,i=0;if(t!==0&&isFinite(t)){i=Re(t)}return i}function He(e,t,i){var a=Math.min(e.length,t.length),n=Math.abs(e.length-t.length),s=0,r;for(r=0;r<a;r++){if(i&&e[r]!==t[r]||!i&&Ee(e[r])!==Ee(t[r])){s++}}return s+n}function Ne(e){if(fe.suppressDeprecationWarnings===false&&typeof console!=="undefined"&&console.warn){console.warn("Deprecation warning: "+e)}}function Ue(e,t){var i=true;return ke((function(){if(fe.deprecationHandler!=null){fe.deprecationHandler(null,e)}if(i){var a=[];var n;for(var s=0;s<arguments.length;s++){n="";if(typeof arguments[s]==="object"){n+="\n["+s+"] ";for(var r in arguments[0]){n+=r+": "+arguments[0][r]+", "}n=n.slice(0,-2)}else{n=arguments[s]}a.push(n)}Ne(e+"\nArguments: "+Array.prototype.slice.call(a).join("")+"\n"+(new Error).stack);i=false}return t.apply(this,arguments)}),t)}var We={};function Ae(e,t){if(fe.deprecationHandler!=null){fe.deprecationHandler(e,t)}if(!We[e]){Ne(t);We[e]=true}}fe.suppressDeprecationWarnings=false;fe.deprecationHandler=null;function Fe(e){return e instanceof Function||Object.prototype.toString.call(e)==="[object Function]"}function qe(e){var t,i;for(i in e){t=e[i];if(Fe(t)){this[i]=t}else{this["_"+i]=t}}this._config=e;this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)}function Ve(e,t){var i=ke({},e),a;for(a in t){if(be(t,a)){if(ve(e[a])&&ve(t[a])){i[a]={};ke(i[a],e[a]);ke(i[a],t[a])}else if(t[a]!=null){i[a]=t[a]}else{delete i[a]}}}for(a in e){if(be(e,a)&&!be(t,a)&&ve(e[a])){i[a]=ke({},i[a])}}return i}function Ge(e){if(e!=null){this.set(e)}}var Ie;if(Object.keys){Ie=Object.keys}else{Ie=function(e){var t,i=[];for(t in e){if(be(e,t)){i.push(t)}}return i}}var Be={sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"};function Ze(e,t,i){var a=this._calendar[e]||this._calendar["sameElse"];return Fe(a)?a.call(t,i):a}var $e={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"};function Je(e){var t=this._longDateFormat[e],i=this._longDateFormat[e.toUpperCase()];if(t||!i){return t}this._longDateFormat[e]=i.replace(/MMMM|MM|DD|dddd/g,(function(e){return e.slice(1)}));return this._longDateFormat[e]}var Xe="Invalid date";function Qe(){return this._invalidDate}var Ke="%d";var et=/\d{1,2}/;function tt(e){return this._ordinal.replace("%d",e)}var it={future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function at(e,t,i,a){var n=this._relativeTime[i];return Fe(n)?n(e,t,i,a):n.replace(/%d/i,e)}function nt(e,t){var i=this._relativeTime[e>0?"future":"past"];return Fe(i)?i(t):i.replace(/%s/i,t)}var st={};function rt(e,t){var i=e.toLowerCase();st[i]=st[i+"s"]=st[t]=e}function ot(e){return typeof e==="string"?st[e]||st[e.toLowerCase()]:undefined}function lt(e){var t={},i,a;for(a in e){if(be(e,a)){i=ot(a);if(i){t[i]=e[a]}}}return t}var ct={};function pt(e,t){ct[e]=t}function dt(e){var t=[];for(var i in e){t.push({unit:i,priority:ct[i]})}t.sort((function(e,t){return e.priority-t.priority}));return t}function ut(e,t,i){var a=""+Math.abs(e),n=t-a.length,s=e>=0;return(s?i?"+":"":"-")+Math.pow(10,Math.max(0,n)).toString().substr(1)+a}var ft=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|YYYYYY|YYYYY|YYYY|YY|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g;var mt=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g;var ht={};var vt={};function gt(e,t,i,a){var n=a;if(typeof a==="string"){n=function(){return this[a]()}}if(e){vt[e]=n}if(t){vt[t[0]]=function(){return ut(n.apply(this,arguments),t[1],t[2])}}if(i){vt[i]=function(){return this.localeData().ordinal(n.apply(this,arguments),e)}}}function xt(e){if(e.match(/\[[\s\S]/)){return e.replace(/^\[|\]$/g,"")}return e.replace(/\\/g,"")}function yt(e){var t=e.match(ft),i,a;for(i=0,a=t.length;i<a;i++){if(vt[t[i]]){t[i]=vt[t[i]]}else{t[i]=xt(t[i])}}return function(i){var n="",s;for(s=0;s<a;s++){n+=Fe(t[s])?t[s].call(i,e):t[s]}return n}}function _t(e,t){if(!e.isValid()){return e.localeData().invalidDate()}t=wt(t,e.localeData());ht[t]=ht[t]||yt(t);return ht[t](e)}function wt(e,t){var i=5;function a(e){return t.longDateFormat(e)||e}mt.lastIndex=0;while(i>=0&&mt.test(e)){e=e.replace(mt,a);mt.lastIndex=0;i-=1}return e}var bt=/\d/;var kt=/\d\d/;var Mt=/\d{3}/;var St=/\d{4}/;var Dt=/[+-]?\d{6}/;var Yt=/\d\d?/;var Ot=/\d\d\d\d?/;var jt=/\d\d\d\d\d\d?/;var Tt=/\d{1,3}/;var Ct=/\d{1,4}/;var zt=/[+-]?\d{1,6}/;var Pt=/\d+/;var Lt=/[+-]?\d+/;var Rt=/Z|[+-]\d\d:?\d\d/gi;var Et=/Z|[+-]\d\d(?::?\d\d)?/gi;var Ht=/[+-]?\d+(\.\d{1,3})?/;var Nt=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i;var Ut={};function Wt(e,t,i){Ut[e]=Fe(t)?t:function(e,a){return e&&i?i:t}}function At(e,t){if(!be(Ut,e)){return new RegExp(Ft(e))}return Ut[e](t._strict,t._locale)}function Ft(e){return qt(e.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,(function(e,t,i,a,n){return t||i||a||n})))}function qt(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}var Vt={};function Gt(e,t){var i,a=t;if(typeof e==="string"){e=[e]}if(ye(t)){a=function(e,i){i[t]=Ee(e)}}for(i=0;i<e.length;i++){Vt[e[i]]=a}}function It(e,t){Gt(e,(function(e,i,a,n){a._w=a._w||{};t(e,a._w,a,n)}))}function Bt(e,t,i){if(t!=null&&be(Vt,e)){Vt[e](t,i._a,i,e)}}var Zt=0;var $t=1;var Jt=2;var Xt=3;var Qt=4;var Kt=5;var ei=6;var ti=7;var ii=8;gt("Y",0,0,(function(){var e=this.year();return e<=9999?""+e:"+"+e}));gt(0,["YY",2],0,(function(){return this.year()%100}));gt(0,["YYYY",4],0,"year");gt(0,["YYYYY",5],0,"year");gt(0,["YYYYYY",6,true],0,"year");rt("year","y");pt("year",1);Wt("Y",Lt);Wt("YY",Yt,kt);Wt("YYYY",Ct,St);Wt("YYYYY",zt,Dt);Wt("YYYYYY",zt,Dt);Gt(["YYYYY","YYYYYY"],Zt);Gt("YYYY",(function(e,t){t[Zt]=e.length===2?fe.parseTwoDigitYear(e):Ee(e)}));Gt("YY",(function(e,t){t[Zt]=fe.parseTwoDigitYear(e)}));Gt("Y",(function(e,t){t[Zt]=parseInt(e,10)}));function ai(e){return ni(e)?366:365}function ni(e){return e%4===0&&e%100!==0||e%400===0}fe.parseTwoDigitYear=function(e){return Ee(e)+(Ee(e)>68?1900:2e3)};var si=oi("FullYear",true);function ri(){return ni(this.year())}function oi(e,t){return function(i){if(i!=null){ci(this,e,i);fe.updateOffset(this,t);return this}else{return li(this,e)}}}function li(e,t){return e.isValid()?e._d["get"+(e._isUTC?"UTC":"")+t]():NaN}function ci(e,t,i){if(e.isValid()&&!isNaN(i)){if(t==="FullYear"&&ni(e.year())&&e.month()===1&&e.date()===29){e._d["set"+(e._isUTC?"UTC":"")+t](i,e.month(),mi(i,e.month()))}else{e._d["set"+(e._isUTC?"UTC":"")+t](i)}}}function pi(e){e=ot(e);if(Fe(this[e])){return this[e]()}return this}function di(e,t){if(typeof e==="object"){e=lt(e);var i=dt(e);for(var a=0;a<i.length;a++){this[i[a].unit](e[i[a].unit])}}else{e=ot(e);if(Fe(this[e])){return this[e](t)}}return this}function ui(e,t){return(e%t+t)%t}var fi;if(Array.prototype.indexOf){fi=Array.prototype.indexOf}else{fi=function(e){var t;for(t=0;t<this.length;++t){if(this[t]===e){return t}}return-1}}function mi(e,t){if(isNaN(e)||isNaN(t)){return NaN}var i=ui(t,12);e+=(t-i)/12;return i===1?ni(e)?29:28:31-i%7%2}gt("M",["MM",2],"Mo",(function(){return this.month()+1}));gt("MMM",0,0,(function(e){return this.localeData().monthsShort(this,e)}));gt("MMMM",0,0,(function(e){return this.localeData().months(this,e)}));rt("month","M");pt("month",8);Wt("M",Yt);Wt("MM",Yt,kt);Wt("MMM",(function(e,t){return t.monthsShortRegex(e)}));Wt("MMMM",(function(e,t){return t.monthsRegex(e)}));Gt(["M","MM"],(function(e,t){t[$t]=Ee(e)-1}));Gt(["MMM","MMMM"],(function(e,t,i,a){var n=i._locale.monthsParse(e,a,i._strict);if(n!=null){t[$t]=n}else{De(i).invalidMonth=e}}));var hi=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/;var vi="January_February_March_April_May_June_July_August_September_October_November_December".split("_");function gi(e,t){if(!e){return he(this._months)?this._months:this._months["standalone"]}return he(this._months)?this._months[e.month()]:this._months[(this._months.isFormat||hi).test(t)?"format":"standalone"][e.month()]}var xi="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_");function yi(e,t){if(!e){return he(this._monthsShort)?this._monthsShort:this._monthsShort["standalone"]}return he(this._monthsShort)?this._monthsShort[e.month()]:this._monthsShort[hi.test(t)?"format":"standalone"][e.month()]}function _i(e,t,i){var a,n,s,r=e.toLocaleLowerCase();if(!this._monthsParse){this._monthsParse=[];this._longMonthsParse=[];this._shortMonthsParse=[];for(a=0;a<12;++a){s=Me([2e3,a]);this._shortMonthsParse[a]=this.monthsShort(s,"").toLocaleLowerCase();this._longMonthsParse[a]=this.months(s,"").toLocaleLowerCase()}}if(i){if(t==="MMM"){n=fi.call(this._shortMonthsParse,r);return n!==-1?n:null}else{n=fi.call(this._longMonthsParse,r);return n!==-1?n:null}}else{if(t==="MMM"){n=fi.call(this._shortMonthsParse,r);if(n!==-1){return n}n=fi.call(this._longMonthsParse,r);return n!==-1?n:null}else{n=fi.call(this._longMonthsParse,r);if(n!==-1){return n}n=fi.call(this._shortMonthsParse,r);return n!==-1?n:null}}}function wi(e,t,i){var a,n,s;if(this._monthsParseExact){return _i.call(this,e,t,i)}if(!this._monthsParse){this._monthsParse=[];this._longMonthsParse=[];this._shortMonthsParse=[]}for(a=0;a<12;a++){n=Me([2e3,a]);if(i&&!this._longMonthsParse[a]){this._longMonthsParse[a]=new RegExp("^"+this.months(n,"").replace(".","")+"$","i");this._shortMonthsParse[a]=new RegExp("^"+this.monthsShort(n,"").replace(".","")+"$","i")}if(!i&&!this._monthsParse[a]){s="^"+this.months(n,"")+"|^"+this.monthsShort(n,"");this._monthsParse[a]=new RegExp(s.replace(".",""),"i")}if(i&&t==="MMMM"&&this._longMonthsParse[a].test(e)){return a}else if(i&&t==="MMM"&&this._shortMonthsParse[a].test(e)){return a}else if(!i&&this._monthsParse[a].test(e)){return a}}}function bi(e,t){var i;if(!e.isValid()){return e}if(typeof t==="string"){if(/^\d+$/.test(t)){t=Ee(t)}else{t=e.localeData().monthsParse(t);if(!ye(t)){return e}}}i=Math.min(e.date(),mi(e.year(),t));e._d["set"+(e._isUTC?"UTC":"")+"Month"](t,i);return e}function ki(e){if(e!=null){bi(this,e);fe.updateOffset(this,true);return this}else{return li(this,"Month")}}function Mi(){return mi(this.year(),this.month())}var Si=Nt;function Di(e){if(this._monthsParseExact){if(!be(this,"_monthsRegex")){ji.call(this)}if(e){return this._monthsShortStrictRegex}else{return this._monthsShortRegex}}else{if(!be(this,"_monthsShortRegex")){this._monthsShortRegex=Si}return this._monthsShortStrictRegex&&e?this._monthsShortStrictRegex:this._monthsShortRegex}}var Yi=Nt;function Oi(e){if(this._monthsParseExact){if(!be(this,"_monthsRegex")){ji.call(this)}if(e){return this._monthsStrictRegex}else{return this._monthsRegex}}else{if(!be(this,"_monthsRegex")){this._monthsRegex=Yi}return this._monthsStrictRegex&&e?this._monthsStrictRegex:this._monthsRegex}}function ji(){function e(e,t){return t.length-e.length}var t=[],i=[],a=[],n,s;for(n=0;n<12;n++){s=Me([2e3,n]);t.push(this.monthsShort(s,""));i.push(this.months(s,""));a.push(this.months(s,""));a.push(this.monthsShort(s,""))}t.sort(e);i.sort(e);a.sort(e);for(n=0;n<12;n++){t[n]=qt(t[n]);i[n]=qt(i[n])}for(n=0;n<24;n++){a[n]=qt(a[n])}this._monthsRegex=new RegExp("^("+a.join("|")+")","i");this._monthsShortRegex=this._monthsRegex;this._monthsStrictRegex=new RegExp("^("+i.join("|")+")","i");this._monthsShortStrictRegex=new RegExp("^("+t.join("|")+")","i")}function Ti(e,t,i,a,n,s,r){var o;if(e<100&&e>=0){o=new Date(e+400,t,i,a,n,s,r);if(isFinite(o.getFullYear())){o.setFullYear(e)}}else{o=new Date(e,t,i,a,n,s,r)}return o}function Ci(e){var t;if(e<100&&e>=0){var i=Array.prototype.slice.call(arguments);i[0]=e+400;t=new Date(Date.UTC.apply(null,i));if(isFinite(t.getUTCFullYear())){t.setUTCFullYear(e)}}else{t=new Date(Date.UTC.apply(null,arguments))}return t}function zi(e,t,i){var a=7+t-i,n=(7+Ci(e,0,a).getUTCDay()-t)%7;return-n+a-1}function Pi(e,t,i,a,n){var s=(7+i-a)%7,r=zi(e,a,n),o=1+7*(t-1)+s+r,l,c;if(o<=0){l=e-1;c=ai(l)+o}else if(o>ai(e)){l=e+1;c=o-ai(e)}else{l=e;c=o}return{year:l,dayOfYear:c}}function Li(e,t,i){var a=zi(e.year(),t,i),n=Math.floor((e.dayOfYear()-a-1)/7)+1,s,r;if(n<1){r=e.year()-1;s=n+Ri(r,t,i)}else if(n>Ri(e.year(),t,i)){s=n-Ri(e.year(),t,i);r=e.year()+1}else{r=e.year();s=n}return{week:s,year:r}}function Ri(e,t,i){var a=zi(e,t,i),n=zi(e+1,t,i);return(ai(e)-a+n)/7}gt("w",["ww",2],"wo","week");gt("W",["WW",2],"Wo","isoWeek");rt("week","w");rt("isoWeek","W");pt("week",5);pt("isoWeek",5);Wt("w",Yt);Wt("ww",Yt,kt);Wt("W",Yt);Wt("WW",Yt,kt);It(["w","ww","W","WW"],(function(e,t,i,a){t[a.substr(0,1)]=Ee(e)}));function Ei(e){return Li(e,this._week.dow,this._week.doy).week}var Hi={dow:0,doy:6};function Ni(){return this._week.dow}function Ui(){return this._week.doy}function Wi(e){var t=this.localeData().week(this);return e==null?t:this.add((e-t)*7,"d")}function Ai(e){var t=Li(this,1,4).week;return e==null?t:this.add((e-t)*7,"d")}gt("d",0,"do","day");gt("dd",0,0,(function(e){return this.localeData().weekdaysMin(this,e)}));gt("ddd",0,0,(function(e){return this.localeData().weekdaysShort(this,e)}));gt("dddd",0,0,(function(e){return this.localeData().weekdays(this,e)}));gt("e",0,0,"weekday");gt("E",0,0,"isoWeekday");rt("day","d");rt("weekday","e");rt("isoWeekday","E");pt("day",11);pt("weekday",11);pt("isoWeekday",11);Wt("d",Yt);Wt("e",Yt);Wt("E",Yt);Wt("dd",(function(e,t){return t.weekdaysMinRegex(e)}));Wt("ddd",(function(e,t){return t.weekdaysShortRegex(e)}));Wt("dddd",(function(e,t){return t.weekdaysRegex(e)}));It(["dd","ddd","dddd"],(function(e,t,i,a){var n=i._locale.weekdaysParse(e,a,i._strict);if(n!=null){t.d=n}else{De(i).invalidWeekday=e}}));It(["d","e","E"],(function(e,t,i,a){t[a]=Ee(e)}));function Fi(e,t){if(typeof e!=="string"){return e}if(!isNaN(e)){return parseInt(e,10)}e=t.weekdaysParse(e);if(typeof e==="number"){return e}return null}function qi(e,t){if(typeof e==="string"){return t.weekdaysParse(e)%7||7}return isNaN(e)?null:e}function Vi(e,t){return e.slice(t,7).concat(e.slice(0,t))}var Gi="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_");function Ii(e,t){var i=he(this._weekdays)?this._weekdays:this._weekdays[e&&e!==true&&this._weekdays.isFormat.test(t)?"format":"standalone"];return e===true?Vi(i,this._week.dow):e?i[e.day()]:i}var Bi="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_");function Zi(e){return e===true?Vi(this._weekdaysShort,this._week.dow):e?this._weekdaysShort[e.day()]:this._weekdaysShort}var $i="Su_Mo_Tu_We_Th_Fr_Sa".split("_");function Ji(e){return e===true?Vi(this._weekdaysMin,this._week.dow):e?this._weekdaysMin[e.day()]:this._weekdaysMin}function Xi(e,t,i){var a,n,s,r=e.toLocaleLowerCase();if(!this._weekdaysParse){this._weekdaysParse=[];this._shortWeekdaysParse=[];this._minWeekdaysParse=[];for(a=0;a<7;++a){s=Me([2e3,1]).day(a);this._minWeekdaysParse[a]=this.weekdaysMin(s,"").toLocaleLowerCase();this._shortWeekdaysParse[a]=this.weekdaysShort(s,"").toLocaleLowerCase();this._weekdaysParse[a]=this.weekdays(s,"").toLocaleLowerCase()}}if(i){if(t==="dddd"){n=fi.call(this._weekdaysParse,r);return n!==-1?n:null}else if(t==="ddd"){n=fi.call(this._shortWeekdaysParse,r);return n!==-1?n:null}else{n=fi.call(this._minWeekdaysParse,r);return n!==-1?n:null}}else{if(t==="dddd"){n=fi.call(this._weekdaysParse,r);if(n!==-1){return n}n=fi.call(this._shortWeekdaysParse,r);if(n!==-1){return n}n=fi.call(this._minWeekdaysParse,r);return n!==-1?n:null}else if(t==="ddd"){n=fi.call(this._shortWeekdaysParse,r);if(n!==-1){return n}n=fi.call(this._weekdaysParse,r);if(n!==-1){return n}n=fi.call(this._minWeekdaysParse,r);return n!==-1?n:null}else{n=fi.call(this._minWeekdaysParse,r);if(n!==-1){return n}n=fi.call(this._weekdaysParse,r);if(n!==-1){return n}n=fi.call(this._shortWeekdaysParse,r);return n!==-1?n:null}}}function Qi(e,t,i){var a,n,s;if(this._weekdaysParseExact){return Xi.call(this,e,t,i)}if(!this._weekdaysParse){this._weekdaysParse=[];this._minWeekdaysParse=[];this._shortWeekdaysParse=[];this._fullWeekdaysParse=[]}for(a=0;a<7;a++){n=Me([2e3,1]).day(a);if(i&&!this._fullWeekdaysParse[a]){this._fullWeekdaysParse[a]=new RegExp("^"+this.weekdays(n,"").replace(".","\\.?")+"$","i");this._shortWeekdaysParse[a]=new RegExp("^"+this.weekdaysShort(n,"").replace(".","\\.?")+"$","i");this._minWeekdaysParse[a]=new RegExp("^"+this.weekdaysMin(n,"").replace(".","\\.?")+"$","i")}if(!this._weekdaysParse[a]){s="^"+this.weekdays(n,"")+"|^"+this.weekdaysShort(n,"")+"|^"+this.weekdaysMin(n,"");this._weekdaysParse[a]=new RegExp(s.replace(".",""),"i")}if(i&&t==="dddd"&&this._fullWeekdaysParse[a].test(e)){return a}else if(i&&t==="ddd"&&this._shortWeekdaysParse[a].test(e)){return a}else if(i&&t==="dd"&&this._minWeekdaysParse[a].test(e)){return a}else if(!i&&this._weekdaysParse[a].test(e)){return a}}}function Ki(e){if(!this.isValid()){return e!=null?this:NaN}var t=this._isUTC?this._d.getUTCDay():this._d.getDay();if(e!=null){e=Fi(e,this.localeData());return this.add(e-t,"d")}else{return t}}function ea(e){if(!this.isValid()){return e!=null?this:NaN}var t=(this.day()+7-this.localeData()._week.dow)%7;return e==null?t:this.add(e-t,"d")}function ta(e){if(!this.isValid()){return e!=null?this:NaN}if(e!=null){var t=qi(e,this.localeData());return this.day(this.day()%7?t:t-7)}else{return this.day()||7}}var ia=Nt;function aa(e){if(this._weekdaysParseExact){if(!be(this,"_weekdaysRegex")){la.call(this)}if(e){return this._weekdaysStrictRegex}else{return this._weekdaysRegex}}else{if(!be(this,"_weekdaysRegex")){this._weekdaysRegex=ia}return this._weekdaysStrictRegex&&e?this._weekdaysStrictRegex:this._weekdaysRegex}}var na=Nt;function sa(e){if(this._weekdaysParseExact){if(!be(this,"_weekdaysRegex")){la.call(this)}if(e){return this._weekdaysShortStrictRegex}else{return this._weekdaysShortRegex}}else{if(!be(this,"_weekdaysShortRegex")){this._weekdaysShortRegex=na}return this._weekdaysShortStrictRegex&&e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex}}var ra=Nt;function oa(e){if(this._weekdaysParseExact){if(!be(this,"_weekdaysRegex")){la.call(this)}if(e){return this._weekdaysMinStrictRegex}else{return this._weekdaysMinRegex}}else{if(!be(this,"_weekdaysMinRegex")){this._weekdaysMinRegex=ra}return this._weekdaysMinStrictRegex&&e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex}}function la(){function e(e,t){return t.length-e.length}var t=[],i=[],a=[],n=[],s,r,o,l,c;for(s=0;s<7;s++){r=Me([2e3,1]).day(s);o=this.weekdaysMin(r,"");l=this.weekdaysShort(r,"");c=this.weekdays(r,"");t.push(o);i.push(l);a.push(c);n.push(o);n.push(l);n.push(c)}t.sort(e);i.sort(e);a.sort(e);n.sort(e);for(s=0;s<7;s++){i[s]=qt(i[s]);a[s]=qt(a[s]);n[s]=qt(n[s])}this._weekdaysRegex=new RegExp("^("+n.join("|")+")","i");this._weekdaysShortRegex=this._weekdaysRegex;this._weekdaysMinRegex=this._weekdaysRegex;this._weekdaysStrictRegex=new RegExp("^("+a.join("|")+")","i");this._weekdaysShortStrictRegex=new RegExp("^("+i.join("|")+")","i");this._weekdaysMinStrictRegex=new RegExp("^("+t.join("|")+")","i")}function ca(){return this.hours()%12||12}function pa(){return this.hours()||24}gt("H",["HH",2],0,"hour");gt("h",["hh",2],0,ca);gt("k",["kk",2],0,pa);gt("hmm",0,0,(function(){return""+ca.apply(this)+ut(this.minutes(),2)}));gt("hmmss",0,0,(function(){return""+ca.apply(this)+ut(this.minutes(),2)+ut(this.seconds(),2)}));gt("Hmm",0,0,(function(){return""+this.hours()+ut(this.minutes(),2)}));gt("Hmmss",0,0,(function(){return""+this.hours()+ut(this.minutes(),2)+ut(this.seconds(),2)}));function da(e,t){gt(e,0,0,(function(){return this.localeData().meridiem(this.hours(),this.minutes(),t)}))}da("a",true);da("A",false);rt("hour","h");pt("hour",13);function ua(e,t){return t._meridiemParse}Wt("a",ua);Wt("A",ua);Wt("H",Yt);Wt("h",Yt);Wt("k",Yt);Wt("HH",Yt,kt);Wt("hh",Yt,kt);Wt("kk",Yt,kt);Wt("hmm",Ot);Wt("hmmss",jt);Wt("Hmm",Ot);Wt("Hmmss",jt);Gt(["H","HH"],Xt);Gt(["k","kk"],(function(e,t,i){var a=Ee(e);t[Xt]=a===24?0:a}));Gt(["a","A"],(function(e,t,i){i._isPm=i._locale.isPM(e);i._meridiem=e}));Gt(["h","hh"],(function(e,t,i){t[Xt]=Ee(e);De(i).bigHour=true}));Gt("hmm",(function(e,t,i){var a=e.length-2;t[Xt]=Ee(e.substr(0,a));t[Qt]=Ee(e.substr(a));De(i).bigHour=true}));Gt("hmmss",(function(e,t,i){var a=e.length-4;var n=e.length-2;t[Xt]=Ee(e.substr(0,a));t[Qt]=Ee(e.substr(a,2));t[Kt]=Ee(e.substr(n));De(i).bigHour=true}));Gt("Hmm",(function(e,t,i){var a=e.length-2;t[Xt]=Ee(e.substr(0,a));t[Qt]=Ee(e.substr(a))}));Gt("Hmmss",(function(e,t,i){var a=e.length-4;var n=e.length-2;t[Xt]=Ee(e.substr(0,a));t[Qt]=Ee(e.substr(a,2));t[Kt]=Ee(e.substr(n))}));function fa(e){return(e+"").toLowerCase().charAt(0)==="p"}var ma=/[ap]\.?m?\.?/i;function ha(e,t,i){if(e>11){return i?"pm":"PM"}else{return i?"am":"AM"}}var va=oi("Hours",true);var ga={calendar:Be,longDateFormat:$e,invalidDate:Xe,ordinal:Ke,dayOfMonthOrdinalParse:et,relativeTime:it,months:vi,monthsShort:xi,week:Hi,weekdays:Gi,weekdaysMin:$i,weekdaysShort:Bi,meridiemParse:ma};var xa={};var ya={};var _a;function wa(e){return e?e.toLowerCase().replace("_","-"):e}function ba(e){var t=0,i,a,n,s;while(t<e.length){s=wa(e[t]).split("-");i=s.length;a=wa(e[t+1]);a=a?a.split("-"):null;while(i>0){n=ka(s.slice(0,i).join("-"));if(n){return n}if(a&&a.length>=i&&He(s,a,true)>=i-1){break}i--}t++}return _a}function ka(e){var t=null;if(!xa[e]&&typeof module!=="undefined"&&module&&module.exports){try{t=_a._abbr;var i=require;i("./locale/"+e);Ma(t)}catch(a){}}return xa[e]}function Ma(e,t){var i;if(e){if(xe(t)){i=Ya(e)}else{i=Sa(e,t)}if(i){_a=i}else{if(typeof console!=="undefined"&&console.warn){console.warn("Locale "+e+" not found. Did you forget to load it?")}}}return _a._abbr}function Sa(e,t){if(t!==null){var i,a=ga;t.abbr=e;if(xa[e]!=null){Ae("defineLocaleOverride","use moment.updateLocale(localeName, config) to change "+"an existing locale. moment.defineLocale(localeName, "+"config) should only be used for creating a new locale "+"See http://momentjs.com/guides/#/warnings/define-locale/ for more info.");a=xa[e]._config}else if(t.parentLocale!=null){if(xa[t.parentLocale]!=null){a=xa[t.parentLocale]._config}else{i=ka(t.parentLocale);if(i!=null){a=i._config}else{if(!ya[t.parentLocale]){ya[t.parentLocale]=[]}ya[t.parentLocale].push({name:e,config:t});return null}}}xa[e]=new Ge(Ve(a,t));if(ya[e]){ya[e].forEach((function(e){Sa(e.name,e.config)}))}Ma(e);return xa[e]}else{delete xa[e];return null}}function Da(e,t){if(t!=null){var i,a,n=ga;a=ka(e);if(a!=null){n=a._config}t=Ve(n,t);i=new Ge(t);i.parentLocale=xa[e];xa[e]=i;Ma(e)}else{if(xa[e]!=null){if(xa[e].parentLocale!=null){xa[e]=xa[e].parentLocale}else if(xa[e]!=null){delete xa[e]}}}return xa[e]}function Ya(e){var t;if(e&&e._locale&&e._locale._abbr){e=e._locale._abbr}if(!e){return _a}if(!he(e)){t=ka(e);if(t){return t}e=[e]}return ba(e)}function Oa(){return Ie(xa)}function ja(e){var t;var i=e._a;if(i&&De(e).overflow===-2){t=i[$t]<0||i[$t]>11?$t:i[Jt]<1||i[Jt]>mi(i[Zt],i[$t])?Jt:i[Xt]<0||i[Xt]>24||i[Xt]===24&&(i[Qt]!==0||i[Kt]!==0||i[ei]!==0)?Xt:i[Qt]<0||i[Qt]>59?Qt:i[Kt]<0||i[Kt]>59?Kt:i[ei]<0||i[ei]>999?ei:-1;if(De(e)._overflowDayOfYear&&(t<Zt||t>Jt)){t=Jt}if(De(e)._overflowWeeks&&t===-1){t=ti}if(De(e)._overflowWeekday&&t===-1){t=ii}De(e).overflow=t}return e}function Ta(e,t,i){if(e!=null){return e}if(t!=null){return t}return i}function Ca(e){var t=new Date(fe.now());if(e._useUTC){return[t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate()]}return[t.getFullYear(),t.getMonth(),t.getDate()]}function za(e){var t,i,a=[],n,s,r;if(e._d){return}n=Ca(e);if(e._w&&e._a[Jt]==null&&e._a[$t]==null){Pa(e)}if(e._dayOfYear!=null){r=Ta(e._a[Zt],n[Zt]);if(e._dayOfYear>ai(r)||e._dayOfYear===0){De(e)._overflowDayOfYear=true}i=Ci(r,0,e._dayOfYear);e._a[$t]=i.getUTCMonth();e._a[Jt]=i.getUTCDate()}for(t=0;t<3&&e._a[t]==null;++t){e._a[t]=a[t]=n[t]}for(;t<7;t++){e._a[t]=a[t]=e._a[t]==null?t===2?1:0:e._a[t]}if(e._a[Xt]===24&&e._a[Qt]===0&&e._a[Kt]===0&&e._a[ei]===0){e._nextDay=true;e._a[Xt]=0}e._d=(e._useUTC?Ci:Ti).apply(null,a);s=e._useUTC?e._d.getUTCDay():e._d.getDay();if(e._tzm!=null){e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm)}if(e._nextDay){e._a[Xt]=24}if(e._w&&typeof e._w.d!=="undefined"&&e._w.d!==s){De(e).weekdayMismatch=true}}function Pa(e){var t,i,a,n,s,r,o,l;t=e._w;if(t.GG!=null||t.W!=null||t.E!=null){s=1;r=4;i=Ta(t.GG,e._a[Zt],Li(sn(),1,4).year);a=Ta(t.W,1);n=Ta(t.E,1);if(n<1||n>7){l=true}}else{s=e._locale._week.dow;r=e._locale._week.doy;var c=Li(sn(),s,r);i=Ta(t.gg,e._a[Zt],c.year);a=Ta(t.w,c.week);if(t.d!=null){n=t.d;if(n<0||n>6){l=true}}else if(t.e!=null){n=t.e+s;if(t.e<0||t.e>6){l=true}}else{n=s}}if(a<1||a>Ri(i,s,r)){De(e)._overflowWeeks=true}else if(l!=null){De(e)._overflowWeekday=true}else{o=Pi(i,a,n,s,r);e._a[Zt]=o.year;e._dayOfYear=o.dayOfYear}}var La=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?$/;var Ra=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?$/;var Ea=/Z|[+-]\d\d(?::?\d\d)?/;var Ha=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,false],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,false],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,false],["YYYYDDD",/\d{7}/]];var Na=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]];var Ua=/^\/?Date\((\-?\d+)/i;function Wa(e){var t,i,a=e._i,n=La.exec(a)||Ra.exec(a),s,r,o,l;if(n){De(e).iso=true;for(t=0,i=Ha.length;t<i;t++){if(Ha[t][1].exec(n[1])){r=Ha[t][0];s=Ha[t][2]!==false;break}}if(r==null){e._isValid=false;return}if(n[3]){for(t=0,i=Na.length;t<i;t++){if(Na[t][1].exec(n[3])){o=(n[2]||" ")+Na[t][0];break}}if(o==null){e._isValid=false;return}}if(!s&&o!=null){e._isValid=false;return}if(n[4]){if(Ea.exec(n[4])){l="Z"}else{e._isValid=false;return}}e._f=r+(o||"")+(l||"");Ja(e)}else{e._isValid=false}}var Aa=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/;function Fa(e,t,i,a,n,s){var r=[qa(e),xi.indexOf(t),parseInt(i,10),parseInt(a,10),parseInt(n,10)];if(s){r.push(parseInt(s,10))}return r}function qa(e){var t=parseInt(e,10);if(t<=49){return 2e3+t}else if(t<=999){return 1900+t}return t}function Va(e){return e.replace(/\([^)]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,"")}function Ga(e,t,i){if(e){var a=Bi.indexOf(e),n=new Date(t[0],t[1],t[2]).getDay();if(a!==n){De(i).weekdayMismatch=true;i._isValid=false;return false}}return true}var Ia={UT:0,GMT:0,EDT:-4*60,EST:-5*60,CDT:-5*60,CST:-6*60,MDT:-6*60,MST:-7*60,PDT:-7*60,PST:-8*60};function Ba(e,t,i){if(e){return Ia[e]}else if(t){return 0}else{var a=parseInt(i,10);var n=a%100,s=(a-n)/100;return s*60+n}}function Za(e){var t=Aa.exec(Va(e._i));if(t){var i=Fa(t[4],t[3],t[2],t[5],t[6],t[7]);if(!Ga(t[1],i,e)){return}e._a=i;e._tzm=Ba(t[8],t[9],t[10]);e._d=Ci.apply(null,e._a);e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm);De(e).rfc2822=true}else{e._isValid=false}}function $a(e){var t=Ua.exec(e._i);if(t!==null){e._d=new Date(+t[1]);return}Wa(e);if(e._isValid===false){delete e._isValid}else{return}Za(e);if(e._isValid===false){delete e._isValid}else{return}fe.createFromInputFallback(e)}fe.createFromInputFallback=Ue("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), "+"which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are "+"discouraged and will be removed in an upcoming major release. Please refer to "+"http://momentjs.com/guides/#/warnings/js-date/ for more info.",(function(e){e._d=new Date(e._i+(e._useUTC?" UTC":""))}));fe.ISO_8601=function(){};fe.RFC_2822=function(){};function Ja(e){if(e._f===fe.ISO_8601){Wa(e);return}if(e._f===fe.RFC_2822){Za(e);return}e._a=[];De(e).empty=true;var t=""+e._i,i,a,n,s,r,o=t.length,l=0;n=wt(e._f,e._locale).match(ft)||[];for(i=0;i<n.length;i++){s=n[i];a=(t.match(At(s,e))||[])[0];if(a){r=t.substr(0,t.indexOf(a));if(r.length>0){De(e).unusedInput.push(r)}t=t.slice(t.indexOf(a)+a.length);l+=a.length}if(vt[s]){if(a){De(e).empty=false}else{De(e).unusedTokens.push(s)}Bt(s,a,e)}else if(e._strict&&!a){De(e).unusedTokens.push(s)}}De(e).charsLeftOver=o-l;if(t.length>0){De(e).unusedInput.push(t)}if(e._a[Xt]<=12&&De(e).bigHour===true&&e._a[Xt]>0){De(e).bigHour=undefined}De(e).parsedDateParts=e._a.slice(0);De(e).meridiem=e._meridiem;e._a[Xt]=Xa(e._locale,e._a[Xt],e._meridiem);za(e);ja(e)}function Xa(e,t,i){var a;if(i==null){return t}if(e.meridiemHour!=null){return e.meridiemHour(t,i)}else if(e.isPM!=null){a=e.isPM(i);if(a&&t<12){t+=12}if(!a&&t===12){t=0}return t}else{return t}}function Qa(e){var t,i,a,n,s;if(e._f.length===0){De(e).invalidFormat=true;e._d=new Date(NaN);return}for(n=0;n<e._f.length;n++){s=0;t=Ce({},e);if(e._useUTC!=null){t._useUTC=e._useUTC}t._f=e._f[n];Ja(t);if(!Oe(t)){continue}s+=De(t).charsLeftOver;s+=De(t).unusedTokens.length*10;De(t).score=s;if(a==null||s<a){a=s;i=t}}ke(e,i||t)}function Ka(e){if(e._d){return}var t=lt(e._i);e._a=we([t.year,t.month,t.day||t.date,t.hour,t.minute,t.second,t.millisecond],(function(e){return e&&parseInt(e,10)}));za(e)}function en(e){var t=new Pe(ja(tn(e)));if(t._nextDay){t.add(1,"d");t._nextDay=undefined}return t}function tn(e){var t=e._i,i=e._f;e._locale=e._locale||Ya(e._l);if(t===null||i===undefined&&t===""){return je({nullInput:true})}if(typeof t==="string"){e._i=t=e._locale.preparse(t)}if(Le(t)){return new Pe(ja(t))}else if(_e(t)){e._d=t}else if(he(i)){Qa(e)}else if(i){Ja(e)}else{an(e)}if(!Oe(e)){e._d=null}return e}function an(e){var t=e._i;if(xe(t)){e._d=new Date(fe.now())}else if(_e(t)){e._d=new Date(t.valueOf())}else if(typeof t==="string"){$a(e)}else if(he(t)){e._a=we(t.slice(0),(function(e){return parseInt(e,10)}));za(e)}else if(ve(t)){Ka(e)}else if(ye(t)){e._d=new Date(t)}else{fe.createFromInputFallback(e)}}function nn(e,t,i,a,n){var s={};if(i===true||i===false){a=i;i=undefined}if(ve(e)&&ge(e)||he(e)&&e.length===0){e=undefined}s._isAMomentObject=true;s._useUTC=s._isUTC=n;s._l=i;s._i=e;s._f=t;s._strict=a;return en(s)}function sn(e,t,i,a){return nn(e,t,i,a,false)}var rn=Ue("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",(function(){var e=sn.apply(null,arguments);if(this.isValid()&&e.isValid()){return e<this?this:e}else{return je()}}));var on=Ue("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",(function(){var e=sn.apply(null,arguments);if(this.isValid()&&e.isValid()){return e>this?this:e}else{return je()}}));function ln(e,t){var i,a;if(t.length===1&&he(t[0])){t=t[0]}if(!t.length){return sn()}i=t[0];for(a=1;a<t.length;++a){if(!t[a].isValid()||t[a][e](i)){i=t[a]}}return i}function cn(){var e=[].slice.call(arguments,0);return ln("isBefore",e)}function pn(){var e=[].slice.call(arguments,0);return ln("isAfter",e)}var dn=function(){return Date.now?Date.now():+new Date};var un=["year","quarter","month","week","day","hour","minute","second","millisecond"];function fn(e){for(var t in e){if(!(fi.call(un,t)!==-1&&(e[t]==null||!isNaN(e[t])))){return false}}var i=false;for(var a=0;a<un.length;++a){if(e[un[a]]){if(i){return false}if(parseFloat(e[un[a]])!==Ee(e[un[a]])){i=true}}}return true}function mn(){return this._isValid}function hn(){return Hn(NaN)}function vn(e){var t=lt(e),i=t.year||0,a=t.quarter||0,n=t.month||0,s=t.week||t.isoWeek||0,r=t.day||0,o=t.hour||0,l=t.minute||0,c=t.second||0,p=t.millisecond||0;this._isValid=fn(t);this._milliseconds=+p+c*1e3+l*6e4+o*1e3*60*60;this._days=+r+s*7;this._months=+n+a*3+i*12;this._data={};this._locale=Ya();this._bubble()}function gn(e){return e instanceof vn}function xn(e){if(e<0){return Math.round(-1*e)*-1}else{return Math.round(e)}}function yn(e,t){gt(e,0,0,(function(){var e=this.utcOffset();var i="+";if(e<0){e=-e;i="-"}return i+ut(~~(e/60),2)+t+ut(~~e%60,2)}))}yn("Z",":");yn("ZZ","");Wt("Z",Et);Wt("ZZ",Et);Gt(["Z","ZZ"],(function(e,t,i){i._useUTC=true;i._tzm=wn(Et,e)}));var _n=/([\+\-]|\d\d)/gi;function wn(e,t){var i=(t||"").match(e);if(i===null){return null}var a=i[i.length-1]||[];var n=(a+"").match(_n)||["-",0,0];var s=+(n[1]*60)+Ee(n[2]);return s===0?0:n[0]==="+"?s:-s}function bn(e,t){var i,a;if(t._isUTC){i=t.clone();a=(Le(e)||_e(e)?e.valueOf():sn(e).valueOf())-i.valueOf();i._d.setTime(i._d.valueOf()+a);fe.updateOffset(i,false);return i}else{return sn(e).local()}}function kn(e){return-Math.round(e._d.getTimezoneOffset()/15)*15}fe.updateOffset=function(){};function Mn(e,t,i){var a=this._offset||0,n;if(!this.isValid()){return e!=null?this:NaN}if(e!=null){if(typeof e==="string"){e=wn(Et,e);if(e===null){return this}}else if(Math.abs(e)<16&&!i){e=e*60}if(!this._isUTC&&t){n=kn(this)}this._offset=e;this._isUTC=true;if(n!=null){this.add(n,"m")}if(a!==e){if(!t||this._changeInProgress){Fn(this,Hn(e-a,"m"),1,false)}else if(!this._changeInProgress){this._changeInProgress=true;fe.updateOffset(this,true);this._changeInProgress=null}}return this}else{return this._isUTC?a:kn(this)}}function Sn(e,t){if(e!=null){if(typeof e!=="string"){e=-e}this.utcOffset(e,t);return this}else{return-this.utcOffset()}}function Dn(e){return this.utcOffset(0,e)}function Yn(e){if(this._isUTC){this.utcOffset(0,e);this._isUTC=false;if(e){this.subtract(kn(this),"m")}}return this}function On(){if(this._tzm!=null){this.utcOffset(this._tzm,false,true)}else if(typeof this._i==="string"){var e=wn(Rt,this._i);if(e!=null){this.utcOffset(e)}else{this.utcOffset(0,true)}}return this}function jn(e){if(!this.isValid()){return false}e=e?sn(e).utcOffset():0;return(this.utcOffset()-e)%60===0}function Tn(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()}function Cn(){if(!xe(this._isDSTShifted)){return this._isDSTShifted}var e={};Ce(e,this);e=tn(e);if(e._a){var t=e._isUTC?Me(e._a):sn(e._a);this._isDSTShifted=this.isValid()&&He(e._a,t.toArray())>0}else{this._isDSTShifted=false}return this._isDSTShifted}function zn(){return this.isValid()?!this._isUTC:false}function Pn(){return this.isValid()?this._isUTC:false}function Ln(){return this.isValid()?this._isUTC&&this._offset===0:false}var Rn=/^(\-|\+)?(?:(\d*)[. ])?(\d+)\:(\d+)(?:\:(\d+)(\.\d*)?)?$/;var En=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function Hn(e,t){var i=e,a=null,n,s,r;if(gn(e)){i={ms:e._milliseconds,d:e._days,M:e._months}}else if(ye(e)){i={};if(t){i[t]=e}else{i.milliseconds=e}}else if(!!(a=Rn.exec(e))){n=a[1]==="-"?-1:1;i={y:0,d:Ee(a[Jt])*n,h:Ee(a[Xt])*n,m:Ee(a[Qt])*n,s:Ee(a[Kt])*n,ms:Ee(xn(a[ei]*1e3))*n}}else if(!!(a=En.exec(e))){n=a[1]==="-"?-1:1;i={y:Nn(a[2],n),M:Nn(a[3],n),w:Nn(a[4],n),d:Nn(a[5],n),h:Nn(a[6],n),m:Nn(a[7],n),s:Nn(a[8],n)}}else if(i==null){i={}}else if(typeof i==="object"&&("from"in i||"to"in i)){r=Wn(sn(i.from),sn(i.to));i={};i.ms=r.milliseconds;i.M=r.months}s=new vn(i);if(gn(e)&&be(e,"_locale")){s._locale=e._locale}return s}Hn.fn=vn.prototype;Hn.invalid=hn;function Nn(e,t){var i=e&&parseFloat(e.replace(",","."));return(isNaN(i)?0:i)*t}function Un(e,t){var i={};i.months=t.month()-e.month()+(t.year()-e.year())*12;if(e.clone().add(i.months,"M").isAfter(t)){--i.months}i.milliseconds=+t-+e.clone().add(i.months,"M");return i}function Wn(e,t){var i;if(!(e.isValid()&&t.isValid())){return{milliseconds:0,months:0}}t=bn(t,e);if(e.isBefore(t)){i=Un(e,t)}else{i=Un(t,e);i.milliseconds=-i.milliseconds;i.months=-i.months}return i}function An(e,t){return function(i,a){var n,s;if(a!==null&&!isNaN(+a)){Ae(t,"moment()."+t+"(period, number) is deprecated. Please use moment()."+t+"(number, period). "+"See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info.");s=i;i=a;a=s}i=typeof i==="string"?+i:i;n=Hn(i,a);Fn(this,n,e);return this}}function Fn(e,t,i,a){var n=t._milliseconds,s=xn(t._days),r=xn(t._months);if(!e.isValid()){return}a=a==null?true:a;if(r){bi(e,li(e,"Month")+r*i)}if(s){ci(e,"Date",li(e,"Date")+s*i)}if(n){e._d.setTime(e._d.valueOf()+n*i)}if(a){fe.updateOffset(e,s||r)}}var qn=An(1,"add");var Vn=An(-1,"subtract");function Gn(e,t){var i=e.diff(t,"days",true);return i<-6?"sameElse":i<-1?"lastWeek":i<0?"lastDay":i<1?"sameDay":i<2?"nextDay":i<7?"nextWeek":"sameElse"}function In(e,t){var i=e||sn(),a=bn(i,this).startOf("day"),n=fe.calendarFormat(this,a)||"sameElse";var s=t&&(Fe(t[n])?t[n].call(this,i):t[n]);return this.format(s||this.localeData().calendar(n,this,sn(i)))}function Bn(){return new Pe(this)}function Zn(e,t){var i=Le(e)?e:sn(e);if(!(this.isValid()&&i.isValid())){return false}t=ot(t)||"millisecond";if(t==="millisecond"){return this.valueOf()>i.valueOf()}else{return i.valueOf()<this.clone().startOf(t).valueOf()}}function $n(e,t){var i=Le(e)?e:sn(e);if(!(this.isValid()&&i.isValid())){return false}t=ot(t)||"millisecond";if(t==="millisecond"){return this.valueOf()<i.valueOf()}else{return this.clone().endOf(t).valueOf()<i.valueOf()}}function Jn(e,t,i,a){var n=Le(e)?e:sn(e),s=Le(t)?t:sn(t);if(!(this.isValid()&&n.isValid()&&s.isValid())){return false}a=a||"()";return(a[0]==="("?this.isAfter(n,i):!this.isBefore(n,i))&&(a[1]===")"?this.isBefore(s,i):!this.isAfter(s,i))}function Xn(e,t){var i=Le(e)?e:sn(e),a;if(!(this.isValid()&&i.isValid())){return false}t=ot(t)||"millisecond";if(t==="millisecond"){return this.valueOf()===i.valueOf()}else{a=i.valueOf();return this.clone().startOf(t).valueOf()<=a&&a<=this.clone().endOf(t).valueOf()}}function Qn(e,t){return this.isSame(e,t)||this.isAfter(e,t)}function Kn(e,t){return this.isSame(e,t)||this.isBefore(e,t)}function es(e,t,i){var a,n,s;if(!this.isValid()){return NaN}a=bn(e,this);if(!a.isValid()){return NaN}n=(a.utcOffset()-this.utcOffset())*6e4;t=ot(t);switch(t){case"year":s=ts(this,a)/12;break;case"month":s=ts(this,a);break;case"quarter":s=ts(this,a)/3;break;case"second":s=(this-a)/1e3;break;case"minute":s=(this-a)/6e4;break;case"hour":s=(this-a)/36e5;break;case"day":s=(this-a-n)/864e5;break;case"week":s=(this-a-n)/6048e5;break;default:s=this-a}return i?s:Re(s)}function ts(e,t){var i=(t.year()-e.year())*12+(t.month()-e.month()),a=e.clone().add(i,"months"),n,s;if(t-a<0){n=e.clone().add(i-1,"months");s=(t-a)/(a-n)}else{n=e.clone().add(i+1,"months");s=(t-a)/(n-a)}return-(i+s)||0}fe.defaultFormat="YYYY-MM-DDTHH:mm:ssZ";fe.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";function is(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")}function as(e){if(!this.isValid()){return null}var t=e!==true;var i=t?this.clone().utc():this;if(i.year()<0||i.year()>9999){return _t(i,t?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ")}if(Fe(Date.prototype.toISOString)){if(t){return this.toDate().toISOString()}else{return new Date(this.valueOf()+this.utcOffset()*60*1e3).toISOString().replace("Z",_t(i,"Z"))}}return _t(i,t?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ")}function ns(){if(!this.isValid()){return"moment.invalid(/* "+this._i+" */)"}var e="moment";var t="";if(!this.isLocal()){e=this.utcOffset()===0?"moment.utc":"moment.parseZone";t="Z"}var i="["+e+'("]';var a=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY";var n="-MM-DD[T]HH:mm:ss.SSS";var s=t+'[")]';return this.format(i+a+n+s)}function ss(e){if(!e){e=this.isUtc()?fe.defaultFormatUtc:fe.defaultFormat}var t=_t(this,e);return this.localeData().postformat(t)}function rs(e,t){if(this.isValid()&&(Le(e)&&e.isValid()||sn(e).isValid())){return Hn({to:this,from:e}).locale(this.locale()).humanize(!t)}else{return this.localeData().invalidDate()}}function os(e){return this.from(sn(),e)}function ls(e,t){if(this.isValid()&&(Le(e)&&e.isValid()||sn(e).isValid())){return Hn({from:this,to:e}).locale(this.locale()).humanize(!t)}else{return this.localeData().invalidDate()}}function cs(e){return this.to(sn(),e)}function ps(e){var t;if(e===undefined){return this._locale._abbr}else{t=Ya(e);if(t!=null){this._locale=t}return this}}var ds=Ue("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",(function(e){if(e===undefined){return this.localeData()}else{return this.locale(e)}}));function us(){return this._locale}var fs=1e3;var ms=60*fs;var hs=60*ms;var vs=(365*400+97)*24*hs;function gs(e,t){return(e%t+t)%t}function xs(e,t,i){if(e<100&&e>=0){return new Date(e+400,t,i)-vs}else{return new Date(e,t,i).valueOf()}}function ys(e,t,i){if(e<100&&e>=0){return Date.UTC(e+400,t,i)-vs}else{return Date.UTC(e,t,i)}}function _s(e){var t;e=ot(e);if(e===undefined||e==="millisecond"||!this.isValid()){return this}var i=this._isUTC?ys:xs;switch(e){case"year":t=i(this.year(),0,1);break;case"quarter":t=i(this.year(),this.month()-this.month()%3,1);break;case"month":t=i(this.year(),this.month(),1);break;case"week":t=i(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":t=i(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":t=i(this.year(),this.month(),this.date());break;case"hour":t=this._d.valueOf();t-=gs(t+(this._isUTC?0:this.utcOffset()*ms),hs);break;case"minute":t=this._d.valueOf();t-=gs(t,ms);break;case"second":t=this._d.valueOf();t-=gs(t,fs);break}this._d.setTime(t);fe.updateOffset(this,true);return this}function ws(e){var t;e=ot(e);if(e===undefined||e==="millisecond"||!this.isValid()){return this}var i=this._isUTC?ys:xs;switch(e){case"year":t=i(this.year()+1,0,1)-1;break;case"quarter":t=i(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":t=i(this.year(),this.month()+1,1)-1;break;case"week":t=i(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":t=i(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":t=i(this.year(),this.month(),this.date()+1)-1;break;case"hour":t=this._d.valueOf();t+=hs-gs(t+(this._isUTC?0:this.utcOffset()*ms),hs)-1;break;case"minute":t=this._d.valueOf();t+=ms-gs(t,ms)-1;break;case"second":t=this._d.valueOf();t+=fs-gs(t,fs)-1;break}this._d.setTime(t);fe.updateOffset(this,true);return this}function bs(){return this._d.valueOf()-(this._offset||0)*6e4}function ks(){return Math.floor(this.valueOf()/1e3)}function Ms(){return new Date(this.valueOf())}function Ss(){var e=this;return[e.year(),e.month(),e.date(),e.hour(),e.minute(),e.second(),e.millisecond()]}function Ds(){var e=this;return{years:e.year(),months:e.month(),date:e.date(),hours:e.hours(),minutes:e.minutes(),seconds:e.seconds(),milliseconds:e.milliseconds()}}function Ys(){return this.isValid()?this.toISOString():null}function Os(){return Oe(this)}function js(){return ke({},De(this))}function Ts(){return De(this).overflow}function Cs(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}}gt(0,["gg",2],0,(function(){return this.weekYear()%100}));gt(0,["GG",2],0,(function(){return this.isoWeekYear()%100}));function zs(e,t){gt(0,[e,e.length],0,t)}zs("gggg","weekYear");zs("ggggg","weekYear");zs("GGGG","isoWeekYear");zs("GGGGG","isoWeekYear");rt("weekYear","gg");rt("isoWeekYear","GG");pt("weekYear",1);pt("isoWeekYear",1);Wt("G",Lt);Wt("g",Lt);Wt("GG",Yt,kt);Wt("gg",Yt,kt);Wt("GGGG",Ct,St);Wt("gggg",Ct,St);Wt("GGGGG",zt,Dt);Wt("ggggg",zt,Dt);It(["gggg","ggggg","GGGG","GGGGG"],(function(e,t,i,a){t[a.substr(0,2)]=Ee(e)}));It(["gg","GG"],(function(e,t,i,a){t[a]=fe.parseTwoDigitYear(e)}));function Ps(e){return Hs.call(this,e,this.week(),this.weekday(),this.localeData()._week.dow,this.localeData()._week.doy)}function Ls(e){return Hs.call(this,e,this.isoWeek(),this.isoWeekday(),1,4)}function Rs(){return Ri(this.year(),1,4)}function Es(){var e=this.localeData()._week;return Ri(this.year(),e.dow,e.doy)}function Hs(e,t,i,a,n){var s;if(e==null){return Li(this,a,n).year}else{s=Ri(e,a,n);if(t>s){t=s}return Ns.call(this,e,t,i,a,n)}}function Ns(e,t,i,a,n){var s=Pi(e,t,i,a,n),r=Ci(s.year,0,s.dayOfYear);this.year(r.getUTCFullYear());this.month(r.getUTCMonth());this.date(r.getUTCDate());return this}gt("Q",0,"Qo","quarter");rt("quarter","Q");pt("quarter",7);Wt("Q",bt);Gt("Q",(function(e,t){t[$t]=(Ee(e)-1)*3}));function Us(e){return e==null?Math.ceil((this.month()+1)/3):this.month((e-1)*3+this.month()%3)}gt("D",["DD",2],"Do","date");rt("date","D");pt("date",9);Wt("D",Yt);Wt("DD",Yt,kt);Wt("Do",(function(e,t){return e?t._dayOfMonthOrdinalParse||t._ordinalParse:t._dayOfMonthOrdinalParseLenient}));Gt(["D","DD"],Jt);Gt("Do",(function(e,t){t[Jt]=Ee(e.match(Yt)[0])}));var Ws=oi("Date",true);gt("DDD",["DDDD",3],"DDDo","dayOfYear");rt("dayOfYear","DDD");pt("dayOfYear",4);Wt("DDD",Tt);Wt("DDDD",Mt);Gt(["DDD","DDDD"],(function(e,t,i){i._dayOfYear=Ee(e)}));function As(e){var t=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return e==null?t:this.add(e-t,"d")}gt("m",["mm",2],0,"minute");rt("minute","m");pt("minute",14);Wt("m",Yt);Wt("mm",Yt,kt);Gt(["m","mm"],Qt);var Fs=oi("Minutes",false);gt("s",["ss",2],0,"second");rt("second","s");pt("second",15);Wt("s",Yt);Wt("ss",Yt,kt);Gt(["s","ss"],Kt);var qs=oi("Seconds",false);gt("S",0,0,(function(){return~~(this.millisecond()/100)}));gt(0,["SS",2],0,(function(){return~~(this.millisecond()/10)}));gt(0,["SSS",3],0,"millisecond");gt(0,["SSSS",4],0,(function(){return this.millisecond()*10}));gt(0,["SSSSS",5],0,(function(){return this.millisecond()*100}));gt(0,["SSSSSS",6],0,(function(){return this.millisecond()*1e3}));gt(0,["SSSSSSS",7],0,(function(){return this.millisecond()*1e4}));gt(0,["SSSSSSSS",8],0,(function(){return this.millisecond()*1e5}));gt(0,["SSSSSSSSS",9],0,(function(){return this.millisecond()*1e6}));rt("millisecond","ms");pt("millisecond",16);Wt("S",Tt,bt);Wt("SS",Tt,kt);Wt("SSS",Tt,Mt);var Vs;for(Vs="SSSS";Vs.length<=9;Vs+="S"){Wt(Vs,Pt)}function Gs(e,t){t[ei]=Ee(("0."+e)*1e3)}for(Vs="S";Vs.length<=9;Vs+="S"){Gt(Vs,Gs)}var Is=oi("Milliseconds",false);gt("z",0,0,"zoneAbbr");gt("zz",0,0,"zoneName");function Bs(){return this._isUTC?"UTC":""}function Zs(){return this._isUTC?"Coordinated Universal Time":""}var $s=Pe.prototype;$s.add=qn;$s.calendar=In;$s.clone=Bn;$s.diff=es;$s.endOf=ws;$s.format=ss;$s.from=rs;$s.fromNow=os;$s.to=ls;$s.toNow=cs;$s.get=pi;$s.invalidAt=Ts;$s.isAfter=Zn;$s.isBefore=$n;$s.isBetween=Jn;$s.isSame=Xn;$s.isSameOrAfter=Qn;$s.isSameOrBefore=Kn;$s.isValid=Os;$s.lang=ds;$s.locale=ps;$s.localeData=us;$s.max=on;$s.min=rn;$s.parsingFlags=js;$s.set=di;$s.startOf=_s;$s.subtract=Vn;$s.toArray=Ss;$s.toObject=Ds;$s.toDate=Ms;$s.toISOString=as;$s.inspect=ns;$s.toJSON=Ys;$s.toString=is;$s.unix=ks;$s.valueOf=bs;$s.creationData=Cs;$s.year=si;$s.isLeapYear=ri;$s.weekYear=Ps;$s.isoWeekYear=Ls;$s.quarter=$s.quarters=Us;$s.month=ki;$s.daysInMonth=Mi;$s.week=$s.weeks=Wi;$s.isoWeek=$s.isoWeeks=Ai;$s.weeksInYear=Es;$s.isoWeeksInYear=Rs;$s.date=Ws;$s.day=$s.days=Ki;$s.weekday=ea;$s.isoWeekday=ta;$s.dayOfYear=As;$s.hour=$s.hours=va;$s.minute=$s.minutes=Fs;$s.second=$s.seconds=qs;$s.millisecond=$s.milliseconds=Is;$s.utcOffset=Mn;$s.utc=Dn;$s.local=Yn;$s.parseZone=On;$s.hasAlignedHourOffset=jn;$s.isDST=Tn;$s.isLocal=zn;$s.isUtcOffset=Pn;$s.isUtc=Ln;$s.isUTC=Ln;$s.zoneAbbr=Bs;$s.zoneName=Zs;$s.dates=Ue("dates accessor is deprecated. Use date instead.",Ws);$s.months=Ue("months accessor is deprecated. Use month instead",ki);$s.years=Ue("years accessor is deprecated. Use year instead",si);$s.zone=Ue("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",Sn);$s.isDSTShifted=Ue("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",Cn);function Js(e){return sn(e*1e3)}function Xs(){return sn.apply(null,arguments).parseZone()}function Qs(e){return e}var Ks=Ge.prototype;Ks.calendar=Ze;Ks.longDateFormat=Je;Ks.invalidDate=Qe;Ks.ordinal=tt;Ks.preparse=Qs;Ks.postformat=Qs;Ks.relativeTime=at;Ks.pastFuture=nt;Ks.set=qe;Ks.months=gi;Ks.monthsShort=yi;Ks.monthsParse=wi;Ks.monthsRegex=Oi;Ks.monthsShortRegex=Di;Ks.week=Ei;Ks.firstDayOfYear=Ui;Ks.firstDayOfWeek=Ni;Ks.weekdays=Ii;Ks.weekdaysMin=Ji;Ks.weekdaysShort=Zi;Ks.weekdaysParse=Qi;Ks.weekdaysRegex=aa;Ks.weekdaysShortRegex=sa;Ks.weekdaysMinRegex=oa;Ks.isPM=fa;Ks.meridiem=ha;function er(e,t,i,a){var n=Ya();var s=Me().set(a,t);return n[i](s,e)}function tr(e,t,i){if(ye(e)){t=e;e=undefined}e=e||"";if(t!=null){return er(e,t,i,"month")}var a;var n=[];for(a=0;a<12;a++){n[a]=er(e,a,i,"month")}return n}function ir(e,t,i,a){if(typeof e==="boolean"){if(ye(t)){i=t;t=undefined}t=t||""}else{t=e;i=t;e=false;if(ye(t)){i=t;t=undefined}t=t||""}var n=Ya(),s=e?n._week.dow:0;if(i!=null){return er(t,(i+s)%7,a,"day")}var r;var o=[];for(r=0;r<7;r++){o[r]=er(t,(r+s)%7,a,"day")}return o}function ar(e,t){return tr(e,t,"months")}function nr(e,t){return tr(e,t,"monthsShort")}function sr(e,t,i){return ir(e,t,i,"weekdays")}function rr(e,t,i){return ir(e,t,i,"weekdaysShort")}function or(e,t,i){return ir(e,t,i,"weekdaysMin")}Ma("en",{dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(e){var t=e%10,i=Ee(e%100/10)===1?"th":t===1?"st":t===2?"nd":t===3?"rd":"th";return e+i}});fe.lang=Ue("moment.lang is deprecated. Use moment.locale instead.",Ma);fe.langData=Ue("moment.langData is deprecated. Use moment.localeData instead.",Ya);var lr=Math.abs;function cr(){var e=this._data;this._milliseconds=lr(this._milliseconds);this._days=lr(this._days);this._months=lr(this._months);e.milliseconds=lr(e.milliseconds);e.seconds=lr(e.seconds);e.minutes=lr(e.minutes);e.hours=lr(e.hours);e.months=lr(e.months);e.years=lr(e.years);return this}function pr(e,t,i,a){var n=Hn(t,i);e._milliseconds+=a*n._milliseconds;e._days+=a*n._days;e._months+=a*n._months;return e._bubble()}function dr(e,t){return pr(this,e,t,1)}function ur(e,t){return pr(this,e,t,-1)}function fr(e){if(e<0){return Math.floor(e)}else{return Math.ceil(e)}}function mr(){var e=this._milliseconds;var t=this._days;var i=this._months;var a=this._data;var n,s,r,o,l;if(!(e>=0&&t>=0&&i>=0||e<=0&&t<=0&&i<=0)){e+=fr(vr(i)+t)*864e5;t=0;i=0}a.milliseconds=e%1e3;n=Re(e/1e3);a.seconds=n%60;s=Re(n/60);a.minutes=s%60;r=Re(s/60);a.hours=r%24;t+=Re(r/24);l=Re(hr(t));i+=l;t-=fr(vr(l));o=Re(i/12);i%=12;a.days=t;a.months=i;a.years=o;return this}function hr(e){return e*4800/146097}function vr(e){return e*146097/4800}function gr(e){if(!this.isValid()){return NaN}var t;var i;var a=this._milliseconds;e=ot(e);if(e==="month"||e==="quarter"||e==="year"){t=this._days+a/864e5;i=this._months+hr(t);switch(e){case"month":return i;case"quarter":return i/3;case"year":return i/12}}else{t=this._days+Math.round(vr(this._months));switch(e){case"week":return t/7+a/6048e5;case"day":return t+a/864e5;case"hour":return t*24+a/36e5;case"minute":return t*1440+a/6e4;case"second":return t*86400+a/1e3;case"millisecond":return Math.floor(t*864e5)+a;default:throw new Error("Unknown unit "+e)}}}function xr(){if(!this.isValid()){return NaN}return this._milliseconds+this._days*864e5+this._months%12*2592e6+Ee(this._months/12)*31536e6}function yr(e){return function(){return this.as(e)}}var _r=yr("ms");var wr=yr("s");var br=yr("m");var kr=yr("h");var Mr=yr("d");var Sr=yr("w");var Dr=yr("M");var Yr=yr("Q");var Or=yr("y");function jr(){return Hn(this)}function Tr(e){e=ot(e);return this.isValid()?this[e+"s"]():NaN}function Cr(e){return function(){return this.isValid()?this._data[e]:NaN}}var zr=Cr("milliseconds");var Pr=Cr("seconds");var Lr=Cr("minutes");var Rr=Cr("hours");var Er=Cr("days");var Hr=Cr("months");var Nr=Cr("years");function Ur(){return Re(this.days()/7)}var Wr=Math.round;var Ar={ss:44,s:45,m:45,h:22,d:26,M:11};function Fr(e,t,i,a,n){return n.relativeTime(t||1,!!i,e,a)}function qr(e,t,i){var a=Hn(e).abs();var n=Wr(a.as("s"));var s=Wr(a.as("m"));var r=Wr(a.as("h"));var o=Wr(a.as("d"));var l=Wr(a.as("M"));var c=Wr(a.as("y"));var p=n<=Ar.ss&&["s",n]||n<Ar.s&&["ss",n]||s<=1&&["m"]||s<Ar.m&&["mm",s]||r<=1&&["h"]||r<Ar.h&&["hh",r]||o<=1&&["d"]||o<Ar.d&&["dd",o]||l<=1&&["M"]||l<Ar.M&&["MM",l]||c<=1&&["y"]||["yy",c];p[2]=t;p[3]=+e>0;p[4]=i;return Fr.apply(null,p)}function Vr(e){if(e===undefined){return Wr}if(typeof e==="function"){Wr=e;return true}return false}function Gr(e,t){if(Ar[e]===undefined){return false}if(t===undefined){return Ar[e]}Ar[e]=t;if(e==="s"){Ar.ss=t-1}return true}function Ir(e){if(!this.isValid()){return this.localeData().invalidDate()}var t=this.localeData();var i=qr(this,!e,t);if(e){i=t.pastFuture(+this,i)}return t.postformat(i)}var Br=Math.abs;function Zr(e){return(e>0)-(e<0)||+e}function $r(){if(!this.isValid()){return this.localeData().invalidDate()}var e=Br(this._milliseconds)/1e3;var t=Br(this._days);var i=Br(this._months);var a,n,s;a=Re(e/60);n=Re(a/60);e%=60;a%=60;s=Re(i/12);i%=12;var r=s;var o=i;var l=t;var c=n;var p=a;var d=e?e.toFixed(3).replace(/\.?0+$/,""):"";var u=this.asSeconds();if(!u){return"P0D"}var f=u<0?"-":"";var m=Zr(this._months)!==Zr(u)?"-":"";var h=Zr(this._days)!==Zr(u)?"-":"";var v=Zr(this._milliseconds)!==Zr(u)?"-":"";return f+"P"+(r?m+r+"Y":"")+(o?m+o+"M":"")+(l?h+l+"D":"")+(c||p||d?"T":"")+(c?v+c+"H":"")+(p?v+p+"M":"")+(d?v+d+"S":"")}var Jr=vn.prototype;Jr.isValid=mn;Jr.abs=cr;Jr.add=dr;Jr.subtract=ur;Jr.as=gr;Jr.asMilliseconds=_r;Jr.asSeconds=wr;Jr.asMinutes=br;Jr.asHours=kr;Jr.asDays=Mr;Jr.asWeeks=Sr;Jr.asMonths=Dr;Jr.asQuarters=Yr;Jr.asYears=Or;Jr.valueOf=xr;Jr._bubble=mr;Jr.clone=jr;Jr.get=Tr;Jr.milliseconds=zr;Jr.seconds=Pr;Jr.minutes=Lr;Jr.hours=Rr;Jr.days=Er;Jr.weeks=Ur;Jr.months=Hr;Jr.years=Nr;Jr.humanize=Ir;Jr.toISOString=$r;Jr.toString=$r;Jr.toJSON=$r;Jr.locale=ps;Jr.localeData=us;Jr.toIsoString=Ue("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",$r);Jr.lang=ds;gt("X",0,0,"unix");gt("x",0,0,"valueOf");Wt("x",Lt);Wt("X",Ht);Gt("X",(function(e,t,i){i._d=new Date(parseFloat(e,10)*1e3)}));Gt("x",(function(e,t,i){i._d=new Date(Ee(e))}));fe.version="2.24.0";me(sn);fe.fn=$s;fe.min=cn;fe.max=pn;fe.now=dn;fe.utc=Me;fe.unix=Js;fe.months=ar;fe.isDate=_e;fe.locale=Ma;fe.invalid=je;fe.duration=Hn;fe.isMoment=Le;fe.weekdays=sr;fe.parseZone=Xs;fe.localeData=Ya;fe.isDuration=gn;fe.monthsShort=nr;fe.weekdaysMin=or;fe.defineLocale=Sa;fe.updateLocale=Da;fe.locales=Oa;fe.weekdaysShort=rr;fe.normalizeUnits=ot;fe.relativeTimeRounding=Vr;fe.relativeTimeThreshold=Gr;fe.calendarFormat=Gn;fe.prototype=$s;fe.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"};var Xr={fr:{send:"Soumission",redraft:"Rediscussion",new:"Nouvelle demande",refused:"Refusée",cancelled:"Annulation",validated:"Validation du devis",draft:""},de:{send:"Empfangen",redraft:"Neu diskutiert",new:"Neue Anfrage",refused:"Abgelehnt",cancelled:"Storniert",validated:"Validiert",draft:"In Bearbeitung"},en:{send:"Send",redraft:"Redraft",new:"New",refused:"Refused",cancelled:"Cancelled",validated:"Validated",draft:"Draft"},nl:{send:"Verzenden",redraft:"Herschrijven",new:"nieuw",refused:"Geweigerd",cancelled:"Geannuleerd",validated:"Gevalideerd",draft:"Concept"}};fe.defineLocale("fr",{months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.".split("_"),monthsParseExact:true,weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Aujourd’hui à] LT",nextDay:"[Demain à] LT",nextWeek:"dddd [à] LT",lastDay:"[Hier à] LT",lastWeek:"dddd [dernier à] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",ss:"%d secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},dayOfMonthOrdinalParse:/\d{1,2}(er|)/,ordinal:function(e,t){switch(t){case"D":return e+(e===1?"er":"");default:case"M":case"Q":case"DDD":case"d":return e+(e===1?"er":"e");case"w":case"W":return e+(e===1?"re":"e")}},week:{dow:1,doy:4}});function Qr(){this._types=Object.create(null);this._extensions=Object.create(null);for(var e=0;e<arguments.length;e++){this.define(arguments[e])}this.define=this.define.bind(this);this.getType=this.getType.bind(this);this.getExtension=this.getExtension.bind(this)}Qr.prototype.define=function(e,t){for(var i in e){var a=e[i].map((function(e){return e.toLowerCase()}));i=i.toLowerCase();for(var n=0;n<a.length;n++){var s=a[n];if(s[0]=="*"){continue}if(!t&&s in this._types){throw new Error('Attempt to change mapping for "'+s+'" extension from "'+this._types[s]+'" to "'+i+'". Pass `force=true` to allow this, otherwise remove "'+s+'" from the list of extensions for "'+i+'".')}this._types[s]=i}if(t||!this._extensions[i]){var s=a[0];this._extensions[i]=s[0]!="*"?s:s.substr(1)}}};Qr.prototype.getType=function(e){e=String(e);var t=e.replace(/^.*[/\\]/,"").toLowerCase();var i=t.replace(/^.*\./,"").toLowerCase();var a=t.length<e.length;var n=i.length<t.length-1;return(n||!a)&&this._types[i]||null};Qr.prototype.getExtension=function(e){e=/^\s*([^;\s]*)/.test(e)&&RegExp.$1;return e&&this._extensions[e.toLowerCase()]||null};var Kr=Qr;var eo={"application/andrew-inset":["ez"],"application/applixware":["aw"],"application/atom+xml":["atom"],"application/atomcat+xml":["atomcat"],"application/atomsvc+xml":["atomsvc"],"application/bdoc":["bdoc"],"application/ccxml+xml":["ccxml"],"application/cdmi-capability":["cdmia"],"application/cdmi-container":["cdmic"],"application/cdmi-domain":["cdmid"],"application/cdmi-object":["cdmio"],"application/cdmi-queue":["cdmiq"],"application/cu-seeme":["cu"],"application/dash+xml":["mpd"],"application/davmount+xml":["davmount"],"application/docbook+xml":["dbk"],"application/dssc+der":["dssc"],"application/dssc+xml":["xdssc"],"application/ecmascript":["ecma","es"],"application/emma+xml":["emma"],"application/epub+zip":["epub"],"application/exi":["exi"],"application/font-tdpfr":["pfr"],"application/geo+json":["geojson"],"application/gml+xml":["gml"],"application/gpx+xml":["gpx"],"application/gxf":["gxf"],"application/gzip":["gz"],"application/hjson":["hjson"],"application/hyperstudio":["stk"],"application/inkml+xml":["ink","inkml"],"application/ipfix":["ipfix"],"application/java-archive":["jar","war","ear"],"application/java-serialized-object":["ser"],"application/java-vm":["class"],"application/javascript":["js","mjs"],"application/json":["json","map"],"application/json5":["json5"],"application/jsonml+json":["jsonml"],"application/ld+json":["jsonld"],"application/lost+xml":["lostxml"],"application/mac-binhex40":["hqx"],"application/mac-compactpro":["cpt"],"application/mads+xml":["mads"],"application/manifest+json":["webmanifest"],"application/marc":["mrc"],"application/marcxml+xml":["mrcx"],"application/mathematica":["ma","nb","mb"],"application/mathml+xml":["mathml"],"application/mbox":["mbox"],"application/mediaservercontrol+xml":["mscml"],"application/metalink+xml":["metalink"],"application/metalink4+xml":["meta4"],"application/mets+xml":["mets"],"application/mods+xml":["mods"],"application/mp21":["m21","mp21"],"application/mp4":["mp4s","m4p"],"application/msword":["doc","dot"],"application/mxf":["mxf"],"application/n-quads":["nq"],"application/n-triples":["nt"],"application/octet-stream":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"],"application/oda":["oda"],"application/oebps-package+xml":["opf"],"application/ogg":["ogx"],"application/omdoc+xml":["omdoc"],"application/onenote":["onetoc","onetoc2","onetmp","onepkg"],"application/oxps":["oxps"],"application/patch-ops-error+xml":["xer"],"application/pdf":["pdf"],"application/pgp-encrypted":["pgp"],"application/pgp-signature":["asc","sig"],"application/pics-rules":["prf"],"application/pkcs10":["p10"],"application/pkcs7-mime":["p7m","p7c"],"application/pkcs7-signature":["p7s"],"application/pkcs8":["p8"],"application/pkix-attr-cert":["ac"],"application/pkix-cert":["cer"],"application/pkix-crl":["crl"],"application/pkix-pkipath":["pkipath"],"application/pkixcmp":["pki"],"application/pls+xml":["pls"],"application/postscript":["ai","eps","ps"],"application/pskc+xml":["pskcxml"],"application/raml+yaml":["raml"],"application/rdf+xml":["rdf","owl"],"application/reginfo+xml":["rif"],"application/relax-ng-compact-syntax":["rnc"],"application/resource-lists+xml":["rl"],"application/resource-lists-diff+xml":["rld"],"application/rls-services+xml":["rs"],"application/rpki-ghostbusters":["gbr"],"application/rpki-manifest":["mft"],"application/rpki-roa":["roa"],"application/rsd+xml":["rsd"],"application/rss+xml":["rss"],"application/rtf":["rtf"],"application/sbml+xml":["sbml"],"application/scvp-cv-request":["scq"],"application/scvp-cv-response":["scs"],"application/scvp-vp-request":["spq"],"application/scvp-vp-response":["spp"],"application/sdp":["sdp"],"application/set-payment-initiation":["setpay"],"application/set-registration-initiation":["setreg"],"application/shf+xml":["shf"],"application/sieve":["siv","sieve"],"application/smil+xml":["smi","smil"],"application/sparql-query":["rq"],"application/sparql-results+xml":["srx"],"application/srgs":["gram"],"application/srgs+xml":["grxml"],"application/sru+xml":["sru"],"application/ssdl+xml":["ssdl"],"application/ssml+xml":["ssml"],"application/tei+xml":["tei","teicorpus"],"application/thraud+xml":["tfi"],"application/timestamped-data":["tsd"],"application/voicexml+xml":["vxml"],"application/wasm":["wasm"],"application/widget":["wgt"],"application/winhlp":["hlp"],"application/wsdl+xml":["wsdl"],"application/wspolicy+xml":["wspolicy"],"application/xaml+xml":["xaml"],"application/xcap-diff+xml":["xdf"],"application/xenc+xml":["xenc"],"application/xhtml+xml":["xhtml","xht"],"application/xml":["xml","xsl","xsd","rng"],"application/xml-dtd":["dtd"],"application/xop+xml":["xop"],"application/xproc+xml":["xpl"],"application/xslt+xml":["xslt"],"application/xspf+xml":["xspf"],"application/xv+xml":["mxml","xhvml","xvml","xvm"],"application/yang":["yang"],"application/yin+xml":["yin"],"application/zip":["zip"],"audio/3gpp":["*3gpp"],"audio/adpcm":["adp"],"audio/basic":["au","snd"],"audio/midi":["mid","midi","kar","rmi"],"audio/mp3":["*mp3"],"audio/mp4":["m4a","mp4a"],"audio/mpeg":["mpga","mp2","mp2a","mp3","m2a","m3a"],"audio/ogg":["oga","ogg","spx"],"audio/s3m":["s3m"],"audio/silk":["sil"],"audio/wav":["wav"],"audio/wave":["*wav"],"audio/webm":["weba"],"audio/xm":["xm"],"font/collection":["ttc"],"font/otf":["otf"],"font/ttf":["ttf"],"font/woff":["woff"],"font/woff2":["woff2"],"image/aces":["exr"],"image/apng":["apng"],"image/bmp":["bmp"],"image/cgm":["cgm"],"image/dicom-rle":["drle"],"image/emf":["emf"],"image/fits":["fits"],"image/g3fax":["g3"],"image/gif":["gif"],"image/heic":["heic"],"image/heic-sequence":["heics"],"image/heif":["heif"],"image/heif-sequence":["heifs"],"image/ief":["ief"],"image/jls":["jls"],"image/jp2":["jp2","jpg2"],"image/jpeg":["jpeg","jpg","jpe"],"image/jpm":["jpm"],"image/jpx":["jpx","jpf"],"image/jxr":["jxr"],"image/ktx":["ktx"],"image/png":["png"],"image/sgi":["sgi"],"image/svg+xml":["svg","svgz"],"image/t38":["t38"],"image/tiff":["tif","tiff"],"image/tiff-fx":["tfx"],"image/webp":["webp"],"image/wmf":["wmf"],"message/disposition-notification":["disposition-notification"],"message/global":["u8msg"],"message/global-delivery-status":["u8dsn"],"message/global-disposition-notification":["u8mdn"],"message/global-headers":["u8hdr"],"message/rfc822":["eml","mime"],"model/3mf":["3mf"],"model/gltf+json":["gltf"],"model/gltf-binary":["glb"],"model/iges":["igs","iges"],"model/mesh":["msh","mesh","silo"],"model/stl":["stl"],"model/vrml":["wrl","vrml"],"model/x3d+binary":["*x3db","x3dbz"],"model/x3d+fastinfoset":["x3db"],"model/x3d+vrml":["*x3dv","x3dvz"],"model/x3d+xml":["x3d","x3dz"],"model/x3d-vrml":["x3dv"],"text/cache-manifest":["appcache","manifest"],"text/calendar":["ics","ifb"],"text/coffeescript":["coffee","litcoffee"],"text/css":["css"],"text/csv":["csv"],"text/html":["html","htm","shtml"],"text/jade":["jade"],"text/jsx":["jsx"],"text/less":["less"],"text/markdown":["markdown","md"],"text/mathml":["mml"],"text/mdx":["mdx"],"text/n3":["n3"],"text/plain":["txt","text","conf","def","list","log","in","ini"],"text/richtext":["rtx"],"text/rtf":["*rtf"],"text/sgml":["sgml","sgm"],"text/shex":["shex"],"text/slim":["slim","slm"],"text/stylus":["stylus","styl"],"text/tab-separated-values":["tsv"],"text/troff":["t","tr","roff","man","me","ms"],"text/turtle":["ttl"],"text/uri-list":["uri","uris","urls"],"text/vcard":["vcard"],"text/vtt":["vtt"],"text/xml":["*xml"],"text/yaml":["yaml","yml"],"video/3gpp":["3gp","3gpp"],"video/3gpp2":["3g2"],"video/h261":["h261"],"video/h263":["h263"],"video/h264":["h264"],"video/jpeg":["jpgv"],"video/jpm":["*jpm","jpgm"],"video/mj2":["mj2","mjp2"],"video/mp2t":["ts"],"video/mp4":["mp4","mp4v","mpg4"],"video/mpeg":["mpeg","mpg","mpe","m1v","m2v"],"video/ogg":["ogv"],"video/quicktime":["qt","mov"],"video/webm":["webm"]};var to={"application/prs.cww":["cww"],"application/vnd.3gpp.pic-bw-large":["plb"],"application/vnd.3gpp.pic-bw-small":["psb"],"application/vnd.3gpp.pic-bw-var":["pvb"],"application/vnd.3gpp2.tcap":["tcap"],"application/vnd.3m.post-it-notes":["pwn"],"application/vnd.accpac.simply.aso":["aso"],"application/vnd.accpac.simply.imp":["imp"],"application/vnd.acucobol":["acu"],"application/vnd.acucorp":["atc","acutc"],"application/vnd.adobe.air-application-installer-package+zip":["air"],"application/vnd.adobe.formscentral.fcdt":["fcdt"],"application/vnd.adobe.fxp":["fxp","fxpl"],"application/vnd.adobe.xdp+xml":["xdp"],"application/vnd.adobe.xfdf":["xfdf"],"application/vnd.ahead.space":["ahead"],"application/vnd.airzip.filesecure.azf":["azf"],"application/vnd.airzip.filesecure.azs":["azs"],"application/vnd.amazon.ebook":["azw"],"application/vnd.americandynamics.acc":["acc"],"application/vnd.amiga.ami":["ami"],"application/vnd.android.package-archive":["apk"],"application/vnd.anser-web-certificate-issue-initiation":["cii"],"application/vnd.anser-web-funds-transfer-initiation":["fti"],"application/vnd.antix.game-component":["atx"],"application/vnd.apple.installer+xml":["mpkg"],"application/vnd.apple.keynote":["keynote"],"application/vnd.apple.mpegurl":["m3u8"],"application/vnd.apple.numbers":["numbers"],"application/vnd.apple.pages":["pages"],"application/vnd.apple.pkpass":["pkpass"],"application/vnd.aristanetworks.swi":["swi"],"application/vnd.astraea-software.iota":["iota"],"application/vnd.audiograph":["aep"],"application/vnd.blueice.multipass":["mpm"],"application/vnd.bmi":["bmi"],"application/vnd.businessobjects":["rep"],"application/vnd.chemdraw+xml":["cdxml"],"application/vnd.chipnuts.karaoke-mmd":["mmd"],"application/vnd.cinderella":["cdy"],"application/vnd.citationstyles.style+xml":["csl"],"application/vnd.claymore":["cla"],"application/vnd.cloanto.rp9":["rp9"],"application/vnd.clonk.c4group":["c4g","c4d","c4f","c4p","c4u"],"application/vnd.cluetrust.cartomobile-config":["c11amc"],"application/vnd.cluetrust.cartomobile-config-pkg":["c11amz"],"application/vnd.commonspace":["csp"],"application/vnd.contact.cmsg":["cdbcmsg"],"application/vnd.cosmocaller":["cmc"],"application/vnd.crick.clicker":["clkx"],"application/vnd.crick.clicker.keyboard":["clkk"],"application/vnd.crick.clicker.palette":["clkp"],"application/vnd.crick.clicker.template":["clkt"],"application/vnd.crick.clicker.wordbank":["clkw"],"application/vnd.criticaltools.wbs+xml":["wbs"],"application/vnd.ctc-posml":["pml"],"application/vnd.cups-ppd":["ppd"],"application/vnd.curl.car":["car"],"application/vnd.curl.pcurl":["pcurl"],"application/vnd.dart":["dart"],"application/vnd.data-vision.rdz":["rdz"],"application/vnd.dece.data":["uvf","uvvf","uvd","uvvd"],"application/vnd.dece.ttml+xml":["uvt","uvvt"],"application/vnd.dece.unspecified":["uvx","uvvx"],"application/vnd.dece.zip":["uvz","uvvz"],"application/vnd.denovo.fcselayout-link":["fe_launch"],"application/vnd.dna":["dna"],"application/vnd.dolby.mlp":["mlp"],"application/vnd.dpgraph":["dpg"],"application/vnd.dreamfactory":["dfac"],"application/vnd.ds-keypoint":["kpxx"],"application/vnd.dvb.ait":["ait"],"application/vnd.dvb.service":["svc"],"application/vnd.dynageo":["geo"],"application/vnd.ecowin.chart":["mag"],"application/vnd.enliven":["nml"],"application/vnd.epson.esf":["esf"],"application/vnd.epson.msf":["msf"],"application/vnd.epson.quickanime":["qam"],"application/vnd.epson.salt":["slt"],"application/vnd.epson.ssf":["ssf"],"application/vnd.eszigno3+xml":["es3","et3"],"application/vnd.ezpix-album":["ez2"],"application/vnd.ezpix-package":["ez3"],"application/vnd.fdf":["fdf"],"application/vnd.fdsn.mseed":["mseed"],"application/vnd.fdsn.seed":["seed","dataless"],"application/vnd.flographit":["gph"],"application/vnd.fluxtime.clip":["ftc"],"application/vnd.framemaker":["fm","frame","maker","book"],"application/vnd.frogans.fnc":["fnc"],"application/vnd.frogans.ltf":["ltf"],"application/vnd.fsc.weblaunch":["fsc"],"application/vnd.fujitsu.oasys":["oas"],"application/vnd.fujitsu.oasys2":["oa2"],"application/vnd.fujitsu.oasys3":["oa3"],"application/vnd.fujitsu.oasysgp":["fg5"],"application/vnd.fujitsu.oasysprs":["bh2"],"application/vnd.fujixerox.ddd":["ddd"],"application/vnd.fujixerox.docuworks":["xdw"],"application/vnd.fujixerox.docuworks.binder":["xbd"],"application/vnd.fuzzysheet":["fzs"],"application/vnd.genomatix.tuxedo":["txd"],"application/vnd.geogebra.file":["ggb"],"application/vnd.geogebra.tool":["ggt"],"application/vnd.geometry-explorer":["gex","gre"],"application/vnd.geonext":["gxt"],"application/vnd.geoplan":["g2w"],"application/vnd.geospace":["g3w"],"application/vnd.gmx":["gmx"],"application/vnd.google-apps.document":["gdoc"],"application/vnd.google-apps.presentation":["gslides"],"application/vnd.google-apps.spreadsheet":["gsheet"],"application/vnd.google-earth.kml+xml":["kml"],"application/vnd.google-earth.kmz":["kmz"],"application/vnd.grafeq":["gqf","gqs"],"application/vnd.groove-account":["gac"],"application/vnd.groove-help":["ghf"],"application/vnd.groove-identity-message":["gim"],"application/vnd.groove-injector":["grv"],"application/vnd.groove-tool-message":["gtm"],"application/vnd.groove-tool-template":["tpl"],"application/vnd.groove-vcard":["vcg"],"application/vnd.hal+xml":["hal"],"application/vnd.handheld-entertainment+xml":["zmm"],"application/vnd.hbci":["hbci"],"application/vnd.hhe.lesson-player":["les"],"application/vnd.hp-hpgl":["hpgl"],"application/vnd.hp-hpid":["hpid"],"application/vnd.hp-hps":["hps"],"application/vnd.hp-jlyt":["jlt"],"application/vnd.hp-pcl":["pcl"],"application/vnd.hp-pclxl":["pclxl"],"application/vnd.hydrostatix.sof-data":["sfd-hdstx"],"application/vnd.ibm.minipay":["mpy"],"application/vnd.ibm.modcap":["afp","listafp","list3820"],"application/vnd.ibm.rights-management":["irm"],"application/vnd.ibm.secure-container":["sc"],"application/vnd.iccprofile":["icc","icm"],"application/vnd.igloader":["igl"],"application/vnd.immervision-ivp":["ivp"],"application/vnd.immervision-ivu":["ivu"],"application/vnd.insors.igm":["igm"],"application/vnd.intercon.formnet":["xpw","xpx"],"application/vnd.intergeo":["i2g"],"application/vnd.intu.qbo":["qbo"],"application/vnd.intu.qfx":["qfx"],"application/vnd.ipunplugged.rcprofile":["rcprofile"],"application/vnd.irepository.package+xml":["irp"],"application/vnd.is-xpr":["xpr"],"application/vnd.isac.fcs":["fcs"],"application/vnd.jam":["jam"],"application/vnd.jcp.javame.midlet-rms":["rms"],"application/vnd.jisp":["jisp"],"application/vnd.joost.joda-archive":["joda"],"application/vnd.kahootz":["ktz","ktr"],"application/vnd.kde.karbon":["karbon"],"application/vnd.kde.kchart":["chrt"],"application/vnd.kde.kformula":["kfo"],"application/vnd.kde.kivio":["flw"],"application/vnd.kde.kontour":["kon"],"application/vnd.kde.kpresenter":["kpr","kpt"],"application/vnd.kde.kspread":["ksp"],"application/vnd.kde.kword":["kwd","kwt"],"application/vnd.kenameaapp":["htke"],"application/vnd.kidspiration":["kia"],"application/vnd.kinar":["kne","knp"],"application/vnd.koan":["skp","skd","skt","skm"],"application/vnd.kodak-descriptor":["sse"],"application/vnd.las.las+xml":["lasxml"],"application/vnd.llamagraphics.life-balance.desktop":["lbd"],"application/vnd.llamagraphics.life-balance.exchange+xml":["lbe"],"application/vnd.lotus-1-2-3":["123"],"application/vnd.lotus-approach":["apr"],"application/vnd.lotus-freelance":["pre"],"application/vnd.lotus-notes":["nsf"],"application/vnd.lotus-organizer":["org"],"application/vnd.lotus-screencam":["scm"],"application/vnd.lotus-wordpro":["lwp"],"application/vnd.macports.portpkg":["portpkg"],"application/vnd.mcd":["mcd"],"application/vnd.medcalcdata":["mc1"],"application/vnd.mediastation.cdkey":["cdkey"],"application/vnd.mfer":["mwf"],"application/vnd.mfmp":["mfm"],"application/vnd.micrografx.flo":["flo"],"application/vnd.micrografx.igx":["igx"],"application/vnd.mif":["mif"],"application/vnd.mobius.daf":["daf"],"application/vnd.mobius.dis":["dis"],"application/vnd.mobius.mbk":["mbk"],"application/vnd.mobius.mqy":["mqy"],"application/vnd.mobius.msl":["msl"],"application/vnd.mobius.plc":["plc"],"application/vnd.mobius.txf":["txf"],"application/vnd.mophun.application":["mpn"],"application/vnd.mophun.certificate":["mpc"],"application/vnd.mozilla.xul+xml":["xul"],"application/vnd.ms-artgalry":["cil"],"application/vnd.ms-cab-compressed":["cab"],"application/vnd.ms-excel":["xls","xlm","xla","xlc","xlt","xlw"],"application/vnd.ms-excel.addin.macroenabled.12":["xlam"],"application/vnd.ms-excel.sheet.binary.macroenabled.12":["xlsb"],"application/vnd.ms-excel.sheet.macroenabled.12":["xlsm"],"application/vnd.ms-excel.template.macroenabled.12":["xltm"],"application/vnd.ms-fontobject":["eot"],"application/vnd.ms-htmlhelp":["chm"],"application/vnd.ms-ims":["ims"],"application/vnd.ms-lrm":["lrm"],"application/vnd.ms-officetheme":["thmx"],"application/vnd.ms-outlook":["msg"],"application/vnd.ms-pki.seccat":["cat"],"application/vnd.ms-pki.stl":["*stl"],"application/vnd.ms-powerpoint":["ppt","pps","pot"],"application/vnd.ms-powerpoint.addin.macroenabled.12":["ppam"],"application/vnd.ms-powerpoint.presentation.macroenabled.12":["pptm"],"application/vnd.ms-powerpoint.slide.macroenabled.12":["sldm"],"application/vnd.ms-powerpoint.slideshow.macroenabled.12":["ppsm"],"application/vnd.ms-powerpoint.template.macroenabled.12":["potm"],"application/vnd.ms-project":["mpp","mpt"],"application/vnd.ms-word.document.macroenabled.12":["docm"],"application/vnd.ms-word.template.macroenabled.12":["dotm"],"application/vnd.ms-works":["wps","wks","wcm","wdb"],"application/vnd.ms-wpl":["wpl"],"application/vnd.ms-xpsdocument":["xps"],"application/vnd.mseq":["mseq"],"application/vnd.musician":["mus"],"application/vnd.muvee.style":["msty"],"application/vnd.mynfc":["taglet"],"application/vnd.neurolanguage.nlu":["nlu"],"application/vnd.nitf":["ntf","nitf"],"application/vnd.noblenet-directory":["nnd"],"application/vnd.noblenet-sealer":["nns"],"application/vnd.noblenet-web":["nnw"],"application/vnd.nokia.n-gage.data":["ngdat"],"application/vnd.nokia.n-gage.symbian.install":["n-gage"],"application/vnd.nokia.radio-preset":["rpst"],"application/vnd.nokia.radio-presets":["rpss"],"application/vnd.novadigm.edm":["edm"],"application/vnd.novadigm.edx":["edx"],"application/vnd.novadigm.ext":["ext"],"application/vnd.oasis.opendocument.chart":["odc"],"application/vnd.oasis.opendocument.chart-template":["otc"],"application/vnd.oasis.opendocument.database":["odb"],"application/vnd.oasis.opendocument.formula":["odf"],"application/vnd.oasis.opendocument.formula-template":["odft"],"application/vnd.oasis.opendocument.graphics":["odg"],"application/vnd.oasis.opendocument.graphics-template":["otg"],"application/vnd.oasis.opendocument.image":["odi"],"application/vnd.oasis.opendocument.image-template":["oti"],"application/vnd.oasis.opendocument.presentation":["odp"],"application/vnd.oasis.opendocument.presentation-template":["otp"],"application/vnd.oasis.opendocument.spreadsheet":["ods"],"application/vnd.oasis.opendocument.spreadsheet-template":["ots"],"application/vnd.oasis.opendocument.text":["odt"],"application/vnd.oasis.opendocument.text-master":["odm"],"application/vnd.oasis.opendocument.text-template":["ott"],"application/vnd.oasis.opendocument.text-web":["oth"],"application/vnd.olpc-sugar":["xo"],"application/vnd.oma.dd2+xml":["dd2"],"application/vnd.openofficeorg.extension":["oxt"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":["pptx"],"application/vnd.openxmlformats-officedocument.presentationml.slide":["sldx"],"application/vnd.openxmlformats-officedocument.presentationml.slideshow":["ppsx"],"application/vnd.openxmlformats-officedocument.presentationml.template":["potx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":["xlsx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.template":["xltx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":["docx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.template":["dotx"],"application/vnd.osgeo.mapguide.package":["mgp"],"application/vnd.osgi.dp":["dp"],"application/vnd.osgi.subsystem":["esa"],"application/vnd.palm":["pdb","pqa","oprc"],"application/vnd.pawaafile":["paw"],"application/vnd.pg.format":["str"],"application/vnd.pg.osasli":["ei6"],"application/vnd.picsel":["efif"],"application/vnd.pmi.widget":["wg"],"application/vnd.pocketlearn":["plf"],"application/vnd.powerbuilder6":["pbd"],"application/vnd.previewsystems.box":["box"],"application/vnd.proteus.magazine":["mgz"],"application/vnd.publishare-delta-tree":["qps"],"application/vnd.pvi.ptid1":["ptid"],"application/vnd.quark.quarkxpress":["qxd","qxt","qwd","qwt","qxl","qxb"],"application/vnd.realvnc.bed":["bed"],"application/vnd.recordare.musicxml":["mxl"],"application/vnd.recordare.musicxml+xml":["musicxml"],"application/vnd.rig.cryptonote":["cryptonote"],"application/vnd.rim.cod":["cod"],"application/vnd.rn-realmedia":["rm"],"application/vnd.rn-realmedia-vbr":["rmvb"],"application/vnd.route66.link66+xml":["link66"],"application/vnd.sailingtracker.track":["st"],"application/vnd.seemail":["see"],"application/vnd.sema":["sema"],"application/vnd.semd":["semd"],"application/vnd.semf":["semf"],"application/vnd.shana.informed.formdata":["ifm"],"application/vnd.shana.informed.formtemplate":["itp"],"application/vnd.shana.informed.interchange":["iif"],"application/vnd.shana.informed.package":["ipk"],"application/vnd.simtech-mindmapper":["twd","twds"],"application/vnd.smaf":["mmf"],"application/vnd.smart.teacher":["teacher"],"application/vnd.solent.sdkm+xml":["sdkm","sdkd"],"application/vnd.spotfire.dxp":["dxp"],"application/vnd.spotfire.sfs":["sfs"],"application/vnd.stardivision.calc":["sdc"],"application/vnd.stardivision.draw":["sda"],"application/vnd.stardivision.impress":["sdd"],"application/vnd.stardivision.math":["smf"],"application/vnd.stardivision.writer":["sdw","vor"],"application/vnd.stardivision.writer-global":["sgl"],"application/vnd.stepmania.package":["smzip"],"application/vnd.stepmania.stepchart":["sm"],"application/vnd.sun.wadl+xml":["wadl"],"application/vnd.sun.xml.calc":["sxc"],"application/vnd.sun.xml.calc.template":["stc"],"application/vnd.sun.xml.draw":["sxd"],"application/vnd.sun.xml.draw.template":["std"],"application/vnd.sun.xml.impress":["sxi"],"application/vnd.sun.xml.impress.template":["sti"],"application/vnd.sun.xml.math":["sxm"],"application/vnd.sun.xml.writer":["sxw"],"application/vnd.sun.xml.writer.global":["sxg"],"application/vnd.sun.xml.writer.template":["stw"],"application/vnd.sus-calendar":["sus","susp"],"application/vnd.svd":["svd"],"application/vnd.symbian.install":["sis","sisx"],"application/vnd.syncml+xml":["xsm"],"application/vnd.syncml.dm+wbxml":["bdm"],"application/vnd.syncml.dm+xml":["xdm"],"application/vnd.tao.intent-module-archive":["tao"],"application/vnd.tcpdump.pcap":["pcap","cap","dmp"],"application/vnd.tmobile-livetv":["tmo"],"application/vnd.trid.tpt":["tpt"],"application/vnd.triscape.mxs":["mxs"],"application/vnd.trueapp":["tra"],"application/vnd.ufdl":["ufd","ufdl"],"application/vnd.uiq.theme":["utz"],"application/vnd.umajin":["umj"],"application/vnd.unity":["unityweb"],"application/vnd.uoml+xml":["uoml"],"application/vnd.vcx":["vcx"],"application/vnd.visio":["vsd","vst","vss","vsw"],"application/vnd.visionary":["vis"],"application/vnd.vsf":["vsf"],"application/vnd.wap.wbxml":["wbxml"],"application/vnd.wap.wmlc":["wmlc"],"application/vnd.wap.wmlscriptc":["wmlsc"],"application/vnd.webturbo":["wtb"],"application/vnd.wolfram.player":["nbp"],"application/vnd.wordperfect":["wpd"],"application/vnd.wqd":["wqd"],"application/vnd.wt.stf":["stf"],"application/vnd.xara":["xar"],"application/vnd.xfdl":["xfdl"],"application/vnd.yamaha.hv-dic":["hvd"],"application/vnd.yamaha.hv-script":["hvs"],"application/vnd.yamaha.hv-voice":["hvp"],"application/vnd.yamaha.openscoreformat":["osf"],"application/vnd.yamaha.openscoreformat.osfpvg+xml":["osfpvg"],"application/vnd.yamaha.smaf-audio":["saf"],"application/vnd.yamaha.smaf-phrase":["spf"],"application/vnd.yellowriver-custom-menu":["cmp"],"application/vnd.zul":["zir","zirz"],"application/vnd.zzazz.deck+xml":["zaz"],"application/x-7z-compressed":["7z"],"application/x-abiword":["abw"],"application/x-ace-compressed":["ace"],"application/x-apple-diskimage":["*dmg"],"application/x-arj":["arj"],"application/x-authorware-bin":["aab","x32","u32","vox"],"application/x-authorware-map":["aam"],"application/x-authorware-seg":["aas"],"application/x-bcpio":["bcpio"],"application/x-bdoc":["*bdoc"],"application/x-bittorrent":["torrent"],"application/x-blorb":["blb","blorb"],"application/x-bzip":["bz"],"application/x-bzip2":["bz2","boz"],"application/x-cbr":["cbr","cba","cbt","cbz","cb7"],"application/x-cdlink":["vcd"],"application/x-cfs-compressed":["cfs"],"application/x-chat":["chat"],"application/x-chess-pgn":["pgn"],"application/x-chrome-extension":["crx"],"application/x-cocoa":["cco"],"application/x-conference":["nsc"],"application/x-cpio":["cpio"],"application/x-csh":["csh"],"application/x-debian-package":["*deb","udeb"],"application/x-dgc-compressed":["dgc"],"application/x-director":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"],"application/x-doom":["wad"],"application/x-dtbncx+xml":["ncx"],"application/x-dtbook+xml":["dtb"],"application/x-dtbresource+xml":["res"],"application/x-dvi":["dvi"],"application/x-envoy":["evy"],"application/x-eva":["eva"],"application/x-font-bdf":["bdf"],"application/x-font-ghostscript":["gsf"],"application/x-font-linux-psf":["psf"],"application/x-font-pcf":["pcf"],"application/x-font-snf":["snf"],"application/x-font-type1":["pfa","pfb","pfm","afm"],"application/x-freearc":["arc"],"application/x-futuresplash":["spl"],"application/x-gca-compressed":["gca"],"application/x-glulx":["ulx"],"application/x-gnumeric":["gnumeric"],"application/x-gramps-xml":["gramps"],"application/x-gtar":["gtar"],"application/x-hdf":["hdf"],"application/x-httpd-php":["php"],"application/x-install-instructions":["install"],"application/x-iso9660-image":["*iso"],"application/x-java-archive-diff":["jardiff"],"application/x-java-jnlp-file":["jnlp"],"application/x-latex":["latex"],"application/x-lua-bytecode":["luac"],"application/x-lzh-compressed":["lzh","lha"],"application/x-makeself":["run"],"application/x-mie":["mie"],"application/x-mobipocket-ebook":["prc","mobi"],"application/x-ms-application":["application"],"application/x-ms-shortcut":["lnk"],"application/x-ms-wmd":["wmd"],"application/x-ms-wmz":["wmz"],"application/x-ms-xbap":["xbap"],"application/x-msaccess":["mdb"],"application/x-msbinder":["obd"],"application/x-mscardfile":["crd"],"application/x-msclip":["clp"],"application/x-msdos-program":["*exe"],"application/x-msdownload":["*exe","*dll","com","bat","*msi"],"application/x-msmediaview":["mvb","m13","m14"],"application/x-msmetafile":["*wmf","*wmz","*emf","emz"],"application/x-msmoney":["mny"],"application/x-mspublisher":["pub"],"application/x-msschedule":["scd"],"application/x-msterminal":["trm"],"application/x-mswrite":["wri"],"application/x-netcdf":["nc","cdf"],"application/x-ns-proxy-autoconfig":["pac"],"application/x-nzb":["nzb"],"application/x-perl":["pl","pm"],"application/x-pilot":["*prc","*pdb"],"application/x-pkcs12":["p12","pfx"],"application/x-pkcs7-certificates":["p7b","spc"],"application/x-pkcs7-certreqresp":["p7r"],"application/x-rar-compressed":["rar"],"application/x-redhat-package-manager":["rpm"],"application/x-research-info-systems":["ris"],"application/x-sea":["sea"],"application/x-sh":["sh"],"application/x-shar":["shar"],"application/x-shockwave-flash":["swf"],"application/x-silverlight-app":["xap"],"application/x-sql":["sql"],"application/x-stuffit":["sit"],"application/x-stuffitx":["sitx"],"application/x-subrip":["srt"],"application/x-sv4cpio":["sv4cpio"],"application/x-sv4crc":["sv4crc"],"application/x-t3vm-image":["t3"],"application/x-tads":["gam"],"application/x-tar":["tar"],"application/x-tcl":["tcl","tk"],"application/x-tex":["tex"],"application/x-tex-tfm":["tfm"],"application/x-texinfo":["texinfo","texi"],"application/x-tgif":["obj"],"application/x-ustar":["ustar"],"application/x-virtualbox-hdd":["hdd"],"application/x-virtualbox-ova":["ova"],"application/x-virtualbox-ovf":["ovf"],"application/x-virtualbox-vbox":["vbox"],"application/x-virtualbox-vbox-extpack":["vbox-extpack"],"application/x-virtualbox-vdi":["vdi"],"application/x-virtualbox-vhd":["vhd"],"application/x-virtualbox-vmdk":["vmdk"],"application/x-wais-source":["src"],"application/x-web-app-manifest+json":["webapp"],"application/x-x509-ca-cert":["der","crt","pem"],"application/x-xfig":["fig"],"application/x-xliff+xml":["xlf"],"application/x-xpinstall":["xpi"],"application/x-xz":["xz"],"application/x-zmachine":["z1","z2","z3","z4","z5","z6","z7","z8"],"audio/vnd.dece.audio":["uva","uvva"],"audio/vnd.digital-winds":["eol"],"audio/vnd.dra":["dra"],"audio/vnd.dts":["dts"],"audio/vnd.dts.hd":["dtshd"],"audio/vnd.lucent.voice":["lvp"],"audio/vnd.ms-playready.media.pya":["pya"],"audio/vnd.nuera.ecelp4800":["ecelp4800"],"audio/vnd.nuera.ecelp7470":["ecelp7470"],"audio/vnd.nuera.ecelp9600":["ecelp9600"],"audio/vnd.rip":["rip"],"audio/x-aac":["aac"],"audio/x-aiff":["aif","aiff","aifc"],"audio/x-caf":["caf"],"audio/x-flac":["flac"],"audio/x-m4a":["*m4a"],"audio/x-matroska":["mka"],"audio/x-mpegurl":["m3u"],"audio/x-ms-wax":["wax"],"audio/x-ms-wma":["wma"],"audio/x-pn-realaudio":["ram","ra"],"audio/x-pn-realaudio-plugin":["rmp"],"audio/x-realaudio":["*ra"],"audio/x-wav":["*wav"],"chemical/x-cdx":["cdx"],"chemical/x-cif":["cif"],"chemical/x-cmdf":["cmdf"],"chemical/x-cml":["cml"],"chemical/x-csml":["csml"],"chemical/x-xyz":["xyz"],"image/prs.btif":["btif"],"image/prs.pti":["pti"],"image/vnd.adobe.photoshop":["psd"],"image/vnd.airzip.accelerator.azv":["azv"],"image/vnd.dece.graphic":["uvi","uvvi","uvg","uvvg"],"image/vnd.djvu":["djvu","djv"],"image/vnd.dvb.subtitle":["*sub"],"image/vnd.dwg":["dwg"],"image/vnd.dxf":["dxf"],"image/vnd.fastbidsheet":["fbs"],"image/vnd.fpx":["fpx"],"image/vnd.fst":["fst"],"image/vnd.fujixerox.edmics-mmr":["mmr"],"image/vnd.fujixerox.edmics-rlc":["rlc"],"image/vnd.microsoft.icon":["ico"],"image/vnd.ms-modi":["mdi"],"image/vnd.ms-photo":["wdp"],"image/vnd.net-fpx":["npx"],"image/vnd.tencent.tap":["tap"],"image/vnd.valve.source.texture":["vtf"],"image/vnd.wap.wbmp":["wbmp"],"image/vnd.xiff":["xif"],"image/vnd.zbrush.pcx":["pcx"],"image/x-3ds":["3ds"],"image/x-cmu-raster":["ras"],"image/x-cmx":["cmx"],"image/x-freehand":["fh","fhc","fh4","fh5","fh7"],"image/x-icon":["*ico"],"image/x-jng":["jng"],"image/x-mrsid-image":["sid"],"image/x-ms-bmp":["*bmp"],"image/x-pcx":["*pcx"],"image/x-pict":["pic","pct"],"image/x-portable-anymap":["pnm"],"image/x-portable-bitmap":["pbm"],"image/x-portable-graymap":["pgm"],"image/x-portable-pixmap":["ppm"],"image/x-rgb":["rgb"],"image/x-tga":["tga"],"image/x-xbitmap":["xbm"],"image/x-xpixmap":["xpm"],"image/x-xwindowdump":["xwd"],"message/vnd.wfa.wsc":["wsc"],"model/vnd.collada+xml":["dae"],"model/vnd.dwf":["dwf"],"model/vnd.gdl":["gdl"],"model/vnd.gtw":["gtw"],"model/vnd.mts":["mts"],"model/vnd.opengex":["ogex"],"model/vnd.parasolid.transmit.binary":["x_b"],"model/vnd.parasolid.transmit.text":["x_t"],"model/vnd.usdz+zip":["usdz"],"model/vnd.valve.source.compiled-map":["bsp"],"model/vnd.vtu":["vtu"],"text/prs.lines.tag":["dsc"],"text/vnd.curl":["curl"],"text/vnd.curl.dcurl":["dcurl"],"text/vnd.curl.mcurl":["mcurl"],"text/vnd.curl.scurl":["scurl"],"text/vnd.dvb.subtitle":["sub"],"text/vnd.fly":["fly"],"text/vnd.fmi.flexstor":["flx"],"text/vnd.graphviz":["gv"],"text/vnd.in3d.3dml":["3dml"],"text/vnd.in3d.spot":["spot"],"text/vnd.sun.j2me.app-descriptor":["jad"],"text/vnd.wap.wml":["wml"],"text/vnd.wap.wmlscript":["wmls"],"text/x-asm":["s","asm"],"text/x-c":["c","cc","cxx","cpp","h","hh","dic"],"text/x-component":["htc"],"text/x-fortran":["f","for","f77","f90"],"text/x-handlebars-template":["hbs"],"text/x-java-source":["java"],"text/x-lua":["lua"],"text/x-markdown":["mkd"],"text/x-nfo":["nfo"],"text/x-opml":["opml"],"text/x-org":["*org"],"text/x-pascal":["p","pas"],"text/x-processing":["pde"],"text/x-sass":["sass"],"text/x-scss":["scss"],"text/x-setext":["etx"],"text/x-sfv":["sfv"],"text/x-suse-ymp":["ymp"],"text/x-uuencode":["uu"],"text/x-vcalendar":["vcs"],"text/x-vcard":["vcf"],"video/vnd.dece.hd":["uvh","uvvh"],"video/vnd.dece.mobile":["uvm","uvvm"],"video/vnd.dece.pd":["uvp","uvvp"],"video/vnd.dece.sd":["uvs","uvvs"],"video/vnd.dece.video":["uvv","uvvv"],"video/vnd.dvb.file":["dvb"],"video/vnd.fvt":["fvt"],"video/vnd.mpegurl":["mxu","m4u"],"video/vnd.ms-playready.media.pyv":["pyv"],"video/vnd.uvvu.mp4":["uvu","uvvu"],"video/vnd.vivo":["viv"],"video/x-f4v":["f4v"],"video/x-fli":["fli"],"video/x-flv":["flv"],"video/x-m4v":["m4v"],"video/x-matroska":["mkv","mk3d","mks"],"video/x-mng":["mng"],"video/x-ms-asf":["asf","asx"],"video/x-ms-vob":["vob"],"video/x-ms-wm":["wm"],"video/x-ms-wmv":["wmv"],"video/x-ms-wmx":["wmx"],"video/x-ms-wvx":["wvx"],"video/x-msvideo":["avi"],"video/x-sgi-movie":["movie"],"video/x-smv":["smv"],"x-conference/x-cooltalk":["ice"]};var io=new Kr(eo,to);fe.locale("fr");var ao=e("t_messager",function(){function e(e){t(this,e);this.req=false;this.refresh_time=1e4;this.language="fr";this.ismobile=false;this.state={messages:[],message:"",files:null,loader:false,showError:false};this.validMime=["application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/msword","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/pdf","image/jpeg","image/gif","image/png","application/zip","application/x-zip-compressed"];this.imgMime=["image/jpeg","image/gif","image/png"]}e.prototype.handleSubmit=function(){var e=this;if(this.state.message.trim()===""&&this.state.files===null){return}var t=this.state.message;if(t.trim()===""){t="Envoyé"}var i=new FormData;i.append("message",t);if(this.state.files&&this.state.files.length>0){i.append("files[]",this.state.files[0],this.state.files[0].name)}this.state=Object.assign(Object.assign({},this.state),{loader:true});de.post(this.post_message_url,i,{responseType:"json",headers:{"content-type":"multipart/form-data"}}).then((function(t){var i=e.state.messages;i.push(t.data);e.state=Object.assign(Object.assign({},e.state),{messages:__spreadArrays(i),message:"",files:null,loader:false});setTimeout((function(){e.el.shadowRoot.getElementById("message").style.cssText="height:auto; padding:0"}),0)})).catch((function(){return e.state=Object.assign(Object.assign({},e.state),{loader:false})}))};e.prototype.fileChange=function(e){var t=false;if(e.length===1){console.log(e.item(0));if(e.item(0).size>=5242880||!this.validMime.includes(e.item(0).type)){t=true}else{this.state.files=e;this.handleSubmit()}}else{this.state.files=null}this.state=Object.assign(Object.assign({},this.state),{showError:t})};e.prototype.scrollToBottom=function(){var e=this.el.shadowRoot.querySelector(".messager_body");e.scrollTop=e.scrollHeight-e.clientHeight};e.prototype.handleChange=function(e){this.state=Object.assign(Object.assign({},this.state),{message:e.target.value})};e.prototype.componentDidLoad=function(){var e=this;this.state=Object.assign(Object.assign({},this.state),{loader:true});de.get(this.get_messages_url).then((function(t){e.state=Object.assign(Object.assign({},e.state),{messages:t.data,loader:false});if(e.refresh_time>0){e.timer=window.setInterval((function(){if(!e.req){e.req=true;de.get(e.refresh_message_url).then((function(t){var i=e.state.messages;if(t.data&&t.data.length>0){i=__spreadArrays(i,t.data);e.state=Object.assign(Object.assign({},e.state),{messages:i})}e.req=false})).catch((function(){return e.req=false}))}}),e.refresh_time)}})).catch((function(){return e.state=Object.assign(Object.assign({},e.state),{loader:false})}))};e.prototype.componentDidUnload=function(){window.clearInterval(this.timer)};e.prototype.componentDidUpdate=function(){this.scrollToBottom()};e.prototype.autosize=function(e){if(e.keyCode==13&&!e.shiftKey){e.preventDefault();this.handleSubmit();return false}else{var t=e.target;setTimeout((function(){t.style.cssText="height:auto; padding:0";t.style.cssText="-moz-box-sizing:content-box";t.style.cssText="height:"+t.scrollHeight+"px"}),0)}};e.prototype.messagesList=function(){var e=this;return i("div",{class:"messager_body"},this.state.messages.map((function(t){return i("div",{class:"messager_body_container",key:t.id},t.body&&i("div",{class:t.sender_id!==e.user_id?"messager_body_message messager_body_message_recipient":"messager_body_message messager_body_message_sender"},i("div",{class:"messager_body_message_date"},t&&t.created_at?fe(t.created_at).format("LLL"):""),e.eventMessage(t),i("div",{class:"messager_body_message_card"},i("div",{class:"messager_body_message_card_user"},t.sender),i("div",{class:"messager_body_message_card_body"},i("div",{innerHTML:t.body}),t.files.length>0&&e.filesMessageDetail(t.files))),t.files.length>0&&e.filesMessage(t.files)))})))};e.prototype.filesMessage=function(e){var t=this;return i("div",null,e.map((function(e){return i("div",null,i("a",{class:"file-link",href:t.file_url+e.id,target:"_blank"},i("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 15 17",height:"16"},i("path",{fill:"#134391","fill-rule":"evenodd",d:"M10.764 3.343c.322-.337-.322-1.01-.645-.674l-7.474 7.81s-1.675 1.75-.129 3.365c1.546 1.616 3.222-.135 3.222-.135l7.861-8.212s2.063-2.154-.128-4.444C11.28-1.236 9.218.92 9.218.92L1.356 9.132s-2.963 3.097-.129 6.06c2.835 2.961 5.8-.136 5.8-.136l7.861-8.213c.322-.336-.322-1.01-.645-.673l-7.86 8.212s-2.32 2.424-4.51.135C-.32 12.228 2 9.805 2 9.805l7.86-8.213s1.418-1.481 2.965.135c1.546 1.616.129 3.097.129 3.097l-7.86 8.212s-1.032 1.078-1.934.135c-.903-.943.129-2.02.129-2.02l7.474-7.808z"})),e.file_name))})))};e.prototype.filesMessageDetail=function(e){var t=this;return i("div",null,e.map((function(e){return i("div",{style:{textAlign:"center"}},i("a",{class:"file-link",href:t.file_url+e.id,target:"_blank"},!t.imgMime.includes(io.getType(e.file_name))&&i("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 15 17",height:"64"},i("path",{fill:"#134391","fill-rule":"evenodd",d:"M10.764 3.343c.322-.337-.322-1.01-.645-.674l-7.474 7.81s-1.675 1.75-.129 3.365c1.546 1.616 3.222-.135 3.222-.135l7.861-8.212s2.063-2.154-.128-4.444C11.28-1.236 9.218.92 9.218.92L1.356 9.132s-2.963 3.097-.129 6.06c2.835 2.961 5.8-.136 5.8-.136l7.861-8.213c.322-.336-.322-1.01-.645-.673l-7.86 8.212s-2.32 2.424-4.51.135C-.32 12.228 2 9.805 2 9.805l7.86-8.213s1.418-1.481 2.965.135c1.546 1.616.129 3.097.129 3.097l-7.86 8.212s-1.032 1.078-1.934.135c-.903-.943.129-2.02.129-2.02l7.474-7.808z"})),t.imgMime.includes(io.getType(e.file_name))&&i("img",{src:t.file_url+e.id,width:128})))})))};e.prototype.eventMessage=function(e){return i("div",null,e.event_name&&i("div",{class:e.event_name+" event"},Xr[this.language][e.event_name]))};e.prototype.render=function(){var e=this;return i("div",{class:this.ismobile?"ismobile messager":"messager"},this.state.loader&&i("div",{class:"messager_loader"},i("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100",preserveAspectRatio:"xMidYMid"},i("circle",{cx:"50",cy:"50",fill:"none","stroke-width":"10",stroke:this.headerbordercolor,r:"35","stroke-dasharray":"164.93361431346415 56.97787143782138",transform:"rotate(222.044 50 50)"},i("animateTransform",{attributeName:"transform",type:"rotate",repeatCount:"indefinite",dur:"1s",values:"0 50 50;360 50 50",keyTimes:"0;1"})))),i("div",{class:"messager_header",style:{background:this.headerbackground,borderColor:this.headerbordercolor}},i("div",{class:"messager_header_logo"},i("img",{src:this.logo,height:"30"}))),this.messagesList(),this.state.showError&&i("div",{class:"error",onClick:function(){return e.state=Object.assign(Object.assign({},e.state),{showError:false})}},"Erreur d'envoi, les formats autorisés sont doc, docx, xls, xlsx, pdf, jpeg, gif, png, zip et la taille max 5Mo"),i("form",{enctype:"multipart/form-data",class:"messager_footer",onSubmit:function(e){e.preventDefault()}},i("div",{class:"messager_footer_editor"},i("textarea",{name:"message",id:"message",placeholder:"Votre message...",value:this.state.message,onInput:function(t){return e.handleChange(t)},onKeyPress:function(t){return e.autosize(t)}})),i("div",{class:"messager_footer_upload"},i("label",{htmlFor:"messager_footer_attachment"},i("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 15 17",height:"16"},i("path",{fill:"#134391","fill-rule":"evenodd",d:"M10.764 3.343c.322-.337-.322-1.01-.645-.674l-7.474 7.81s-1.675 1.75-.129 3.365c1.546 1.616 3.222-.135 3.222-.135l7.861-8.212s2.063-2.154-.128-4.444C11.28-1.236 9.218.92 9.218.92L1.356 9.132s-2.963 3.097-.129 6.06c2.835 2.961 5.8-.136 5.8-.136l7.861-8.213c.322-.336-.322-1.01-.645-.673l-7.86 8.212s-2.32 2.424-4.51.135C-.32 12.228 2 9.805 2 9.805l7.86-8.213s1.418-1.481 2.965.135c1.546 1.616.129 3.097.129 3.097l-7.86 8.212s-1.032 1.078-1.934.135c-.903-.943.129-2.02.129-2.02l7.474-7.808z"}))),i("input",{style:{display:"none"},type:"file",name:"messager_footer_attachment",id:"messager_footer_attachment",value:this.state.files,onInput:function(t){return e.fileChange(t.target.files)}})),i("div",{class:"messager_footer_send"},i("button",{type:"submit",onClick:function(){return e.handleSubmit()}},i("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 10.121108 11.034",height:"16"},i("path",{fill:"#134391","fill-rule":"evenodd",d:"m -1.439446,0 v 4.472 l 7.06,0.968 -7.06,0.908 v 4.686 l 13,-5.594 z"}))))))};Object.defineProperty(e.prototype,"el",{get:function(){return a(this)},enumerable:true,configurable:true});Object.defineProperty(e,"style",{get:function(){return"\@-webkit-keyframes rotating{0%{-webkit-transform:rotate(0deg)}to{-webkit-transform:rotate(1turn)}}\@keyframes rotating{0%{-webkit-transform:rotate(0deg)}to{-webkit-transform:rotate(1turn)}}.file-link{color:#134391}.messager{font-family:Arial,Helvetica,sans-serif;font-size:12px;border-radius:17px;-webkit-box-shadow:0 2px 9px 4px rgba(0,0,0,.15);box-shadow:0 2px 9px 4px rgba(0,0,0,.15);display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;overflow:hidden;background:#fff;position:relative}.messager *{-webkit-box-sizing:border-box;box-sizing:border-box}.messager_header{display:-ms-flexbox;display:flex;padding:15px;border-bottom:1px solid}.messager_header_logo{-ms-flex:1;flex:1;text-align:center}.messager_header_logo img{height:30px}.messager_loader{position:absolute;top:0;left:0;right:0;bottom:0;display:-ms-flexbox;display:flex;background:rgba(0,0,0,.3);-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}.messager_loader svg{width:40px;height:40px}.messager_loader svg circle{-webkit-transform-origin:50% 50%;transform-origin:50% 50%;-webkit-animation:rotating 2s linear infinite;animation:rotating 2s linear infinite}.messager_body{overflow:auto;min-height:60px;max-height:328px;display:block;padding:10px}.messager_body_container{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}.messager_body_message{max-width:80%;margin:10px 0}.messager_body_message .event{font-style:italic;font-weight:700;display:inline-block;padding:3px 8px;border-radius:10px}.messager_body_message .draft{display:none}.messager_body_message .redraft{color:#fff;background:#f49812}.messager_body_message .cancelled,.messager_body_message .refused{color:#fff;background:#ed0000}.messager_body_message .new,.messager_body_message .send,.messager_body_message .validated{color:#fff;background:#6b9f33}.messager_body_message_date{-ms-flex-item-align:center;align-self:center;color:#134391}.messager_body_message_card{background:#f0f0f0;padding:10px 20px;border-radius:10px}.messager_body_message_card_user{font-weight:700;margin-bottom:10px}.messager_body_message_recipient{-ms-flex-item-align:start;align-self:flex-start}.messager_body_message_sender{-ms-flex-item-align:end;align-self:flex-end}.messager_body_message_sender .messager_body_message_card{background:#ecf4fb}.messager_footer{display:-ms-flexbox;display:flex;padding:0;margin:0;border-top:1px solid #e2e4ed;padding:15px 30px}.messager_footer_editor{-ms-flex:1;flex:1}.messager_footer_editor textarea{display:block;resize:none;border:none!important;width:100%;margin:0;line-height:20px;height:20px;padding:0;max-height:60px;font-family:Arial,Helvetica,sans-serif;font-size:12px}.messager_footer svg{height:16px;width:16px}.messager_footer_upload label{padding:0 10px;display:-ms-flexbox;display:flex;line-height:20px}.messager_footer_upload label:hover{cursor:pointer}.messager_footer_send button{display:-ms-flexbox;display:flex;border:none;background:none;padding:0 10px;margin:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.messager_footer_send button:hover{cursor:pointer}.messager.ismobile{height:100vh;border-radius:0}.messager.ismobile .messager_body{max-height:none;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column}.error{background:#e10532;color:#fff;padding:5px;text-align:center}"},enumerable:true,configurable:true});return e}())}}}));