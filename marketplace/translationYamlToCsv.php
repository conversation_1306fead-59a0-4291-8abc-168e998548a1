<?php

include 'vendor/autoload.php';

$fileFr = __DIR__ . '/src/AppBundle/Resources/translations/AppBundle.fr.yml';
$fileEn = __DIR__ . '/src/AppBundle/Resources/translations/AppBundle.en.yml';
$fileEs = __DIR__ . '/src/AppBundle/Resources/translations/AppBundle.es.yml';
$fileIt = __DIR__ . '/src/AppBundle/Resources/translations/AppBundle.it.yml';
$fileDe = __DIR__ . '/src/AppBundle/Resources/translations/AppBundle.de.yml';
$fileNl = __DIR__ . '/src/AppBundle/Resources/translations/AppBundle.nl.yml';

$dataFr = flattenKey(\Symfony\Component\Yaml\Yaml::parseFile($fileFr));
$dataEn = flattenKey(\Symfony\Component\Yaml\Yaml::parseFile($fileEn));
$dataEs = flattenKey(\Symfony\Component\Yaml\Yaml::parseFile($fileEs));
$dataIt = flattenKey(\Symfony\Component\Yaml\Yaml::parseFile($fileIt));
$dataDe = flattenKey(\Symfony\Component\Yaml\Yaml::parseFile($fileDe));
$dataNl = flattenKey(\Symfony\Component\Yaml\Yaml::parseFile($fileNl));

ksort($dataFr);
ksort($dataEn);
ksort($dataEs);
ksort($dataIt);
ksort($dataDe);
ksort($dataNl);

$allKeys = array_merge(array_keys($dataFr),array_keys($dataEn), array_keys($dataEs), array_keys($dataIt), array_keys($dataDe), array_keys($dataNl));

sort($allKeys);
$allKeys = array_unique($allKeys);

$csvData = [];

unlink('traduction.csv');

foreach($allKeys as $key) {
    $csvDataRow = [$key, sanitize($dataFr[$key] ?? ''), sanitize($dataEn[$key] ?? ''), sanitize($dataEs[$key] ?? ''), sanitize($dataIt[$key] ?? ''), sanitize($dataDe[$key] ?? ''), sanitize($dataNl[$key] ?? '')];

    $data = implode(';', $csvDataRow). "\n";
    //$data = mb_convert_encoding($data,'UTF-16LE','UTF-8');

    file_put_contents('traduction.csv', $data, FILE_APPEND);

    $csvData[] = $csvDataRow;
}



function flattenKey($children, $parentKey = null, $result = [])
{
    foreach($children as $childKey => $childValue) {
        $key = implode('.', ($parentKey)? [$parentKey, $childKey] : [$childKey]);
        if (!is_array($childValue)) {
            $result[$key] = $childValue;
        } else {
            $result = array_merge($result, flattenKey($childValue, $key, $result));
        }
    }

    return $result;
}

function sanitize($value)
{
    $sanitized  = str_replace("\n","\\n", $value);
    $sanitized =  str_replace("\r","\\r", $sanitized);
    return $sanitized;
}

echo "done\n;";
