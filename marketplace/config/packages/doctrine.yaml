doctrine:
    dbal:
        url: '%env(resolve:DATABASE_URL)%'
        default_table_options:
            charset: utf8
            collate: utf8_unicode_ci
        # IMPORTANT: You MUST configure your server version,
        # either here or in the DATABASE_URL env var (see .env file)
        #server_version: '13'
    orm:
        auto_generate_proxy_classes: true
        naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware
        auto_mapping: true
        mappings:
            App:
                is_bundle: false
                type: annotation
                dir: '%kernel.project_dir%/src/AppBundle/Entity'
                prefix: 'AppBundle\Entity'
                alias: AppBundle
        filters:
            published_node: AppBundle\Doctrine\NodeStatusFilter
            node_language: AppBundle\Doctrine\NodeLanguageFilter
            