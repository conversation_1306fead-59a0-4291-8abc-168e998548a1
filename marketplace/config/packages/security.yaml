security:
    enable_authenticator_manager: true
    encoders:
        AppBundle\Entity\User: bcrypt
    password_hashers:
        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: 'bcrypt'
    providers:
        user_bdd:
            entity:
                class: AppBundle\Entity\User
                property: email
        supplier_db:
            entity:
                class: AppBundle\Entity\Merchant

    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false

        supplier:
            lazy: true
            pattern: ^(/front/supplier)|(/api(?!/buyer))
            provider: supplier_db
            custom_authenticators:
                - AppBundle\Security\Authenticator\IzbergVendorAuthenticator
            logout:
                path: supplier.logout
                target: supplier.logout.page
                invalidate_session: true

        admin:
            lazy: true
            pattern: /(admin(.*)|elfinder|efconnect)
            provider: user_bdd
            user_checker: AppBundle\Security\Checker\UserStatusChecker
            custom_authenticators:
                - AppBundle\Security\Authenticator\AdminFormAuthenticator
            logout:
                path: admin.logout
                target: admin.dashboard

        buyer-test:
            lazy: true
            provider: user_bdd
            user_checker: AppBundle\Security\Checker\UserStatusChecker
            custom_authenticators:
                - AppBundle\Security\Authenticator\BuyerFormAuthenticator
            logout:
                path: buyer.logout
                target: buyer.logout.page
                handlers: [ AppBundle\EventListener\ForceLogoutListener ]

        buyer:
            lazy: true
            provider: user_bdd
            user_checker: AppBundle\Security\Checker\UserStatusChecker
            custom_authenticators:
                - AppBundle\Security\Authenticator\TotalAuthenticator
            logout:
                path: buyer.logout
                target: buyer.logout.page
                invalidate_session: true
                handlers: [ AppBundle\EventListener\ForceLogoutListener ]

    role_hierarchy:
        ROLE_BUYER: ROLE_USER
        ROLE_SUPPLIER: ROLE_SUPPLIER
        ROLE_OPERATOR: [ROLE_BUYER]
        ROLE_SUPER_ADMIN: [ROLE_OPERATOR]
        ROLE_MANAGER: ROLE_BUYER
        ROLE_ENTITY_REPORTING: ROLE_MANAGER
        ROLE_MARKETPLACE_REPORTING: ROLE_ENTITY_REPORTING

    access_control:
        - { path: ^/attachment, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/contact, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/login$, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/register, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/resetting, role: IS_AUTHENTICATED_ANONYMOUSLY }

        - { path: ^/admin/login, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/admin/logout, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/admin/login_check, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/admin/resetting, role: IS_AUTHENTICATED_ANONYMOUSLY }

        - { path: ^/admin/, role: ROLE_OPERATOR }
        - { path: ^/efconnect, role: ROLE_OPERATOR }
        - { path: ^/elfinder, role: ROLE_OPERATOR }
        - { path: ^/admin/translations, role: ROLE_SUPER_ADMIN }
        - { path: ^/admin/jobs, role: ROLE_SUPER_ADMIN }

        # Front supplier access control
        - { path: ^/front/supplier/connect/start, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/front/supplier/logout/info, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/front/supplier, role: ROLE_SUPPLIER }
        - { path: ^/api(?!/buyer), role: ROLE_SUPPLIER }
        - { path: ^/api/buyer, role: ROLE_BUYER }
