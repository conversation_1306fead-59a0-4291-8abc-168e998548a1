fm_elfinder:
    #assets_path: / # default is /assets, this is where css/js elfinder files are
    instances:
        default:
            locale: '%locale%'
            editor: ckeditor # other options are tinymce, tinymce4, fm_tinymce, form, simple, custom
            #editor_template: custom template for your editor # default null
            #path_prefix: / # for setting custom assets path prefix, useful for non vhost configurations, i.e. http://127.0.0.1/mysite/
            #fullscreen: true|false # default is true, applies to simple and ckeditor editors
            #theme: smoothness # jquery theme, default is 'smoothness'
            #include_assets: true # disable if you want to manage loading of javascript and css assets manually
            #visible_mime_types: ['image/png', 'image/jpg', 'image/jpeg'] # only show these mime types, defaults to show all
            connector:
                #debug: true|false # defaults to false
                roots: # at least one root must be defined, defines root filemanager directories
                    uploads:
                        #show_hidden: true|false # defaults to false, hides dotfiles
                        driver: LocalFileSystem
                        path: images/uploads
                        upload_allow: [ 'image/png', 'image/jpg', 'image/jpeg' ]
                        upload_deny: [ 'all' ]
                        upload_max_size: 2M # also file upload sizes restricted in php.ini
                        #attributes: example of setting attributes permission
                        #    - { pattern: '/(.*?)/', read: true, write: false, locked: true }