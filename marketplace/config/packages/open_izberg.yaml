open_izberg:
    default_connection:
        ### BASICS ###
        domain: "%izberg_api_domain%"
        version: "%izberg_api_version%"
        protocol: "%izberg_api_protocol%"
        email: "%izberg_email%"
        first_name: "%izberg_first_name%"
        last_name: "%izberg_last_name%"
        seller_email_domain: '%izberg_email_seller_domain%'
        audience: '%izberg_audience%'
        identity_url: '%izberg_identity_url%'

        ### CHANGE WITH PLATFORM -> BY DEFAULT WE USE FRANCE PLATFORM ###
        application_id: "%izberg_france_application_id%"
        application_namespace: "%izberg_france_application_namespace%"
        access_token: "%izberg_france_access_token%"
        secret_key: "%izberg_france_secret_key%"
        username: "%izberg_france_username%"
        client_id: "%izberg_france_client_id%"
        client_secret: "%izberg_france_client_secret%"
        domain_id: "%izberg_france_domain_id%"

    other_connections:
        console:
            username: '%izberg_console_username%'
            access_token: '%izberg_console_access_token%'
        webhook:
            username: "%izberg_webhook_username%"
            access_token: "%izberg_webhook_access_token%"
