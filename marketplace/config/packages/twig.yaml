twig:
    default_path: '%kernel.project_dir%/templates'
    debug: '%kernel.debug%'
    strict_variables: '%kernel.debug%'
    globals:
        captcha_public: '%captcha_public%'
        captcha_enabled: '%captcha_enabled%'
        menuService: AppBundle\Repository\MenuRepository"
        seller_domain_url: "%izberg_merchant_base_url%"
        seller_domain_urls: "%izberg_merchant_base_urls%"
        catalog_csv_template_download: "https://s3.eu-central-1.amazonaws.com/stationone-media/prod/StationOne_My_Catalog_template.csv"
        ga_tracking: '%ga_tracking%'
        news_urls: "%news_urls%"
    form_themes:
        - '@OpenBack/Form/fields.html.twig'
        - '@FOSCKEditor/Form/ckeditor_widget.html.twig'
        #- 'AppBundle:Form:fields.html.twig'
        #- 'OpenAdminBundle:Form:javascripts.html.twig'

    number_format:
        decimals: 2
        decimal_point: '.'
        thousands_separator: ''

when@test:
    twig:
        strict_variables: true
