knpu_oauth2_client:
    # configure your clients as described here: https://github.com/knpuniversity/oauth2-client-bundle#configuration
    clients:
        total_buyer:
            type: generic
            client_id: '%env(TOTAL_OPEN_ID_CONNECT_CLIENT_ID)%'
            client_secret: '%env(TOTAL_OPEN_ID_CONNECT_CLIENT_SECRET)%'
            redirect_route: 'connect_buyer_check'
            provider_class: AppBundle\Security\Provider\TotalBuyerProvider
            client_class: AppBundle\Security\Model\TotalAuthClient
            provider_options:
                urlAuthorize: '%env(TOTAL_OPEN_ID_CONNECT_URL_AUTHORIZE)%'
                urlAccessToken: '%env(TOTAL_OPEN_ID_CONNECT_URL_ACCESS_TOKEN)%'
                urlResourceOwnerDetails: '%env(TOTAL_OPEN_ID_CONNECT_URL_RESOURCE_OWNER_DETAILS)%'
                urlLogout: '%env(TOTAL_OPEN_ID_CONNECT_URL_LOGOUT)%'
