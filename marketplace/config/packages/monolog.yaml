monolog:
    channels: ["ideal"]
    use_microseconds: true
    handlers:
        security_info:
            bubble: false
            type: stream
            path: '%logs_dir%/security.log'
            level: info
            channels: [ 'security' ]
            formatter: Monolog\Formatter\LineFormatter
        symfony_info:
            bubble: false
            type: stream
            path: '%logs_dir%/symfony.log'
            level: info
            channels: [ '!security', '!cron_analysed_logs', '!analysed_logs' ]
            formatter: Monolog\Formatter\LineFormatter
        app_info:
            bubble: false
            formatter: Monolog\Formatter\LineFormatter
            type: stream
            path: "%logs_dir%/app.log"
            level: info
            channels: [ 'analysed_logs' ]
        cron_app_info:
            bubble: false
            type: stream
            path: "%logs_dir%/cron_app.log"
            level: info
            channels: [ 'cron_analysed_logs' ]
            formatter: Monolog\Formatter\LineFormatter
        ideal:
            bubble: false
            type: stream
            path: "%logs_dir%/symfony.log"
            level: debug
            channels: [ 'ideal' ]
            formatter: Monolog\Formatter\LineFormatter
