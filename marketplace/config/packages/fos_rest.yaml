# Read the documentation: https://symfony.com/doc/master/bundles/FOSRestBundle/index.html
fos_rest:
    param_fetcher_listener: true
    format_listener:
        enabled: true
        rules:
            - { path: '^/api', priorities: [ 'json', 'xml' ], fallback_format: json, prefer_extension: false }
            - { path: '^/', stop: true } # Available for version >= 1.5

    body_converter:
        enabled: true
    view:
        view_response_listener: true
        formats: { json: true, xml: false, rss: false }
    serializer:
        serialize_null: true
    exception:
        enabled: true
        codes:
          { Open\FrontVendorBundle\Exception\ApiException: 400 }
