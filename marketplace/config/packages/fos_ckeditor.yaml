# Read the documentation: https://symfony.com/doc/current/bundles/FOSCKEditorBundle/index.html
fos_ck_editor:
    plugins:
        strinsert:
            path: "/bundles/app/ckeditor/strinsert/"
            filename: "plugin.js"
        showprotected:
            path: "/bundles/app/ckeditor/showprotected/"
            filename: "plugin.js"
    default_config: default
    configs:
        default:
            toolbar: [["Source","-","NewPage","Preview","Print","-","Templates"],["Cut","Copy","Paste","PasteText","PasteFromWord","-","Undo","Redo"],["Find","Replace","-","SelectAll","-","Scayt"],["Form","Checkbox","Radio","TextField","Textarea","SelectField","Button","ImageButton","HiddenField", "strinsert"],"\\/",["Bold","Italic","Underline","Strike","Subscript","Superscript","-","RemoveFormat"],["NumberedList","BulletedList","-","Outdent","Indent","-","Blockquote","CreateDiv","-","JustifyLeft","JustifyCenter","JustifyRight","JustifyBlock","-","BidiLtr","BidiRtl"],["Link","Unlink","Anchor"],["Image","Flash","Table","HorizontalRule","SpecialChar","Smiley","PageBreak","Iframe"],"\\/",["Styles","Format","Font","FontSize","TextColor","BGColor"],["TextColor","BGColor"],["Maximize","ShowBlocks"],["About"]]
            basicEntities: false
            entities: false
            entities_greek: false
            entities_latin: false
            entities_processNumerical: false
            forceSimpleAmpersand: true
            filebrowserBrowseRoute: elfinder
            filebrowserBrowseRouteParameters: [ ]
            allowedContent: true
            autoParagraph: false
        cms_content:
            toolbar: full #standard
            allowedContent: true
