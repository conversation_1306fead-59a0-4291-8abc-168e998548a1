parameters:
    elastic_hostname: '%env(string:ELASTIC<PERSON>ARCH_HOST)%:%env(int:ELASTICSEARCH_PORT)%'
    elastic_index: '%env(string:ELASTIC_INDEX)%'
    elastic_search_by_relevance: '%env(bool:ELASTIC_SEARCH_BY_RELEVANCE)%'
    elastic_show_score: '%env(bool:ELASTIC_SHOW_SCORE)%'
    elastic_ssl: '%env(bool:ELASTIC_SSL)%'
    elastic_certificate_path: '%env(string:ELASTIC_CERTIFICATE_PATH)%'
    ignored_categories_id: '%env(json:IGNORED_CATEGORIES_ID)%'
    home_category_list_france: '%env(json:HOME_CATEGORY_LIST_FRANCE)%'
    home_category_list_germany: '%env(json:HOME_CATEGORY_LIST_GERMANY)%'
    home_category_list_belgium: '%env(json:HOME_CATEGORY_LIST_BELGIUM)%'
    categories_priorities_list_france: '%env(json:CATEGORIES_PRIORITIES_LIST_FRANCE)%'
    categories_priorities_list_germany: '%env(json:CATEGORIES_PRIORITIES_LIST_GERMANY)%'
    categories_priorities_list_belgium: '%env(json:CATEGORIES_PRIORITIES_LIST_BELGIUM)%'
    elastic_facet_fields:
        merchant.name: 1
        merchant.id: 0
    facets_order_list:
        - "product.application_categories"
        - "merchant.name"
        - "attributes.0650_input_language.keyword"
        - "attributes.0170_shipping.keyword"
        - "attributes.0500_field.keyword"
        - "attributes.0820_rechargeable.keyword"
    elastic_searchable_fields:
        - "product.application_categories_dict.name^20"
        - "product.application_categories_dict.keywords"
        - "name^10"
        - "product.name"
        - "product.keywords^15"
        - "description"
        - "merchant.name.text"
        - "merchant.slug"
        - "_id"
        - "sku"

