parameters:
    logs_dir: '%env(resolve:APP_LOGS_DIR)%'
    root_dir: '%kernel.project_dir%'
    public_dir: '%kernel.project_dir%/public'
    quote_logo: '%kernel.project_dir%/public/images/logo_mp.png'
    slider_background_directory: '%kernel.project_dir%/public/images/sliders_backgrounds/'
    upload_directory: '/tmp/'
    quote_event_char: '#'
    quote_link_pattern:
        - '#LIEN:.*$'
        - '#Link:.*$'
        - '&gt;LIEN:.*$'
        - '#Lien:.*$'
    mail_bcc: { }
    epsa_address: 'Experbuy SAS'
    epsa_address2: '4 Quai des Etroits'
    epsa_zip_code: '69005'
    epsa_city: 'Lyon'
    epsa_country: 'france'
    ga_tracking: ''
    cgu_slug: 'gtu-buyers'
    anonymous_products_root: 'create_account'
    shipping_address_header: 'CLICK&BUY'
    epi_filter_category: '%env(int:EPI_FILTER_CATEGORY)%'
    merchant_language:
        - FR
        - EN
        - DE
        - NL

    captcha_public: '%env(resolve:CAPTCHA_PUBLIC)%'
    captcha_secret: '%env(resolve:CAPTCHA_SECRET)%'
    captcha_enabled: '%env(bool:CAPTCHA_ENABLED)%'
    mailer_from_email: '%env(string:MAILER_FROM_EMAIL)%'
    mailer_from_name: '%env(string:MAILER_FROM_NAME)%'
    domain: '%env(string:DOMAIN)%'
    protocol: '%env(string:PROTOCOL)%'
    bug_izberg_link_protocol: '%env(string:BUG_IZBERG_LINK_PROTOCOL)%'
    environment: '[%env(string:APP_ENV)%] '
    mail_environment: '%env(string:ENVIRONMENT)%'
    crypt_key: '%env(string:CRYPT_KEY)%'
    support_operator_email: '%env(string:SUPPORT_OPERATOR_EMAIL)%'
    marketplace_currency: '%env(string:MARKETPLACE_CURRENCY)%'

    default_redirect_type: '%env(int:DEFAULT_REDIRECT_TYPE)%'
    session_maxidletime: '%env(int:SESSION_MAXIDLETIME)%'
    stock_minimum: '%env(int:STOCK_MINIMUM)%'
    app_session_max_idle_time: '%env(int:APP_SESSION_MAX_IDLE_TIME)%'
    gc_maxlifetime: '%env(int:GC_MAXLIFETIME)%'
    gc_divisor: '%env(int:GC_DIVISOR)%'
    cache_default_lifetime: '%env(int:CACHE_DEFAULT_LIFETIME)%'
    reporting_min_average_offer_description_length: '%env(int:REPORTING_MIN_AVERAGE_OFFER_DESCRIPTION_LENGTH)%'

    api_vat_check: '%env(bool:API_VAT_CHECK)%'
    display_first_level_category_only: '%env(bool:DISPLAY_FIRST_LEVEL_CATEGORY_ONLY)%'

    supported_currencies: '%env(json:SUPPORTED_CURRENCIES)%'
    trusted_hosts: '%env(json:TRUSTED_HOSTS)%'
    common_email_providers: '%env(json:COMMON_EMAIL_PROVIDERS)%'
    countries:
        country.france: 1
        country.belgium: 2
        country.switzerland: 3
    genders:
        profile.gender.male: 'M'
        profile.gender.female: 'F'
    civs:
        profile.civ.mr: 'mr'
        profile.civ.ms: 'ms'

    ### TO CHECK IF IT WILL BE DELETED ###
    max_item_comparison: '%env(int:MAX_ITEM_COMPARISON)%'
    cache_payment_terms_ttl: '%env(int:CACHE_PAYMENT_TERMS_TTL)%'
    order_max_day_limit_before_autocancel: 15
    payment_nb_days_before_reminder: 5
    payment_nb_days_before_late_reminder:
        - 10
        - 20
        - 30
    payment_nb_hours_before_refund: 48
    credit_cart_timeout: 30
    cache_directory: '%kernel.project_dir%/var/cache/'
    cache_expiration: 3600
    reporting_export_time_limit: '%env(int:REPORTING_EXPORT_TIME_LIMIT)%'
    news_urls: '%env(json:NEWS_URLS)%'
    cart_lifetime_days: '%env(int:CART_LIFETIME_DAYS)%'
    risk_categories: '%env(json:RISK_CATEGORIES)%'
    default_risk_email: '%env(string:DEFAULT_RISK_EMAIL)%'
    sftp_params: '%env(json:SFTP_PARAMS)%'
    $sftp_epsa: '%env(json:SFTP_EPSA)%'
