parameters:
    #######################
    ### OPEN_ID_CONNECT ###
    #######################
    izberg_vendor_openidconnect_client_id: '%env(string:IZBERG_VENDOR_OPENIDCONNECT_CLIENT_ID)%'
    izberg_vendor_openidconnect_client_secret: '%env(string:IZBERG_VENDOR_OPENIDCONNECT_CLIENT_SECRET)%'
    izberg_vendor_openidconnect_url_authorize: '%env(string:IZBERG_VENDOR_OPENIDCONNECT_URL_AUTHORIZE)%'
    izberg_vendor_openidconnect_url_access_token: '%env(string:IZBERG_VENDOR_OPENIDCONNECT_URL_ACCESS_TOKEN)%'

    ##########################
    ### SSO AUTHENTICATION ###
    ##########################

    ### BASICS ###
    izberg_sso_valid_jwt_keys_url: "%env(string:IZBERG_SSO_VALID_JWT_KEYS_URL)%"
    izberg_sso_auth_base_url: "%env(string:IZBERG_SSO_AUTH_BASE_URL)%"
    izberg_sso_auth_token_url: "%env(string:IZBERG_SSO_AUTH_TOKEN_URL)%"

    ### FRANCE PLATFORM
    izberg_sso_france_client_id: "%env(string:IZBERG_SSO_FRANCE_CLIENT_ID)%"
    izberg_sso_france_client_secret: "%env(string:IZBERG_SSO_FRANCE_CLIENT_SECRET)%"
    izberg_sso_france_redirect_uri: "front_supplier_connect_check"

    ### GERMANY PLATFORM
    izberg_sso_germany_client_id: "%env(string:IZBERG_SSO_GERMANY_CLIENT_ID)%"
    izberg_sso_germany_client_secret: "%env(string:IZBERG_SSO_GERMANY_CLIENT_SECRET)%"
    izberg_sso_germany_redirect_uri: "front_supplier_connect_check"

    ### BELGIUM PLATFORM
    izberg_sso_belgium_client_id: "%env(string:IZBERG_SSO_BELGIUM_CLIENT_ID)%"
    izberg_sso_belgium_client_secret: "%env(string:IZBERG_SSO_BELGIUM_CLIENT_SECRET)%"
    izberg_sso_belgium_redirect_uri: "front_supplier_connect_check"
