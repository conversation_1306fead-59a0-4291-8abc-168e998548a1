parameters:
    app_settings:
        security: #domain
            login: #group
                login_attempt_max: #key
                    value_type: integer
                    required: true
                    tags: [ 'login' ]
                    default_value: 7
                login_administrator_token:
                    value_type: string
                    required: tue
                    tags: [ 'login' ]
                    default_value: x5RUkstQLuaUNi7OOrD0
                login_banned_user_unlock_timeout: #key
                    value_type: integer
                    required: true
                    tags: [ 'login','test' ]
                    default_value: 180
        offers: #domain
            popular: #group
                offer_1: #key
                    value_type: string
                    required: true
                    tags: [ 'offers' ]
                    default_value: ""
                offer_2: #key
                    value_type: string
                    required: false
                    tags: [ 'offers' ]
                    default_value: ""
                offer_3: #key
                    value_type: string
                    required: false
                    tags: [ 'offers' ]
                    default_value: ""
                offer_4: #key
                    value_type: string
                    required: false
                    tags: [ 'offers' ]
                    default_value: ""
                offer_5: #key
                    value_type: string
                    required: false
                    tags: [ 'offers' ]
                    default_value: ""
                offer_6: #key
                    value_type: string
                    required: false
                    tags: [ 'offers' ]
                    default_value: ""
                offer_7: #key
                    value_type: string
                    required: false
                    tags: [ 'offers' ]
                    default_value: ""
                offer_8: #key
                    value_type: string
                    required: false
                    tags: [ 'offers' ]
                    default_value: ""