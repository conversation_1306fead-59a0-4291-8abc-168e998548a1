parameters:
    izberg_api_domain: '%env(string:IZBERG_API_DOMAIN)%'
    izberg_api_version: '%env(int:IZBERG_API_VERSION)%'
    izberg_api_protocol: '%env(string:IZBERG_API_PROTOCOL)%'
    izberg_email: '%env(string:IZBERG_EMAIL)%'
    izberg_first_name: '%env(string:IZBERG_FIRST_NAME)%'
    izberg_last_name: '%env(string:IZBERG_LAST_NAME)%'
    izberg_merchant_base_url: '%env(string:IZBERG_MERCHANT_BASE_URL)%'
    izberg_merchant_base_urls: '%env(json:IZBERG_MERCHANT_BASE_URLS)%'
    izberg_email_seller_domain: '%env(string:IZBERG_EMAIL_SELLER_DOMAIN)%'
    izberg_audience: '%env(string:IZBERG_AUDIENCE)%'
    izberg_identity_url: '%env(string:IZBERG_IDENTITY_URL)%'
    izberg_console_username: '%env(string:IZBERG_CONSOLE_USERNAME)%'
    izberg_console_access_token: '%env(string:IZBERG_CONSOLE_ACCESS_TOKEN)%'
    izberg_webhook_username: '%env(string:IZBERG_WEBHOOK_USERNAME)%'
    izberg_webhook_access_token: '%env(string:IZBERG_WEBHOOK_ACCESS_TOKEN)%'
    izberg_category_icons_path: '%env(string:IZBERG_CATEGORY_ICONS_PATH)%'
    izberg_category_icons: '%env(json:IZBERG_CATEGORY_ICONS)%'
    izberg_date_pattern: '%env(string:IZBERG_DATE_PATTERN)%'
    izberg_merchant_carrier: '%env(int:IZBERG_MERCHANT_CARRIER)%'
    izberg_merchant_shipping_provider: '%env(int:IZBERG_MERCHANT_SHIPPING_PROVIDER)%'
    izberg_merchant_zone: '%env(int:IZBERG_MERCHANT_ZONE)%'

    izberg_attribute_adapted: "017_adapted_company"
    izberg_security_attribute: "015_contrat_cadre"
    izberg_attribute_branch: "013_branch"


    ### FRANCE PLATFORM ###
    izberg_france_application_id: '%env(int:IZBERG_FRANCE_APPLICATION_ID)%'
    izberg_france_application_namespace: '%env(string:IZBERG_FRANCE_APPLICATION_NAMESPACE)%'
    izberg_france_access_token: '%env(string:IZBERG_FRANCE_ACCESS_TOKEN)%'
    izberg_france_secret_key: '%env(string:IZBERG_FRANCE_SECRET_KEY)%'
    izberg_france_username: '%env(string:IZBERG_FRANCE_USERNAME)%'
    izberg_france_client_id: '%env(string:IZBERG_FRANCE_CLIENT_ID)%'
    izberg_france_client_secret: '%env(string:IZBERG_FRANCE_CLIENT_SECRET)%'
    izberg_france_domain_id: '%env(string:IZBERG_FRANCE_DOMAIN_ID)%'

    ### GERMANY PLATFORM ###
    izberg_germany_application_id: '%env(int:IZBERG_GERMANY_APPLICATION_ID)%'
    izberg_germany_application_namespace: '%env(string:IZBERG_GERMANY_APPLICATION_NAMESPACE)%'
    izberg_germany_access_token: '%env(string:IZBERG_GERMANY_ACCESS_TOKEN)%'
    izberg_germany_secret_key: '%env(string:IZBERG_GERMANY_SECRET_KEY)%'
    izberg_germany_username: '%env(string:IZBERG_GERMANY_USERNAME)%'
    izberg_germany_client_id: '%env(string:IZBERG_GERMANY_CLIENT_ID)%'
    izberg_germany_client_secret: '%env(string:IZBERG_GERMANY_CLIENT_SECRET)%'
    izberg_germany_domain_id: '%env(string:IZBERG_GERMANY_DOMAIN_ID)%'


    ### BELGIUM PLATFORM ###
    izberg_belgium_application_id: '%env(int:IZBERG_BELGIUM_APPLICATION_ID)%'
    izberg_belgium_application_namespace: '%env(string:IZBERG_BELGIUM_APPLICATION_NAMESPACE)%'
    izberg_belgium_access_token: '%env(string:IZBERG_BELGIUM_ACCESS_TOKEN)%'
    izberg_belgium_secret_key: '%env(string:IZBERG_BELGIUM_SECRET_KEY)%'
    izberg_belgium_username: '%env(string:IZBERG_BELGIUM_USERNAME)%'
    izberg_belgium_client_id: '%env(string:IZBERG_BELGIUM_CLIENT_ID)%'
    izberg_belgium_client_secret: '%env(string:IZBERG_BELGIUM_CLIENT_SECRET)%'
    izberg_belgium_domain_id: '%env(string:IZBERG_BELGIUM_DOMAIN_ID)%'
