open_izberg:
    other_connections:
        [CONF_KEY_FROM_MARKETPLACE_DB_TABLE]:
            application_namespace: "[application_namespace]"
            application_id: "[application_id]"
            username: "[username]"
            access_token: "[access_token]"
            secret_key: "[secret_key]"
            client_id: "[client_id]"
            client_secret: "[client_secret]"
            domain_id: "[domain_id]"

knpu_oauth2_client:
    clients:
        izberg_vendor_client_[choose_a_name]:
            type: generic
            client_id: '[client_id]'
            client_secret: '[client_secret]'
            provider_class: \AppBundle\Security\IzbergVendorProvider
            redirect_route: front_supplier_connect_check
            redirect_params:
                marketplace: [choose_a_name] #same as in line 14
            provider_options:
                urlAuthorize: '%izberg_vendor_openidconnect_url_authorize%'
                urlAccessToken: '%izberg_vendor_openidconnect_url_access_token%'
                izbergApiDomain: '%izberg_api_domain%'

## add import in config_mkp.yml
## don't forget A<PERSON><PERSON><PERSON><PERSON>\Security\IzbergVendorAuthenticator in services.yml
