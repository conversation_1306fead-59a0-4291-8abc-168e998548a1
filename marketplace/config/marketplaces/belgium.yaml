open_izberg:
    other_connections:
        sand_click_and_buy_belgium:
            application_id: "%izberg_belgium_application_id%"
            application_namespace: "%izberg_belgium_application_namespace%"
            access_token: "%izberg_belgium_access_token%"
            secret_key: "%izberg_belgium_secret_key%"
            username: "%izberg_belgium_username%"
            client_id: "%izberg_belgium_client_id%"
            client_secret: "%izberg_belgium_client_secret%"
            domain_id: "%izberg_belgium_domain_id%"

knpu_oauth2_client:
    clients:
        izberg_vendor_client_belgium:
            type: generic
            client_id: '%izberg_sso_belgium_client_id%'
            client_secret: '%izberg_sso_belgium_client_secret%'
            redirect_route: '%izberg_sso_belgium_redirect_uri%'
            provider_class: AppBundle\Security\IzbergVendorProvider
            redirect_params:
                marketplace: 'belgium'
            provider_options:
                urlAuthorize: '%izberg_vendor_openidconnect_url_authorize%'
                urlAccessToken: '%izberg_vendor_openidconnect_url_access_token%'
                izbergApiDomain: '%izberg_api_domain%'
