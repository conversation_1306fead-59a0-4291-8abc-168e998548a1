open_izberg:
    other_connections:
        sand_clickandbuy_02:
            application_id: "%izberg_germany_application_id%"
            application_namespace: "%izberg_germany_application_namespace%"
            access_token: "%izberg_germany_access_token%"
            secret_key: "%izberg_germany_secret_key%"
            username: "%izberg_germany_username%"
            client_id: "%izberg_germany_client_id%"
            client_secret: "%izberg_germany_client_secret%"
            domain_id: "%izberg_germany_domain_id%"

knpu_oauth2_client:
    clients:
        izberg_vendor_client_germany:
            type: generic
            client_id: '%izberg_sso_germany_client_id%'
            client_secret: '%izberg_sso_germany_client_secret%'
            redirect_route: '%izberg_sso_germany_redirect_uri%'
            provider_class: AppBundle\Security\IzbergVendorProvider
            redirect_params:
                marketplace: 'germany'
            provider_options:
                urlAuthorize: '%izberg_vendor_openidconnect_url_authorize%'
                urlAccessToken: '%izberg_vendor_openidconnect_url_access_token%'
                izbergApiDomain: '%izberg_api_domain%'
