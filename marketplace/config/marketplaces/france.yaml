open_izberg:
    default_connection:
        application_id: "%izberg_france_application_id%"
        application_namespace: "%izberg_france_application_namespace%"
        access_token: "%izberg_france_access_token%"
        secret_key: "%izberg_france_secret_key%"
        username: "%izberg_france_username%"
        client_id: "%izberg_france_client_id%"
        client_secret: "%izberg_france_client_secret%"
        domain_id: "%izberg_france_domain_id%"


knpu_oauth2_client:
    clients:
        izberg_vendor_client_france:
            type: generic
            client_id: '%izberg_sso_france_client_id%'
            client_secret: '%izberg_sso_france_client_secret%'
            redirect_route: '%izberg_sso_france_redirect_uri%'
            provider_class: AppBundle\Security\IzbergVendorProvider
            redirect_params:
                marketplace: 'france'
            provider_options:
                urlAuthorize: '%izberg_vendor_openidconnect_url_authorize%'
                urlAccessToken: '%izberg_vendor_openidconnect_url_access_token%'
                izbergApiDomain: '%izberg_api_domain%'
