login_check:
    path: /login_check
    options:
        i18n: false

login_failure_route:
    path: /login/failure
    defaults: { _controller: OpenFrontBundle:Login:loginFailure }
    options:
        i18n: false

login_success_route:
    path: /login/success
    defaults: { _controller: OpenFrontBundle:Login:loginSuccess }
    options:
        i18n: false

login:
    path: /login
    defaults: { _controller: OpenFrontBundle:Login:login }
    options:
        i18n: false

fos_js_routing:
    resource: "@FOSJsRoutingBundle/Resources/config/routing/routing.xml"

open_ticket:
    resource: "@OpenTicketBundle/Controller/"
    type:     annotation
    prefix:   /

admin_tickets_list:
    path: /admin/tickets
    defaults: { _controller: OpenTicketBundle:Ticket:listAdminTickets}
    options:
        i18n: false

admin_ticket_edit:
    path: /admin/ticket/edit
    defaults: { _controller: OpenTicketBundle:Ticket:editAdminTicket}
    options:
        i18n: false

admin_ticket_create:
    path: /admin/ticket/create
    defaults: { _controller: OpenTicketBundle:Ticket:createAdminTicket}
    options:
        i18n: false

app:
    resource: "@AppBundle/Controller/"
    type: annotation

admin_area:
    resource: "@OpenBackBundle/Controller/"
    type:     annotation
    prefix:   /admin
    options:
        i18n: false

front_area:
    resource: "@OpenFrontBundle/Controller/"
    type: annotation

front_vendor_area:
    resource: '@OpenFrontVendorBundle/Controller'
    type: annotation
    options:
        i18n: false

izberg_webhook:
    resource: "@OpenIzbergBundle/WebHook/"
    type: annotation
    options:
        i18n: false

izberg_webhelp:
    resource: "@OpenWebhelpBundle/Controller/"
    type: annotation
    options:
        i18n: false
catch_all:
    path:     /{slug}
    defaults:
        _controller: "Open\\FrontBundle\\Controller\\DefaultController::catchAllAction"
    requirements:
        slug: ".+" #probably not needed since the '/' route is already used for the homepage