kernel:
    resource: ../../src/AppBundle/Kernel.php
    type: annotation

open_front:
    resource: '@OpenFrontBundle/Controller/'
    type: annotation
    prefix: /{_locale}
    requirements:
        _locale: '%app_locales%'
    defaults:
        _locale: '%locale%'

open_front_vendor:
    resource: '@OpenFrontVendorBundle/Controller/'
    type: annotation

app_bundle:
    resource: '@AppBundle/Controller/'
    type: annotation
    prefix: /{_locale}
    requirements:
        _locale: '%app_locales%'
    defaults:
        _locale: '%locale%'

open_back:
    resource: '@OpenBackBundle/Controller/'
    type: annotation
    prefix:   /admin
    options:
        i18n: false

open_izberg:
    resource: '@OpenIzbergBundle/WebHook/'
    type: annotation
