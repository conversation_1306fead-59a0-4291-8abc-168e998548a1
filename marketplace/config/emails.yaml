RESET_PASSWORD_TO_USER:
    body: "Hello  {{firstName}} {{lastName}}.  Follow link to change password : {{url}}"
    variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "url"
      default: ""
    - name: "host"
      default: ""

VENDOR_ACCOUNT_VALIDATED_TO_VENDOR:
    body: "Hello {{firstName}} {{lastName}} Your supplier profile {{companyName}} has been validated by the Click & Buy team on markeplace {{marketplace}}. You now have access to your profile through the following link:<a href=\"{{link}}\">Your supplier account</a>.\r\n
    We invite you to proceed to the last stage of registration on the platform by adding the missing information and legal documents.\r\n
    Once these elements are provided, we invite you to send a confirmation message to the Click & Buy team within the platform. To do this, please click on 'Messages' and send your confirmation to the following recipient: Total - Europe \r\n
    Point of attention: this confirmation is a prerequisite for the final validation of your account! \r\n
    We thank you in advance ! \r\n
    Cordially \r\n
    The Click & Buy team "
    variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "companyName"
      default: "my company online shop"
    - name: "host"
      default: "https://total-europe.izberg-marketplace.com/"
    - name: "email"
      default: "email"
    - name: "link"
      default: "https://total-europe.izberg-marketplace.com/"
    - name: "marketplace"
      default: "marketplace"



VENDOR_ACCOUNT_CREATION_TO_VENDOR:
    body: "Hello {{firstName}} {{lastName}} Your supplier account for the company {{companyName}} on marketplace {{marketplace}} has been taken into account. Our teams study the first information transmitted within 48 hours. \r\n
    Thank you \r\n
    Cordially \r\n
    The Click & Buy team  \r\n"
    variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "companyName"
      default: ""
    - name: "host"
      default: ""
    - name: "marketplace"
      default: "marketplace"


VENDOR_ACCOUNT_CREATION_TO_OPERATOR:
    body: "Hello {{firstName}} {{lastName}}. \r\n
    A new supplier has registered on the platform {{marketplace}}. \r\n
    Company name: {{companyName}} \r\n
    User: {{merchantFirstName}} {{merchantLastName}}  \r\n
    E-mail : {{merchantEmail}}"
    variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "companyName"
      default: ""
    - name: "merchantFirstName"
      default: "Jean"
    - name : "merchantLastName"
      default: "Dupont"
    - name : "merchantEmail"
      default: "<EMAIL>"
    - name: "host"
      default: ""
    - name: "url"
      default: ""
    - name: "marketplace"
      default: "marketplace"

VENDOR_ACCOUNT_REJECTED_TO_VENDOR:
    body: "Hello {{firstName}} {{lastName}}. Your account provider {{companyName}} unfortunately has not been validated on marketplace {{marketplace}} by the team Click & Buy for the following reasons: {{comment}} \r\n
    Thank you for submitting your profile. \r\n
    Cordially \r\n
    The Click & Buy team"
    variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "companyName"
      default: ""
    - name: "comment"
      default: "Some information about your company are missing. Please contact us for more information"
    - name: "host"
      default: ""
    - name: "marketplace"
      default: "marketplace"

ILLEGAL_CONTENT_TO_OPERATOR:
    body: "Hello {{firstName}} {{lastName}}. An illegal content has been reported by {{reporterFirstName}} {{reporterLastName}} from company {{reporterCompanyName}} on the following {{url}} <br> {{content}}"
    variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "reporterFirstName"
      default: "reporterFirstName"
    - name: "reporterLastName"
      default: "reporterLastName"
    - name: "reporterCompanyName"
      default: "my buyer company"
    - name: "url"
      default: ""
    - name: "content"
      default: "this is illegal content. Please moderate me"
    - name: "host"
      default: ""

INVITED_SUPPLIER_ONBOARDING_PROGRESS_TO_BUYER:
    body: "The supplier {{supplierName}} that you have invited, just passed the step on marketplace {{marketplace}}: {{registrationStep}}{% if reason|length>0%}<p>reason: {{reason}}</p>{%endif%} "
    variables:
      - name: "supplierName"
        default: "supplierName"
      - name: "registrationStep"
        default: "ONBOARDING_STEP"
      - name: "reason"
        default: "reason"
      - name: "marketplace"
        default: "marketplace"

TICKET_THREAD_UPDATED_BY_VENDOR_TO_BUYER:
    body: "Hello {{firstName}} {{lastName}}. Vendor {{vendor}} has replied to your message: {{content}}<P>Click this <a href='{{url}}'>link</a> to see the thread.</P>"
    variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "vendor"
      default: "Vendor Name"
    - name: "content"
      default: "Hello, I confirm that we can deliver this item."
    - name: "host"
      default: ""
    - name: "ticketNumber"
      default: "123ABC"
    - name: "subject"
      default: "this is the subject"
    - name: "url"
      default : "url"

TICKET_THREAD_UPDATED_BY_OPERATOR_TO_BUYER:
    body: "Hello {{firstName}} {{lastName}}. An operator has replied to your message: {{content}}<P>Click this <a href='{{url}}'>link</a> to see the thread.</P>"
    variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "content"
      default: "Hello, I confirm that we can deliver this item."
    - name: "host"
      default: ""
    - name: "ticketNumber"
      default: "123ABC"
    - name: "subject"
      default: "this is the subject"
    - name: "url"
      default : "url"

BEST_OFFER_REPORTING_TO_VENDOR:
  body: "<p>Bonjour {{firstName}} {{lastName}},</p> <p>Voici les statistiques de votre boutique {{merchantName}}.</p> <p>Sur {{totalCountOffers}} articles importés au catalogue, l'analyse a pu se faire sur {{countAnalysedOffers}} articles</p>{%if percentWithoutPicture > 0 %}<p>{{percentWithoutPicture}}% de vos articles n'ont pas d'image. L'utilisation d'images permet d'améliorer la visibilité de vos produits</p>{%endif%}{%if descriptionTooShort %} <p>Alerte : La description de vos produits/services semblent trop courte: En moyenne {{averageCharsInDescription}} caractères </p>{%endif%} <p>{{percentBest}}% de vos articles analysés sont les mieux placés commercialement</p><p>Vous trouverez en pièce jointe, le reporting par article</p>"
  variables:
  - name: "firstName"
    default: "firstName"
  - name: "lastName"
    default: "lastName"
  - name: "merchantName"
    default: "merchantName"
  - name: "totalCountOffers"
    default: 10
  - name: "countAnalysedOffers"
    default: 10
  - name: "percentWithoutPicture"
    default: 90
  - name: "descriptionTooShort"
    default: true
  - name: "averageCharsInDescription"
    default: 8
  - name: "percentBest"
    default: 92

# Confirm buyer his checkout
CART_CHECKOUT_CONFIRMATION_TO_BUYER:
  body: '<p>Hello {{firstName}} {{lastName}},</p><p>We confirm your orders : </p><ul>{%for item in orderItems%}<li>{{item.orderNumber}}</li>{%endfor%}</ul><p>from cart number {{ cartNumber }}.</p><p>Your orders pending manager validation.</p>'
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "cartNumber"
      default: "cartNumber"
    - name: "ordersNumber"
      default: "ordersNumber "
    - name: "orderItems"
      children:
        - name: "orderNumber"
          default: "orderNumber"

# Confirm buyer his checkout without manager validation
CART_CONFIRMED_DIRECTLY_TO_BUYER:
  body: '<p>Hello {{firstName}} {{lastName}},</p>
         <p>We confirm your orders :<br>Order number : {{ cartNumber }}.</p>
         <p>List of items:
             <table style="width: 100%;">
                 <thead>
                     <tr>
                         <th style="color: #537DC7;">
                             Supplier
                         </th>
                         <th style="color: #537DC7;">
                             Quantity
                         </th>
                         <th style="color: #537DC7;">
                             Name
                         </th>
                         <th style="color: #537DC7;">
                             Unit price HT
                         </th>
                         <th style="color: #537DC7;">
                             Total price HT
                         </th>
                     </tr>
                 </thead>
                 <tbody>
                     {%for item in items%}
                     <tr>
                         <td>
                             {{item.merchantName}}
                         </td>
                         <td>
                             {{item.quantity}}
                         </td>
                         <td>
                             {{item.name}}
                         </td>
                         <td>
                             {{item.unitPrice}} {{item.currency}}
                         </td>
                         <td>
                             {{item.totalPrice}} {{item.currency}}
                         </td>
                     </tr>
                     {%endfor%}
                 </tbody>
             </table>
         <p>'
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "cartNumber"
      default: "cartNumber"
    - name: "items"
      children:
        - name: "name"
          default: "item name"
        - name: "quantity"
          default: "item quantity"
        - name: "unitPrice"
          default: "item unit price"
        - name: "totalPrice"
          default: "item total price"
        - name: "currency"
          default: "item currency"
        - name: "merchantName"
          default: "merchant name"

# Warn manager that a buyer send an order to validation
CART_PENDING_VALIDATION_TO_MANAGER_OR_DELEGATE:
  body: '<p>Hello {{firstName}} {{lastName}},</p><p>You have an order from {{firstNameBuyer}} {{lastNameBuyer}} waiting for your validation.<br>Order number : {{ cartNumber }}.</p><p>Please click on this link to open the order and manage it :<br><a href="{{ validationUrl }}">{{ validationUrl }}</a></p>{{ msgIfRiskCart }}<p>List of items:{%for item in items%} <p>{{item.name}} {{item.quantity}} {{item.unitPrice}} {{item.currency}} {{item.totalPrice}} {{item.currency}} {{item.merchantName}}</p> {%endfor%}<p>'
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "firstNameBuyer"
      default: "lastNameBuyer"
    - name: "lastNameBuyer"
      default: "lastNameBuyer"
    - name: "cartNumber"
      default: "cartNumber"
    - name: "validationUrl"
      default: "validationUrl"
    - name: "shippingAmountHt"
      default: "shippingAmountHt"
    - name: "msgIfRiskCart"
      default: "msgIfRiskCart"
    - name: "items"
      children:
        - name: "name"
          default: "item name"
        - name: "quantity"
          default: "item quantity"
        - name: "unitPrice"
          default: "item unit price"
        - name: "totalPrice"
          default: "item total price"
        - name: "currency"
          default: "item currency"
        - name: "merchantName"
          default: "merchant name"

# Warn buyer that his manager have validated an order
CART_CONFIRMED_TO_BUYER:
  body: '<p>Hello {{firstName}} {{lastName}},</p><p>Your cart n° {{ cartNumber }} have been accepted by {{firstNameValidator}} {{lastNameValidator}} (orders can be validated/refused by your manager or one of his delegates).</p><p>Your order is now sent to supplier to acceptation.</p><p>Best regards</p>'
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "firstNameValidator"
      default: "firstNameValidator"
    - name: "lastNameValidator"
      default: "lastNameValidator"
    - name: "cartNumber"
      default: "cartNumber"

# Warn buyer that manager have rejected his order
CART_REJECTED_TO_BUYER:
  body: '<p>Hello {{firstName}} {{lastName}},</p><p>Your cart n° {{ cartNumber }} have been refused by {{firstNameValidator}} {{lastNameValidator}} (orders can be validated/refused by your manager or one of his delegates).<p>Reason : {{ reason }}</p><p>Best regards</p>'
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "firstNameValidator"
      default: "firstNameValidator"
    - name: "lastNameValidator"
      default: "lastNameValidator"
    - name: "cartNumber"
      default: "cartNumber"
    - name: "reason"
      default: "reason"

# Warn validator that buyer have cancelled his order
CART_CANCELLED_TO_MANAGER_OR_DELEGATE:
  body: '<p>Hello {{firstNameValidator}} {{lastNameValidator}},</p>{%if cartNumber is empty %}<p>The cart n° {{ cartNumber }}{%endif%} {%if orderNumber is empty %}<p>The order n° {{ orderNumber }}{%endif%} has been cancelled by {{firstName}} {{lastName}}.<p>Reason : {{ reason }}</p><p>Best regards</p>'
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "firstNameValidator"
      default: "firstNameValidator"
    - name: "lastNameValidator"
      default: "lastNameValidator"
    - name: "cartNumber"
      default: "cartNumber"
    - name: "orderNumber"
      default: "orderNumber"
    - name: "reason"
      default: "reason"

CART_CANCELLED_TO_MERCHANT:
  body: '<p>Hello {{firstName}} {{lastName}},</p><p>The order n° {{ orderNumber }} have been cancelled by {{userFirstName}} {{userLastName}}.</p><p>Reason: {{reason}}</p><p> Follow this <a href="https://supplier.clickandbuy.total/{{merchantSlug}}/orders/details/{{orderId}}/">link</a> to see the order.</p><p>Best regards</p>'
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "orderNumber"
      default: "orderNumber"
    - name: "reason"
      default: "reason"
    - name: "merchantSlug"
      default: "merchantSlug"
    - name: "userFirstName"
      default: "userFirstName"
    - name: "userLastName"
      default: "userLastName"
    - name: "reason"
      default: "reason"
    - name: "orderId"
      default: "orderId"

CART_CANCELLED_TO_BUYER:
  body: '<p>Hello {{firstName}} {{lastName}},</p> {%if cartNumber is defined %}<p>Your cart n° {{ cartNumber }} have been cancelled.</p>{%endif%} {%if orderNumber is defined %}<p>Your order n° {{ orderNumber }} have been cancelled.</p>{%endif%} <p> Your reason: {{reason}}</p> <p>Best regards</p>'
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "cartNumber"
      default: "cartNumber"
    - name: "orderNumber"
      default: "orderNumber"
    - name: "reason"
      default: "reason"

# Warn buyer that seller have validated his order
ORDER_CONFIRMED_BY_VENDOR_TO_BUYER:
  body: "<p>Hello {{firstName}} {{lastName}}.</p> <p>Merchant {{vendor}} has validated your order {{orderNumber}}.</p> <p>List of validated items:{%for item in validatedItems%} <p>{{item.name}} {{item.quantity}} {{item.amount}} {{item.totalPrice}} {{item.currency}} </p> {%endfor%}{%for item in cancelledItems%} <p>{{item.name}} {{item.quantity}} {{item.amount}} {{item.totalPrice}} {{item.currency}} </p> {%endfor%}<p> Share your experience following {{feedbackLink}} </p>"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "vendor"
      default: "Vendor Name"
    - name: "orderNumber"
      default: "orderNumber"
    - name: "cartNumber"
      default: "cartNumber"
    - name: "feedbackLink"
      default: "feedbackLink"
    - name: "shippingAmountHt"
      default: "shippingAmountHt"
    - name: "validatedItems"
      children:
        - name: "name"
          default: "name"
        - name: "quantity"
          default: "quantity"
        - name: "amount"
          default: "amount"
        - name: "totalPrice"
          default: "total price"
        - name: "currency"
          default: "currency"
    - name: "cancelledItems"
      children:
        - name: "name"
          default: "name"
        - name: "quantity"
          default: "quantity"
        - name: "amount"
          default: "amount"
        - name: "totalPrice"
          default: "total price"
        - name: "currency"
          default: "currency"

# Warn buyer that seller have reject his order
ORDER_REJECTED_BY_VENDOR_TO_BUYER:
  body: "<p>Hello {{firstName}} {{lastName}}.</p> <p>Merchant {{vendor}} has rejected your order {{orderNumber}}.</p> <p>List of rejected items:{%for item in items%} <p>{{item.name}} {{item.quantity}} {{item.unitPrice}} {{item.currency}} {{item.totalPrice}} {{item.currency}} </p> {%endfor%}"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "vendor"
      default: "Vendor Name"
    - name: "orderNumber"
      default: "orderNumber"
    - name: "cartNumber"
      default: "cartNumber"
    - name: "reason"
      default: "reason"
    - name: "shippingAmountHt"
      default: "shippingAmountHt"
    - name: "items"
      children:
        - name: "name"
          default: "name"
        - name: "quantity"
          default: "quantity"
        - name: "unitPrice"
          default: "unitPrice"
        - name: "totalPrice"
          default: "total price"
        - name: "currency"
          default: "currency"

ORDER_REJECTED_BY_VENDOR_TO_VENDOR:
  body: "<p>Hello {{firstNameVendor}} {{lastNameVendor}}.</p> <p><a href=\"{{orderUrl}}\">You have rejected this order {{orderNumber}}</a>.</p><p>List of rejected items:{%for item in items%} <p>{{item.name}} {{item.quantity}} {{item.unitPrice}} {{item.currency}} {{item.totalPrice}} {{item.currency}} </p> {%endfor%}"
  variables:
    - name: "firstNameVendor"
      default: "firstNameVendor"
    - name: "lastNameVendor"
      default: "lastNameVendor"
    - name: "firstNameBuyer"
      default: "firstNameBuyer"
    - name: "lastNameBuyer"
      default: "lastNameBuyer"
    - name: "orderNumber"
      default: "orderNumber"
    - name: "cartNumber"
      default: "cartNumber"
    - name: "reason"
      default: "reason"
    - name: "orderUrl"
      default: "orderUrl"
    - name: "shippingAmountHt"
      default: "shippingAmountHt"
    - name: "items"
      children:
        - name: "name"
          default: "name"
        - name: "quantity"
          default: "quantity"
        - name: "unitPrice"
          default: "unitPrice"
        - name: "totalPrice"
          default: "total price"
        - name: "currency"
          default: "currency"

# Invite supplier
BUYER_INVITIATION_TO_SUPPLIER:
  body: |
    <p>Hello {{firstNameSupplier}} {{lastNameSupplier}},</p>
    <p>{{userNeeds|raw}}</p>
    <p>{{message|raw}}</p>
    <p>If you are agree, <a href="{{ urlRegister }}">click here to register an account</a></p>
    <p><a href="mailto: {{ emailBuyer }}">{{ firstNameBuyer }} {{ lastNameBuyer }}</a></p>
  variables:
    - name: "marketplace"
      default: "marketplace"
    - name: "firstNameBuyer"
      default: "firstNameBuyer"
    - name: "lastNameBuyer"
      default: "lastNameBuyer"
    - name: "emailBuyer"
      default: "emailBuyer"
    - name: "firstNameSupplier"
      default: "firstNameSupplier"
    - name: "lastNameSupplier"
      default: "lastNameSupplier"
    - name: "companyNameSupplier"
      default: "companyNameSupplier"
    - name: "phoneSupplier"
      default: "phoneSupplier"
    - name: "emailSupplier"
      default: "emailSupplier"
    - name: "urlRegister"
      default: "urlRegister"
    - name: "userNeeds"
      default: "userNeeds"
    - name: "message"
      default: "message body"

# Invite reminder supplier
BUYER_INVITATION_REMINDER_TO_SUPPLIER:
  body: |
    <p>Hello {{firstNameSupplier}} {{lastNameSupplier}},</p>
    <p>{{message|raw}}</p>
    <p><a href="mailto: {{ emailBuyer }}">{{ firstNameBuyer }} {{ lastNameBuyer }}</a></p>
  variables:
    - name: "firstNameBuyer"
      default: "firstNameBuyer"
    - name: "lastNameBuyer"
      default: "lastNameBuyer"
    - name: "emailBuyer"
      default: "emailBuyer"
    - name: "firstNameSupplier"
      default: "firstNameSupplier"
    - name: "lastNameSupplier"
      default: "lastNameSupplier"
    - name: "companyNameSupplier"
      default: "companyNameSupplier"
    - name: "phoneSupplier"
      default: "phoneSupplier"
    - name: "emailSupplier"
      default: "emailSupplier"
    - name: "urlRegister"
      default: "urlRegister"
    - name: "message"
      default: "message body"

# Notify cart validation to managers and buyers
CART_CONFIRMED_TO_MANAGER_OR_DELEGATE:
  body: '<p>Hello {{firstName}} {{lastName}},</p><p>The order {{ cartNumber }} from {{firstNameBuyer}} {{lastNameBuyer}} have been accepted.</p><p>You can see detail with this link :<br><a href="{{orderUrl}}">{{orderUrl}}</a></p>'
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "firstNameBuyer"
      default: "firstNameBuyer"
    - name: "lastNameBuyer"
      default: "lastNameBuyer"
    - name: "cartNumber"
      default: "cartNumber"
    - name: "orderUrl"
      default: "orderUrl"

# Notify cart reject to managers and buyers
CART_REJECTED_TO_MANAGER_OR_DELEGATE:
  body: '<p>Hello {{firstName}} {{lastName}},</p><p>The order {{ cartNumber }} from {{firstNameBuyer}} {{lastNameBuyer}} have been rejected.</p><p>You can see detail with this link :<br><a href="{{orderUrl}}">{{orderUrl}}</a></p>'
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "firstNameBuyer"
      default: "firstNameBuyer"
    - name: "lastNameBuyer"
      default: "lastNameBuyer"
    - name: "cartNumber"
      default: "cartNumber"
    - name: "orderUrl"
      default: "orderUrl"

DELEGATION_ADDED_BY_MANAGER_TO_MANAGER:
  body: "<p>Hello {{firstName}} {{lastName}}</p><p>You added {{ firstNameDelegate }} {{ lastNameDelegate }} as delegate of your manager account."
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "firstNameDelegate"
      default: "firstNameDelegate"
    - name: "lastNameDelegate"
      default: "lastNameDelegate"

DELEGATION_ADDED_BY_MANAGER_TO_DELEGATE:
  body: "<p>Hello {{firstName}} {{lastName}}</p><p>You have been added as delegate by {{ firstNameManager }} {{ lastNameManager }} for orders management."
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "firstNameManager"
      default: "firstNameManager"
    - name: "lastNameManager"
      default: "lastNameManager"


DELEGATION_REMOVE_BY_MANAGER_TO_MANAGER:
  body: "<p>Hello {{firstName}} {{lastName}}</p><p>You removed {{ firstNameDelegate }} {{ lastNameDelegate }} as delegate of your manager account."
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "firstNameDelegate"
      default: "firstNameDelegate"
    - name: "lastNameDelegate"
      default: "lastNameDelegate"

DELEGATION_REMOVED_BY_MANAGER_TO_DELEGATE:
  body: "<p>Hello {{firstName}} {{lastName}}</p><p>You have been removed as delegate of {{ firstNameManager }} {{ lastNameManager }} for orders management."
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "firstNameManager"
      default: "firstNameManager"
    - name: "lastNameManager"
      default: "lastNameManager"

ADMIN_ACCOUNT_CREATION_TO_ACCOUNT:
  body: "<p>Hello {{firstName}} {{lastName}},</p><p>An account have been created for you on our platform with this email : {{email}}.</p><p>follow this link to define your password : <a href=\"{{link}}\">Your account</a></p>"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "link"
      default: "[link default value]"
    - name: "email"
      default: "[email default value]"

QUOTE_NEW_QUOTE_TO_BUYER:
  body: "<p>Hello {{firstName}} {{lastName}}</p><p>You have ask for a quote for the following offer:</p><p> {{offerName}}</p><p>Supplier: {{supplierName}}</p><p>Message is: {{message}}</p><p> You can follow this quote at this <a href=\"{{url}}\">Link</a></p>"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "offerName"
      default: "offerName"
    - name: "supplierName"
      default: "supplierName"
    - name: "url"
      default: "url"
    - name: "message"
      default: "message"

QUOTE_NEW_QUOTE_TO_VENDOR:
  body: "<p>Hello {{firstNameVendor}} {{lastNameVendor}}</p><p>{{firstNameBuyer}} {{lastNameBuyer}} asking you for quote for the following offer:</p><p> {{offerName}}</p><p>Message is: {{message}}</p><p> You can follow this quote at this <a href=\"{{url}}\">Link</a></p>"
  variables:
    - name: "firstNameVendor"
      default: "firstNameVendor"
    - name: "lastNameVendor"
      default: "lastNameVendor"
    - name: "firstNameBuyer"
      default: "firstNameBuyer"
    - name: "lastNameBuyer"
      default: "lastNameBuyer"
    - name: "offerName"
      default: "offerName"
    - name: "url"
      default: "url"
    - name: "message"
      default: "message"
    - name: "url"
      default: "url"

QUOTE_SEND_QUOTE_TO_BUYER:
  body: "<p>Hello {{firstName}} {{lastName}}</p><p>Supplier: {{supplierName}}</p><p>You have receive a response to a quote asking for the following offer:</p><p> {{offerName}}</p><p> Quote number: {{quoteNumber}} </p><p>You can see this quote at this <a href=\"{{url}}\">Link</a></p>"
  variables:
    - name: "quoteNumber"
      default: "quoteNumber"
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "supplierName"
      default: "supplierName"
    - name: "offerName"
      default: "offerName"
    - name: "url"
      default: "url"

QUOTE_SEND_QUOTE_TO_VENDOR:
  body: "<p>Hello {{firstNameVendor}} {{lastNameVendor}}</p><p>Your quote response have been sent to {{firstNameBuyer}} {{lastNameBuyer}}</p><p>Reference: {{title}} N°{{number}} </p><p> Link to the quote : <a href=\"{{url}}\">Link</a></p>"
  variables:
    - name: "firstNameVendor"
      default: "firstNameVendor"
    - name: "lastNameVendor"
      default: "lastNameVendor"
    - name: "firstNameBuyer"
      default: "firstNameBuyer"
    - name: "lastNameBuyer"
      default: "lastNameBuyer"
    - name: "title"
      default: "title"
    - name: "number"
      default: "number"
    - name: "url"
      default: "url"

QUOTE_REFUSE_QUOTE_TO_BUYER:
  body: "<p>Hello {{firstName}} {{lastName}}</p><p>Supplier: {{supplierName}}</p><p>The vendor has refused to do a quote response for the following offer:</p><p> {{offerName}}</p><p>The reason is: {{message}}</p><p> You can see this quote at this <a href=\"{{url}}\">Link</a></p>"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "supplierName"
      default: "supplierName"
    - name: "offerName"
      default: "offerName"
    - name: "url"
      default: "url"
    - name: "message"
      default: "message"

QUOTE_REFUSE_QUOTE_TO_VENDOR:
  body: "<p>Hello {{firstNameVendor}} {{lastNameVendor}}</p><p>Your have refused a quote on this offer:</p> {{offerName}} for {{firstNameBuyer}} {{lastNameBuyer}}</p><p>The reason is: {{message}}</p><p> Link to the quote : <a href=\"{{url}}\">Link</a></p>"
  variables:
    - name: "firstNameVendor"
      default: "firstNameVendor"
    - name: "lastNameVendor"
      default: "lastNameVendor"
    - name: "firstNameBuyer"
      default: "firstNameBuyer"
    - name: "lastNameBuyer"
      default: "lastNameBuyer"
    - name: "offerName"
      default: "offerName"
    - name: "message"
      default: "message"
    - name: "url"
      default: "url"

QUOTE_ACCEPT_QUOTE_TO_BUYER:
  body: "<p>Hello {{firstName}} {{lastName}}</p><p>Supplier: {{supplierName}}</p><p>You have accepted this quote:</p><p>{{title}} n°{{number}}</p><p> You can see this quote at this <a href=\"{{url}}\">Link</a></p>"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "supplierName"
      default: "supplierName"
    - name: "title"
      default: "title"
    - name: "number"
      default: "number"
    - name: "url"
      default: "url"

QUOTE_ACCEPT_QUOTE_TO_VENDOR:
  body: "<p>Hello {{firstNameVendor}} {{lastNameVendor}}</p><p>{{firstNameBuyer}} {{lastNameBuyer}} accepts your quote</p><p>Reference: {{title}} N°{{number}} </p><p> Link to the quote : <a href=\"{{url}}\">Link</a></p>"
  variables:
    - name: "firstNameVendor"
      default: "firstNameVendor"
    - name: "lastNameVendor"
      default: "lastNameVendor"
    - name: "firstNameBuyer"
      default: "firstNameBuyer"
    - name: "lastNameBuyer"
      default: "lastNameBuyer"
    - name: "title"
      default: "title"
    - name: "number"
      default: "number"
    - name: "url"
      default: "url"

QUOTE_CANCEL_QUOTE_TO_BUYER:
  body: "<p>Hello {{firstName}} {{lastName}}</p><p>Supplier: {{supplierName}}</p><p>Yoy have cancelled a quote for the following offer:</p><p> {{offerName}}</p><p>Your reason is {{message}} </p><p> You can see this quote at this <a href=\"{{url}}\">Link</a></p>"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "supplierName"
      default: "supplierName"
    - name: "offerName"
      default: "offerName"
    - name: "url"
      default: "url"
    - name: "message"
      default: "message"

QUOTE_CANCEL_QUOTE_TO_VENDOR:
  body: "<p>Hello {{firstNameVendor}} {{lastNameVendor}}</p><p>{{firstNameBuyer}} {{lastNameBuyer}} has cancel a quote on this offer:</p> {{offerName}} </p><p>The reason is: {{message}}</p><p> Link to the quote : <a href=\"{{url}}\">Link</a></p>"
  variables:
    - name: "firstNameVendor"
      default: "firstNameVendor"
    - name: "lastNameVendor"
      default: "lastNameVendor"
    - name: "firstNameBuyer"
      default: "firstNameBuyer"
    - name: "lastNameBuyer"
      default: "lastNameBuyer"
    - name: "offerName"
      default: "offerName"
    - name: "message"
      default: "message"
    - name: "url"
      default: "url"

QUOTE_NEGOCIATE_QUOTE_TO_VENDOR:
  body: "<p>Hello {{firstNameVendor}} {{lastNameVendor}}</p><p>{{firstNameBuyer}} {{lastNameBuyer}} wants to negociate for the following quote:</p><p>Reference: {{title}} N°{{number}} </p><p>Message is: {{message}}</p><p> Link to the quote : <a href=\"{{url}}\">Link</a></p>"
  variables:
    - name: "firstNameVendor"
      default: "firstNameVendor"
    - name: "lastNameVendor"
      default: "lastNameVendor"
    - name: "firstNameBuyer"
      default: "firstNameBuyer"
    - name: "lastNameBuyer"
      default: "lastNameBuyer"
    - name: "title"
      default: "title"
    - name: "number"
      default: "number"
    - name: "message"
      default: "message"
    - name: "url"
      default: "url"

QUOTE_NEW_MESSAGE_TO_BUYER:
  body: "<p>Hello {{firstName}} {{lastName}}</p><p>You have received a message related to a quote.</p><p>Supplier : {{supplierName}}</p><p>Message is: {{message}}</p><p> You can see this quote at this <a href=\"{{url}}\">Link</a></p>"
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "supplierName"
      default: "supplierName"
    - name: "offerName"
      default: "offerName"
    - name: "url"
      default: "url"
    - name: "message"
      default: "message"

QUOTE_NEW_MESSAGE_TO_VENDOR:
  body: "<p>Hello {{firstNameVendor}} {{lastNameVendor}}</p><p>You have received a message related to a quote from {{firstNameBuyer}} {{lastNameBuyer}} i.</p><p>Offer: {{offerName}}</p><p>Message is: {{message}}</p><p> You can see this quote at this <a href=\"{{url}}\">Link</a></p>"
  variables:
    - name: "firstNameVendor"
      default: "firstNameVendor"
    - name: "lastNameVendor"
      default: "lastNameVendor"
    - name: "firstNameBuyer"
      default: "firstNameBuyer"
    - name: "lastNameBuyer"
      default: "lastNameBuyer"
    - name: "offerName"
      default: "offerName"
    - name: "message"
      default: "message"
    - name: "url"
      default: "url"
TICKET_THREAD_UPDATED_BY_BUYER_TO_VENDOR:
  body: "<p>Hello {{vendorFirstName}} {{vendorLastName}},</p><p>You have received a new message from {{firstNameBuyer}} {{lastNameBuyer}} in Click & Buy.</p><p>Object : {{message}}</p><p> You can access to the message at this <a href=\"{{url}}\">Link</a></p><p>Best regards,<br/>Click & Buy team.</p>"
  variables:
    - name: "vendorFirstName"
      default: "vendorFirstName"
    - name: "vendorLastName"
      default: "vendorLastName"
    - name: "firstNameBuyer"
      default: "firstNameBuyer"
    - name: "lastNameBuyer"
      default: "lastNameBuyer"
    - name: "message"
      default: "message"
    - name: "url"
      default: "url"
    - name: "topic"
      default: "topic"

# Warn manager that a buyer send an order to validation
CXML_ORDER_ERROR_TO_VENDOR:
  body: '<p>Hello {{firstName}} {{lastName}},</p><p>An error has occured during the transfert of an order.<br>Order number : {{ orderNumber }}.</p><p>Please click on this link to open the order :<br><a href="{{ orderUrl }}">{{ orderUrl }}</a></p><p>List of items:{%for item in items%} <p>{{item.name}} {{item.quantity}} {{item.totalPrice}} {{item.currency}}</p> {%endfor%}<p>'
  variables:
    - name: "firstName"
      default: "firstName"
    - name: "lastName"
      default: "lastName"
    - name: "orderNumber"
      default: "orderNumber"
    - name: "orderUrl"
      default: "orderUrl"
    - name: "items"
      children:
        -   name: "name"
            default: "name"
        -   name: "quantity"
            default: "quantity"
        -   name: "amount"
            default: "amount"
        -   name: "totalPrice"
            default: "total price"
        -   name: "currency"
            default: "currency"

ORDER_PENDING_CONFIRMATION_TO_VENDOR:
  body: '<p>Hello {{firstName}} {{lastName}},</p><p> You have receive a new order to confirm</p> <li>{{merchant_slug}}</li><li>{{order_id}}</li><li>{{order_date}}</li><li>{{price_ht}}</li><li>{{shipping_price}}</li><li>{{total_order_price_ht}}</li><li>{{shipping_first_name}}</li><li>{{shipping_last_name}}</li><li>{{shipping_address}}</li><li>{{shipping_address2}}</li><li>{{shipping_zipcode}}</li><li>{{shipping_city}}</li><li>{{shipping_state}}</li><li>{{shipping_country}}</li><li>{{billing_address}}</li><li>{{billing_address2}}</li><li>{{billing_zipcode}}</li><li>{{billing_city}}</li><li>{{billing_state}}</li><li>{{billing_country}}</li><p>List of product:{%for product in products%} <p> {{product.sku}} {{product.quantity}} {{ product.price_ht}} </p> {%endfor%}'
  variables:
    -   name: "firstName"
        default: "firstName"
    -   name: "lastName"
        default: "lastName"
    -   name: "merchant_slug"
        default: "merchant_slug"
    -   name: "order_id"
        default: "order_id"
    -   name: "orderUrl"
        default: "orderUrl"
    -   name: "order_date"
        default: "order_date"
    -   name: "price_ht"
        default: "price_ht"
    -   name: "shipping_price"
        default: "shipping_price"
    -   name: "total_order_price_ht"
        default: "total_order_price_ht"
    -   name: "shipping_first_name"
        default: "shipping_first_name"
    -   name: "shipping_last_name"
        default: "shipping_last_name"
    -   name: "shipping_address"
        default: "shipping_address"
    -   name: "shipping_address2"
        default: "shipping_address2"
    -   name: "shipping_zipcode"
        default: "shipping_zipcode"
    -   name: "shipping_city"
        default: "shipping_city"
    -   name: "shipping_state"
        default: "shipping_state"
    -   name: "shipping_country"
        default: "shipping_country"
    -   name: "billing_address"
        default: "billing_address"
    -   name: "billing_address2"
        default: "billing_address2"
    -   name: "billing_zipcode"
        default: "billing_zipcode"
    -   name: "billing_city"
        default: "billing_city"
    -   name: "billing_state"
        default: "billing_state"
    -   name: "billing_country"
        default: "billing_country"
    -   name: "shippingAmountHt"
        default: "shippingAmountHt"
    -   name: "products"
        children:
          -   name: "name"
              default: "name"
          -   name: "sku"
              default: "sku"
          -   name: "quantity"
              default: "quantity"
          -   name: "price_ht"
              default: "price_ht"


ORDER_AUTO_CONFIRMED_TO_VENDOR:
  body: '<p>Hello {{firstName}} {{lastName}},</p><p> You have receive a new order</p> <li>{{merchant_slug}}</li><li>{{order_id}}</li><li>{{order_date}}</li><li>{{price_ht}}</li><li>{{shipping_price}}</li><li>{{total_order_price_ht}}</li><li>{{shipping_first_name}}</li><li>{{shipping_last_name}}</li><li>{{shipping_address}}</li><li>{{shipping_address2}}</li><li>{{shipping_zipcode}}</li><li>{{shipping_city}}</li><li>{{shipping_state}}</li><li>{{shipping_country}}</li><li>{{billing_address}}</li><li>{{billing_address2}}</li><li>{{billing_zipcode}}</li><li>{{billing_city}}</li><li>{{billing_state}}</li><li>{{billing_country}}</li><p>List of product:{%for product in products%} <p> {{product.sku}} {{product.quantity}} {{ product.price_ht}} </p> {%endfor%}'
  variables:
    -   name: "firstName"
        default: "firstName"
    -   name: "lastName"
        default: "lastName"
    -   name: "merchant_slug"
        default: "merchant_slug"
    -   name: "order_id"
        default: "order_id"
    -   name: "orderUrl"
        default: "orderUrl"
    -   name: "order_date"
        default: "order_date"
    -   name: "price_ht"
        default: "price_ht"
    -   name: "shipping_price"
        default: "shipping_price"
    -   name: "total_order_price_ht"
        default: "total_order_price_ht"
    -   name: "shipping_first_name"
        default: "shipping_first_name"
    -   name: "shipping_last_name"
        default: "shipping_last_name"
    -   name: "shipping_address"
        default: "shipping_address"
    -   name: "shipping_address2"
        default: "shipping_address2"
    -   name: "shipping_zipcode"
        default: "shipping_zipcode"
    -   name: "shipping_city"
        default: "shipping_city"
    -   name: "shipping_state"
        default: "shipping_state"
    -   name: "shipping_country"
        default: "shipping_country"
    -   name: "billing_address"
        default: "billing_address"
    -   name: "billing_address2"
        default: "billing_address2"
    -   name: "billing_zipcode"
        default: "billing_zipcode"
    -   name: "billing_city"
        default: "billing_city"
    -   name: "billing_state"
        default: "billing_state"
    -   name: "billing_country"
        default: "billing_country"
    -   name: "shippingAmountHt"
        default: "shippingAmountHt"
    -   name: "products"
        children:
          -   name: "name"
              default: "name"
          -   name: "sku"
              default: "sku"
          -   name: "quantity"
              default: "quantity"
          -   name: "price_ht"
              default: "price_ht"
ALERT_ORDER_RISK_CONFIRMED_TO_HSE:
  body: '<p>Hello,</p><p>{{userFirstName}} {{userLastName}} using username {{IGG}} passed on {{creationDate}} an Order with risk products confirmed by {{firstNameValidator}} {{lastNameValidator}}</p><p>List of products with risk :</p><p>{%for product in products%}<ul><li>supplier : {{product.supplier}}</li><li>category : {{product.category}}</li><li>Item : {{product.itemName}}</li><li>price : {{ product.price}}</li></ul>{%endfor%}</p>'
  variables:
    -   name: "userFirstName"
        default: "userFirstName"
    -   name: "userLastName"
        default: "userLastName"
    -   name: "IGG"
        default: "IGG"
    -   name: "creationDate"
        default: "creationDate"
    -   name: "firstNameValidator"
        default: "firstNameValidator"
    -   name: "lastNameValidator"
        default: "lastNameValidator"
    -   name: "products"
        children:
          -   name: "category"
              default: "category"
          -   name: "itemName"
              default: "itemName"
          -   name: "supplier"
              default: "supplier"
          -   name: "price"
              default: "price"
ORDER_CONFIRMED_BY_VENDOR_TO_VENDOR:
  body: '<p>Hello {{firstName}} {{lastName}},</p><p> You have a confirmed order</p> <li><b>Merchant Slug : </b>{{merchant_slug}}</li><li><b>order Id : </b>{{order_id}}</li><li><b>Order Date : </b>{{order_date}}</li><li><b>Price HT : </b>{{price_ht}}</li><li><b>Shipping Price : </b>{{shipping_price}}</li><li><b>Total Order Price HT : </b>{{total_order_price_ht}}</li><li><b>Shipping Address : </b>{{shipping_contact}}<br>{{shipping_address}}<br>{{shipping_address2}}<br>{{shipping_zipcode}}<br>{{shipping_city}}<br>{{shipping_country}}</li><p><b>List of product : </b>{%for product in products%} <p> {{product.sku}} {{product.quantity}} {{ product.price_ht}} </p> {%endfor%}'
  variables:
    -   name: "firstName"
        default: "firstName"
    -   name: "lastName"
        default: "lastName"
    -   name: "merchant_slug"
        default: "merchant_slug"
    -   name: "order_id"
        default: "order_id"
    -   name: "orderUrl"
        default: "orderUrl"
    -   name: "order_date"
        default: "order_date"
    -   name: "price_ht"
        default: "price_ht"
    -   name: "shipping_price"
        default: "shipping_price"
    -   name: "total_order_price_ht"
        default: "total_order_price_ht"
    -   name: "shipping_contact"
        default: "shipping_contact"
    -   name: "shipping_address"
        default: "shipping_address"
    -   name: "shipping_address2"
        default: "shipping_address2"
    -   name: "shipping_zipcode"
        default: "shipping_zipcode"
    -   name: "shipping_city"
        default: "shipping_city"
    -   name: "shipping_country"
        default: "shipping_country"
    -   name: "shippingAmountHt"
        default: "shippingAmountHt"
    -   name: "products"
        children:
          -   name: "name"
              default: "name"
          -   name: "sku"
              default: "sku"
          -   name: "quantity"
              default: "quantity"
          -   name: "price_ht"
              default: "price_ht"