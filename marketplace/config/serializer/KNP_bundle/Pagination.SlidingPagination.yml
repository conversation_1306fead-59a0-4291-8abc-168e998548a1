Knp\Bundle\PaginatorBundle\Pagination\SlidingPagination:
  exclusion_policy: ALL
  virtual_properties:
    getItems:
      expose: true
      serialized_name: items
      type: array
      groups: [pagination]
    getPageCount:
      expose: true
      serialized_name: page_count
      type: integer
      groups: [pagination]
    getItemNumberPerPage:
      expose: true
      serialized_name: items_per_page
      groups: [pagination]
    getTotalItemCount:
      expose: true
      serialized_name: total_item_count
      groups: [pagination]
    getCurrentPageNumber:
      expose: true
      serialized_name: current_page
