<?php

namespace AppBundle;

use Monolog\ErrorHandler;
use Symfony\Bridge\Monolog\Logger;
use Symfony\Bundle\FrameworkBundle\Kernel\MicroKernelTrait;
use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;
use Symfony\Component\HttpKernel\Kernel as BaseKernel;
use Symfony\Component\Routing\Loader\Configurator\RoutingConfigurator;

class Kernel extends BaseKernel
{
    use MicroKernelTrait;

    protected function configureContainer(ContainerConfigurator $container): void
    {
        $container->import('../../config/{packages}/*.yaml');
        $container->import('../../config/{packages}/'.$this->environment.'/*.yaml');
        $container->import('../../config/{parameters}/*.yaml');
    }

    protected function configureRoutes(RoutingConfigurator $routes): void
    {
        $routes->import('../../config/{routes}/'.$this->environment.'/*.yaml');
        $routes->import('../../config/{routes}/*.yaml');

        if (is_file(dirname(__DIR__, 2) .'/config/routes.yaml')) {
            $routes->import('../../config/routes.yaml');
        } else {
            $routes->import('../../config/{routes}.php');
        }
    }

    public function boot()
    {
        if (true === $this->booted) {
            return;
        }
        parent::boot();

        $logger = new Logger("deprecation");
        ErrorHandler::register($logger, false);
    }
}
