<?php

namespace AppBundle\Services;

use AppBundle\Entity\MarketPlace;
use AppBundle\Model\Category;
use AppBundle\Model\CategoryTree;
use AppBundle\Model\Facet;
use AppBundle\Model\FacetValue;
use AppBundle\Model\SearchResult;
use Open\IzbergBundle\Service\AttributeService;
use Open\IzbergBundle\Service\CategoryService;

class FacetService
{
    public const FIELD_PRODUCT_CATEGORY = 'product.application_categories';
    public const FIELD_MERCHANT_NAME = 'merchant.name';
    public const FIELD_PRODUCT_NAME = 'product.name';

    private IzbergCustomAttributes $izbergCustomAttributes;
    private array $excludedCustomAttributes;
    private AttributeService $attributeService;
    private CategoryService $categoryService;
    private array $facetsOrder;

    public function __construct(
        array $facetsOrder,
        IzbergCustomAttributes $izbergCustomAttributes,
        AttributeService $attributeService,
        CategoryService $categoryService,
    ) {
        $this->facetsOrder = $facetsOrder;
        $this->izbergCustomAttributes = $izbergCustomAttributes;
        $this->attributeService = $attributeService;
        $this->categoryService = $categoryService;
        $this->excludedCustomAttributes = [];
    }

    ######################################### GETTERS AND SETTERS ##############################################
    public function getCategoryFacetField(): string
    {
        return str_replace('.', ':', self::FIELD_PRODUCT_CATEGORY);
    }

    public function setExcludedCustomAttributes(array $excludedCustomAttributes)
    {
        $this->excludedCustomAttributes = $excludedCustomAttributes;
    }

    ########################################### PRIVATE METHODS ################################################
    private function getFacetLabelFieldName(Facet $facet, string $locale): void
    {
        $fieldName = $facet->getFieldName();

        if ($fieldName === self::FIELD_PRODUCT_CATEGORY) {
            $facet->setLabel('search.facet.categories');
            return;
        }

        $izbergAttribute = str_replace('attributes.', '', $fieldName);
        $izbergAttribute = str_replace('.keyword', '', $izbergAttribute);

        $attributeFacet = $this->attributeService->getAttribute($izbergAttribute, $locale);
        $facet->setLabel($attributeFacet->getLabel());
    }

    private function popCategory(array &$categories, int $categoryId): ?Category
    {
        $category = null;
        for ($cpt = 0; $cpt < count($categories); $cpt++) {

            if ($categories[$cpt] != null && $categories[$cpt]->getId() == $categoryId) {
                $category = $categories[$cpt];
                $categories[$cpt] = null;
                break;
            }
        }
        return $category;
    }

    public function popFacet(array &$facets, string $facetFieldName): ?Facet
    {
        $facet = null;
        for ($cpt = 0; $cpt < count($facets); $cpt++) {

            if ($facets[$cpt] != null && $facets[$cpt]->getFieldName() === $facetFieldName) {
                $facet = $facets[$cpt];
                $facets[$cpt] = null;
                break;
            }
        }
        return $facet;
    }

    ############################################ PUBLIC METHODS ################################################
    public function allFacets(): array
    {
        $allFacets = [
            self::FIELD_PRODUCT_CATEGORY => 1,
            self::FIELD_MERCHANT_NAME => 1,
        ];

        foreach ($this->getSpecificFacetsFields() as $specificAttribute) {
            $allFacets[str_replace(':', '.', $specificAttribute)] = 1;
        }

        return $allFacets;

    }

    public function getSpecificFacetsFields(): array
    {
        $specificFacets = array();
        foreach ($this->attributeService->getAttributesKeys() as $attribute) {
            if (!in_array($attribute, $this->excludedCustomAttributes)) {
                $specificFacets[] = 'attributes:' . $attribute . ':keyword';
            }
        }
        return $specificFacets;
    }

    public function buildFacet(Facet $facet, string $locale = 'fr'): void
    {
        // get label
        $this->getFacetLabelFieldName($facet, $locale);
        // get values
        if ($facet->getFieldName() === self::FIELD_PRODUCT_CATEGORY) {
            $facet->setValues(array_map(
                function (FacetValue $facetValue) {
                    // get FacetValue label
                    $facetValueLabel = $this->categoryService->getCategoryName($facetValue->getValue());
                    $facetValue->setLabel($facetValueLabel);

                    // get FacetValue level
                    $initialLevel = 1;
                    $facetValue->setLevel(
                        $this->categoryService->getCategoryLevel((int) $facetValue->getValue(), $initialLevel, (int) $facetValue->getValue())
                    );

                    // get FacetValue parent
                    $facetValue->setParent(
                        intval($this->categoryService->getParent((int) $facetValue->getValue()))
                    );

                    return $facetValue;
                },
                $facet->getValues()
            ));
        }
    }

    public function orderFacets(SearchResult $searchResult): SearchResult
    {
        $orderedFacets = [];
        $facets = $searchResult->getFacets();
        foreach ($this->facetsOrder as $facetFieldName) {
            $facet = $this->popFacet($facets, $facetFieldName);
            if ($facet != null) {
                $orderedFacets[] = $facet;
            }
        }
        foreach ($facets as $facet) {
            if ($facet != null) {
                $orderedFacets[] = $facet;
            }
        }
        $searchResult->setFacets($orderedFacets);

        return $searchResult;
    }

    public function orderBuiltCategoryTree(CategoryTree $categoryTree, MarketPlace $marketPlace, array $categoriesPriorities): CategoryTree
    {
        $orderedCategories = [];
        $categories = $categoryTree->getChildren();
        foreach ($categoriesPriorities as $categoryId) {
            $category = $this->popCategory($categories, $categoryId);
            if ($category != null) {
                $orderedCategories[] = $category;
            }
        }

        foreach ($categories as $category) {
            if ($category != null) {
                $orderedCategories[] = $category;
            }
        }
        $categoryTree->setChildren($orderedCategories);

        return $categoryTree;
    }

    public function buildRefinedCategoryTree(SearchResult $searchResult, ?int $currentCategoryId): CategoryTree
    {
        /** @var CategoryTree $categoryTree */
        $categoryTree = $this->buildCategoryTree($searchResult, $currentCategoryId);
        /** @var array<Category> $categoryTreeChildren */
        $categoryTreeChildren = $categoryTree->getChildren();

        while (count($categoryTreeChildren) == 1) {
            $currentCategoryId = $categoryTreeChildren[0]->getId();
            $categoryTreeChildren = $this->buildCategoryTree($searchResult, $currentCategoryId)->getChildren();

            if (count($categoryTreeChildren) == 0) {
                break;
            }

            if ($categoryTreeChildren[0]->getId() == $currentCategoryId) {
                break;
            }
        }
        $categoryTree->setChildren($categoryTreeChildren);
        return $categoryTree;
    }

    public function buildCategoryTree(SearchResult $searchResult, ?int $currentCategoryId): CategoryTree
    {
        $tree = null;

        $categoryTree = new CategoryTree();

        $facets = $searchResult->getFacets();

        $categoryFacet = null;

        /** @var Facet $facet */
        foreach ($facets as $facet) {
            if ($facet->getFieldName() === 'product.application_categories') {
                $categoryFacet = $facet;
                break;
            }
        }

        if (!$categoryFacet) {
            return $categoryTree;
        }

        $children = [];

        if ($currentCategoryId) {
            $tree = $this->categoryService->buildCategoryTree($currentCategoryId);
            $categoryTree->setCurrent(
                (new Category())
                    ->setId($currentCategoryId)
            );

            // Set the current hits
            /** @var FacetValue $facetValue */
            foreach ($categoryFacet->getValues() as $facetValue) {
                if ($facetValue->getValue() == $currentCategoryId) {
                    $categoryTree->getCurrent()->setHits($facetValue->getHits());
                    break;
                }
            }

            reset($tree);
            $level = count($tree) + 1;
            $previousCategoryId = next($tree);


            $categoryTree->setPrevious(
                (new Category())
                    ->setId($previousCategoryId)
            );


            foreach ($categoryFacet->getValues() as $facetValue) {
                if ($facetValue->getLevel() === $level && $facetValue->getParent() === $currentCategoryId) {
                    $children[] = (new Category())
                        ->setId($facetValue->getValue())
                        ->setLabel($facetValue->getLabel())
                        ->setHits($facetValue->getHits());
                }
            }

            if (!count($children)) {
                $level = $level - 1;

                foreach ($categoryFacet->getValues() as $facetValue) {
                    if ($facetValue->getLevel() === $level && $facetValue->getParent() === $previousCategoryId) {
                        $children[] = (new Category())
                            ->setId($facetValue->getValue())
                            ->setLabel($facetValue->getLabel())
                            ->setHits($facetValue->getHits());
                    }
                }
            }

        } else {
            /** @var FacetValue $facetValue */
            foreach ($categoryFacet->getValues() as $facetValue) {
                if ($facetValue->getLevel() === 1) {
                    $children[] = (new Category())
                        ->setId((int) $facetValue->getValue())
                        ->setLabel($facetValue->getLabel())
                        ->setHits($facetValue->getHits());
                }
            }
        }

        $categoryTree->setChildren($children);

        return $categoryTree;
    }

}
