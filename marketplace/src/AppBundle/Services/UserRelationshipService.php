<?php

namespace AppBundle\Services;

use AppBundle\Repository\UserToUserRelationshipRepository;
use Open\IzbergBundle\Service\RedisService;
use Psr\Cache\InvalidArgumentException;

class UserRelationshipService
{
    private const FETCH_NX_CHILDREN_CACHE_PREFIX = 'USER_FETCH_NX_CHILDREN';
    private const FETCH_NX_PARENTS_CACHE_PREFIX = 'USER_FETCH_NX_PARENTS';
    private const FETCH_USER_PARENTS = 1;
    private const FETCH_USER_CHILDREN = 2;

    private UserToUserRelationshipRepository $userToUserRelationshipRepository;
    private RedisService $cache;

    public function __construct(UserToUserRelationshipRepository $userRelationshipRepository, RedisService $cache)
    {
        $this->userToUserRelationshipRepository = $userRelationshipRepository;
        $this->cache = $cache;
    }

    /***
     * @param int $childId
     * @param bool $n1Only
     * @return array|mixed|null
     * @throws InvalidArgumentException
     */
    public function fetchNXUserParents(int $childId, bool $n1Only = false)
    {
        return $this->fetchNXUsers($childId, self::FETCH_USER_PARENTS, $n1Only);
    }

    /**
     * @param int $parentId
     * @param bool $n1Only
     * @return int|mixed|string
     */
    public function fetchNXUserChildren(int $parentId, bool $n1Only = false)
    {
        return $this->fetchNXUsers($parentId, self::FETCH_USER_CHILDREN, $n1Only);
    }

    /**
     * @param int $userId
     * @param int $fetchParentsOrChildren
     * @param bool $n1Only
     * @return int|mixed|string
     */
    private function fetchNXUsers(int $userId, int $fetchParentsOrChildren, bool $n1Only = false)
    {
        switch ($fetchParentsOrChildren) {
            case self::FETCH_USER_PARENTS:
                $cacheKey = sprintf("%s_%s_%s", self::FETCH_NX_PARENTS_CACHE_PREFIX, $userId, (int)$n1Only);
                break;
            default:
                $cacheKey = sprintf("%s_%s_%s", self::FETCH_NX_CHILDREN_CACHE_PREFIX, $userId, (int)$n1Only);
                break;
        }

        $cacheItem = $this->cache->getItem($cacheKey);

        if ($cacheItem !== null) {
            return $cacheItem;
        }

        $childUserIds = $this->fetchNXUsersRecursive($userId, $fetchParentsOrChildren, $n1Only);

        $this->cache->saveItem($cacheKey, $childUserIds, 10);

        return $childUserIds;
    }

    /**
     * @param int $userId
     * @param int $fetchParentsOrChildren
     * @param bool $n1Only
     * @param array $userProcessed
     * @return int|mixed|string
     */
    private function fetchNXUsersRecursive(int $userId, int $fetchParentsOrChildren, bool $n1Only = false, array $userProcessed = [])
    {
        // select all children with parent = buyer id
        $qb = $this->userToUserRelationshipRepository->createQueryBuilder('user');

        switch ($fetchParentsOrChildren) {
            case self::FETCH_USER_PARENTS:
                $qb
                    ->select('IDENTITY(user.parentUser)')
                    ->where('user.childUser = :parentId')
                ;
                break;
            default:
                $qb
                    ->select('IDENTITY(user.childUser)')
                    ->where('user.parentUser = :parentId')
                ;
                break;
        }

        $qb->setParameter('parentId', $userId);

        $usersIds = $qb->getQuery()->getResult();
        $usersIds = array_map(function (array $idAsArray) {return array_shift($idAsArray); }, $usersIds);

        $usersIds = array_unique(
            array_merge(
                [$userId],
                array_map(function ($id) {
                    return (int)$id;
                }, $usersIds)
            )
        );

        $userProcessed[] = $userId;
        if (!$n1Only) {
            // foreach child run recursive Hierarchy
            $usersIds = array_reduce(
                $usersIds,
                function (array $usersIds, int $userId) use ($userProcessed, $fetchParentsOrChildren) {
                    if (in_array($userId, $userProcessed)) {
                        return $usersIds;
                    }

                    return array_unique(
                        array_merge($usersIds, $this->fetchNXUsersRecursive($userId, $fetchParentsOrChildren, false, $userProcessed))
                    );
                },
                $usersIds
            );
        }

        return array_unique($usersIds);
    }
}
