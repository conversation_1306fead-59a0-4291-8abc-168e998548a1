<?php

namespace AppBundle\Services;

use Open\LogBundle\Service\LogService;

interface SimpleLogServiceInterface
{

  public function __construct(LogService $logService, SecurityService $securityService);

  public function error($message, $eventName, $userName = null, $data = [], $checkEventName = true);

  public function simple_error(\Exception $e);

  public function info($message, $eventName, $userName = null, $data = [], $checkEventName = true);

  public function critical($message, $eventName, $userName = null, $data = [], $checkEventName = true);

  public function debug($message, $eventName, $userName = null, $data = [], $checkEventName = true);

}