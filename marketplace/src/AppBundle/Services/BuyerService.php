<?php

namespace AppBundle\Services;

use AppBundle\Entity\BuyerAddress;
use AppBundle\Entity\MarketPlace;
use AppBundle\Entity\MetaCart;
use AppBundle\Entity\Country;
use AppBundle\Entity\User;
use AppBundle\Entity\UserToUserRelationship;
use AppBundle\Model\BuyerAddressModel;
use AppBundle\Repository\BuyerAddressRepository;
use AppBundle\Repository\CountryRepository;
use AppBundle\Repository\MetaCartRepository;
use AppBundle\Repository\UserRepository;
use AppBundle\Repository\UserToUserRelationshipRepository;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Doctrine\Common\Collections\ArrayCollection;
use Open\FrontBundle\Exception\MailAlreadyUsedException;
use Open\IzbergBundle\Api\AuthenticationApi;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

class BuyerService
{
    private UserBddService $userManager;
    private AuthenticationApi $authenticationApi;
    private UserToUserRelationshipRepository $userRelationshipRepository;
    private CountryRepository $countryRepository;
    private MetaCartRepository $metaCartRepository;
    private UserRepository $userRepository;
    private AddressService $addressService;
    private LogService $loggerService;
    private BuyerAddressRepository $buyerAddressRepository;
    private EntityManagerInterface $em;
    private UserPasswordHasherInterface $passwordHasher;

    public function __construct(
        UserBddService                   $userManager,
        UserToUserRelationshipRepository $userRelationshipRepository,
        AuthenticationApi                $authenticationApi,
        CountryRepository                $countryRepository,
        MetaCartRepository               $metaCartRepository,
        AddressService                   $addressService,
        UserRepository                   $userRepository,
        LogService                       $loggerService,
        BuyerAddressRepository           $buyerAddressRepository,
        EntityManagerInterface           $em,
        UserPasswordHasherInterface      $passwordHasher
    )
    {
        $this->userManager = $userManager;
        $this->authenticationApi = $authenticationApi;
        $this->userRelationshipRepository = $userRelationshipRepository;
        $this->countryRepository = $countryRepository;
        $this->metaCartRepository = $metaCartRepository;
        $this->addressService = $addressService;
        $this->userRepository = $userRepository;
        $this->loggerService = $loggerService;
        $this->em = $em;
        $this->buyerAddressRepository = $buyerAddressRepository;
        $this->passwordHasher = $passwordHasher;
    }

    public function getBuyerFromEmail(?string $email): ?User
    {
        return $this->userManager->findUserByUsernameOrEmail($email);
    }

    public function getBuyerFromUsername(?string $username): ?User
    {
        return $this->userManager->findUserByUsername($username);
    }

    public function getBuyerFromIzbergId(int $buyerIzbId): ?User
    {
        return $this->userManager->findUserBy([
            'izbergUserId' => $buyerIzbId,
        ]);
    }

    public function getBuyerFromTechnicalId(?string $technicalId): ?User
    {
        if ($technicalId) {
            return $this->userManager->findUserBy([
                'technicalId' => $technicalId,
            ]);
        }
        return null;
    }

    public function createBuyer(string $email, string $password, string $firstName, string $lastName, string $username = null, $organization = null, ?string $costCenter = null, $telephone = null, ?Country $country = null): User
    {
        $username = $username ?? uniqid('buyer_', false);

        $user = new User();
        $user->setUsername($username);
        $user->setEmail($email);
        $user->setFirstName($firstName);
        $user->setLastName($lastName);
        $user->setPassword($this->passwordHasher->hashPassword($user, $password));
        $user->setEnabled(false);
        $user->setFunction('default');
        $user->setRoles(['ROLE_BUYER']);
        $user->setUserType();
        $user->setOrganization($organization);
        $user->setCostCenter($costCenter);
        $user->setBranch($this->getBranch($organization));
        $user->setCountryOfDelivery($country);
        $user->setLocale($country->getLocale());
        if ($telephone) {
            $user->setMainPhoneNumber($telephone);
        }

        $this->userManager->saveUser($user);

        $this->loggerService->info("User Created", EventNameEnum::COMMAND_IMPORT_BUYER, ["username" => $username, "email" => $email]);
        return $user;
    }

    public function createOrUpdateBuyer(string $email, string $password, string $firstName, string $lastName, string $organization, ?string $costCenter, ?string $username, ?string $telephone, $userEnd, $country): ?User
    {
        /** @var User $user */
        $user = $this->getBuyerFromUsername($username);

        if (!$user) {
            $userEmail = $this->getBuyerFromEmail($email);
            if ($userEmail) {
                $this->loggerService->error("user already exist with this email", "createOrUpdateBuyer", ["username" => $username, "email" => $userEmail]);
                return null;
            }
            if (!$userEnd) {
                return $this->createBuyer($email, $password, $firstName, $lastName, $username, $organization, $costCenter, $telephone, $country);
            } else {
                return null;
            }
        }

        try {
            $this->checkEmailChanging($user, $email, $username);
        } catch (MailAlreadyUsedException $ex) {
            return null;
        }

        $user->setEmail($email);
        $user->setFirstname($firstName);
        $user->setLastname($lastName);
        $user->setUserType();
        $user->setIsDeleted($userEnd);
        $user->setOrganization($organization);
        $user->setCostCenter($costCenter);
        $user->setBranch($this->getBranch($organization));
        $user->setCountryOfDelivery($country);

        if ($telephone) {
            $user->setMainPhoneNumber($telephone);
        }

        $this->userManager->saveUser($user);
        $this->loggerService->info("User updated", EventNameEnum::COMMAND_IMPORT_BUYER, ["username" => $username, "email" => $email]);
        return $user;
    }

    /**
     * @param User $buyer
     * @param User $superior
     * @param bool $withFlush
     * @throws \Doctrine\ORM\ORMException
     */
    public function attachBuyerToSuperior(User $buyer, User $superior, $withFlush = true): void
    {
        // Buyer User expect only one lineManager (Superior)

        $userRelationship = new UserToUserRelationship();

        $userRelationship->setChildUser($buyer);
        $userRelationship->setParentUser($superior);
        $userRelationship->setIsDelegate(false);
        $this->loggerService->info(sprintf("user %s, manager %s", $buyer->getUsername(), $superior->getUsername()), "attachBuyerToSuperior", [
            'USER_ID' => $buyer->getId(),
            'ENABLER_ID' => $buyer->getUserEnabler()?->getId(),
            'USER_ENABLED' => $buyer->isEnabled(),
            'ENABLER_ENABLED' => $buyer->getUserEnabler()?->getEnabled(),
            'SUPERIOR_ID' => $superior->getId(),
        ]);
        if ($buyer->isEnabled() && !$superior->isEnabled()) {
            // Manager must be auto enabled
            $superior->setAutoEnabled(true);
            $superior->setEnabled(true);
            $this->em->persist($superior);
        }

        try {
            $this->userRelationshipRepository->createRelationship($userRelationship, $withFlush);
        }
        catch (\Throwable $exception) {
            $this->loggerService->error("error while creating relationship : " .$exception->getMessage(), "attachBuyerToSuperior", [
                'USER_ID' => $buyer->getId(),
                'ENABLER_ID' => $buyer->getUserEnabler()?->getId(),
                'USER_ENABLED' => $buyer->isEnabled(),
                'ENABLER_ENABLED' => $buyer->getUserEnabler()?->getEnabled(),
                'SUPERIOR_ID' => $superior->getId(),
            ]);
        }
    }

    /**
     * @param User $buyer
     * @param User $delegate
     * @param User $user user who change delegation
     * @param bool $isOperator true if user is operator
     */
    public function attachBuyerToDelegate(User $buyer, User $delegate, ?User $user, bool $isOperator): void
    {
        // Buyer User can have multiple delegate

        $userRelationship = new UserToUserRelationship();

        $userRelationship->setChildUser($buyer);
        $userRelationship->setParentUser($delegate);
        $userRelationship->setIsDelegate(true);

        $userType = User::DELEGATE_USER_TYPE_USER;
        if ($isOperator) {
            $userType = User::DELEGATE_USER_TYPE_ADMIN;
        }
        $buyer->setDelegateUserType($userType);
        if ($user) {
            $buyer->setDelegateUserName($user->getFirstname() . " " . $user->getLastname());
        }
        $buyer->setDelegateDate(new \DateTime("now"));

        $delegates = $buyer->getDelegates();
        $delegates[] = $delegate;
        $delegatesIgg = array_map(function (User $user) {
            return $user->getUsername();
        }, $delegates);
        $buyer->setDelegateLabel(implode(",", $delegatesIgg));


        $this->userRelationshipRepository->createRelationship($userRelationship);

    }

    public function deleteDelegate(User $buyer, User $delegate, ?User $user, bool $isOperator)
    {
        $delegateRelationShip = new UserToUserRelationship();

        $delegateRelationShip->setChildUser($buyer);
        $delegateRelationShip->setParentUser($delegate);
        $delegateRelationShip->setIsDelegate(true);

        // delegate is last one
        if (count($buyer->getDelegates()) == 1) {
            $buyer->setDelegateUserType(null);
            $buyer->setDelegateUserName(null);
            $buyer->setDelegateDate(null);

        } else {
            $userType = User::DELEGATE_USER_TYPE_USER;
            if ($isOperator) {
                $userType = User::DELEGATE_USER_TYPE_ADMIN;
            }
            $buyer->setDelegateUserType($userType);
            if ($user) {
                $buyer->setDelegateUserName($user->getFirstname() . " " . $user->getLastname());
            }
            $buyer->setDelegateDate(new \DateTime("now"));
        }

        $delegatesIgg = array_map(function (User $user) {
            return $user->getUsername();
        }, $buyer->getDelegates());
        if (($key = array_search($delegate->getUsername(), $delegatesIgg)) !== false) {
            unset($delegatesIgg[$key]);
        }
        $buyer->setDelegateLabel(implode(",", $delegatesIgg));
        $this->userRelationshipRepository->deleteRelationship($delegateRelationShip);

    }

    /**
     * @param int $buyerId
     * @return bool return true if a manager relation was deleted
     */
    public function deleteManagerRelation(int $buyerId): bool
    {
        return $this->userRelationshipRepository->deleteManager($buyerId);
    }

    public function attachBuyerDefaultAddress(User $user, BuyerAddress $buyerAddress): void
    {
        $user->setDefaultBuyerAddress($buyerAddress);
    }

    public function isUnRegistereIzbergBuyer(User $user): bool
    {

        if ($user->hasRole(User::ROLE_BUYER)) {
            // A buyer must have

            if (!$user->getIzbergUserId()) {
                // Trying to set an IzbergID for this buyer

                $this->authenticationApi->configureApiConnection($user->getMarketPlaceConfigurationKey());
                $response = $this->authenticationApi->authenticateUser(
                    $user->getEmail(),
                    $user->getUsername(),
                    $user->getFirstname(),
                    $user->getLastname()
                );


                $user->setIzbergUserId($response->getUser()->getId());

                $this->userManager->saveUser($user);
            }

            // A Buyer without IzbergUserId must be able to throw an exception inside the caller
            return !empty($user->getIzbergUserId());
        }

        return true;
    }

    public function getBuyerSuperior(User $buyer): ?User
    {
        if (!$buyer->hasRole(User::ROLE_BUYER)) {
            return null;
        }

        /**
         * Get all relationship entity link with $buyer
         * BUYER are link to his superior by relationship
         * - buyer is a child inside relationship entity
         * - manager is a parent inside relationship entity with isDelegate attribute to false
         *
         * @var ArrayCollection $buyerRelationships
         */
        $buyerRelationships = $buyer->getChildUsers();

        /**
         * @var User|null $manager
         */
        $manager = null;

        /**
         * For each relationship entity, get parent buyer
         * @var UserToUserRelationship $relation
         */
        foreach ($buyerRelationships as $relation) {
            if (!$relation->getIsDelegate()) {
                // When we got the manager, we break loop

                $manager = $relation->getParentUser();
                break;
            }
        }

        return $manager;
    }

    public function getBuyerDelegates(User $buyer): array
    {
        return $buyer->getDelegates();
    }

    public function getValidator(User $buyer): array
    {
        $manager = $this->getBuyerSuperior($buyer);
        $usersToNotify = [];
        if ($manager && $manager->isEnabled()) {
            $usersToNotify = $this->getBuyerDelegates($manager);

            if (!count($usersToNotify)) {
                $usersToNotify = [$manager];
            }
        }
        return $usersToNotify;
    }

    public function getManagerWhoIAmDelegate(User $delegate): ?array
    {
        if (!$delegate->hasRole(User::ROLE_BUYER)) {
            return [];
        }

        $delegationRelationships = $delegate->getParentUsers()->toArray();

        $users = [];

        /**
         * @var UserToUserRelationship $relation
         */
        foreach ($delegationRelationships as $relation) {
            if ($relation->getIsDelegate()) {
                $users[] = $relation->getChildUser();
            }
        }
        $manager = new ArrayCollection($users);

        return $manager->getValues();
    }

    public function isManager(User $user)
    {
        return $user->isManager();
    }

    public function isDelegate(User $user): bool
    {
        $userRelationships = $user->getParentUsers()->getValues();
        /** @var UserToUserRelationship $relationship */
        foreach ($userRelationships as $relationship) {
            if ($relationship->getIsDelegate()) {
                return true;
            }
        }
        return false;
    }

    /**
     * This function will turn on the flag for the new adress system.
     *
     * @param User $buyer
     * @return User
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function buyerSelectAdressInCart(User $buyer): User
    {
        $buyer->setHasUsedCartBefore(true);
        $this->em->persist($buyer);
        $this->em->flush();
        return $buyer;
    }

    /**
     * @param User $buyer
     * @return BuyerAddress|null
     */
    public function getDefaultBuyerBillingAddress(User $buyer): ?BuyerAddress
    {
        $epsaBuyerAddressModel = new BuyerAddressModel();

        /** @var Country $country */
        $country = $this->countryRepository->findOneBy(['code' => $buyer->getMarketPlace()->getBillingCountryCode()]);

        $epsaBuyerAddressModel->setName($buyer->getMarketPlace()->getBillingName());
        $epsaBuyerAddressModel->setAddress($buyer->getMarketPlace()->getBillingAddress());
        $epsaBuyerAddressModel->setAddress2($buyer->getMarketPlace()->getBillingAddress2());
        $epsaBuyerAddressModel->setZipCode($buyer->getMarketPlace()->getBillingZipCode());
        $epsaBuyerAddressModel->setCity($buyer->getMarketPlace()->getBillingCity());
        $epsaBuyerAddressModel->setCountry($country);

        $buyerAddress = $this->addressService->getBuyerAddressAndCreateItIfNotExists($buyer, BuyerAddress::TYPE_BILLING, $epsaBuyerAddressModel);

        return $buyerAddress;
    }

    /**
     * @param User $buyer
     * @return BuyerAddress|null
     */
    public function getDefaultBuyerShippingAddress(User $buyer): ?BuyerAddress
    {
        $defaultBuyerAddress = $buyer->getDefaultBuyerAddress();

        $buyerAddressModel = new BuyerAddressModel();
        $buyerAddressModel->setName($defaultBuyerAddress->getName());
        $buyerAddressModel->setAddress($defaultBuyerAddress->getAddress());
        $buyerAddressModel->setAddress2($defaultBuyerAddress->getAddress2());
        $buyerAddressModel->setZipCode($defaultBuyerAddress->getZipCode());
        $buyerAddressModel->setCity($defaultBuyerAddress->getCity());
        $buyerAddressModel->setCountry($defaultBuyerAddress->getCountry());

        $lastBuyerAddress = $this->addressService->getLastUsedTotalRecipient($buyer, $defaultBuyerAddress);

        if ($lastBuyerAddress) {
            $buyerAddressModel->setContact(
                empty($lastBuyerAddress->getContact()) ? $buyer->getFirstname() . ' ' . $buyer->getLastname() : $lastBuyerAddress->getContact()
            );
            $buyerAddressModel->setPhone(
                empty($lastBuyerAddress->getPhone()) ? $buyer->getMainPhoneNumber() : $lastBuyerAddress->getPhone()
            );
            $buyerAddressModel->setComment($lastBuyerAddress->getComment());
        } else {
            $buyerAddressModel->setContact($buyer->getFirstname() . ' ' . $buyer->getLastname());
            $buyerAddressModel->setPhone($defaultBuyerAddress->getPhone() ?? '+00 00 00 00 00 00');
            $buyerAddressModel->setComment($defaultBuyerAddress->getComment());
        }

        $buyerAddress = $this->addressService->getBuyerAddressAndCreateItIfNotExists($buyer, BuyerAddress::TYPE_TOTAL, $buyerAddressModel);

        return $buyerAddress;
    }

    private function getCurrentMetaCart(User $buyer): ?MetaCart
    {
        /** @var MetaCart $metaCart */
        $metaCart = $this->metaCartRepository->findOneBy([
            'buyer' => $buyer,
            'status' => MetaCart::STATUS_CREATED,
        ]);

        return $metaCart;
    }

    /**
     * Returns true if default country of delivery has been set for the given user
     * Returns false if the country of delivery already exists for the given user
     *
     * @param User $user
     * @return bool
     */
    public function setDefaultBuyerCountryOfDelivery(User $user): bool
    {
        $countryOfDelivery = $user->getCountryOfDelivery();
        if (!$countryOfDelivery) {
            $possibleCountryCode = array_map(function (Country $country) {
                return $country->getIzbergCode();
            },
                $this->countryRepository->findAvailableCountryOfDelivery());

            $countryOfDelivery = $user->getInvoiceEntity()?->getCountry();
            if (!in_array($countryOfDelivery?->getIzbergCode(), $possibleCountryCode)) {
                $countryOfDelivery = $this->countryRepository->findByCode('france');
            }
            $user->setCountryOfDelivery($countryOfDelivery);
            $this->userManager->saveUser($user);

            return true;
        }

        return false;
    }

    /**
     * Returns true if country of delivery for the given buyer has been changed
     * Returns false if country cannot be found
     * Returns false if user's current country of delivery is the same than the one passed as argument
     *
     * @param User $user
     * @param string $countryCode
     * @return bool
     */
    public function changeBuyerCountryOfDelivery(User $user, string $countryCode): bool
    {
        $countryOfDelivery = $this->countryRepository->findByCode($countryCode);

        if (!$countryOfDelivery) {
            return false;
        }

        $userCountryOfDelivery = $user->getCountryOfDelivery();

        if ($userCountryOfDelivery && ($countryOfDelivery->getCode() === $userCountryOfDelivery->getCode())) {
            return false;
        }

        $user->setCountryOfDelivery($countryOfDelivery);
        $this->userManager->saveUser($user);

        $metaCart = $this->getCurrentMetaCart($user);
        if ($metaCart) {
            // Can't refer to CartService class (cyclic reference appear).
            // I can't do without duplicate CartService::emptyCart code.

            $metaCart->setStatus(MetaCart::STATUS_EMPTIED);
            $metaCart->setIsCurrent(false);

            $this->metaCartRepository->save($metaCart);
        }


        return true;
    }

    public function getPotentialDelegates(string $term, User $buyer)
    {
        return $this->userRepository->findPotentialDelegateByNameOrIgg($term, $buyer);
    }


    /**
     * @param User $buyer
     * @param User $operator
     * @param bool $status
     * @param bool $manually to tell if user has been modified by a real operator/admin user
     * @return User
     */
    public function setBuyerStatus(User $buyer, User $operator, bool $status = false, bool $manually = false): User
    {

        // Here, $buyer will not auto enabled. Say it !
        $buyer->setAutoEnabled(false);

        /** @var User $manager */
        $manager = $this->getBuyerSuperior($buyer);

        $this->loggerService->info(
            "Update buyer status",
            'UPDATE_USER_STATUS',
            $buyer->getUsername(), [
                'ID' => $buyer->getId(),
                'ENABLER_ID' => $buyer->getUserEnabler()?->getId(),
                'USER_ENABLED' => $buyer->isEnabled(),
                'ENABLER_ENABLED' => $buyer->getUserEnabler()?->getEnabled(),
                'OPERATOR_ID' => $operator->getId(),
                'OPERATOR_USERNAME' => $operator->getUsername(),
                'STATUS' => print_r($status, true),
            ]
        );

        if ($status) {
            // We must test N+1 status

            if ($manager && !$manager->isEnabled()) {
                // Manager must be auto enabled

                $manager->setAutoEnabled(true);
                $this->enableBuyer($manager, $operator);
            }

            return $this->enableBuyer($buyer, $operator);
        }

        // Disable User
        if ($this->haveActiveBuyers($buyer)) {
            // Before i'll be deactivated, have i some 'N' as 'N+1'
            // I'm stay active but autoEnabled

            $buyer->setAutoEnabled(true);
            $buyer = $this->enableBuyer($buyer, $operator);
        } else {
            $buyer = $this->disableBuyer($buyer, $operator);
        }

        // Before i'll be deactivated, if my 'N+1' have any 'N' as 'N+1'
        if ($manager && $manager->isAutoEnabled() && !$this->haveActiveBuyers($manager)) {
            $this->disableBuyer($manager, $operator);
        }

        return $buyer;
    }

    /**
     * @param User $buyer
     * @param float $cartPrice
     * @return bool
     */
    public function isValidationNeeded(User $buyer, float $cartPrice): bool
    {

        if ($buyer->getInvoiceEntity()->isManagerValidation()) {
            if ($cartPrice >= $buyer->getInvoiceEntity()->getValidationThreshold()) {
                return true;
            }
        }
        return false;
    }

    public function isValidationActivated(User $buyer): bool
    {
        return $buyer->getInvoiceEntity()->isManagerValidation();
    }

    private function haveActiveBuyers(User $user)
    {
        $userRelationships = $user->getParentUsers()->getValues();

        if (empty($userRelationships)) {
            // Have any relationships

            return false;
        }

        return array_reduce(
            $userRelationships,
            static function (bool $isManager, UserToUserRelationship $relationship): bool {
                return $isManager || (!$relationship->getIsDelegate() && $relationship->getChildUser()->isEnabled() && !$relationship->getChildUser()->isAutoEnabled());
            },
            false
        );
    }

    /**
     * @param User $buyer
     * @param User $operator
     * @return User
     */
    private function enableBuyer(User $buyer, User $operator): User
    {
        try {
            $buyer->setEnabled(true);
            $buyer->setDisabledAt(null);

            $buyer = $this->userRepository->save($buyer);

            $this->loggerService->info(
                sprintf('User %s has been enabled by %s',
                    $buyer->getUsername(),
                    $operator->getUsername()
                ),
                EventNameEnum::USER_ENABLE, [
                    'ENABLER_ID' => $buyer->getUserEnabler()?->getId(),
                    'USER_ENABLED' => $buyer->isEnabled(),
                    'ENABLER_ENABLED' => $buyer->getUserEnabler()?->getEnabled(),
                    'OPERATOR_ID' => $operator->getId(),
                    'OPERATOR_USERNAME' => $operator->getUsername()
                ]);
        } catch (Exception $e) {
            $this->loggerService->error(
                sprintf('Error while enabling user: %s', $buyer->getUsername()), "USER_UPDATE",
                $operator->getTechnicalId(), array("error" => $e)
            );
        }
        return $buyer;
    }

    /**
     * @param User $buyer
     * @param User $operator
     * @return User
     */
    private function disableBuyer(User $buyer, User $operator): User
    {
        try {
            // Try to remove all of his delegates

            $delegates = $this->getBuyerDelegates($buyer);

            if ($delegates) {
                foreach ($delegates as $delegate) {
                    $delegateRelationShip = new UserToUserRelationship();

                    $delegateRelationShip->setChildUser($buyer);
                    $delegateRelationShip->setParentUser($delegate);
                    $delegateRelationShip->setIsDelegate(true);

                    $this->userRelationshipRepository->deleteRelationship($delegateRelationShip);
                }
            }

            $buyer->setEnabled(false);
            $buyer->setAutoEnabled(false);
            $buyer->setDisabledAt(new \DateTime());

            $buyer = $this->userRepository->save($buyer);

            $this->loggerService->info(
                sprintf('User %s has been disabled by %s',
                    $buyer->getUsername(),
                    $operator->getUsername()
                ),
                EventNameEnum::USER_DISABLE,
                [
                    'ENABLER_ID' => $buyer->getUserEnabler()?->getId(),
                    'USER_ENABLED' => $buyer->isEnabled(),
                    'ENABLER_ENABLED' => $buyer->getUserEnabler()?->getEnabled(),
                    'OPERATOR_ID' => $operator->getId(),
                    'OPERATOR_USERNAME' => $operator->getUsername()
                ]
            );
        } catch (Exception $e) {
            $this->loggerService->error(
                sprintf('Error while disabling user: %s', $buyer->getUsername()), "USER_UPDATE",
                $operator->getTechnicalId(), array("error" => $e)
            );
        }

        return $buyer;
    }

    /**
     * branch is the first part of organization field. ( example  'branch/part2/part3/part4')
     * @param String $organization
     * @return mixed|String
     */
    private function getBranch(string $organization)
    {
        if (!$organization) {
            return null;
        }
        $parts = explode("/", $organization);
        if (count($parts) > 1) {
            return $parts[0];
        }
        return $organization;
    }

    /**
     * @throws MailAlreadyUsedException
     */
    private function checkEmailChanging(User $user, string $email, string $username)
    {
        //mail is changing ?
        if (strcasecmp($user->getEmail(), $email) != 0) {

            $userEmail = $this->getBuyerFromEmail($email);
            // new mail already used, can't use it
            if ($userEmail) {
                $this->loggerService->error("update with an already existing mail", "createOrUpdateBuyer", ["username" => $username, "email" => $userEmail]);
                throw new MailAlreadyUsedException();
            }

            //reboot some izberg info
            $user->setIzbergUserId(null);
            $this->buyerAddressRepository->resetBuyerAddressIzbergId($user);
            $this->metaCartRepository->fixMetaCartIsCurrent($user);
            $user->setForceLogout(true);
        }

    }

    public function switchUserMarketplace(User $user, MarketPlace $marketPlace)
    {
        // set izbergId to null
        $user->setIzbergUserId(null);

        // reset user cart
        $user->setHasUsedCartBefore(false);
        $this->metaCartRepository->resetCurrentUserMetaCart($user);

        // update user marketplace and country of delivery
        $user->setMarketPlace($marketPlace);
        $user->setCountryOfDelivery($marketPlace->getCountryOfDelivery());
        $this->userRepository->save($user);

        // unset all buyer addresses
        $this->buyerAddressRepository->unsetBuyerAddresses($user);
    }

    public function disableNotActiveBuyers()
    {
        $this->userRepository->disableNotActiveBuyers();
    }
}
