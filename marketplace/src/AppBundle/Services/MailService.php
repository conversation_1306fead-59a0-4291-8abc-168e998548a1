<?php

namespace AppBundle\Services;

use AppBundle\Entity\Node;
use AppBundle\Entity\NodeContent;
use AppBundle\Entity\User;
use AppBundle\Exception\MailException;
use AppBundle\Exception\MailValidationException;
use AppBundle\Model\EmailTemplate;
use AppBundle\Repository\NodeRepository;
use Doctrine\ORM\EntityManagerInterface;
use Html2Text\Html2Text;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Swift_Mailer;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Twig\Environment;

class MailService
{
    public const DEFAULT_LOCALE = "en";

    //common template variables (to avoid misspelling errors and for more consistency)
    public const FIRST_NAME_VAR = "firstName";
    public const LAST_NAME_VAR = "lastName";

    //LIST OF TEMPLATES
    public const USER_RESET_MDP = 'RESET_PASSWORD_TO_USER';
    public const MERCHANT_REGISTRATION_CONFIRMED = 'VENDOR_ACCOUNT_CREATION_TO_VENDOR';
    public const OPERATOR_MERCHANT_REGISTRATION_CONFIRMED = 'VENDOR_ACCOUNT_CREATION_TO_OPERATOR';

    public const ADMIN_ACCOUNT_CREATION = 'ADMIN_ACCOUNT_CREATION_TO_ACCOUNT';

    public const MERCHANT_REJECTED = 'VENDOR_ACCOUNT_REJECTED_TO_VENDOR';
    public const MERCHANT_ACCEPTED = 'VENDOR_ACCOUNT_VALIDATED_TO_VENDOR';

    public const BUYER_INVITIATION_TO_SUPPLIER = 'BUYER_INVITIATION_TO_SUPPLIER';                                       // BUYER_INVITE_VENDOR_ON_PLATFORM
    public const BUYER_INVITATION_REMINDER_TO_SUPPLIER = 'BUYER_INVITATION_REMINDER_TO_SUPPLIER';

    // TOTAL Process notifications
    public const CART_CHECKOUT_CONFIRMATION_TO_BUYER = "CART_CHECKOUT_CONFIRMATION_TO_BUYER";
    public const CART_PENDING_VALIDATION_TO_MANAGER_OR_DELEGATE = "CART_PENDING_VALIDATION_TO_MANAGER_OR_DELEGATE";    // CART_PENDING_VALIDATION_BY_MANAGER
    public const ORDER_CONFIRMED_BY_VENDOR_TO_BUYER = "ORDER_CONFIRMED_BY_VENDOR_TO_BUYER";
    public const CART_CONFIRMED_TO_BUYER = "CART_CONFIRMED_TO_BUYER";
    public const CART_REJECTED_TO_BUYER = "CART_REJECTED_TO_BUYER";
    public const CART_CANCELLED_TO_MANAGER_OR_DELEGATE = "CART_CANCELLED_TO_MANAGER_OR_DELEGATE";
    public const CART_CANCELLED_TO_MERCHANT = "CART_CANCELLED_TO_MERCHANT";
    public const CART_CANCELLED_TO_BUYER = "CART_CANCELLED_TO_BUYER";
    public const ORDER_REJECTED_BY_VENDOR_TO_BUYER = "ORDER_REJECTED_BY_VENDOR_TO_BUYER";
    public const ORDER_REJECTED_BY_VENDOR_TO_VENDOR = "ORDER_REJECTED_BY_VENDOR_TO_VENDOR";
    public const CART_CONFIRMED_DIRECTLY_TO_BUYER = "CART_CONFIRMED_DIRECTLY_TO_BUYER";

    public const CART_CONFIRMED_TO_MANAGER_OR_DELEGATE = "CART_CONFIRMED_TO_MANAGER_OR_DELEGATE";                       // CART_CONFIRMED_BY_MANAGER_OR_DELEGATE
    public const CART_REJECTED_TO_MANAGER_OR_DELEGATE = "CART_REJECTED_TO_MANAGER_OR_DELEGATE";                         // CART_REJECTED_BY_MANAGER_OR_DELEGATE

    public const DELEGATION_ADDED_BY_MANAGER_TO_MANAGER = "DELEGATION_ADDED_BY_MANAGER_TO_MANAGER";                     // NOTIFY_MANAGER_DELEGATION_ADDED_BY_MANAGER
    public const DELEGATION_ADDED_BY_MANAGER_TO_DELEGATE = "DELEGATION_ADDED_BY_MANAGER_TO_DELEGATE";                   // NOTIFY_DELEGATES_DELEGATION_ADDED_BY_MANAGER
    public const DELEGATION_REMOVE_BY_MANAGER_TO_MANAGER = "DELEGATION_REMOVE_BY_MANAGER_TO_MANAGER";                   // NOTIFY_MANAGER_DELEGATION_REMOVE_BY_MANAGER
    public const DELEGATION_REMOVED_BY_MANAGER_TO_DELEGATE = "DELEGATION_REMOVED_BY_MANAGER_TO_DELEGATE";               // NOTIFY_DELEGATES_DELEGATION_REMOVE_BY_MANAGER

    public const ILLEGAL_CONTENT_CREATED = "ILLEGAL_CONTENT_TO_OPERATOR";

    public const INVITED_SUPPLIER_ONBOARDING_PROGRESS_TO_BUYER = "INVITED_SUPPLIER_ONBOARDING_PROGRESS_TO_BUYER";

    public const BEST_OFFER_REPORTING_TO_VENDOR = "BEST_OFFER_REPORTING_TO_VENDOR";

    private const EMAIL_ID = "EMAIL_ID";
    private const LANGUAGE = "LANGUAGE";
    private const USERS = "USERS";

    private const PARAM_MAILER_FROM_EMAIL = "mailer_from_email";
    private const PARAM_MAILER_FROM_NAME = "mailer_from_name";

    private const PARAM_FORCE_LOCALE = "force_locale";

    private const LOG_EXCEPTION_STR = 'exception';

    public const NEW_MERCHANT_RESPONSE = 'TICKET_THREAD_UPDATED_BY_VENDOR_TO_BUYER';
    public const NEW_OPERATOR_RESPONSE = 'TICKET_THREAD_UPDATED_BY_OPERATOR_TO_BUYER';

    public const QUOTE_NEW_QUOTE_TO_BUYER = 'QUOTE_NEW_QUOTE_TO_BUYER';
    public const QUOTE_NEW_QUOTE_TO_VENDOR = 'QUOTE_NEW_QUOTE_TO_VENDOR';
    public const QUOTE_SEND_QUOTE_TO_BUYER = 'QUOTE_SEND_QUOTE_TO_BUYER';
    public const QUOTE_SEND_QUOTE_TO_VENDOR = 'QUOTE_SEND_QUOTE_TO_VENDOR';
    public const QUOTE_REFUSE_QUOTE_TO_BUYER = 'QUOTE_REFUSE_QUOTE_TO_BUYER';
    public const QUOTE_REFUSE_QUOTE_TO_VENDOR = 'QUOTE_REFUSE_QUOTE_TO_VENDOR';
    public const QUOTE_ACCEPT_QUOTE_TO_BUYER = 'QUOTE_ACCEPT_QUOTE_TO_BUYER';
    public const QUOTE_ACCEPT_QUOTE_TO_VENDOR = 'QUOTE_ACCEPT_QUOTE_TO_VENDOR';
    public const QUOTE_CANCEL_QUOTE_TO_BUYER = 'QUOTE_CANCEL_QUOTE_TO_BUYER';
    public const QUOTE_CANCEL_QUOTE_TO_VENDOR = 'QUOTE_CANCEL_QUOTE_TO_VENDOR';
    public const QUOTE_NEGOCIATE_QUOTE_TO_VENDOR = 'QUOTE_NEGOCIATE_QUOTE_TO_VENDOR';
    public const QUOTE_NEW_MESSAGE_TO_BUYER = 'QUOTE_NEW_MESSAGE_TO_BUYER';
    public const QUOTE_NEW_MESSAGE_TO_VENDOR = 'QUOTE_NEW_MESSAGE_TO_VENDOR';
    public const TICKET_THREAD_UPDATED_BY_BUYER_TO_VENDOR = 'TICKET_THREAD_UPDATED_BY_BUYER_TO_VENDOR';

    public const CXML_ORDER_ERROR_TO_VENDOR = 'CXML_ORDER_ERROR_TO_VENDOR';
    public const ORDER_AUTO_CONFIRMED_TO_VENDOR = 'ORDER_AUTO_CONFIRMED_TO_VENDOR';
    public const ORDER_PENDING_CONFIRMATION_TO_VENDOR = 'ORDER_PENDING_CONFIRMATION_TO_VENDOR';

    public const ALERT_ORDER_RISK_CONFIRMED_TO_HSE = 'ALERT_ORDER_RISK_CONFIRMED_TO_HSE';
    public const ORDER_CONFIRMED_BY_VENDOR_TO_VENDOR = 'ORDER_CONFIRMED_BY_VENDOR_TO_VENDOR';

    protected Swift_Mailer $mailer;
    protected LogService $logger;
    protected EntityManagerInterface $em;
    protected Environment $twig;
    protected SecurityService $securityService;
    protected RouterInterface $router;
    protected NodeRepository $nodeRepository;
    protected array $parameters;
    private string $absoluteUrl;
    private string $protocol;
    private string $environment;


    private array $bcc;

    const TOKEN = 'token';

    public function __construct(
        SecurityService        $securityService,
        Swift_Mailer           $mailer,
        LogService             $logger,
        EntityManagerInterface $em,
        Environment            $twig,
        RouterInterface        $router,
        NodeRepository         $nodeRepository,
        array                  $parameters,
        string                 $absoluteUrl,
        string                 $protocol,
        string                 $environment
    )
    {
        $this->mailer = $mailer;
        $this->securityService = $securityService;
        $this->logger = $logger;
        $this->em = $em;
        $this->twig = $twig;
        $this->router = $router;
        $this->nodeRepository = $nodeRepository;
        $this->parameters = $parameters;
        $this->absoluteUrl = $absoluteUrl;
        $this->protocol = $protocol;
        $this->environment = $environment;
    }

    /**
     * Send a email
     *
     * @param string $emailIdentifier  the identifier of the email to send
     * @param string $lang             the language of the email
     * @param mixed  $toUsers          one email address or an array of email address (array of string)
     * @param string $fromEmailAddress the email address of the sender. If null get the one from the configuration.
     * @param string $fromName         the name to use for the sender. If null get the one from the configuration.
     * @param array  $data             the list of variables for the template
     * @param array  $attachFiles      optional files to be attached to the email. List of Swift_Attachment or AttachmentLink
     * @param array  $toUsersBcc       optional users' mail address in Bcc
     *
     */
    public function sendEmailMessage(
        string $emailIdentifier,
        string $lang,
               $toUsers,
        array  $data,
        string $fromEmailAddress = null,
        string $fromName = null,
               $attachFiles = [],
        array  $toUsersBcc = []
    )
    {
        $lang = strtolower($lang);
        //
        // AJOUTEZ --> , force_locale: 'en'
        // Dans services.yml pour activer cette fonctionnalité
        //
        // LANG OVERRIDE
        //
        if (isset($this->parameters[self::PARAM_FORCE_LOCALE])) {
            if ($this->parameters[self::PARAM_FORCE_LOCALE] !== false) {
                $lang = $this->parameters[self::PARAM_FORCE_LOCALE];
            }
        }

        //add a variable host that can be used to get the base url of the application
        $data["host"] = $this->protocol . $this->absoluteUrl;

        //load the template

        /**
         * @var Node $node
         */
        $node =  $this->nodeRepository->findEmailBySlugAndLanguage($emailIdentifier, $lang);
        if ($node === null) {
            $message = "error while getting email template: No template found or more than one template found";
            $this->triggerError($emailIdentifier, $lang, $toUsers, $message);
            return false;
        }

        /**
         * @var NodeContent $template
         */
        $template = $node->getContent($lang);
        $emailBody = $this->buildContentFromTwigTemplate($template->getBody(), $data, $emailIdentifier);
        $emailObject = $this->buildContentFromTwigTemplate($template->getTitle(), $data, $emailIdentifier);
        if ($emailBody === null || $emailObject === null) {
            return false;
        }

        if (!$this->checkEmails($toUsers, $emailBody)) {
            return false;
        }

        if (!$this->checkEmails($toUsersBcc, $emailBody, true)) {
            return false;
        }

        $fromEmailParam = $fromEmailAddress;

        if ($fromEmailParam === null) {
            $fromEmailParam = $this->parameters[self::PARAM_MAILER_FROM_EMAIL];
        }

        $fromEmailParam = trim($fromEmailParam);

        if (!filter_var($fromEmailParam, FILTER_VALIDATE_EMAIL)) {
            $this->logger->error("sender email address invalid", EventNameEnum::EMAIL_ERROR, null, ["subject" => $emailBody, "to" => $toUsers]);
            return false;
        }

        $fromNameParam = $fromName;
        if ($fromNameParam === null) {
            $fromNameParam = $this->parameters[self::PARAM_MAILER_FROM_NAME];
        }

        if ($this->environment != null && $this->environment != '') {
            $emailObject = $this->environment . $emailObject;
        }

        $message = (new \Swift_Message($emailObject))
            ->setFrom($fromEmailParam, $fromNameParam)
            ->setTo($toUsers)
            ->setBcc($toUsersBcc);

        if ($this->hasConfiguredBcc()) {
            foreach ($this->bcc as $bccEmailAddress) {
                $message->addBcc($bccEmailAddress);
            }
        }

        foreach ($attachFiles as $file) {
            if ($file instanceof \Swift_Attachment) {
                $message->attach($file);
            } else {
                //add attachment to the end of the email
                $emailBody .= "<p><a href='" . $file->getLink() . "'>" . $file->getFilename() . "</a></p>";
            }
        }

        $message->setBody($emailBody, "text/html")
            ->addPart($this->getTextFromHtml($emailBody), "text/plain");

        //send the message
        $result = $this->mailer->send($message);
        if ($result === 0) {
            $this->logger->error("Error occurred while Sending email: SwiftMailer return a 0 status", EventNameEnum::EMAIL_ERROR, null, ["subject" => $emailBody, "to" => $toUsers]);
            return false;
        }

        //here, we consider, that the email has been sent, so add a log
        $this->logger->info("an email has been sent", EventNameEnum::EMAIL_SENT, null, [
            "subject" => $emailObject,
            "from" => $fromEmailParam,
            "to" => $toUsers,
            "slug" => $emailIdentifier]);

        return true;
    }

    /**
     * Send an email to a user to confirm the account creation.
     *
     * @param UserInterface $user
     */
    public function sendConfirmationEmailMessage(UserInterface $user): bool
    {
        if (!$user instanceof User) {
            throw new \InvalidArgumentException();
        }

        return false;
    }

    /**
     * validate an email template. Throw an error if a validation error occurred
     *
     * @param EmailTemplate $emailTemplate
     *
     * @throws MailValidationException|MailException
     */
    public function validateEmailTemplate(EmailTemplate $emailTemplate): bool
    {
        $content = $this->buildContentFromTwigTemplate(
            $emailTemplate->getContent(),
            $emailTemplate->getVariables(),
            $emailTemplate->getTemplateName(),
            true
        );

        $subject = $this->buildContentFromTwigTemplate(
            $emailTemplate->getSubject(),
            $emailTemplate->getVariables(),
            $emailTemplate->getTemplateName(),
            true
        );

        $validationWarnings = [];
        foreach ([$content, $subject] as $template) {
            if (preg_match('#\{.*\{.*\}.*\}#', $template)) {
                $validationWarnings[] = 'template has incorrect formatted variables: ' . $template;
            }
        }

        if (count($validationWarnings)) {
            throw new MailValidationException(implode("\n", $validationWarnings));
        }

        return true;
    }

    /**
     * Send an email to a user to confirm the password reset.
     *
     * @param User $user
     *
     * @return bool
     */
    public function sendResettingEmailMessage(?User $user)
    {
        if (!$user instanceof User) {
            throw new \InvalidArgumentException();
        }

        $notificationId = self::USER_RESET_MDP;
        $url = $this->router->generate(
            'user.password.reset',
            array(
                self::TOKEN => $user->getResetPasswordToken()
            ),
            UrlGeneratorInterface::ABSOLUTE_URL
        );
        if ($user->isEnabled() || $user->hasRole(User::ROLE_OPERATOR) || $user->hasRole(User::ROLE_SUPER_ADMIN)) {
            return $this->sendEmailMessage(
                $notificationId,
                $user->getLocale(),
                $user->getEmail(),
                array(
                    MailService::FIRST_NAME_VAR => $user->getFirstname(),
                    MailService::LAST_NAME_VAR => $user->getLastname(),
                    "url" => $url
                )
            );
        }

        return false;
    }

    /*************************************************************************************************
     * INTERNAL TOOLS
     ************************************************************************************************/

    /**
     * @param string $html the html content
     *
     * @return string
     */
    private function getTextFromHtml(string $html)
    {
        //buid text part of the email
        $htmlBody = new Html2Text($html);
        return $htmlBody->getText();
    }

    /**
     * @param        $emails
     * @param string $emailBody
     * @param bool   $canBeEmpty
     *
     * @return bool
     */
    private function checkEmails(&$emails, string $emailBody, bool $canBeEmpty = false): bool
    {
        if (is_array($emails)) {
            $receivers = [];
            foreach ($emails as $key => $receiver) {
                $receiver = trim($receiver);
                if (!filter_var($receiver, FILTER_VALIDATE_EMAIL)) {
                    $this->logger->error("receiver email address invalid", EventNameEnum::EMAIL_ERROR, null, ["subject" => $emailBody, "to" => $receiver]);
                } else {
                    $receivers[] = $receiver;
                }
            }
            if (count($receivers) > 0 || $canBeEmpty) {
                $emails = $receivers;
            } else {
                return false;
            }
        } else {
            $emails = trim($emails);
            if (!filter_var($emails, FILTER_VALIDATE_EMAIL)) {
                $this->logger->error("receiver email address invalid", EventNameEnum::EMAIL_ERROR, null, ["subject" => $emailBody, "to" => $emails]);
                return false;
            }
        }

        return true;
    }

    /**
     * trigger an email error
     *
     * @param string $emailIdentifier the identifier of the email
     * @param string $lang            the language of the email
     * @param mixed  $toUsers         the list of recipients
     * @param string $message         the error message
     */
    private function triggerError(string $emailIdentifier, string $lang, $toUsers, string $message)
    {
        $this->logger->error($message, EventNameEnum::EMAIL_ERROR, null, [self::EMAIL_ID => $emailIdentifier,
            self::LANGUAGE => $lang,
            self::USERS => $toUsers]);
    }

    /**
     * build a content from a twig template
     *
     * @param string  $templateText   the text of the template
     * @param array   $variables      the variables of the template
     * @param boolean $throwException if true, the function throws an exception when an error occurred. If false the function returns null in an error case.
     *
     * @return ?string the message as a string
     * @throws MailException if any error occurred when the $throwException is true
     */
    private function buildContentFromTwigTemplate($templateText, $variables, $slug, $throwException = false)
    {
        try {
            $this->twig->enableStrictVariables();
            $templateText = str_replace('{{content}}', '{{content|raw}}', $templateText);

            $template = $this->twig->createTemplate($templateText);
            $renderTemplate = $template->render($variables);
            $this->twig->disableStrictVariables();

        } catch (\Throwable $e) {
            $this->twig->disableStrictVariables();
            $this->logger->error(
                "Error while building email from template: " . $e->getMessage(),
                EventNameEnum::EMAIL_ERROR,
                null,
                [
                    self::LOG_EXCEPTION_STR => $e->getTraceAsString(),
                    'available_template_variables' => $variables,
                    'slug' => $slug,
                ]
            );
            if ($throwException) {
                throw new MailException($e->getMessage(), 0, $e);
            } else {
                return null;
            }
        }

        return $renderTemplate;
    }

    public function generateUrl($routeName, $parameters): string
    {
        $path = $this->router->generate($routeName, $parameters, UrlGeneratorInterface::ABSOLUTE_PATH);
        return $this->protocol . $this->absoluteUrl . $path;
    }

    public function setBcc(array $bcc): void
    {
        $this->bcc = $bcc;
    }

    public function hasConfiguredBcc(): bool
    {
        return (count($this->bcc)>0);
    }
}
