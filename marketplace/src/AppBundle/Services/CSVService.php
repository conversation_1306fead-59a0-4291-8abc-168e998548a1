<?php

namespace AppBundle\Services;

use AppBundle\Exception\CSVException;
use Symfony\Contracts\Translation\TranslatorInterface;

class CSVService
{
    private const TRANSLATION_DOMAIN = 'AppBundle';
    private TranslatorInterface $translator;

    public function __construct(TranslatorInterface $translator)
    {
        $this->translator = $translator;
    }

    /**
     * @param mixed $objects array list of objects
     * @param array $headers array name of headers (first line of the CSV). Can be null
     * @param array $properties array list of the properties of the objects to include in the CSV
     * @return string the CSV as a string
     * @throws CSVException when an error occurred while building the CSV stream
     */
    public function objArrayToCSV ($objects, $headers, $properties)
    {

        try {

            $row = [];

            //if header are included
            if ($headers != null){

                //error case
                if (count($headers) != count($properties)){
                    throw new CSVException("the number of headers (".count($headers).") and properties (".count($properties).") don't match");
                }

                $translatedHeaders = [];
                foreach ($headers as $header){
                    array_push($translatedHeaders, $this->escapeCSV($this->translator->trans($header, array(), self::TRANSLATION_DOMAIN)));
                }
                array_push($row, implode(";", $translatedHeaders));
            }


            foreach ($objects as $object) {

                $reflect = new \ReflectionClass($object);

                $data = [];
                foreach ($properties as $property) {
                    array_push($data, $this->escapeCSV($this->getPropertyValue($object, $reflect, $property)));

                }
                array_push($row, implode(';', $data));
            }

            $content = implode("\r\n", $row);

            $content = utf8_decode($content);

            return $content;
        }catch(\ReflectionException $e){
            throw new CSVException("error wile building CVS file", 0, $e);
        }
    }

    /**
     * get the value of a property of the specified object
     * @param  mixed $object the original object
     * @param \ReflectionClass $reflectObject
     * @param $propertyName
     * @return string the value of the property. If property was not found, return an empty string
     */
    private function getPropertyValue ($object, $reflectObject, $propertyName){
        if ($reflectObject->hasProperty($propertyName)){
            $reflexiveProperty = $reflectObject->getProperty($propertyName);
            $reflexiveProperty->setAccessible(true);
            return $reflexiveProperty->getValue($object);
        }
        else{
            return $object->getPropertyValue($propertyName);
        }
    }

    /**
     * @param $content string escape a content to
     * @return string escaped string
     */
    private function escapeCSV ($content)
    {
      // Le standard CSV impose que le caractere quote soit doublé dans les cellules " ==> ""

      return str_replace('"', '""', $content);
    }
}