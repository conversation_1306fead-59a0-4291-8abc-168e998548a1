<?php

namespace AppBundle\Services;

use J<PERSON>\Serializer\SerializerInterface;

class SerializerService
{
    private SerializerInterface $serializer;

    public function __construct(SerializerInterface $serializer)
    {
        $this->serializer = $serializer;
    }

    public function serialize($object): string
    {
        return $this->serializer->serialize($object, 'json');
    }

    public function deserialize(string $serializedObject, string $objectClass)
    {
        return $this->serializer->deserialize($serializedObject, $objectClass, 'json');
    }
}