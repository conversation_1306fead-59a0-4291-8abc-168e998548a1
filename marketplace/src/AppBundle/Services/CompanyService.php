<?php

namespace AppBundle\Services;

use AppBundle\Entity\Address;
use AppBundle\Entity\Contact;
use AppBundle\FilterQueryBuilder\CompanyQueryBuilder;
use AppBundle\Repository\CompanyRepository;
use AppBundle\Entity\Company;
use Doctrine\ORM\EntityManagerInterface;
use Knp\Component\Pager\PaginatorInterface;
use Open\IzbergBundle\Api\ApiClientManager;
use Open\LogBundle\Service\LogService;
use Symfony\Component\Routing\RouterInterface;

class CompanyService extends AbstractPaginatedService
{
    const REPO = 'AppBundle:Company';

    private CountryService $countryService;
    private MailService $mailService;
    private LogService $logService;
    private SecurityService $securityService;
    private RouterInterface $router;
    private ApiClientManager $apiClientManager;

    public function __construct(
        EntityManagerInterface $em,
        PaginatorInterface $paginator,
        CompanyQueryBuilder $filterQueryBuilder,
        CountryService $country_service,
        MailService $mailService,
        LogService $logService,
        SecurityService $securityService,
        RouterInterface $router,
        ApiClientManager $apiClientManager
    ) {
        parent::__construct($em, 'AppBundle:Company', $paginator, $filterQueryBuilder);
        $this->countryService = $country_service;
        $this->mailService = $mailService;
        $this->logService = $logService;
        $this->securityService = $securityService;
        $this->router = $router;
        $this->apiClientManager = $apiClientManager;
    }


    /**
     * get a company by its izberg user id
     */
    public function findByIzbergUserId($izbergUserId): ?Company
    {
        /**
         * @var CompanyRepository $repository
         */
        $repository = $this->em->getRepository("AppBundle:Company");
        return $repository->findCompanyByIzbergUserId($izbergUserId);
    }


    public function getCustomFilteredPaginator($page, $numberPerPage, $request, $data, $qualifier)
    {
        $qb = $this->getQueryBuilder($qualifier);

        if ($qualifier != 'all') {
            $data['status'] = null;
        }

        if ($qualifier == 'enabled') {
            $data['category'] = null;
        }

        $this->filterQueryBuilder->build($qb, $data);

        return $this->paginator->paginate(
            $qb->getQuery(),
            $request->query->getInt('page', 1),
            $numberPerPage,
            array('defaultSortFieldName' => 'e.createdAt', 'defaultSortDirection' => 'desc')
        );
    }

    public function getAcceptableCompany($page, $numberPerPage, $request, $data)
    {
        $qb = $this->em->createQueryBuilder();
        $qb->select('e')
            ->from($this->entityName, 'e')
            ->where('e.cgu = 0')
            ->andWhere('e.rejected = 0')
            ->andWhere('e.enabled = false');

        $qb->orderBy('e.createdAt', 'DESC');

        $this->filterQueryBuilder->build($qb, $data);

        return $this->paginator->paginate(
            $qb->getQuery(),
            $request->query->getInt('acceptableCompany', 1),
            $numberPerPage,
            array(
                'pageParameterName' => 'acceptableCompany')
        );
    }

    private function getQueryBuilder($qualifier)
    {
        $qb = $this->em->createQueryBuilder();
        $qb->select('e')
            ->from($this->entityName, 'e')
            ->leftJoin('e.mainAddress', 'a')
            ->leftJoin('a.country', 'co')
            ->leftJoin('e.documents', 'do')
            ->leftJoin('e.category', 'ca');

        if ($qualifier === 'pending') {
            $qb->where('e.cgu = 0')
                ->andWhere('e.rejected = 0')
                ->andWhere('e.enabled = false');
        } else if ($qualifier === 'enabled') {
            $qb->where("e.enabled = true and e.status = 'valid'");
        } else if ($qualifier == 'disabled') {
            $qb->where('e.enabled = false');
        }

        return $qb;
    }

    public function getAdminsByFilterAndQualifier($data, $qualifier)
    {
        $qb = $this->getQueryBuilder($qualifier);

        if ($qualifier != 'all') {
            $data['status'] = null;
        }

        if ($qualifier == 'enabled') {
            $data['category'] = null;
        }

        $this->filterQueryBuilder->build($qb, $data);
        $qb->orderBy('e.createdAt', 'desc');
        $query = $qb->getQuery();
        return $query->getResult();
    }


    /***
     * @param \AppBundle\Entity\Company $company
     */
    public function askForTermPaymentBL(Company &$company)
    {
        // On demande à bénéficier du term payment
        // Pour l'instant on remet à false, ce sera l'OPERATOR qui mettra à true
        $company->setTermpaymentMoneyTransfertEnabled(false);
        $company->setTermpaymentMoneyTransfertPending(true);
        $company->setTermpaymentMoneyTransfertRequestDate(new \DateTime());
    }

    /**
     * @param \AppBundle\Entity\Company $company
     */
    public function removeTermPayment(Company &$company)
    {
        $company->setTermpaymentMoneyTransfertEnabled(false);
        $company->setTermpaymentMoneyTransfertPending(false);
        $company->setTermpaymentMoneyTransfertAcceptDate(null);
        $company->setTermpaymentMoneyTransfertRequestDate(null);
        $company->setTermpaymentMoneyTransfertDenyDate(null);
    }


    /**
     * Company Owner ASK for term payment
     * @param \AppBundle\Entity\Company $company
     *
     * @throws \Exception
     */
    public function askForTermPayment(Company &$company)
    {
        // Business logic
        $this->askForTermPaymentBL($company);

        $this->logService->info("[" . $company->getName() . "]'owner requests OPERATOR to have payment term",
            'COMPANY_REQUEST_PAYMENT_TERM',
            $this->securityService->getUser());
    }


    /***
     * @param \AppBundle\Entity\Company $company
     */
    public function rejectTermPaymentBL(Company &$company)
    {
        $company->setTermpaymentMoneyTransfertEnabled(false);
        $company->setTermpaymentMoneyTransfertPending(false);
        $company->setTermpaymentMoneyTransfertRequestDate(null);
        $company->setTermpaymentMoneyTransfertAcceptDate(null);
        $company->setTermpaymentMoneyTransfertDenyDate(new \DateTime());
    }

    /***
     * OPERATOR reject term payment for this buyer company
     * @param \AppBundle\Entity\Company $company
     * @param $reason
     *
     * @throws \Exception
     */
    public function rejectTermPayment(Company &$company, string $reason)
    {
        // Business Logic Part
        $this->rejectTermPaymentBL($company);

        $company->setTermpaymentMoneyTransfertDenyReason($reason);

        $this->logService->info("OPERATOR rejects  [" . $company->getName() . "] to have payment term",
            'OPERATOR_REJECT_COMPANY_PAYMENT_TERM',
            $this->securityService->getUser());
    }

    public function acceptTermPaymentBL(Company &$company)
    {
        $company->setTermpaymentMoneyTransfertEnabled(true);
        $company->setTermpaymentMoneyTransfertPending(false);
        $company->setTermpaymentMoneyTransfertAcceptDate(new \DateTime());
        $company->setTermpaymentMoneyTransfertDenyDate(null);
        $company->setTermpaymentMoneyTransfertDenyReason(null);
    }

    /***
     * OPERATOR accept term payment for this buyer company
     * @param \AppBundle\Entity\Company $company
     *
     * @throws \Exception
     */
    public function acceptTermPayment(Company &$company)
    {
        // Business Logic Part
        $this->acceptTermPaymentBL($company);

        $this->logService->info("OPERATOR accepts  [" . $company->getName() . "] to have payment term",
            'OPERATOR_ACCEPT_COMPANY_PAYMENT_TERM',
            $this->securityService->getUser());
    }

    /***
     * @param $numberPerPage
     * @param $request
     * @param $data
     * @param $qualifier
     *
     * @return array
     */
    public function getCompaniesWithTermPaymentFilteredPaginator($numberPerPage, $request, $data, $qualifier)
    {
        // filter :
        // 'all' : all companies with term payment requested or accepted
        // 'pending' : all companies with term payment requested (pending)

        $qb = $this->em->createQueryBuilder();
        $qb->select('e')
            ->from($this->entityName, 'e')
            ->leftJoin('e.mainAddress', 'a')
            ->leftJoin('a.country', 'co');

        if ($qualifier === 'all') {
            $qb->where(" e.termpaymentMoneyTransfertRequestDate is not null or e.termpaymentMoneyTransfertAcceptDate is not null or e.termpaymentMoneyTransfertDenyDate is not null");
        } elseif ($qualifier === 'pending') {
            $qb->where("e.termpaymentMoneyTransfertEnabled = 0 and
                            e.termpaymentMoneyTransfertPending = 1 ");
        }

        $qb->orderBy('e.termpaymentMoneyTransfertRequestDate', 'desc');

        $this->filterQueryBuilder->build($qb, $data);

        return $this->paginator->paginate(
            $qb->getQuery(),
            $request->query->getInt('page', 1),
            $numberPerPage,
            array('defaultSortFieldName' => 'e.termpaymentMoneyTransfertRequestDate', 'defaultSortDirection' => 'desc')
        );
    }

    /***
     * @param $numberPerPage
     * @param $request
     * @param $data
     * @param $qualifier
     *
     * @return array
     */
    public function getCompaniesWithTermPaymentPaginator($numberPerPage, $request)
    {
        // filter :
        // 'all' : all companies with term payment requested or accepted
        // 'pending' : all companies with term payment requested (pending)

        $qb = $this->em->createQueryBuilder();
        $qb->select('e')
            ->from($this->entityName, 'e')
            ->leftJoin('e.mainAddress', 'a')
            ->leftJoin('a.country', 'co');
        $qb->where("e.termpaymentMoneyTransfertEnabled = 0 and
                            e.termpaymentMoneyTransfertPending = 1 ");

        return $this->paginator->paginate(
            $qb->getQuery(),
            $request->query->getInt('termPayment', 1),
            $numberPerPage,
            array(
                'pageParameterName' => 'termPayment'
            )
        );
    }

    /***
     * @param $company
     *
     * @return array
     */
    public function getDataFromDB($company)
    {
        return $this->em->getUnitOfWork()->getOriginalEntityData($company);
    }

    /**
     * @param $company
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function save($company)
    {
        $this->em->persist($company);
        $this->em->flush();
    }

    /***
     * @param $raison_sociale
     * @param $identification
     * @param $country_id
     * @param $email
     * @param $first_name
     * @param $last_name
     * @param $main_phone
     * @param $opt_phone
     *
     * @return \AppBundle\Entity\Company
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function createCompany($raison_sociale, $identification, $country_id, $email, $first_name, $last_name, $main_phone, $opt_phone): Company
    {
        $company = new Company();

        $db_country = $this->countryService->getCountryById($country_id);

        if ($db_country) {
            $address = new Address();

            $address->setCountry($db_country);

            $this->em->persist($address);
            $this->em->flush();

            $company->setMainAddress($address);
        }

        $contact = new Contact();

        $contact->setEmail($email);
        $contact->setFirstName($first_name);
        $contact->setLastName($last_name);

        $contact->setPhone1($main_phone);

        if (!empty($opt_phone)) {
            $contact->setPhone2($opt_phone);
        }

        $this->em->persist($contact);
        $this->em->flush();

        $company->setMainContact($contact);

        $company->setName($raison_sociale);
        $company->setIdentification($identification);
        $company->setEnabled(false);


        //generate izberg email
        $company->generateIzbergEmail($this->apiClientManager->getConfiguration()->getSellerEmailDomain());

        // Persist company entity
        $this->em->persist($company);
        $this->em->flush();

        return $company;
    }

    /***
     * @param $id
     *
     * @return \AppBundle\Entity\Company|null|object
     */
    public function get($id)
    {
        return $this->em->getRepository('AppBundle:Company')->find($id);
    }

    /**
     * check if ident/country already exists in DB
     * @param $country
     * @param $ident
     *
     * @return bool
     */
    function company_identification_already_exists($country, $ident)
    {
        $qb = $this->em->createQueryBuilder();
        $qb->select('c')
            ->from("AppBundle:Company", "c")
            ->leftJoin("c.mainAddress", "a")
            ->leftJoin("a.country", "country")
            ->where("c.identification = :indentification and country.id = :country_id")
            ->setParameter("indentification", $ident)
            ->setParameter("country_id", $country);

        $companies = $qb->getQuery()->getResult();

        return (count($companies) != 0);
    }

    /***
     * @param $izebergUserId
     *
     * @return \AppBundle\Entity\Company|null|object
     */
    public function getCompanyByIzbergUserId($izebergUserId)
    {
        return $this->em->getRepository('AppBundle:Company')->findOneBy(array('izbergUserId' => $izebergUserId));
    }

    /***
     * @param $ident
     *
     * @return \AppBundle\Entity\Company|null|object
     */
    public function getCompanyByIdent($ident)
    {
        return $this->em->getRepository('AppBundle:Company')->findOneBy(array('identification' => $ident));
    }
}