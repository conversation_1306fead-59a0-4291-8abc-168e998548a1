<?php

namespace AppBundle\Services;

use AppBundle\Entity\User;
use AppBundle\FilterQueryBuilder\UserQueryBuilder;
use AppBundle\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\QueryBuilder;
use Knp\Component\Pager\PaginatorInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;

class UserBddService extends AbstractPaginatedService
{
    private AuthorizationCheckerInterface $authorizationChecker;
    private UserRepository $userRepository;

    public function __construct(
        EntityManagerInterface $em,
        PaginatorInterface $paginator,
        UserQueryBuilder $userFilterQueryBuilder,
        AuthorizationCheckerInterface $authorizationChecker,
        UserRepository $userRepository
    ) {
        parent::__construct($em, User::class, $paginator, $userFilterQueryBuilder);

        $this->authorizationChecker = $authorizationChecker;
        $this->userRepository = $userRepository;

    }

    public function findByUsername(string $username)
    {
        return $this->repository->findOneBy(array('username' => $username));
    }

    public function findUserByEmail(string $email)
    {
        return $this->repository->findOneBy(array('email' => $email));
    }

    public function findPotentialDelegateByStartToken(string $token, int $userId)
    {
        $user = $this->findById($userId);
        return $this->repository->findPotentialDelegateByNameOrIgg($token, $user);
    }

    /**
     * Fetch an user with his ID
     *
     * @param int $id
     *
     * @return User
     */
    public function findById($id): User
    {
        return $this->repository->find($id);
    }


    public function getCustomFilteredPaginator($page, $numberPerPage, $request, $data, $qualifier)
    {


        $qb = $this->getUserQueryBuilder($qualifier);

        $this->filterQueryBuilder->build($qb, $data);

        return $this->paginator->paginate(
            $qb->getQuery(),
            $request->query->getInt('page', 1),
            $numberPerPage,
            array('defaultSortFieldName' => 'e.createdAt', 'defaultSortDirection' => 'desc')
        );

    }

    public function getCustomUsersFilteredPaginator($page, $numberPerPage, $request, $data, $qualifier)
    {
        $qb = $this->getUserQueryBuilder($qualifier);
        if ($data != null && array_key_exists('page', $data) && $data['page'] == 'adminList') {
            // Filter with administrators
            $or = $qb->expr()->orX();
            $or->addMultiple(['e.roles LIKE :admin1',
                'e.roles LIKE :admin2']);
            $qb->andWhere($or);
            $qb->setParameter(':admin1', '%SUPER_ADMIN%');
            $qb->setParameter(':admin2', '%ROLE_OPERATOR%');
        } else {
            $qb->andWhere("e.roles NOT LIKE :admin1");
            $qb->setParameter(':admin1', '%SUPER_ADMIN%');
            $qb->andWhere("e.roles NOT LIKE :admin2");
            $qb->setParameter(':admin2', '%ROLE_OPERATOR%');
        }


        // Filter with other data
        $this->filterQueryBuilder->build($qb, $data);

        return $this->paginator->paginate(
            $qb->getQuery(),
            $request->query->getInt('page', 1),
            $numberPerPage,
            array('defaultSortFieldName' => 'e.createdAt', 'defaultSortDirection' => 'desc')
        );
    }


    /**
     * @return mixed
     */
    public function getAdmin()
    {
        return $this->userRepository->findAdmin();
    }

    /**
     * @param $page
     * @param $numberPerPage
     * @param $request
     * @param $userId
     */
    public function getBuyerFilteredPaginator($page, $numberPerPage, $request, $data, $qualifier)
    {
        $qb = $this->getBuyerQueryBuilder($data);
        // Filter with other data
        if ($this->filterQueryBuilder instanceof UserQueryBuilder) {
            $this->filterQueryBuilder->buildBuyer($qb, $data);
        }

        return $this->paginator->paginate(
            $qb->getQuery(),
            $request->query->getInt('page', 1),
            $numberPerPage,
            array('defaultSortFieldName' => 'e.username', 'defaultSortDirection' => 'desc')
        );
    }

    public function getUserConnectionsPaginator($page, $numberPerPage, $request, $userId)
    {
        $qb = $this->em->createQueryBuilder();
        $qb->select('c')
            ->from("AppBundle:Connection", "c")
            ->leftJoin("c.user", "u")
            ->where("u.id = :userId")
            ->orderBy('c.connectedAt', 'desc')
            ->setParameter("userId", $userId);


        return $this->paginator->paginate(
            $qb->getQuery(),
            $request->query->getInt('page', 1),
            $numberPerPage,
            array()
        );

    }

    private function getUserQueryBuilder($qualifier): QueryBuilder
    {
        $qb = $this->em->createQueryBuilder();
        $qb->select('e')
            ->from($this->entityName, 'e')
            ->leftJoin('e.company', 'c');

        //add qualifier in request if needed
        if ($qualifier === "toConfirmed") {
            $qb->where("e.emailConfirmed = false");
        } else if ($qualifier === "enabled") {
            $qb->where("e.enabled = true");
        } else if ($qualifier === "disabled") {
            $qb->where("e.enabled = false");
        }

        //check operator/admin: if not admin, do not not list admin user
        if (!$this->authorizationChecker->isGranted('ROLE_SUPER_ADMIN')) {
            $qb->andWhere("e.roles not like '%ROLE_SUPER_ADMIN%'");
        }


        return $qb;
    }

    private function getBuyerQueryBuilder(array $data): QueryBuilder
    {
        $qb = $this->em->createQueryBuilder();

        $qb->select('e')
            ->from(User::class, 'e')
            ->leftJoin('e.childUsers', 'r')
            ->leftJoin('r.parentUser', 'm')
            ->leftJoin('e.invoiceEntity', 'i')
            ->leftJoin('e.childUsers', 'relation')
            ->leftJoin('relation.parentUser', 'delegate')
            ->andWhere("e.roles LIKE '%ROLE_BUYER%'")
            ->andWhere("e.isDeleted = false");
        return $qb;
    }

    /**
     * get all users depending on the qualifier (filter)
     *
     * @param $qualifier string how to filter the request
     *
     * @return array the list of users
     */
    public function getUsersByQualifier($qualifier)
    {
        $qb = $this->getUserQueryBuilder($qualifier);
        $query = $qb->getQuery();
        return $query->getResult();
    }

    /**
     * get all administrators depending on the qualifier (filter)
     *
     * @param $qualifier string how to filter the request
     *
     * @return array the list of administrators
     */
    public function getAdminsByQualifier($qualifier)
    {
        $qb = $this->getUserQueryBuilder($qualifier);
        // Display only administrators
        $or = $qb->expr()->orX();
        $or->addMultiple(['e.roles LIKE :admin1',
            'e.roles LIKE :admin2']);
        $qb->andWhere($or);
        $qb->setParameter(':admin1', '%SUPER_ADMIN%');
        $qb->setParameter(':admin2', '%ROLE_OPERATOR%');
        $query = $qb->getQuery();
        return $query->getResult();
    }

    public function getOnlyUsersByQualifier($qualifier)
    {
        $qb = $this->getUserQueryBuilder($qualifier);
        $qb->andWhere("e.roles NOT LIKE :admin1");
        $qb->setParameter(':admin1', '%SUPER_ADMIN%');
        $qb->andWhere("e.roles NOT LIKE :admin2");
        $qb->setParameter(':admin2', '%ROLE_OPERATOR%');
        $query = $qb->getQuery();
        return $query->getResult();
    }

    public function getCompanyCustomFilteredPaginator($page, $numberPerPage, $request, $data, $companyId)
    {
        $qb = $this->getCompanyUserQueryBuilder($companyId);

        $this->filterQueryBuilder->build($qb, $data);

        return $this->paginator->paginate(
            $qb->getQuery(),
            $request->query->getInt('page', 1),
            $numberPerPage
        );

    }

    /**
     * @param $user
     *
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function updateUser($user)
    {
        $this->em->persist($user);
        $this->em->flush($user);
    }

    /**
     * get operators
     * @return array
     */
    public function getOperatorsUsers()
    {
        /** @var UserRepository $userRepository */
        $userRepository = $this->em->getRepository($this->entityName);
        return $userRepository->findOperator();
    }

    public function getCompanyUsersFiltered($companyId, $data)
    {
        $qb = $this->getCompanyUserQueryBuilder($companyId);
        $this->filterQueryBuilder->build($qb, $data);
        $query = $qb->getQuery();
        return $query->getResult();
    }

    public function getBuyerUsersByCompany($companyId)
    {
        $userRepository = $this->em->getRepository('AppBundle:User');
        return $userRepository->findBuyersForCompany($companyId);
    }

    public function getUsersBySite($siteId, $userId)
    {
        $userRepository = $this->em->getRepository('AppBundle:User');
        return $userRepository->findUsersForSite($siteId, $userId);
    }

    public function saveUser(User $user)
    {
        $this->em->persist($user);
        $this->em->flush($user);
    }

    private function getCompanyUserQueryBuilder($companyId)
    {
        $qb = $this->em->createQueryBuilder();
        $qb->select('e')
            ->from($this->entityName, 'e')
            ->leftJoin('e.company', 'c');

        $qb->where("c.id = :companyId")->setParameter('companyId', $companyId);

        //check operator/admin: if not admin, do not not list admin user
        if (!$this->authorizationChecker->isGranted('ROLE_SUPER_ADMIN')) {
            $qb->andWhere("e.roles not like '%ROLE_SUPER_ADMIN%'");
        }


        return $qb;
    }

    public function findUserBy(array $filter): ?User
    {
        return $this->userRepository->findOneBy($filter);
    }

    public function findUserByUsername (string $username): ?User
    {
        return $this->userRepository->findUserByUsername($username);
    }

    public function findUserByUsernameOrEmail(string $username): ?User
    {
        return $this->userRepository->findUserByUsernameOrEmail($username);
    }

    public function findUserByPasswordToken(string $token): ?User
    {
        return $this->userRepository->findUserByPasswordToken($token);
    }

    public function setNewTokenPassword(User $user)
    {
        $date = new \DateTime();
        $token = uniqid('', true).'__'.$date->getTimestamp();
        $user->setResetPasswordToken($token);
        $this->userRepository->save($user);
    }

}
