<?php

namespace AppBundle\Services;

use AppBundle\Event\ProcessBeginForEvent;
use AppBundle\Event\ProcessEndForEvent;
use AppBundle\Factory\ProcessIdFactory;
use AppBundle\Model\ProcessId;
use Symfony\Component\EventDispatcher\EventDispatcher;

class ProcessService
{
    private EventDispatcher $eventDispatcher;
    private ProcessIdFactory $processIdFactory;

    public function __construct(EventDispatcher $eventDispatcher, ProcessIdFactory $processIdFactory)
    {
        $this->eventDispatcher = $eventDispatcher;
        $this->processIdFactory = $processIdFactory;
    }

    public function processBeginFor($object): ProcessId
    {
        $processId = $this->processIdFactory->build($object);

        $this->eventDispatcher->dispatch(new ProcessBeginForEvent($processId));
        return $processId;
    }

    public function processEndFor(ProcessId $processId)
    {
        $this->eventDispatcher->dispatch(new ProcessEndForEvent($processId));
    }
}
