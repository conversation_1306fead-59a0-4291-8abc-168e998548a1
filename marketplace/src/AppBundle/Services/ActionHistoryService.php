<?php

namespace AppBundle\Services;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Query;
use Knp\Component\Pager\PaginatorInterface;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;

class ActionHistoryService extends AbstractPaginatedService
{
    private LogService $logger;

    public function __construct(EntityManagerInterface $em, PaginatorInterface $paginator, LogService $logger)
    {
        parent::__construct($em, 'AppBundle:ActionHistorization', $paginator);
        $this->logger = $logger;
    }

    /**
     * @param $page
     * @param $numberPerPage
     * @param $request
     * @param $userId
     * @return mixed
     */
    public function getHistoryPaginatorForUser ($page, $numberPerPage, $request, $userId){
        $qb = $this->em->createQueryBuilder();
        $qb->select('a')
            ->from("AppBundle:ActionHistorization", "a")
            ->leftJoin("a.users", "u")
            ->where("u.id = :userId")
            ->orderBy('a.createdAt', 'desc')
            ->setParameter("userId", $userId);

        try {
            $history = $qb->getQuery()->getArrayResult(Query::HYDRATE_ARRAY);

        }catch (\Exception $e){
            $this->logger->critical("unable to generate history for user with id ".$userId.": ".$e->getMessage(),
                EventNameEnum::TECHNICAL_ERROR,
                null,
                [
                    "trace" => $e->getTraceAsString()
                ]);
            $history = [];
        }


        return $this->paginator->paginate(
            $history,
            $request->query->getInt('actionPage', 1),
            $numberPerPage,
            array(
                'pageParameterName' => 'actionPage')
        );

    }

    /**
     * @param $page
     * @param $numberPerPage
     * @param $request
     * @param $userId
     * @return mixed
     */
    public function getHistoryPaginatorForEntity ($page, $numberPerPage, $request, $technicalRef){
        $qb = $this->em->createQueryBuilder();
        $qb->select('a,u')
            ->from("AppBundle:ActionHistorization", "a")
            ->leftJoin("a.users", "u")
            ->where('a.refEntityId = :ref')
            ->orderBy('a.createdAt', 'desc')
            ->setParameter("ref", $technicalRef);

        try {
            $history = $qb->getQuery()->getArrayResult();

        }catch (\Exception $e){
            $this->logger->critical("unable to generate history for entity with tech ref ".$technicalRef.": ".$e->getMessage(),
                EventNameEnum::TECHNICAL_ERROR,
                null,
                [
                    "trace" => $e->getTraceAsString()
                ]);
            $history = [];
        }
        return $this->paginator->paginate(
            $history,
            $request->query->getInt('actionPage', 1),
            $numberPerPage,
            array(
                'pageParameterName' => 'actionPage')
        );

    }



}
