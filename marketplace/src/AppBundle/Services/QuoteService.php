<?php

namespace AppBundle\Services;

use AppBundle\Domain\QuoteDomain;
use AppBundle\Domain\QuoteItemDomain;
use AppBundle\Entity\Merchant;
use AppBundle\Entity\Quote;
use AppBundle\Entity\QuoteItem;
use AppBundle\Entity\QuoteVersion;
use AppBundle\Entity\TvaRate;
use AppBundle\Entity\User;
use AppBundle\Exception\ForbiddenActionException;
use AppBundle\FilterQueryBuilder\QuoteQueryBuilder;
use AppBundle\Model\Invitation\DataTableResponse;
use AppBundle\Model\Offer;
use AppBundle\Model\Pdf\PriceByVat;
use AppBundle\Model\Quote\QuoteData;
use AppBundle\Repository\MerchantRepository;
use AppBundle\Repository\QuoteRepository;
use AppBundle\Repository\QuoteVersionRepository;
use AppBundle\Repository\TvaRateRepository;
use AppBundle\Repository\UserRepository;
use AppBundle\Util\DateUtil;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Doctrine\ORM\QueryBuilder;
use Exception;
use Knp\Component\Pager\PaginatorInterface;
use Mpdf\Config\ConfigVariables;
use Mpdf\Config\FontVariables;
use Mpdf\Mpdf;
use Mpdf\MpdfException;
use Open\FrontVendorBundle\Exception\ApiException;
use Open\IzbergBundle\Api\ProductApi;
use Open\IzbergBundle\Api\ProductOfferApi;
use Open\IzbergBundle\Model\ApplicationCategory;
use Open\IzbergBundle\Model\Product;
use Open\IzbergBundle\Model\ProductOffer;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Environment;
use Twig\Error\Error;

/**
 * Class QuoteService.
 */
class QuoteService extends AbstractPaginatedService
{
    const TRANSLATION_BUNDLE = 'AppBundle';
    const PDF_EXTENSION = 'pdf';
    const ERROR_CODE_NUMBER_UNICITY = 'error.quote_number.unicity';
    const FIRST_OFFER_INDEX = 1;

    private QuoteItemService $quoteItemService;
    private QuoteRepository $quoteRepository;
    private TvaRateRepository $tvaRepository;
    private QuoteVersionRepository $quoteVersionRepository;
    private TranslatorInterface $translator;
    private Environment $templating;
    private SerializerService $serializer;
    private ProductOfferApi $offerApi;
    private OfferService $offerService;
    private ProductApi $productApi;
    private CartService $cartService;
    private MessageQuoteService $messageQuoteService;
    private IzbergCustomAttributes $izbergCustomAttributes;
    private string $logo;
    private MailService $mailService;
    private MerchantService $merchantService;
    private CurrencyExchangeRateService $currencyRateService;
    private RequestStack $requestStack;


    public function __construct(
        PaginatorInterface          $paginator,
        ?QuoteQueryBuilder          $filterQueryBuilder,
        QuoteItemService            $quoteItemService,
        QuoteRepository             $quoteRepository,
        QuoteVersionRepository      $quoteVersionRepository,
        EntityManagerInterface      $entityManager,
        MessageQuoteService         $messageQuoteService,
        TranslatorInterface         $translator,
        Environment                 $templating,
        SerializerService           $serializer,
        ProductOfferApi             $productOfferApi,
        OfferService                $offerService,
        CartService                 $cartService,
        IzbergCustomAttributes      $izbergCustomAttributes,
        ProductApi                  $productApi,
        MailService                 $mailService,
        MerchantService             $merchantService,
        CurrencyExchangeRateService $currencyRateService,
        RequestStack                $requestStack,
        TvaRateRepository           $tvaRateRepository
    )
    {
        parent::__construct($entityManager, 'AppBundle:Quote', $paginator, $filterQueryBuilder);
        $this->quoteItemService = $quoteItemService;
        $this->quoteRepository = $quoteRepository;
        $this->em = $entityManager;
        $this->messageQuoteService = $messageQuoteService;
        $this->translator = $translator;
        $this->templating = $templating;
        $this->quoteVersionRepository = $quoteVersionRepository;
        $this->serializer = $serializer;
        $this->offerApi = $productOfferApi;
        $this->offerService = $offerService;
        $this->cartService = $cartService;
        $this->izbergCustomAttributes = $izbergCustomAttributes;
        $this->productApi = $productApi;
        $this->mailService = $mailService;
        $this->merchantService = $merchantService;
        $this->tvaRepository = $tvaRateRepository;
        $this->currencyRateService = $currencyRateService;
        $this->requestStack = $requestStack;
    }

    public function setLogoPdf(string $logo)
    {
        $this->logo = $logo;
    }

    public function askQuote(User $buyer, Merchant $supplier, Offer $offer, $message, ...$attachments)
    {
        /** @var Quote $quote */
        $quote = new Quote();
        $quote->setStatus(Quote::STATUS_NEW);
        $quote->setBuyer($buyer);
        $quote->setVendor($supplier);
        $quote->setInitialOfferUrl($offer->getPicture());
        $quote->setInitialOfferTitle(trim(mb_substr($offer->getName(), 0, 200)));
        $quote->setInitialOfferReference((string)$offer->getId());
        $quote->setInitialOfferDescription($offer->getDescription());
        $quote->setVatGroupName($offer->getTaxRate());
        $subject = trim(mb_substr($this->messageQuoteService->generateSubject($quote, $buyer->getLocale()), 0, 200));
        $quote->setSubject($subject);

        $quote->setCurrencyRateCountryOfdelivery(
            $this->currencyRateService->getCurrencyRate(
                $buyer->getCountryOfDelivery()->getCurrency(),
                $supplier->getCurrency()));

        $this->em->persist($quote);
        $this->em->flush();
        // need the id to generate link;
        $link = $this->messageQuoteService->generateLinkPart($quote, "supplier_detail", $quote->getVendor()->getLanguage());
        $threadId = $this->messageQuoteService->startQuoteThread($quote, $offer, $subject, $message . $link, ...$attachments);
        $quote->setThreadId($threadId);

        $this->em->persist($quote);
        $this->em->flush();

        $this->sendMail($quote, MailService::QUOTE_NEW_QUOTE_TO_BUYER, MailService::QUOTE_NEW_QUOTE_TO_VENDOR, $message);


        return $quote;
    }

    /**
     * @param $quoteId
     * @param Merchant $merchant
     *
     * @return Quote|null
     * @throws ForbiddenActionException
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function readQuoteVendor($quoteId, ?Merchant $merchant)
    {
        $quoteBdd = $this->getQuote($quoteId);
        $this->checkOwnerMerchant($quoteBdd, $merchant);
        if ($quoteBdd->isVendorUnread()) {
            $quoteBdd->setVendorUnread(false);
            $this->saveQuote($quoteBdd);
        }

        return $quoteBdd;
    }

    /**
     * @param $quoteId
     * @param User $buyer
     *
     * @return Quote|null
     * @throws ForbiddenActionException
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function readQuoteBuyer($quoteId, ?User $buyer)
    {
        $quoteBdd = $this->getQuote($quoteId);

        $this->checkOwnerBuyer($quoteBdd, $buyer);
        // buyer must not see draft and redraft quote. (TOTALMP-1267)

        if ($quoteBdd->isBuyerUnread()) {
            $quoteBdd->setBuyerUnread(false);
            $this->saveQuote($quoteBdd);
        }
        return $quoteBdd;


    }

    /**
     * find a quote by id.
     *
     * @param int $quoteId
     *
     * @return Quote
     */
    public function getQuote(int $quoteId): ?Quote
    {
        return $this->quoteRepository->find($quoteId);
    }

    public function findQuoteByThreadId(int $threadId): ?Quote
    {
        return $this->quoteRepository->findOneBy(["threadId" => $threadId]);
    }

    /**
     * @param Quote $quote detached quote
     *
     * @return Quote
     * @throws OptimisticLockException
     *
     * @throws ORMException
     */
    public function saveDetachedQuote(Quote $quote): Quote
    {
        /** @var UserRepository $userRepo */
        $userRepo = $this->em->getRepository('AppBundle:User');
        /** @var MerchantRepository $merchantRepository */
        $merchantRepository = $this->em->getRepository('AppBundle:Merchant');
        $buyer = $userRepo->find($quote->getBuyer());
        /** @var Merchant $vendor */
        $vendor = $merchantRepository->find($quote->getVendor());
        if (!is_null($quote->getQuoteItems())) {
            /** @var QuoteItem $quoteItem */
            foreach ($quote->getQuoteItems() as $quoteItem) {
                $quoteItem->setQuote($quote);
            }
        }
        $quote->setBuyer($buyer);
        $quote->setVendor($vendor);

        $quote = $this->saveQuote($quote);

        return $quote;
    }

    /**
     * @param QuoteDomain $quote
     * @param Merchant|null $merchant
     *
     * @return Quote
     * @throws ORMException
     * @throws OptimisticLockException
     *
     * @throws ForbiddenActionException
     * @throws ApiException
     */
    public function updateDraftQuote(QuoteDomain $quote, ?Merchant $merchant): Quote
    {
        /** @var Quote $quoteBdd */
        $quoteBdd = $this->quoteRepository->find($quote->getQuoteId());
        // if quoteNumber is updated check value unicity for merchant
        if ($quoteBdd->getQuoteNumber() != $quote->getQuoteNumber()) {
            $this->checkNumber($quote->getQuoteNumber(), $quoteBdd->getVendor()->getId());
        }
        $this->checkOwnerMerchant($quoteBdd, $merchant);
        if ($quoteBdd->getStatus() == Quote::STATUS_NEW) {
            $quoteBdd->setStatus(Quote::STATUS_DRAFT);
        }
        $quoteBdd->setUpdatedAt();
        // update draft doesn't change status [draft or redraft]
        $quoteBdd->setTitle($quote->getTitle());
        $quoteBdd->setQuoteNumber($quote->getQuoteNumber());
        $quoteBdd->setMessage($quote->getMessage());
        $quoteBdd->setTotalPrice($this->computeTotalPrice($quote->getQuoteItems()));
        $quoteBdd->setVatGroupName($quote->getVatGroupName());

        /** @var QuoteItem $quoteItem */
        //remove orphan
        foreach ($quoteBdd->getQuoteItems() as $quoteItemBdd) {
            if (!$quote->containQuoteItem($quoteItemBdd->getId())) {
                $quoteBdd->getQuoteItems()->removeElement($quoteItemBdd);
            }
        }

        // update quoteItem
        foreach ($quote->getQuoteItems() as $quoteItem) {

            $quoteItemBdd = $quoteBdd->getQuoteItemBdd($quoteItem->getId());
            if ($quoteItemBdd != null) {
                $this->quoteItemService->updateQuoteItem($quoteItem, $quoteItemBdd);
            } else {
                $quoteItemBdd = $this->quoteItemService->updateQuoteItem($quoteItem);
                $quoteItemBdd->setQuote($quoteBdd);
                $quoteItemBdd->setTotalPrice($quoteItem->getUnitPrice() * $quoteItem->getQuantity());
                $quoteBdd->getQuoteItems()->add($quoteItemBdd);
            }
        }

        $this->em->persist($quoteBdd);
        $this->em->flush();

        return $quoteBdd;
    }

    /**
     * @param QuoteDomain $quote detached quote
     * @param Merchant|null $merchant
     *
     * @return Quote
     * @throws OptimisticLockException
     * @throws ForbiddenActionException
     *
     * @throws ORMException
     */
    public function sendQuote(QuoteDomain $quote, ?Merchant $merchant): Quote
    {
        $quoteBdd = $this->updateDraftQuote($quote, $merchant);
        $this->checkOwnerMerchant($quoteBdd, $merchant);
        $quoteBdd->setBuyerUnread(true);
        $this->doStatusTransition($quoteBdd, Quote::STATUS_SEND);
        $this->messageQuoteService->eventToThread($quoteBdd, $quoteBdd->getStatus(), $quoteBdd->getVendor());
        $this->saveQuote($quoteBdd);

        $this->sendMail($quoteBdd, MailService::QUOTE_SEND_QUOTE_TO_BUYER, MailService::QUOTE_SEND_QUOTE_TO_VENDOR, $quote->getMessage());

        return $quoteBdd;
    }

    /**
     * @param string $reason
     * @param $quoteId
     * @param Merchant $merchant
     *
     * @return Quote
     * @throws OptimisticLockException
     * @throws ForbiddenActionException
     *
     * @throws ORMException
     */
    public function refuseQuote($quoteId, $reason, ?Merchant $merchant): Quote
    {
        $quoteBdd = $this->getQuote($quoteId);
        $this->checkOwnerMerchant($quoteBdd, $merchant);
        $quoteBdd->setCancelDate(new \DateTime());
        $quoteBdd->setBuyerUnread(true);
        $this->doStatusTransition($quoteBdd, Quote::STATUS_REFUSED);
        $this->saveQuote($quoteBdd);
        $this->messageQuoteService->eventToThread($quoteBdd, $quoteBdd->getStatus(), $quoteBdd->getVendor(), $reason);

        $this->sendMail($quoteBdd, MailService::QUOTE_REFUSE_QUOTE_TO_BUYER, MailService::QUOTE_REFUSE_QUOTE_TO_VENDOR, $reason);
        return $quoteBdd;
    }

    /**
     * get list of quote for a vendor ( and status).
     *
     * @param $request \Symfony\Component\HttpFoundation\Request
     * @param $vendorId
     * @param $status
     *
     * @return \Knp\Component\Pager\Pagination\PaginationInterface
     */
    public function getQuoteByVendorPaginated($request, $vendorId, $status)
    {
        $page = $request->query->getInt('page', 1);
        $numberPerPage = $request->query->getInt('limit', 10);
        $data['status'] = $status;
        $data['vendor'] = $vendorId;

        return $this->getCustomFilteredPaginator($data, $page, $numberPerPage);
    }

    /**
     * get list of quote for a vendor ( and status).
     *
     * @param $request
     * @param User $buyer
     * @param $status
     * @param mixed $data
     * @param mixed $page
     * @param mixed $numberPerPage
     *
     * @return \Knp\Component\Pager\Pagination\PaginationInterface
     */
    public function getQuoteByBuyerPaginated(User $buyer, $data, $status, $page = 1, $numberPerPage = 10)
    {
        $data['status'] = $status;
        $data['buyer'] = $buyer->getId();

        return $this->getCustomFilteredPaginator($data, $page, $numberPerPage);
    }

    public function convertToDatatableResponse($draw, $paginator, $local): DataTableResponse
    {
        $dataResponse = new DataTableResponse();
        $data = [];
        // @var Quote $quote
        foreach ($paginator->getItems() as $merchant) {
            $data[] = $this->convertQuoteToData($merchant, $local);
        }
        $dataResponse->setDraw($draw);
        $dataResponse->setData($data);
        $dataResponse->setRecordFiltered($paginator->getTotalItemCount());
        $dataResponse->setRecordTotal($paginator->getTotalItemCount());

        return $dataResponse;
    }

    /**
     * Add quote to cart action
     * @param Quote $quote
     * @throws Exception
     */
    public function addToCart(Quote $quote)
    {

        // get offer info from origin quote offer
        $originOfferArray = $this->offerApi->getProductOffer($quote->getInitialOfferReference(), $this->getCurrentRequestLocale());
        $originOffer = $this->offerService->findOffer((int) $quote->getInitialOfferReference(), $quote->getBuyer()->getLocale(), $quote->getBuyer()->getMarketPlace());
        $productId = $originOffer->getProduct()->getId();
        $currency = $originOffer->getCurrency();
        $merchantId = $originOffer->getMerchant()->getId();
        // create new Product
        $product = $this->duplicateProduct($productId, $merchantId);

        // group item by vat
        $groupQuoteItemByVat = $this->groupQuoteItemByVatRate($quote);

        //create offers
        $offers = $this->createOffers($product->getId(), $merchantId, $quote, $originOfferArray, $originOffer->getLanguage(), $groupQuoteItemByVat, $currency);

        $this->cartService->loadCurrentBuyerCart($quote->getBuyer());


        // add offer to cart
        foreach ($offers as $offer) {
            if ($originOffer->isRisk()) {
                $this->cartService->addOfferToBuyerCurrentCart($offer, $quote->getBuyer(), 1, ['quote' => 'yes', 'risk' => 'yes']);
            } else {
                $this->cartService->addOfferToBuyerCurrentCart($offer, $quote->getBuyer(), 1, ['quote' => 'yes']);
            }
        }

        // send instant message
        $this->messageQuoteService->eventToThread($quote, Quote::STATUS_VALIDATED, $quote->getBuyer(), "achat");

        $this->sendMail($quote, MailService::QUOTE_ACCEPT_QUOTE_TO_BUYER, MailService::QUOTE_ACCEPT_QUOTE_TO_VENDOR);

        // change quote status
        $quote->setValidationDate(new \DateTime());
        $this->doStatusTransition($quote, QUOTE::STATUS_VALIDATED);
        $quote->setBuyerUnread(true);
        $this->saveQuote($quote);

    }

    private function duplicateProduct($productId, $merchantId)
    {
        /** @var Product $product */
        $product = $this->offerApi->getProduct($productId, $this->getCurrentRequestLocale());

        $keywords = $product->getKeywords();
        if (is_null($keywords) || count($keywords) == 0) {
            $keywords = [];
            $keywords[] = "Devis"; //TODO: localization
        }
        $categories = $product->getApplicationCategories();

        $categories = array_map(function (ApplicationCategory $category) {
            return $category->getId();
        },
            $categories);
        return $this->productApi->createProduct($merchantId, $product, $keywords, $categories);


    }

    private function getCustom($objAttributes)
    {
        $custom = [];
        foreach ($objAttributes as $key => $attribute) {
            $custom[$key] = $attribute;
        }
        return $custom;
    }


    /**
     * @param $quote
     *
     * @throws Exception
     */
    public function negociate($quote)
    {
        $quote = $this->doStatusTransition($quote, Quote::STATUS_REDRAFT);

        $quote->setVendorUnread(true);
        $quote = $this->version($quote);
        $this->saveQuote($quote);
        $this->messageQuoteService->eventToThread($quote, $quote->getStatus(), $quote->getBuyer(), $quote->getReason());

        $this->sendMail($quote, null, MailService::QUOTE_NEGOCIATE_QUOTE_TO_VENDOR, $quote->getReason());
    }

    /**
     * @param Quote $quote
     *
     * @throws \Exception
     */
    public function cancel($quote)
    {
        $quote->setCancelDate(new \DateTime());
        $this->doStatusTransition($quote, Quote::STATUS_CANCELLED);
        $quote->setVendorUnread(true);
        $this->saveQuote($quote);
        $this->messageQuoteService->eventToThread($quote, $quote->getStatus(), $quote->getBuyer(), $quote->getReason());
        $this->sendMail($quote, MailService::QUOTE_CANCEL_QUOTE_TO_BUYER, MailService::QUOTE_CANCEL_QUOTE_TO_VENDOR, $quote->getReason());

    }

    /**
     * count unread quote message for buyer view group by meta status
     *
     * @param User $buyer
     *
     * @return array
     */
    public function countUnreadBuyerQuote(User $buyer)
    {
        $datas = $this->quoteRepository->countUnreadBuyerMessage($buyer);

        return $this->aggregateCount($datas, true);
    }

    /**
     * count total unread quote message for buyer view.
     *
     * @param User $buyer
     *
     * @return array
     */
    public function countTotalUnreadBuyerQuote(User $buyer)
    {
        $datas = $this->quoteRepository->countTotalUnreadBuyerMessage($buyer);
        return $datas[0]['nb'];
    }

    /**
     * count unread quote message for vendor view.
     *
     * @param $merchantId
     *
     * @return array
     */
    public function countUnreadVendorQuote($merchantId)
    {
        $datas = $this->quoteRepository->countUnreadVendorMessage($merchantId);

        return $this->aggregateCount($datas, false);
    }

    /**
     * count total unread quote message for vendor view.
     *
     * @param User $buyer
     *
     * @return array
     */
    public function countTotalUnreadVendorQuote($merchantId)
    {
        $datas = $this->quoteRepository->countTotalVendorMessage($merchantId);
        return $datas[0]['nb'];
    }

    public function getQuoteVersion($quoteVersionId)
    {
        return $this->quoteVersionRepository->find($quoteVersionId);
    }

    /**
     * @param QuoteVersion $quoteVersion
     * @param $customFontDir
     * @return string
     * @throws Error
     * @throws MpdfException
     */
    public function generateQuoteVersionPdf(QuoteVersion $quoteVersion, $customFontDir)
    {
        $locale = $quoteVersion->getQuote()->getBuyer()->getLocale();
        $quoteVersionContent = $this->serializer->deserialize($quoteVersion->getQuoteDataJson(), Quote::class);
        return $this->generatePdf($quoteVersionContent, $locale, $customFontDir, $quoteVersion->getId());
    }

    /**
     * @param Quote $quote
     * @param $customFontDir
     * @return string
     * @throws Error
     * @throws MpdfException
     */
    public function generateQuotePdf(Quote $quote, $customFontDir)
    {
        $locale = $quote->getBuyer()->getLocale();
        $quoteVersion = $quote->getQuoteVersions()->count() + 1;
        return $this->generatePdf($quote, $locale, $customFontDir, $quoteVersion);
    }


    /**
     * @param Quote $quote for version $quote is detached (from json)
     * @param string $locale
     * @param string $customFontDir
     * @param int $quoteVersion
     * @return string
     * @throws MpdfException
     * @throws Error
     */
    private function generatePdf(Quote $quote, string $locale, string $customFontDir, int $quoteVersion = 1)
    {
        $priceGroupByVats = $this->getPriceGroupByVat($quote);

        $totalVat = array_sum(array_map(
            function (PriceByVat $priceByVat) {
                return $priceByVat->getTotalPrice();
            },
            $priceGroupByVats));

        // Generate contract
        $html = $this->templating->render(
            '@OpenFront/pdf/quote/quote_version.html.twig',
            [
                'quote' => $quote,
                'price_by_vats' => $priceGroupByVats,
                'price_with_vat' => $quote->getTotalPrice() + $totalVat,
                'locale' => $locale,
                'logo' => $this->logo,
            ]
        );

        $defaultConfig = (new ConfigVariables())->getDefaults();
        $fontDirs = $defaultConfig['fontDir'];

        $defaultFontConfig = (new FontVariables())->getDefaults();
        $fontData = $defaultFontConfig['fontdata'];


        $mpdf = new Mpdf([
            'fontDir' => array_merge($fontDirs, [
                $customFontDir,
            ]),
            'fontdata' => $fontData + [
                    'OpenSans' => [
                        'R' => 'OpenSans-Regular.ttf',
                        'I' => 'OpenSans-Italic.ttf',
                        'B' => 'OpenSans-Bold.ttf'
                    ]
                ],
            'default_font' => 'OpenSans',
            'margin_top' => 0,
            'margin_right' => 0,
            'margin_bottom' => 0,
            'margin_left' => 0,
            'margin_header' => 0,
            'margin_footer' => 1,
            'tempDir' => '/tmp',
            'mode' => 'utf-8',
            'format' => [210, 297],
            'setAutoTopMargin' => 'pad',
            'setAutoBottomMargin' => false,
            'autoMarginPadding' => 0,
            'bleedMargin' => 0,
            'crossMarkMargin' => 0,
            'cropMarkMargin' => 0,
            'nonPrintMargin' => 0,
            'margBuffer' => 0,
            'collapseBlockMargins' => false,
        ]);
        $mpdf->setTitle($this->translator->trans("quote.pdf.title", ['%num%' => $quote->getQuoteNumber(), '%version%' => $quoteVersion], self::TRANSLATION_BUNDLE));
        $mpdf->SetDisplayMode('fullpage');
        $mpdf->shrink_tables_to_fit = 1;


        /* TODO files */
        $mpdf->SetHTMLHeader($this->templating->render(
            '@OpenFront/pdf/quote/quote_header_block.html.twig'
        ));

        $mpdf->SetHTMLFooter($this->templating->render(
            '@OpenFront/pdf/quote/quote_footer_block.html.twig'
        ));

        $mpdf->WriteHTML($html);

        return $mpdf->output('', 'S');
    }

    private function getPriceGroupByVat(Quote $quote)
    {

        $itemGroupByVat = $this->groupQuoteItemByVatRate($quote);
        $priceByVats = [];
        foreach ($itemGroupByVat as $vatGroupName => $quoteItems) {

            $priceByVat = new PriceByVat();
            $vatPercent = $this->getVatPercent($vatGroupName, $quote->getUpdatedAt());

            $totalPriceWithoutVat = array_sum(array_map(
                function (QuoteItem $quoteItem) {
                    return $quoteItem->getTotalPrice();
                },
                $quoteItems));

            $priceByVat->setVatPercent($vatPercent->getRate());
            $priceByVat->setVatGroupName($vatGroupName);
            $priceByVat->setTotalPrice($this->getVatAmount($vatPercent, $totalPriceWithoutVat));
            $priceByVats[$vatGroupName] = $priceByVat;
        }

        return $priceByVats;


    }


    private function aggregateCount($datas, $forBuyer)
    {
        $aggregat = [];
        foreach ($datas as $data) {
            if ($forBuyer) {
                $metastatus = Quote::getMetaStatusBuyer($data['status']);
            } else {
                $metastatus = Quote::getMetaStatusVendor($data['status']);
            }
            if (!array_key_exists($metastatus, $aggregat)) {
                $aggregat[$metastatus] = 0;
            }
            $aggregat[$metastatus] = $aggregat[$metastatus] + intval($data['nb']);
        }

        return $aggregat;
    }

    private function doStatusTransition(Quote $quote, $wantedStatus): Quote
    {
        if ($quote->checkStatusTransition($wantedStatus)) {
            $quote->setStatus($wantedStatus);

            return $quote;
        }

        throw new \InvalidArgumentException(sprintf('invalid status transition %s->%s', $quote->getStatus(), $wantedStatus));
    }

    /**
     * @param Quote $quote
     * @param QuoteItemDomain $quoteItem
     *
     * @return boolean
     */
    private function containQuoteItem(Quote $quote, QuoteItemDomain $quoteItem)
    {
        return $quote->getQuoteItems()->exists(function ($key, $element) use ($quoteItem) {
            // @var QuoteItem $element
            return !is_null($quoteItem->getId()) && $quoteItem->getId() === $element->getId();
        });
    }

    /**
     * get a query builder for quoteEntity.
     *
     * @return QueryBuilder
     */
    private function getQueryBuilder()
    {
        $qb = $this->em->createQueryBuilder();
        $qb->select('q')
            ->from($this->entityName, 'q')
            ->leftJoin('q.buyer', 'b')
            ->leftJoin('q.vendor', 'v');

        return $qb;
    }

    /**
     * Get paginated list of quote, filtered with data.
     *
     * @param $numberPerPage
     * @param $page
     * @param $data
     *
     * @return \Knp\Component\Pager\Pagination\PaginationInterface
     */
    private function getCustomFilteredPaginator($data, int $page = 0, int $numberPerPage = 10)
    {
        $qb = $this->getQueryBuilder();
        $this->filterQueryBuilder->build($qb, $data);

        return $this->paginator->paginate(
            $qb->getQuery(),
            $page,
            $numberPerPage,
            ['defaultSortFieldName' => 'q.createdAt', 'defaultSortDirection' => 'desc']
        );
    }

    /**
     * @param Quote $quote
     * @return Quote
     * @throws ORMException
     * @throws OptimisticLockException
     */
    private function saveQuote(Quote $quote)
    {

        $quote->setUpdatedAt();
        $this->em->persist($quote);
        $this->em->flush();
        return $quote;
    }

    private function convertQuoteToData(Quote $quote, $local)
    {

        $subject = $this->messageQuoteService->generateSubject($quote, $local);
        $data = new QuoteData();
        $data
            ->setQuoteId($quote->getQuoteId())
            ->setSupplier($quote->getVendor()->getName())
            ->setQuoteNumber(is_null($quote->getQuoteNumber()) ? '' : $quote->getQuoteNumber())
            ->setCreationDate(DateUtil::printDate($quote->getCreatedAt(), $local))
            ->setSubject($subject)
            ->setValidationDate(DateUtil::printDate($quote->getValidationDate(), $local))
            ->setCancelDate(DateUtil::printDate($quote->getCancelDate(), $local))
            ->setUnread($quote->isBuyerUnread())
            ->setStatus($this->translator->trans('quote.status.' . $quote->getStatus(), [], self::TRANSLATION_BUNDLE));;

        return $data;
    }

    private function version(Quote $quote): Quote
    {
        $quoteVersion = new QuoteVersion();
        $quoteVersion->setPdfName($this->getPdfName($quote));
        $quote->addVersion($quoteVersion);
        $quote->setVersion($quote->getVersion() + 1);
        $quoteVersion->setQuoteDataJson($this->serializer->serialize($quote));
        $this->em->persist($quoteVersion);
        $this->em->flush();

        return $quote;
    }

    public function getPdfName(Quote $quote)
    {
        $name = join('_', [$quote->getQuoteNumber(), $quote->getTitle(), $quote->getVersion()]);

        return sprintf('%s.%s', $name, self::PDF_EXTENSION);
    }

    public function setQuoteUnreadBuyer(Quote $quote)
    {

        $quote->setBuyerUnread(true);
        $this->em->persist($quote);
        $this->em->flush();
    }


    /**
     * @param Quote $quote
     * @param Merchant $merchant
     *
     * @throws ForbiddenActionException
     */
    private function checkOwnerMerchant(Quote $quote, ?Merchant $merchant)
    {
        return;
        // TODO: see how to handle session at best
        if ($quote->getVendor()->getId() != $merchant->getId()) {
            throw new ForbiddenActionException();
        }
    }

    /**
     * @param Quote $quote
     * @param null|User $user
     *
     * @throws ForbiddenActionException
     */
    private function checkOwnerBuyer(Quote $quote, ?User $user)
    {
        if ($quote->getBuyer()->getId() != $user->getId()) {
            throw new ForbiddenActionException();
        }
    }

    private function generateOfferDescription(Quote $quote, $currency)
    {

        // Generate description
        $description = $this->templating->render("@OpenFront/template/quoteOfferDescription.twig.html",
            array(
                "quote" => $quote,
                "currency" => $currency
            )
        );
        return $description;

    }

    private function computeTotalPrice($quoteItems)
    {

        $total = 0;
        /** @var QuoteItem $quoteItem */
        foreach ($quoteItems as $quoteItem) {
            $total += $quoteItem->getUnitPrice() * (int)$quoteItem->getQuantity();
        }
        return $total;
    }

    private function generateOfferName(Quote $quote, $index)
    {
        $locale = $quote->getVendor()->getMarketplace()->getLocale();
        if ($index == self::FIRST_OFFER_INDEX) {
            return $this->translator->trans(
                'quote.generate.offer.title',
                ["%quoteTitle%" => $quote->getTitle(), "%quoteNumber%" => $quote->getQuoteNumber()],
                self::TRANSLATION_BUNDLE, $locale);
        } else {
            return $this->translator->trans(
                'quote.generate.offer.title_part',
                ["%quoteTitle%" => $quote->getTitle(), "%quoteNumber%" => $quote->getQuoteNumber(), "%index%" => $index],
                self::TRANSLATION_BUNDLE, $locale);
        }
    }

    public function sendMail(Quote $quote, $templateBuyer, $templateMerchant, $message = null)
    {
        /** @var Merchant $merchantEntity */
        $merchantEntity = $quote->getVendor();

        /** @var \AppBundle\Model\Merchant $merchant */
        $merchant = $this->merchantService->findMerchantById($merchantEntity->getIzbergId());

        $data = [];
        if (!is_null($message)) {
            $data['message'] = $message;
        }
        if (!is_null($quote->getTitle())) {
            $data['title'] = $quote->getTitle();
        }
        if (!is_null($quote->getQuoteNumber())) {
            $data['number'] = $quote->getQuoteNumber();
        }
        $data['offerName'] = $quote->getInitialOfferTitle();

        // buyer
        if ($templateBuyer) {
            $dataBuyer = [];
            $dataBuyer ['quoteNumber'] = $quote->getQuoteNumber();
            $dataBuyer ['supplierName'] = $merchant->getName();
            $dataBuyer ['firstName'] = $quote->getBuyer()->getFirstname();
            $dataBuyer ['lastName'] = $quote->getBuyer()->getLastname();
            $dataBuyer['url'] = $this->mailService->generateUrl('front.quote.detail', [
                '_locale' => $quote->getBuyer()->getLocale(),
                'id' => $quote->getQuoteId(),
                'marketplace' => $quote->getBuyer()->getMarketPlaceName()
            ]);

            $this->mailService->sendEmailMessage(
                $templateBuyer,
                $quote->getBuyer()->getLocale(),
                $quote->getBuyer()->getEmail(),
                array_merge($data, $dataBuyer)
            );
        }

        //vendor
        if ($templateMerchant) {
            $dataVendor = [];
            $dataVendor ['firstNameVendor'] = $merchant->getMainContactFirstName();
            $dataVendor ['lastNameVendor'] = $merchant->getMainContactLastName();
            $dataVendor ['firstNameBuyer'] = $quote->getBuyer()->getFirstname();
            $dataVendor ['lastNameBuyer'] = $quote->getBuyer()->getLastname();
            $dataVendor['url'] = $this->mailService->generateUrl('supplier_detail', [
                'id' => $quote->getQuoteId(),
                'marketplace' => $quote->getBuyer()->getMarketPlaceName()
            ]);
            $contactList = $this->merchantService->buildNotificationContacts($merchant, $templateMerchant);

            $this->mailService->sendEmailMessage(
                $templateMerchant,
                $merchant->getLanguage(),
                $contactList,
                array_merge($data, $dataVendor),
                null,
                null,
                []
            );
        }

    }

    /**
     * @param $number
     * @param $merchantId
     * @throws ApiException
     */
    private function checkNumber($number, $merchantId)
    {

        $datas = $this->quoteRepository->countQuoteNumberByVendor($number, $merchantId);
        $nb = $datas[0]['nb'];
        if ($nb > 0) {
            throw new ApiException(self::ERROR_CODE_NUMBER_UNICITY, 400);
        }
    }

    private function getVatPercent($vat_group_name, $date)
    {
        if ($vat_group_name) {
            $tva = $this->tvaRepository->getTaxRateFromTaxGroupAndDate($vat_group_name, new \DateTimeImmutable);
            return $tva;
        }
        return 0;
    }

    private function getVatAmount(TvaRate $vat, $price)
    {

        return $vat->getRate() * $price / 100;
    }

    private function generateSubject(Quote $quote, $locale)
    {
        return
            $this->translator->trans(
                'quote.detail.message_subject',
                ["#offerName#" => $quote->getInitialOfferTitle(), "#offerId#" => $quote->getInitialOfferReference()],
                self::TRANSLATION_BUNDLE, $locale);


    }

    private function groupQuoteItemByVatRate(Quote $quote)
    {

        $groupBy = [];
        /** @var QuoteItem $quoteItem */
        foreach ($quote->getQuoteItems() as $quoteItem) {
            if (!array_key_exists($quoteItem->getVat(), $groupBy)) {
                $groupBy[$quoteItem->getVat()] = [];
                $groupBy[$quoteItem->getVat()][] = $quoteItem;
            } else {
                $groupBy[$quoteItem->getVat()][] = $quoteItem;
            }
        }

        return $groupBy;

    }

    private function createOffers($productId, $merchantId, $quote, $originOffer,$language, $groupByVat, $currency)
    {
        $offers = [];
        $index = self::FIRST_OFFER_INDEX;
        $description = $this->generateOfferDescription($quote, $currency);
        foreach ($groupByVat as $vatGroupName => $quoteItems) {
            $offer = $this->createOffer($productId, $merchantId, $quote, $originOffer,$language, $description, $quoteItems, $index);

            $offers[] = $this->convertOffer($offer, $vatGroupName);
            $index++;
        }
        return $offers;
    }

    private function createOffer($productId, $merchantId, $quote, $originOffer,$language, $description, array $quoteItems, $index)
    {
        $customAttributes = $originOffer['attributes'];
        //as quoteItems as group by vat, all the list as same vat.
        $customAttributes[$this->izbergCustomAttributes->getVatRate()] = $quoteItems[0]->getVat();
        $colorCustomAttribute = $this->izbergCustomAttributes->getColor();

        if (array_key_exists($colorCustomAttribute, $customAttributes) && is_array($customAttributes[$colorCustomAttribute])) {
            $customAttributes[$colorCustomAttribute] = array_shift($customAttributes[$colorCustomAttribute]);
        }
        // Correction temporaire pour le Tuleap #46878
        // Contredit complètement la ligne précédente
        if (array_key_exists($colorCustomAttribute, $customAttributes) && !is_array($customAttributes[$colorCustomAttribute])) {
            $customAttributes[$colorCustomAttribute] = array($customAttributes[$colorCustomAttribute]);
        }
        $secondary = 'No';
        if ($index != self::FIRST_OFFER_INDEX) {
            $secondary = 'Yes';
        }

        $customAttributes[$this->izbergCustomAttributes->getQuoteSecondaryOffer()] = $secondary;
        $offerName = $this->generateOfferName($quote, $index);
        // create offer in izberg
        $productOffer = $this->offerApi->createProductOffer(
            $productId,
            $merchantId,
            $offerName,
            $this->computeTotalPrice($quoteItems),
            $description,
            $quote->getInitialOfferUrl(),
            $customAttributes,
            $language
        );

        //assign an image
        $imageId = $this->offerApi->getImageId($originOffer);
        if ($imageId) {
            $this->offerApi->assignImage($productOffer->getId(), $imageId);
        }

        // activate offer in izberg
        $this->offerApi->activateProduct($productOffer->getId());

        return $productOffer;
    }

    private function convertOffer(ProductOffer $productOffer, $vatGroupName)
    {

        // convert product offer Izberg model to elasticSearch offer model
        $offer = new Offer();
        $offer->setId($productOffer->getId());
        $offer->setCurrency($productOffer->getCurrency());
        $offer->setPrice($productOffer->getPrice());
        $merchant = new \AppBundle\Model\Merchant();
        $merchant->setId($productOffer->getMerchant()->getId());
        $offer->setMerchant($merchant);
        $offer->setTaxRate($vatGroupName);
        return $offer;
    }

    private function getCurrentRequestLocale(): string
    {
        return $this->requestStack->getCurrentRequest()->getLocale();
    }
}
