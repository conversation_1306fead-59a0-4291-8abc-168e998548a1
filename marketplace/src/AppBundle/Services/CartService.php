<?php

namespace AppBundle\Services;

use AppBundle\Entity\BuyerAddress;
use AppBundle\Entity\Cart as CurrencyCart;
use AppBundle\Entity\MetaCart;
use AppBundle\Entity\User;
use AppBundle\Repository\CartRepository;
use Open\IzbergBundle\Model\CartItem;
use AppBundle\Model\Cart\CartItemShippingGroup;
use Open\IzbergBundle\Api\CartApi;
use AppBundle\Model\Offer;
use AppBundle\Model\Cart\Cart;
use AppBundle\Factory\CartFactory;
use AppBundle\Repository\MetaCartRepository;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;

class CartService
{
    private CartFactory $cartFactory;
    private MetaCartRepository $metaCartRepository;
    private CurrencyCartService $currencyCartService;
    private CartApi $cartApi;
    private OfferService $offerService;
    private BuyerService $buyerService;
    private CartAddressService $cartAddressService;
    private MetaCartMerchantService $metaCartMerchantService;
    private AddressService $addressService;
    private CartRepository $cartRepository;
    protected LogService $logService;
    private int $cartLifetimeDays;

    public function __construct(
        CartFactory $cartFactory,
        MetaCartRepository $metaCartRepository,
        CurrencyCartService $currencyCartService,
        CartAPI $cartApi,
        OfferService $offerService,
        BuyerService $buyerService,
        CartAddressService $cartAddressService,
        MetaCartMerchantService $metaCartMerchantService,
        AddressService $addressService,
        CartRepository $cartRepository,
        LogService $logService,
        int $cartLifetimeDays
    ) {
        $this->cartFactory = $cartFactory;
        $this->metaCartRepository = $metaCartRepository;
        $this->currencyCartService = $currencyCartService;
        $this->cartApi = $cartApi;
        $this->offerService = $offerService;
        $this->buyerService = $buyerService;
        $this->cartAddressService = $cartAddressService;
        $this->metaCartMerchantService = $metaCartMerchantService;
        $this->addressService = $addressService;
        $this->cartRepository = $cartRepository;
        $this->logService = $logService;
        $this->cartLifetimeDays = $cartLifetimeDays;
    }

    /**
     * @param User $buyer
     * @return MetaCart|null
     */
    public function findCurrentBuyerMetaCart(User $buyer): ?MetaCart
    {
        return $this->metaCartRepository->findOneBy(['buyer' => $buyer, 'isCurrent' => true]);
    }

    public function loadCurrentBuyerCart(User $buyer): Cart
    {
        $metaCart = $this->metaCartRepository->findOneBy(['buyer' => $buyer, 'isCurrent' => true]);

        if (!$metaCart) {

            $metaCart = (new MetaCart())
                ->setBuyer($buyer)
                ->setCountryOfDelivery($buyer->getMarketPlace()->getTotalDiscriminator())
                ->setIsCurrent(true)
                ->setCurrency('EUR')
                ->setItemsCount(0)
                ->setStatus(MetaCart::STATUS_CREATED)

                ->setBuyerBillingAddress($this->buyerService->getDefaultBuyerBillingAddress($buyer));
            if($buyer->hasUsedCartBefore()){
                $lastUsedAddress = $this->addressService->findLastUsedShippingAddress($buyer);
                if($lastUsedAddress instanceof BuyerAddress){
                    $metaCart->setBuyerShippingAddress($lastUsedAddress);
                }

            }
            $metaCart = $this->metaCartRepository->save($metaCart);
        }
        if(!$buyer->hasUsedCartBefore()){
            $metaCart->setBuyerShippingAddress(null);
            $metaCart = $this->metaCartRepository->save($metaCart);
        }else if($metaCart->getBuyerShippingAddress() === null) {
            $metaCart->setBuyerShippingAddress($this->buyerService->getDefaultBuyerShippingAddress($buyer));
            $metaCart = $this->metaCartRepository->save($metaCart);
        }

        if($metaCart->getBuyerBillingAddress() === null) {
            $metaCart->setBuyerBillingAddress($this->buyerService->getDefaultBuyerBillingAddress($buyer));
            $metaCart = $this->metaCartRepository->save($metaCart);
        }

        $this->verifyCurrentUsermetaCart($buyer, $metaCart);

        return $this->cartFactory->buildCartFromMetaCart($metaCart);
    }

    public function verifyCurrentUsermetaCart(User $buyer, MetaCart $metaCart)
    {
        foreach ($metaCart->getCarts() as $cart) {
            /*  @var \AppBundle\Entity\Cart $cart */
            try{
                $this->cartApi->fetchIzbergCart($cart->getId());
            } catch (\Exception $e){
                $this->logService->error(
                    sprintf(
                        'SECURITY ALERT - Database cart Id %s unauthorized on Izberg for buyer IzbergUserId %s',
                        $cart->getId(),
                        $buyer->getIzbergUserId()
                    ),
                    EventNameEnum::IZBERG_API
                );
                /*  @var \Open\IzbergBundle\Model\Cart $currentCart */
                $currentCart = $this->cartApi->getCart(null, false, $buyer->getCurrency()??"EUR");
                if ($currentCart) {
                    if ($cart->getId() !== $currentCart->getId()) {
                        if ($this->cartRepository->find($currentCart->getId())) {
                            $currency = $currentCart->getCurrency();
                            $izbergCart = $this->cartApi->createCart($currency);
                            $currencyCart = new CurrencyCart();
                            $currencyCart->setCartId($izbergCart->getId());
                            $currencyCart->setStatus(CurrencyCart::STATUS_CREATE);
                            $currencyCart->setCreatedUser($metaCart->getBuyer());
                            $currencyCart->setCurrentUser($metaCart->getBuyer());
                            $currencyCart->setCartCurrency($currency);
                            $currencyCart->setMetaCart($metaCart);
                            $currencyCart->setItemsCount(0);
                            $this->cartRepository->save($currencyCart);
                            $metaCart->removeCart($cart);
                            $metaCart->addCart($currencyCart);
                        } else {
                            $metaCart->removeCart($cart);
                            $cart->setId($currentCart->getId());
                            $cart->setItemsCount($currentCart->getItemsCount());
                            $this->cartRepository->save($cart);
                            $metaCart->addCart($cart);
                        }
                    }
                } else {
                    $metaCart->removeCart($cart);
                }
            }
        }
    }

    public function loadBuyerCart(User $buyer, int $cartId): ?Cart
    {
        $metaCart = $this->metaCartRepository->findBuyerMetaCart($buyer, $cartId);
        if(!$metaCart) {
            return null;
        }

        return $this->cartFactory->buildCartFromMetaCart($metaCart);
    }

    public function buildCartFromMetaCart (MetaCart $metaCart) : Cart{
        return $this->cartFactory->buildCartFromMetaCart($metaCart);
    }

    public function loadCartToValidate(User $manager, int $cartId): ?Cart
    {
        $metaCart = $this->metaCartRepository->findOneBy(['buyerManager' => $this->getManagerFromManagerOrDelegate($manager), 'id' => $cartId]);
        if (!$metaCart instanceof MetaCart) {
            return null;
        }

        $buyer = $metaCart->getBuyer();
        $this->cartApi->startUserConnection($buyer);
        $cart = $this->cartFactory->buildCartFromMetaCart($metaCart);
        $this->cartApi->endUserConnection();
        return $cart;
    }

    public function loadBuyerCarts(User $buyer): array
    {
        return $this->metaCartRepository->findBy(['buyer' => $buyer]);
    }

    public function loadManagerPendingCarts(User $manager, int $page = null, int $pageSize = null): array
    {
        $offset = null;
        $limit = null;

        if($page > 0 && $pageSize > 0) {
            $offset = ($page - 1) * $pageSize;
            $limit = $pageSize;
        }

        return $this->metaCartRepository->findBy(
            ['buyerManager' => $this->getManagerFromManagerOrDelegate($manager), 'status' => MetaCart::STATUS_ASSIGNED],
            ['orderCreatedAt' => 'DESC'],
            $limit,
            $offset
        );
    }

    public function loadManagerAcceptedCarts(User $manager, int $page = null, int $pageSize = null): array
    {
        $offset = null;
        $limit = null;

        if($page > 0 && $pageSize > 0) {
            $offset = ($page - 1) * $pageSize;
            $limit = $pageSize;
        }

        return $this->metaCartRepository->findBy(
            ['buyerManager' => $this->getManagerFromManagerOrDelegate($manager), 'status' => MetaCart::STATUS_ORDERED, 'autoValidation' => false],
            ['validatedAt' => 'DESC'],
            $limit,
            $offset
        );
    }

    public function loadManagerRejectedCarts(User $manager, int $page = null, int $pageSize = null): array
    {
        $offset = null;
        $limit = null;

        if($page > 0 && $pageSize > 0) {
            $offset = ($page - 1) * $pageSize;
            $limit = $pageSize;
        }

        return $this->metaCartRepository->findBy(
            ['buyerManager' => $this->getManagerFromManagerOrDelegate($manager), 'status' => MetaCart::STATUS_REJECTED],
            ['validatedAt' => 'DESC'],
            $limit,
            $offset
        );
    }

    public function emptyCart(User $buyer, Cart $cart): bool
    {
        /** @var MetaCart $metaCart */
        $metaCart = $this->metaCartRepository->findOneBy([
            'id' => $cart->getId(),
            'buyer' => $buyer,
            'status' => MetaCart::STATUS_CREATED,
        ]);

        if (!$metaCart) {
            return false;
        }

        $metaCart->setStatus(MetaCart::STATUS_EMPTIED);

        // remove the isCurrent flag
        $metaCart->setIsCurrent(false);

        $this->metaCartRepository->save($metaCart);

        return true;
    }

    public function addOfferToCart(Offer $offer, Cart $cart, int $quantity): Cart
    {
        $metaCart = $this->metaCartRepository->findMetaCart($cart->getId());
        $this->addOfferToIzbergCart($offer, $metaCart, $quantity);

        return $cart;
    }

    /**
     * @param Offer $offer
     * @param User $buyer
     * @param array $extraInfo
     * @param int $quantity
     *
     * @return int|false returns number of items in metacart or false is the adding to cart failed
     */
    public function addOfferToBuyerCurrentCart(Offer $offer, User $buyer, int $quantity, array $extraInfo=[])
    {
        $metaCart = $this->metaCartRepository->findBuyerCurrentMetaCart($buyer);
        $numberOfItems = $this->addOfferToIzbergCart($offer, $metaCart, $quantity,false,$extraInfo);

        return $numberOfItems;
    }

    /**
     * @param User $buyer
     * @param int $cartId
     * @param bool $enable
     * @return bool
     */
    public function enableGift(User $buyer, int $cartId, bool $enable = true): bool
    {
        /** @var MetaCart $metaCart */
        $metaCart = $this->metaCartRepository->findOneBy([
            'id' => $cartId,
            'buyer' => $buyer,
            'status' => MetaCart::STATUS_CREATED,
        ]);

        if (!$metaCart) {
            return false;
        }

        $metaCart->setGift($enable);
        $this->metaCartRepository->save($metaCart);

        return true;
    }

    /**
     * @param User $buyer
     * @param int $cartId
     * @param bool $enable
     * @return bool
     */
    public function enableRisk(User $buyer, int $cartId, bool $enable = true): bool
    {
        /** @var MetaCart $metaCart */
        $metaCart = $this->metaCartRepository->findOneBy([
            'id' => $cartId,
            'buyer' => $buyer,
            'status' => MetaCart::STATUS_CREATED,
        ]);

        if (!$metaCart) {
            return false;
        }

        $metaCart->setRisk($enable);
        $this->metaCartRepository->save($metaCart);

        return true;
    }

    /**
     * @param User $buyer
     * @param int $cartId
     * @param int $cartItemId
     * @param bool $enable
     * @return bool
     */
    public function enableCartItemGift(User $buyer, int $cartId, int $cartItemId, bool $enable = true): bool
    {
        /** @var MetaCart $metaCart */
        $metaCart = $this->metaCartRepository->findOneBy([
            'id' => $cartId,
            'buyer' => $buyer,
            'status' => MetaCart::STATUS_CREATED,
        ]);

        if (!$metaCart) {
            return false;
        }

        // update cartItemExtraInfo
        if ($enable) {
            $this->cartApi->updateItemExtraInfo($cartItemId, 'gift', 'yes');
        } else {
            $this->cartApi->removeItemExtraInfo($cartItemId, 'gift');
        }

        return true;
    }

    /**
     * @param User $buyer
     * @param int $cartId
     * @param int $cartItemId
     * @param bool $enable
     * @return bool
     */
    public function enableCartItemRisk(User $buyer, int $cartId, int $cartItemId, bool $enable = true): bool
    {
        /** @var MetaCart $metaCart */
        $metaCart = $this->metaCartRepository->findOneBy([
            'id' => $cartId,
            'buyer' => $buyer,
            'status' => MetaCart::STATUS_CREATED,
        ]);

        if (!$metaCart) {
            return false;
        }

        // update cartItemExtraInfo
        if ($enable) {
            $this->cartApi->updateItemExtraInfo($cartItemId, 'risk', 'yes');
        } else {
            $this->cartApi->removeItemExtraInfo($cartItemId, 'risk');
        }

        return true;
    }

    /**
     * @param Offer $offer
     * @param User $buyer
     * @param int $metaCartId
     * @param int $quantity
     *
     * @return int|false returns number of items in metacart or false is the adding to cart failed
     */
    public function addOfferToBuyerCurrentCartWithExtra(Offer $offer, User $buyer, int $quantity, $extraInfo=[])
    {
        $metaCart = $this->metaCartRepository->findBuyerCurrentMetaCart($buyer);
        $numberOfItems = $this->addOfferToIzbergCart($offer, $metaCart, $quantity, $extraInfo);

        return $numberOfItems;
    }

    public function removeItemFromCart(User $buyer, int $cartId, int $itemId)
    {
        /** @var MetaCart $metaCart */
        $metaCart = $this->metaCartRepository->findOneBy([
            'id' => $cartId,
            'buyer' => $buyer,
            'status' => MetaCart::STATUS_CREATED,
        ]);

        if (!$metaCart) {
            return false;
        }

        $this->removeItemFromIzbergCart($itemId);
        $this->metaCartMerchantService->removeItem($metaCart, $itemId);

        return true;
    }

    public function updateItemFromCart(MetaCart $metaCart, int $itemId, int $quantity)
    {
        $cartItem = $this->cartApi->getItem($itemId);
        $offer = $this->offerService->findOffer($cartItem->getOffer()->getId(), 'fr', $metaCart->getBuyer()->getMarketPlace());

        // addOfferToIzbergCart will overwrite the item if it exists
        $this->addOfferToIzbergCart(
            $offer,
            $metaCart,
            $quantity,
            true
        );
    }

    /**
     * @param Offer $offer
     * @param MetaCart $metaCart
     * @param int $quantity
     * @param bool $overwriteQuantity
     * @param array $extraInfo
     * @return int returns number of items
     */
    private function addOfferToIzbergCart(Offer $offer, MetaCart $metaCart, int $quantity, bool $overwriteQuantity = false, array $extraInfo=[]): int
    {
        $currency = $offer->getCurrency();
        $currencyCart = $this->currencyCartService->findCurrencyCart($metaCart, $currency);
        $this->currencyCartService->addOfferToCurrencyCart($currencyCart, $offer, $quantity, $overwriteQuantity,$extraInfo);



        // find all currency carts for given metacart
        // compute number of items
        // save total number of items in metacart
        $metaCart = $this->metaCartRepository->find($metaCart->getId());
        $totalNumberOfItems = array_reduce(
            $metaCart->getCarts()->toArray(),
            function($totalNumberOfItems, \AppBundle\Entity\Cart $cart) {
                $totalNumberOfItems = $totalNumberOfItems + $cart->getItemsCount();

                return $totalNumberOfItems;
            },
            0
        );
        
        $metaCart->setItemsCount($totalNumberOfItems);
        $this->metaCartRepository->save($metaCart);

        return $metaCart->getItemsCount();
    }

    private function removeItemFromIzbergCart($itemId): void {
        $this->cartApi->removeItem($itemId);
    }

    public function updateBuyerShippingAddress(User $buyer, BuyerAddress $buyerAddress): bool {
        /** @var MetaCart $metaCart */
        $metaCart = $this->metaCartRepository->findOneBy([
            'buyer' => $buyer,
            'status' => MetaCart::STATUS_CREATED,
            'isCurrent' => true
        ]);

        if (!$metaCart) {
            return false;
        }

        $metaCart->setBuyerShippingAddress($buyerAddress);
        $this->cartAddressService->configureShipping($metaCart);

        $this->metaCartRepository->save($metaCart);
        return true;
    }

    public function changeCartShippingModeForMerchantId(Cart $cart, string $shippingMode, int $merchantId): bool
    {
        $merchant = $cart->findMerchant($merchantId);
        $shippingOptions = [];

        if ($shippingMode === 'standard') {
            $shippingOptions = $merchant->getStandardShippingOptions();
        }

        if($shippingMode === 'fast') {
            $shippingOptions = $merchant->getFastShippingOptions();
        }

        usort(
            $shippingOptions,
            static function(CartItemShippingGroup $shippingOptionA, CartItemShippingGroup $shippingOptionB) {
                return ($shippingOptionA->getOrder() < $shippingOptionB->getOrder()) ? -1 : 1;
            }
        );

        $shippingOptionIds = array_map(
            static function(CartItemShippingGroup $shippingOption) {
                return $shippingOption->getShippingId();
            },
            $shippingOptions
        );

        try {
            $this->cartApi->changeShipping($merchant->getCartId(), $shippingOptionIds);
        } catch(\Exception $e) {
            return false;
        }

        return true;
    }

    public function countPendingCarts(User $user): int {
        $manager = $user;       // $user as manager
        $managerDelegation = $this->buyerService->getManagerWhoIAmDelegate($user);

        $managerOrDelegate = array_merge(
            [ $manager ],
            $managerDelegation
        );

        return $this->metaCartRepository->count(
            ['buyerManager' => $managerOrDelegate, 'status' => MetaCart::STATUS_ASSIGNED]
        );
    }

    private function getManagerFromManagerOrDelegate(User $managerOrDelegate): array {
        // $managerOrDelegate is manager, add $managerOrDelegate
        // Get managers who $managerOrDelegate have delegation

        $managerDelegation = $this->buyerService->getManagerWhoIAmDelegate($managerOrDelegate);
        return array_merge(
            [ $managerOrDelegate ],
            $managerDelegation
        );
    }
    public function cleanOldCart(User $user)
    {return null;
        $currentMetaCart = $this->findCurrentBuyerMetaCart($user);
        if ($currentMetaCart) {
            $this->cartApi->startUserConnection($user);
            $cart = $this->currencyCartService->findCurrencyCart($currentMetaCart, $user->getCurrency()??"EUR") ;
            if ($cart->getUpdatedAt()->diff(new \DateTimeImmutable())->days >= $this->cartLifetimeDays) {
                $cartItems = $this->cartApi->getCartItems($cart->getCartId());
                $quote = false;
                foreach ($cartItems as $cartItem) {
                    /** @var CartItem $cartItem */
                    if ($cartItem->getExtraInfo()->getQuote()) {
                        $quote = true;
                        break;
                    }
                }
                if (!$quote) {
                    $currentMetaCart->setStatus(MetaCart::STATUS_EMPTIED);
                    // remove the isCurrent flag
                    $currentMetaCart->setIsCurrent(false);
                    $this->metaCartRepository->save($currentMetaCart);
                }
            }
        }
    }
}
