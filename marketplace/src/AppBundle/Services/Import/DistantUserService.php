<?php

namespace AppBundle\Services\Import;

use AppBundle\Entity\DistantUser;
use AppBundle\Repository\DistantUserRepository;
use AppBundle\Util\Str;
use Doctrine\ORM\EntityManagerInterface;
use JsonMachine\Exception\PathNotFoundException;
use JsonMachine\Items;
use JsonMachine\JsonDecoder\PassThruDecoder;
use Open\IdealBundle\IdealClient;
use Open\IdealBundle\Model\Entry;
use Open\IdealBundle\Model\MainRecord;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Serializer\SerializerInterface;

class DistantUserService
{
    private LogService $logService;
    private IdealClient $idealClient;
    private EntityManagerInterface $em;
    private DistantUserRepository $distantUserRepository;
    private SerializerInterface $serializer;

    public function __construct(IdealClient $idealClient, EntityManagerInterface $em, DistantUserRepository $distantUserRepository, SerializerInterface $serializer, LogService $logService)
    {
        $this->idealClient = $idealClient;
        $this->logService = $logService;
        $this->em = $em;
        $this->serializer = $serializer;
        $this->distantUserRepository = $distantUserRepository;
    }

    public function disableSqlLog()
    {
        $connection = $this->em->getConnection();
        $connection->getConfiguration()->setSQLLogger(null);
    }

    private function flush()
    {
        // flush and clear
        $this->em->flush();
        $this->em->clear();

        // call garbage collector to force clearing doctrine objects
        gc_collect_cycles();
    }

    public function retrieveUsers(OutputInterface $output): int
    {
        $this->disableSqlLog();
        gc_enable();

        $this->clearDistantUsers();

        $index = 1;
        $distantUsers = $this->retrieveDistantUsers($output);

        foreach ($distantUsers as $distantUser) {

            $this->em->persist($distantUser);
            if ($index % 100 == 0) {
                $this->flush();
            }
            $index++;
        }
        $this->flush();
        return $index;
    }

    private function clearDistantUsers(): void
    {
        $this->distantUserRepository->clearAll();
    }


    private function retrieveDistantUsers(OutputInterface $output): \Generator
    {

        $continuationToken = null;
        do {
            $output->write(".");
            $stream = $this->idealClient->userApi->retrieveUsers($continuationToken);

            $chunkGenerator = $this->idealClient->userApi->httpClientChunks($stream);
            try {
                $users = Items::fromIterable($chunkGenerator, ['pointer' => ['/Result', '/ContinuationToken'], 'decoder' => new PassThruDecoder()]);

                foreach ($users as $userStr) {
                    if ($users->getCurrentJsonPointer() == "/ContinuationToken") {
                        $continuationToken = json_decode($userStr);
                    } else {
                        try {
                            $entry = $this->serializer->deserialize($userStr, Entry::class, 'json');
                            yield $this->mapUser($entry);
                        } catch (\Throwable $t) {
                            $output->write('Error trying to deserialize/map user: ' . $t->__toString());
                            $this->logService->error('Error trying to deserialize/map user: ' . $t->__toString(), EventNameEnum::COMMAND_RETRIEVE_BUYER);
                        }
                    }
                }
            } catch (PathNotFoundException $ex) { // no attribute 'ContinuationToken' in last json response !
                $continuationToken = null;
            }
        } while ($continuationToken !== null);

    }

    private function mapUser(Entry $entry): DistantUser
    {
        $record = $entry->getMainRecord();
        $manager = $record->getManager();
        $preferredLanguage = $record->getPreferredLanguage();
        $organization = $record->getOrganization();

        return (new DistantUser())
            ->setIgg($entry->getMainEmployeeId()) //used
            ->setFirstName($entry->getMainPhoneticFirstName()) //used
            ->setLastName(utf8_encode($entry->getMainLastName())) //used
            ->setPersonalTitle($record->getPersonalTitle()?->getNameFr()) //used
            ->setPreferredLanguage($preferredLanguage !== null ? $preferredLanguage->getDisplayName() : "EN") //used
            ->setEmail($record->getEmail()) //used
            ->setTelephone($record->getPhone()) //used
            ->setOrganization($organization !== null ? utf8_encode($record->getOrganization()?->getDisplayEntity()) : null) //used
            ->setManagerIgg($manager !== null ? $record->getManager()?->getMainEmployeeId() : null) //used
            ->setCostCenter($this->getCostCenter($record)) //used
            ->setSiteName($record->getSiteName()) //used
            ->setSiteStreet($record->getSiteStreet()) //used
            ->setSiteStreet2($record->getSiteStreet2()) //used
            ->setSiteZipcode($record->getSiteZipCode()) //used
            ->setSiteLocality($record->getSiteLocality()) //used
            ->setSiteCountry($record->getSiteCountry()) //used
            ->setSiteCountryCode($record->getCountryCode())
            ->setInvoicingEntityName($record->getInvoiceEntityName()) //used
            ->setInvoicingEntityShortName(null)
            ->setCategory(null)
            ->setInvoicingEntityStreet(null)
            ->setInvoicingEntityStreet2(null)
            ->setInvoicingEntityZipcode(null)
            ->setInvoicingEntityLocality(null)
            ->setInvoicingEntityCountry($record->getInvoicingEntityCountry()) //used
            ->setInvoicingEntityCountryCode($record->getInvoicingEntityCountryCode()) //used
            ->setStartDate($record->getUserRecordWhenIn()) //used
            ->setEndDate($record->getUserRecordWhenOut()) //used
            ->setUpdateDate(null)
            ->setContrat($record->getContrat());

    }

    private function getCostCenter(MainRecord $record): ?string
    {
        if (!Str::isNullOrEmpty($record->getCostCenterText())) {
            return $record->getCostCenterText();
        }
        if ($record->getCostCenter() != null) {
            return $record->getCostCenter()->getName();
        }
        return "";
    }

}