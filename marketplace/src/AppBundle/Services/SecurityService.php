<?php

namespace AppBundle\Services;

use AppBundle\Entity\Company;
use AppBundle\Entity\User;
use AppBundle\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Core\Authentication\Token\AnonymousToken;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Component\Security\Core\Encoder\UserPasswordEncoderInterface;
use Symfony\Component\Security\Core\Role\Role;
use Symfony\Component\Security\Core\Role\RoleHierarchyInterface;

class SecurityService
{
    public const ROLE_BUYER = "ROLE_BUYER";
    public const ROLE_OPERATOR = 'ROLE_OPERATOR';
    public const ROLE_SUPER_ADMIN = 'ROLE_SUPER_ADMIN';
    public const ANONYMOUS_USER = 'anonymous';

    private EntityManagerInterface $em;
    private AuthorizationCheckerInterface $authChecker;
    private TokenStorageInterface $tokenStorage;
    private RoleHierarchyInterface $roleHierarchy;
    private UserRepository $userRepository;
    private UserPasswordEncoderInterface $encoder;

    public function __construct(EntityManagerInterface                 $em,
                                AuthorizationCheckerInterface $authChecker,
                                TokenStorageInterface         $tokenStorage,
                                RoleHierarchyInterface        $roleHierarchy,
                                UserRepository                $userRepository,
                                UserPasswordEncoderInterface  $encoder
    ) {
        $this->em = $em;
        $this->authChecker = $authChecker;
        $this->tokenStorage = $tokenStorage;
        $this->roleHierarchy = $roleHierarchy;
        $this->userRepository = $userRepository;
        $this->encoder = $encoder;
    }

    public function getCurrentUserName()
    {
        /**
         * @var TokenInterface $token
         */
        $token = $this->tokenStorage->getToken();

        if ($token == null) {
            return self::ANONYMOUS_USER;
        }
        if ($token instanceof AnonymousToken) {
            return self::ANONYMOUS_USER;
        }

        return $token->getUsername();
    }

    /***
     * @return bool
     */
    public function isAnonymous()
    {
        return ($this->getCurrentUserName() === self::ANONYMOUS_USER);
    }

    /**
     * @param User $user
     *
     * @return bool
     */
    public function isAdminCompany($user)
    {
        return $this->isGranted("ROLE_BUYER", $user);
    }

    /**
     * @param User $user
     *
     * @return bool
     */
    public function isAdmin($user)
    {
        return $this->isGranted("ROLE_OPERATOR", $user) || $this->isGranted("ROLE_SUPER_ADMIN", $user);
    }


    public function isBuyer($user)
    {

        //as role are inherited only test test basic one
        return $this->isGranted(self::ROLE_BUYER, $user);

    }

    public function isAdminPiggyBack()
    {
        try {
            return $this->authChecker->isGranted('ROLE_PREVIOUS_ADMIN');
        } catch (\Exception $e) {
            return false;
        }
    }


    /**
     * isGranted
     *
     * @param string        $role
     * @param User $user
     *
     * @return bool
     */
    public function isGranted($role, $user)
    {
        //deal with anonymous user
        if ($user === null || is_string($user)) {
            return false;
        }

        foreach ($user->getRoles() as $userRole) {
            if (in_array($role, $this->roleHierarchy->getReachableRoleNames(array($userRole)))) {
                return true;
            }
        }

        return false;
    }

    /**
     * @param      $role
     * @param User $user
     *
     * @return bool
     */
    public function isGrantedNotReachable($role, $user)
    {
        //deal with anonymous user
        if ($user === null || is_string($user)) {
            return false;
        }

        foreach ($user->getRoles() as $userRole) {
            if ($role === $userRole) {
                return true;
            }
        }

        return false;
    }

    /**
     *  get the company admin users
     *
     * @param Company $company
     *
     * @return array users
     */
    public function getCompanyAdminUsers($company)
    {
        $adminUsers = [];
        foreach ($company->getUsers() as $user) {
            if ($this->isAdminCompany($user)) {
                $adminUsers[] = $user;
            }
        }
        return $adminUsers;
    }


    /***
     * @return mixed
     */
    public function getOperators()
    {
        return $this->userRepository->findOperator();
    }

    /***
     * @return mixed
     */
    public function getAdmins()
    {
        return $this->userRepository->findAdmin();
    }

    /***
     * @return array
     */
    public function getOperatorMails()
    {
        $operators = $this->getOperators();
        $mails = [];
        /**
         * @var User $operator
         */
        foreach ($operators as $operator) {
            if ($operator->isEnabled()) {
                $mails[] = $operator->getEmail();
            }
        }
        return $mails;
    }


    /***
     * @return mixed
     */
    public function getUser()
    {
        return $this->tokenStorage->getToken()->getUser();
    }

    /***
     *
     * Build the country for Import purpose (we need to know in which country the company is located)
     *
     * @return \AppBundle\Entity\Country|null
     */
    public function getFiscalCountry(?User $user = null)
    {
        if (!$user) {
            /** @var user $user */
            $user = $this->getUser();
        }

        if (!$user || is_string($user)) {
            return null;
        }

        return $user->getCountryOfDelivery();
    }

    public function checkPasswordTokenValidity(string $token): bool
    {
        $timestamp = (int)substr($token, strrpos($token, '__') + 2);
        $timestamp = strtotime('+10minutes', $timestamp);

        $date = new \DateTime();
        $currentTimestamp = $date->getTimestamp();

        if ($currentTimestamp > $timestamp) {
            return false;
        }

        return true;
    }

    public function reinitUserPassword(User $user, string $password)
    {
        $user->setPassword($this->encoder->encodePassword($user, $password));
        $user->setResetPasswordToken(null);
        $this->userRepository->save($user);
    }
}
