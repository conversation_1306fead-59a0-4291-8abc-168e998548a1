<?php

namespace AppBundle\Services;

use AppBundle\Entity\User;
use AppBundle\Model\Favori\Favori;
use AppBundle\Repository\FavoriRepository;
use AppBundle\Entity\Favori AS FavoriEntity;

class FavoriService
{
    private FavoriRepository $favoriRepository;
    private OfferService $offerService;

    public function __construct(
        FavoriRepository $favoriRepository,
        OfferService $offerService
    ) {
        $this->favoriRepository = $favoriRepository;
        $this->offerService = $offerService;
    }

    public function loadCurrentBuyerFavoris(User $buyer, $locale): array
    {
        $favoris = $this->favoriRepository->findByBuyer($buyer);
        $favorisList = [];
        if ($favoris) {
            $offerService = $this->offerService;

            $that = $this;
            $favorisList = array_filter(
                array_map(static function (FavoriEntity $favoriEntity) use ($buyer, $offerService, $locale, $that) {

                    $esOffer = $offerService->findOfferForCountryOfDelivery($favoriEntity->getOfferId(), $buyer->getCountryOfDelivery(), $locale, $buyer->getMarketPlace());
                    if (!$esOffer) {
                        $that->removeItems($buyer, [$favoriEntity->getOfferId()]);
                        return null;
                    }
                    if ($esOffer->getStatus() !== "active" ) {
                        return null;
                    }
                    $favori = new Favori();
                    $favori->setId($favoriEntity->getId());
                    $favori->setCreator($buyer);
                    $favori->setOffer($esOffer);
                    return $favori;

                }, $favoris)
            );
        }
        return $favorisList;
    }

    public function removeItems(User $buyer, array $offerIds) :void
    {
        $this->favoriRepository->removeForBuyer($buyer,$offerIds);
    }

    public function removeItemsById(array $ids) :void
    {
        $this->favoriRepository->delete($ids);
    }

    /**
     *
     * @param User $buyer
     * @param int $offerId
     *
     * @return Favori|null
     */
    public function toggleOffer(User $buyer, int $offerId, $locale): ?Favori
    {
        $favoriExists = $this->favoriRepository->findBuyerOffer($buyer, $offerId);
        if ($favoriExists) {
            $this->favoriRepository->remove($favoriExists);
            return null;
        } else {
            $esOffer = $this->offerService->findOfferForCountryOfDelivery($offerId, $buyer->getCountryOfDelivery(), $locale, $buyer->getMarketPlace());
            if ($esOffer) {
                if ($esOffer->getStatus() !== "active") {
                    return null;
                }
                $favoriEntity = $this->favoriRepository->addForBuyer($buyer, $offerId);
                $favori = new Favori();
                $favori->setId($favoriEntity->getId());
                $favori->setCreator($buyer);
                $favori->setOffer($esOffer);
                return $favori;
            } else {
                return null;
            }
        }
    }

}
