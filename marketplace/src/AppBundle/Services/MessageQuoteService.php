<?php

namespace AppBundle\Services;

use AppBundle\Entity\Quote;
use AppBundle\Entity\User;
use AppBundle\Model\Message;
use AppBundle\Model\MessageAttachment;
use AppBundle\Model\Offer;
use AppBundle\Model\QuoteMessage;
use AppBundle\Util\DateUtil;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Open\IzbergBundle\Api\MessageApi;
use Open\IzbergBundle\Api\QuoteApi;
use Open\WebhelpBundle\ApiException;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class MessageQuoteService
{
    const TRANSLATION_BUNDLE = 'AppBundle';

    public const INSTANT_MESSAGE = 'message';
    // not nice but ask by lateos MP_1339
    private  const SUBJECT_EVENT = [
        "FR"=>[
            Quote::STATUS_NEW => "Nouveau devis", // buyer ask quote
            Quote::STATUS_SEND => "Soumission devis", // merchant has send quote  to buyer
            Quote::STATUS_REFUSED => "Refus devis", // merchant has refused quote
            Quote::STATUS_CANCELLED => "Annulation devis", // buyer has cancelled the quote
            Quote::STATUS_REDRAFT => "Rediscussion devis", // buyer need to negociate
            Quote::STATUS_VALIDATED => "Validation devis", // buyer has validated the quote]
            self::INSTANT_MESSAGE => "Message devis" // instant message
        ],
        "EN"=>[
            Quote::STATUS_NEW => "New quotation", // buyer ask quote
            Quote::STATUS_SEND => "Submission quotation", // merchant has send quote  to buyer
            Quote::STATUS_REFUSED => "Refuse quotation", // merchant has refused quote
            Quote::STATUS_CANCELLED => "Cancellation quotation", // buyer has cancelled the quote
            Quote::STATUS_REDRAFT => "Negotiation quotation", // buyer need to negociate
            Quote::STATUS_VALIDATED => "Validation quotation", // buyer has validated the quote]
            self::INSTANT_MESSAGE => "Message quotation" // instant message
        ],
        "DE"=>[
            Quote::STATUS_NEW => "neue Kostenvoranschlags", // buyer ask quote
            Quote::STATUS_SEND => "Kostenvoranschlag gesendet", // merchant has send quote  to buyer
            Quote::STATUS_REFUSED => "Kostenvoranschlag Abgelehnt", // merchant has refused quote
            Quote::STATUS_CANCELLED => "Kostenvoranschlag Storniert", // buyer has cancelled the quote
            Quote::STATUS_REDRAFT => "Kostenvoranschlag Neu diskutiert", // buyer need to negociate
            Quote::STATUS_VALIDATED => "Kostenvoranschlag Validiert", // buyer has validated the quote]
            self::INSTANT_MESSAGE => "Kostenvoranschlag Nachricht" // instant message
        ]


    ];

    // I is reserved for Instant message don't use it
    private const EVENT_SUBJECT = [
        "Nouveau devis" => Quote::STATUS_NEW,
        "Soumission devis" => Quote::STATUS_SEND,
        "Refus devis" => Quote::STATUS_REFUSED,
        "Annulation devis" =>  Quote::STATUS_CANCELLED,
        "Rediscussion devis" => Quote::STATUS_REDRAFT,
        "Validation devis" => Quote::STATUS_VALIDATED,
        "New quotation" => Quote::STATUS_NEW,
        "Submission quotation" => Quote::STATUS_SEND,
        "Refuse quotation" => Quote::STATUS_REFUSED,
        "Cancellation quotation" =>  Quote::STATUS_CANCELLED,
        "Negotiation quotation" => Quote::STATUS_REDRAFT,
        "neue Kostenvoranschlags" => Quote::STATUS_NEW,
        "Kostenvoranschlag gesendet" =>Quote::STATUS_SEND,
        "Kostenvoranschlag Abgelehnt" => Quote::STATUS_REFUSED,
        "Kostenvoranschlag Storniert" => Quote::STATUS_CANCELLED,
        "Kostenvoranschlag Neu diskutiert" => Quote::STATUS_REDRAFT,
        "Kostenvoranschlag Validiert" => Quote::STATUS_VALIDATED

    ];

    protected EntityManagerInterface $em;
    private MessageApi $messageApi;
    private QuoteApi $quoteApi;
    private TranslatorInterface $translator;
    private string $izbergBugLinkDomain;
    private string $izbergBugLinkProtocol;
    protected RouterInterface $router;
    private string $quoteEventChar;
    private array $quoteLinkPattern;

    public function __construct(
        $izbergBugLinkDomain,
        $izbergBugLinkProtocol,
        string $quoteEventChar,
        array $quoteLinkPattern,
        EntityManagerInterface $entityManager,
        MessageApi $messageApi,
        QuoteApi $quoteApi,
        TranslatorInterface $translator,
        RouterInterface $router
    ) {
        $this->em = $entityManager;
        $this->messageApi = $messageApi;
        $this->translator = $translator;
        $this->router = $router;
        $this->izbergBugLinkDomain = $izbergBugLinkDomain;
        $this->izbergBugLinkProtocol = $izbergBugLinkProtocol;
        $this->quoteApi = $quoteApi;
        $this->quoteEventChar = $quoteEventChar;
        $this->quoteLinkPattern = $quoteLinkPattern;
    }

    public function  isQuoteMessage ($subject) {
        $key = $this->getMessageTagfromSubject($subject);
        return !is_null($key);
    }


    /**
     * @param Quote $quote
     * @param Offer $offer
     * @param string $message
     * @param UploadedFile ...$attachments
     * @return int thread Id
     */
    public function startQuoteThread(Quote $quote, Offer $offer, string $subject, string $message, UploadedFile ...$attachments) : int
    {
        $eventTag = $this->generateTagFromEvent(Quote::STATUS_NEW, $quote->getVendor()->getLanguage());
        $messageTmp = $this->translator->trans('quote.event.prefix.new', ["%message%"=>$message], 'AppBundle');

        return $this->messageApi->startQuoteThread(
            $eventTag." ".$subject,
            $messageTmp,
            $quote->getBuyer()->getIzbergUserId(),
            $quote->getVendor()->getIzbergId(),
            ...$attachments
        );
    }

    public function eventToThread(Quote $quote,string $event,$user,string $message =""){

        $messageTmp  = $this->translator->trans('quote.event.prefix.'.$event,["%message%"=>$message],'AppBundle', $quote->getBuyer()->getLocale());
        $eventTag = $this->generateTagFromEvent($event, $quote->getVendor()->getLanguage());
        $subject = $this->generateSubject($quote, $quote->getBuyer()->getLocale());
        $this->replyToThread($quote,$user, $eventTag." ".$subject, $messageTmp );


    }

    public function  instantMessage (Quote $quote, $user, $message, ...$files){

        $eventTag = $this->generateTagFromEvent(MessageQuoteService::INSTANT_MESSAGE, $quote->getVendor()->getLanguage());
        return $this->replyToThread($quote, $user, $eventTag, $message, ...$files);
    }

    /**
     * @param Quote $quote
     * @param $user
     * @param string|null $subject
     * @param string $message
     * @param UploadedFile ...$attachments
     * @return QuoteMessage|null
     * @throws ApiException
     */
    public function replyToThread(Quote $quote, $user, ?string $subject, string $message, UploadedFile ...$attachments) : ?QuoteMessage
    {

        $username  = null;
        if($user instanceof \AppBundle\Entity\Merchant ||is_null($user)){
            // ask by vincent for the route on 25/03/20
            $messageTmp = $message.$this->generateLinkPart($quote,"supplier_detail", $quote->getBuyer()->getLocale());
            $username = $user->getName();
            $userId = $user->getIzbergId();
          $data =  $this->messageApi->replyToUserQuote($subject,$messageTmp, $quote->getBuyer()->getIzbergUserId(), $quote->getVendor()->getIzbergId(), $quote->getThreadId(), ...$attachments);
          $quote->setBuyerUnread(true);
        }else{
            $messageTmp = $message.$this->generateLinkPart($quote,"supplier_detail",$quote->getVendor()->getLanguage());
            /** @var User $user */
            $username = $user->getFirstName().' '.$user->getLastName();
            $data =   $this->messageApi->replyToMerchantMessageQuote($subject,$messageTmp, $quote->getBuyer()->getIzbergUserId(), $quote->getVendor()->getIzbergId(), $quote->getThreadId(), ...$attachments);
            $userId = $user->getIzbergUserId();
            $quote->setVendorUnread(true);
        }

        if(is_null($data)){
            throw new ApiException("error creating message");
        }

        $this->em->persist($quote);
        $this->em->flush();

        return $this->buildMessageModelFromIzbergMessage($data);

    }


    public function countBuyerUnreadThreadNumber($accountIzbergId): int
    {
        $unreadMessagesIzb = $this->messageApi->getUnreadMessageForUser($accountIzbergId);

        return count(
            array_unique(
                array_filter(
                    array_map(
                        function($izbergUnreadMessage) {
                            if (!preg_match('#/user/#',$izbergUnreadMessage->to_resource_uri)) {
                                return null;
                            }

                            $rootMessageId = $izbergUnreadMessage->root_msg;
                            $rootMessageId = substr($rootMessageId, 0, -1);
                            $rootMessageId = substr($rootMessageId, strrpos($rootMessageId, '/') + 1);

                            return $rootMessageId;
                        },
                        $unreadMessagesIzb->objects
                    )
                )
            )
        );
    }


    public function fetchUserThread(int $threadId, UserInterface $user ): ?array
    {
        //$this->messageApi->configureApiConnection($user->getMarketPlaceName());
        $messagesRaw = $this->messageApi->getMessagesByRootId($threadId);

        $messages =  $this->buildFullThreadModelFromIzbergThread($messagesRaw);
        $this->markAsReadMessages($user,$messages);
        return $messages;
    }

    public function fetchUserThreadRefresh(int $threadId, UserInterface $user ): ?array
    {
        $messagesRaw = $this->messageApi->getUnreadMessageListForUser($threadId, $this->getIzbergId($user));
        $messages =  $this->buildFullThreadModelFromIzbergThread($messagesRaw);
        $this->markAsReadMessages($user,$messages);
        return $messages;
    }

    public function markAsReadMessages(UserInterface $user, $messages)
    {
        $izbergId = $this->getIzbergId($user);
        /** @var Message $message */
        foreach ($messages as $message) {

            if($message->isUnread() && $message->getSenderId() != $izbergId) {
                   $this->messageApi->markAsRead($message->getId());
            }
        }
    }


    private function buildFullThreadModelFromIzbergThread($messagesRaw)
    {
        $messages = array_map(
            function(\stdClass $izbergMessage): QuoteMessage {
                return $this->buildMessageModelFromIzbergMessage($izbergMessage);
            },
            $messagesRaw->objects
        );
        $messages = array_reverse($messages);
        return $messages;
    }

    private function buildMessageModelFromIzbergMessage(\stdClass $izbergMessage): QuoteMessage
    {
        $message = (new QuoteMessage())
            ->setEventName($this->getEventFromSubject($izbergMessage->subject))
            ->setId($izbergMessage->id)
            ->setCreatedAt(new \DateTimeImmutable($izbergMessage->sent_at))
            ->setBody($this->removeTagFromMessage($izbergMessage->body))
            ->setSender($izbergMessage->from_display_name)
            ->setSenderId($izbergMessage->sender->id)
            ->setStatus($izbergMessage->status);


        if ($izbergMessage->attachment_count > 0) {
            $messageAttachments = $this->messageApi->getMessageAttachments($izbergMessage->id);

            /** @var \stdClass $messageAttachment */
            foreach($messageAttachments as $izbergAttachment) {
                $messageAttachment = (new MessageAttachment())
                    ->setId($izbergAttachment->id)
                    ->setFileName($izbergAttachment->file_name);
                $message->addFile($messageAttachment);
            }
        }

        return $message;
    }

    public function generateTagFromEvent (string $event, $locale){
        $locale = $locale != null ? $locale : 'en';
        if(!array_key_exists($locale,self::EVENT_SUBJECT)){
            $locale = 'en';
        }
        if (array_key_exists($event, self::SUBJECT_EVENT[strtoupper ($locale)])) {
            return sprintf("%s%s :",$this->quoteEventChar,self::SUBJECT_EVENT[strtoupper($locale)][$event]);
        }
        return "";
    }


    private function getEventFromSubject (string $subject) {

        $event = null;
        $key = $this->getMessageTagfromSubject($subject);
        if($key && array_key_exists($key,self::EVENT_SUBJECT)){
                $event = self::EVENT_SUBJECT[$key];
        }
        return $event;
    }

    private function getMessageTagfromSubject (string $subject){
        if(preg_match('/^'.$this->quoteEventChar.'(.+?) :/',
            $subject, $matches)) {
            $key = $matches[1];
            return $key;
        }
        return null;
    }



    private  function removeTagFromMessage (?string $message): ?string{

       $patterns = array_map(function (string $row){ return '/('.$row.')/';},$this->quoteLinkPattern);
       $replacements = array_fill(0,count($patterns),"");
        $result =preg_replace($patterns, $replacements, $message);

       return $result;

    }

    public function generateLinkPart (Quote $quote, string $routeName, ?string $locale) : string {

        $parameters = [ "id"=>$quote->getQuoteId()];

         $baseUrl = $this->izbergBugLinkProtocol . $this->izbergBugLinkDomain;
        //TODO: replace when izbergBug is fix
        $url  = $baseUrl . $this->router->generate($routeName, $parameters,UrlGeneratorInterface::ABSOLUTE_PATH);

        $linkIntro = $this->translator->trans('quote.link_intro', [], 'AppBundle', $locale);
        return sprintf("\r\n%sLien: %s <a href='%s'>$url</a>",$this->quoteEventChar,$linkIntro, $url);
    }

    private function getIzbergId (UserInterface $user) {
        $userIzbergId = null;
        if($user instanceof \AppBundle\Entity\Merchant){
            $userIzbergId = $user->getIzbergId();
        }else{
            if($user instanceof User){
                $userIzbergId = $user->getIzbergUserId();
            }
        }
        return $userIzbergId;
    }

    public function generateSubject(Quote $quote, ?string $locale) : ?string {
        return $this->translator->trans(
            'quote.detail.message_subject',
            ["#offerName#"=>$quote->getInitialOfferTitle()],
            self::TRANSLATION_BUNDLE, $locale
        );
    }

    /**
     *  find id from URI resource from izberg
     * @param $uri
     * @return string| null
     */
    public static function getIdFromResourceUri (string $uri) : ?string {
        // TODO: place this in util service
        // find lasts occurence of / to decrypt id from string like
        //https://api.sandbox.iceberg.technology/v1/message/1010562/

        $uriTmp = substr($uri, 0, -1);
        $pos = strrpos($uriTmp, '/');
        if($pos){
            return substr($uriTmp,$pos+1);
        }
       return null;
    }
}
