<?php

namespace AppBundle\Services;

use AppBundle\Entity\Country;
use AppBundle\Entity\Merchant;
use AppBundle\Entity\TvaRate;
use AppBundle\Model\Tax\TaxRate;
use AppBundle\Repository\TvaGroupRepository;
use AppBundle\Repository\TvaRateRepository;

class TaxService
{
    private TvaGroupRepository $tvaGroupRepository;
    private TvaRateRepository $tvaRateRepository;

    public function __construct(TvaGroupRepository $tvaGroupRepository, TvaRateRepository $tvaRateRepository)
    {
        $this->tvaGroupRepository = $tvaGroupRepository;
        $this->tvaRateRepository = $tvaRateRepository;
    }


    public function taxRateFromCountry(Country $country)
    {
        if ($country->getCode() !== 'france') {
            return 0;
        }

        $taxRate = $this->tvaRateRepository->getTaxRateFromTaxGroupAndDate('FRA-standard', new \DateTimeImmutable());
        if (!$taxRate) {
            return 0;
        }


        return $taxRate->getRate();
    }

    public function getTaxRates (Merchant $merchant) {

        $tvaGroupRates =  $this->tvaRateRepository->findTaxRate($merchant->getCountry());
        $taxRates = [];
        /** @var TvaRate $tvaRate */
        foreach ($tvaGroupRates as $tvaRate) {
            $taxRate = new TaxRate();
            $taxRate->setTaxRate($tvaRate->getRate());
            $taxRate->setKeyName($tvaRate->getGroup()->getGroupName());
            $taxRates[] = $taxRate;
        }
        return $taxRates;
    }
}
