<?php

namespace AppBundle\Services;

use AppBundle\Entity\User;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;

class SimpleLogService implements SimpleLogServiceInterface
{
    private SecurityService $securityService;
    private LogService $logService;

    public function __construct(LogService $logService, SecurityService $securityService)
    {
        $this->logService = $logService;
        $this->securityService = $securityService;
    }

    public function error($message, $eventName, $userName = null, $data = [], $checkEventName = true)
    {
        $this->logService->error($message, $eventName, $userName, $data);
    }

    /***
     *
     * Build log guessing informations from context
     *
     * @param \Exception $e
     */
    public function simple_error(\Exception $e)
    {
        /** @var User $user */
        $user = $this->securityService->getUser();

        $this->logService->error($e->getMessage(), EventNameEnum::TECHNICAL_ERROR, $user,
            [
                'Company' => $user->getCompany(),
                'File' => $e->getFile(),
                'Line' => $e->getLine(),
                'Trace' => $e->getTraceAsString(),
            ]);
    }

    public function info($message, $eventName, $userName = null, $data = [], $checkEventName = true)
    {
        $this->logService->info($message, $eventName, $userName, $data);
    }

    public function critical($message, $eventName, $userName = null, $data = [], $checkEventName = true)
    {
        $this->logService->info($message, $eventName, $userName, $data);
    }

    public function debug($message, $eventName, $userName = null, $data = [], $checkEventName = true)
    {
        $this->logService->info($message, $eventName, $userName, $data);
    }
}