<?php

namespace AppBundle\Services;

use AppBundle\Entity\User;
use AppBundle\Model\Invitation\DataTableResponse;
use AppBundle\Model\Merchant;
use AppBundle\Model\MerchantOrder;
use AppBundle\Model\Message;
use AppBundle\Model\MessageAttachment;
use AppBundle\Model\MessageData;
use AppBundle\Model\Offer;
use AppBundle\Model\Thread;
use AppBundle\Repository\MerchantOrderRepository;
use AppBundle\Util\DateUtil;
use Open\IzbergBundle\Api\MessageApi;
use Open\IzbergBundle\IzbergUtil;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Contracts\Translation\TranslatorInterface;

class MessageService
{
    private MessageApi $messageApi;
    private TranslatorInterface $translator;
    private MailService $mailService;
    private MessageQuoteService $messageQuoteService;
    private MerchantService $merchantService;
    private MerchantOrderRepository $merchantOrderRepository;

    public function __construct(
        MessageApi $messageApi,
        TranslatorInterface $translator,
        MailService $mailService,
        MessageQuoteService $messageQuoteService,
        MerchantService $merchantService,
        MerchantOrderRepository $merchantOrderRepository
    ) {
        $this->messageApi = $messageApi;
        $this->translator = $translator;
        $this->mailService = $mailService;
        $this->messageQuoteService = $messageQuoteService;
        $this->merchantService = $merchantService;
        $this->merchantOrderRepository = $merchantOrderRepository;
    }

    private function sendTicketThreadUpdatedByBuyerToVendorNotification(User $buyer, Merchant $supplier, string $message, string $threadId, string $topic = '')
    {
        $merchantEntity = $this->merchantService->findMerchantEntityByIzbergId($supplier->getId());
        $url = sprintf("%s%s/messages/thread/%s/", $this->merchantService->getMarketplaceUrl($merchantEntity), $supplier->getSlug(), $threadId);
        $contactList = $this->merchantService->buildNotificationContacts($supplier, MailService::TICKET_THREAD_UPDATED_BY_BUYER_TO_VENDOR);

        $this->mailService->sendEmailMessage(
            MailService::TICKET_THREAD_UPDATED_BY_BUYER_TO_VENDOR,
            $merchantEntity->getLanguage(),
            $contactList,
            [
                'vendorFirstName' => $supplier->getMainContactFirstName(),
                'vendorLastName' => $supplier->getMainContactLastName(),
                'firstNameBuyer' => $buyer->getFirstname(),
                'lastNameBuyer' => $buyer->getLastname(),
                'message' => $message,
                'url' => $url,
                'topic' => $topic
            ],
            null,
            null,
            []

        );
    }

    public function buyerAskSupplierAboutOffer(User $buyer, Merchant $supplier, Offer $offer, string $message)
    {
        $subject = sprintf($this->translator->trans('message.object.offer', [], 'AppBundle') . '%s', $offer->getId());
        // Contact supplier
        $threadId = $this->messageApi->contactMerchant($subject, $message, $buyer->getIzbergUserId(), $supplier->getId());
        // Notify supplier
        $this->sendTicketThreadUpdatedByBuyerToVendorNotification($buyer, $supplier, $message, $threadId, $subject);
    }

    public function buyerAskSupplierAboutOrder(User $buyer, Merchant $supplier, MerchantOrder $merchantOrder, string $message)
    {
        $subject = sprintf($this->translator->trans('message.object.order', [], 'AppBundle') . '%s', $merchantOrder->getId());
        // Contact supplier
        $threadId = $this->messageApi->disputeMerchantOrder($subject, $message, $buyer->getIzbergUserId(), $supplier->getId(), $merchantOrder->getId());
        // Notify supplier
        $this->sendTicketThreadUpdatedByBuyerToVendorNotification($buyer, $supplier, $message,$threadId, $subject);
    }

    public function buyerAskSupplierForQuote(User $buyer, Merchant $supplier, Offer $offer, string $message, UploadedFile ...$attachments)
    {
        $subject = sprintf($this->translator->trans('message.object.quotation', [], 'AppBundle') . '%s', $offer->getId());
        $this->messageApi->contactMerchant(
            $subject,
            $message,
            $buyer->getIzbergUserId(),
            $supplier->getId(),
            ...$attachments
        );
    }

    public function buyerReportIllicitContent(User $buyer, ?string $url, ?Merchant $supplier, ?Offer $offer, string $reportType, string $message)
    {
        $subject = $reportType;
        $extraInfo = $this->translator->trans('message.body.illicit', ['%body%' => $reportType], 'AppBundle');

        if ($url) {
            $subject .= $this->translator->trans('message.object.illicitpage', ['%page%' => $url], 'AppBundle');
            $extraInfo = sprintf("%s\nurl: %s", $extraInfo, $url);
        }

        if ($offer && $supplier) {
            $subject .= $this->translator->trans('message.object.illicitproduct', ['%type%' => $reportType, '%product%' => $offer->getId(), '%supplier%' => $supplier->getName()], 'AppBundle');
            $extraInfo = sprintf("%s\noffer id: %d\nsupplier: %s", $extraInfo, $offer->getId(), $supplier->getName());
        }

        $body = sprintf("%s\n\n%s", $message, $extraInfo);

        $this->messageApi->contactOperator($subject, $body, (int)$buyer->getIzbergUserId());

    }

    public function buyerReplyToThread(User $buyer, Thread $thread, ?Merchant $supplier, string $message, UploadedFile ...$attachments)
    {
        if ($thread->hasMerchantInterlocutor()) {
            $this->messageApi->replyToMerchantMessage(
                $thread->getSubject(),
                $message,
                (int)$buyer->getIzbergUserId(),
                $thread->getInterlocutorId(),
                $thread->getId(),
                ...$attachments
            );
        } else {
            $this->messageApi->replyToOperatorMessage(
                $thread->getSubject(),
                $message,
                $buyer->getIzbergUserId(),
                $thread->getId(),
                ...$attachments
            );
        }
         // Sent notification
        if ($supplier) {
            $this->sendTicketThreadUpdatedByBuyerToVendorNotification($buyer, $supplier, $message,  (string)$thread->getId(),  $thread->getSubject());
        }
    }

    public function buyerStartNewThread(User $buyer, Merchant $supplier, string $message, string $subject, UploadedFile ...$attachments)
    {
        $threadId = $this->messageApi->contactMerchant(
            $subject,
            $message,
            $buyer->getIzbergUserId(),
            $supplier->getId(),
            ...$attachments
        );
        // Sent notification
        if ($threadId) {
            $this->sendTicketThreadUpdatedByBuyerToVendorNotification($buyer, $supplier, $message,  $threadId, $subject);
        }
    }

    public function countBuyerUnreadThreadNumber(User $buyer): int
    {
        $unreadMessagesIzb = $this->messageApi->getUnreadMessageForUser($buyer->getIzbergUserId());

        return count(
            array_unique(
                array_filter(
                    array_map(
                        function ($izbergUnreadMessage) {
                            if (!preg_match('#/user/#', $izbergUnreadMessage->to_resource_uri)) {
                                return null;
                            }
                            $rootMessageId = $izbergUnreadMessage->root_msg;
                            $rootMessageId = substr($rootMessageId, 0, -1);
                            $rootMessageId = substr($rootMessageId, strrpos($rootMessageId, '/') + 1);

                            return $rootMessageId;
                        },
                        $unreadMessagesIzb->objects
                    )
                )
            )
        );
    }

    /*
     * @return \Knp\Component\Pager\Pagination\PaginationInterface
     */
    public function fetchBuyerOpenedThreads(User $buyer, int $offset = 0, int $limit = 10)
    {
        $buyerThreads = $this->messageApi->getBuyerThreads($offset, $limit);
        $threads = array_map(
            function (\stdClass $izbergThread) use ($buyer) : ?Thread {
                return $this->buildLightThreadModelFromIzbergThread($izbergThread, (int)$buyer->getIzbergUserId());
            },
            $buyerThreads->objects
        );
        $threads = array_filter($threads);

       /* usort(
            $threads,
            function (Thread $threadA, Thread $threadB) {
                return ($threadA->getLastMessageAt() < $threadB->getLastMessageAt()) ? 1 : -1;
            }
        );*/

        return [
            "threads"=>$threads,
            "meta"=>$buyerThreads->meta
        ];
    }

    public function fetchBuyerThread(User $buyer, int $threadId): ?Thread
    {
        $izbergThread = $this->messageApi->getMessageById($threadId);

        return $this->buildFullThreadModelFromIzbergThread($izbergThread, (int)$buyer->getIzbergUserId());
    }

    public function buyerReadThread(User $buyer, Thread $thread)
    {
        /** @var Message $message */
        foreach ($thread->getMessages() as $message) {

            if ($message->isUnread() && $message->getSenderId() != $buyer->getIzbergUserId()) {
                $this->messageApi->markAsRead($message->getId());
            }
        }
    }

    public function fetchAttachmentUrl(string $attachmentId): ?string
    {
        return $this->messageApi->fetchAttachmentUrl($attachmentId);
    }

    private function buildFullThreadModelFromIzbergThread(\stdClass $izbergThread, int $buyerIzbergId)
    {
        // define merchant
        if (strpos($izbergThread->from_resource_uri, '/v1/merchant/') === 0) {
            $interlocutorName = $izbergThread->sender->from_display_name;
            $interlocutorId = $izbergThread->sender->id;
        } else {
            $interlocutorName = $izbergThread->receiver->to_display_name;
            $interlocutorId = $izbergThread->receiver->id;
        }
        $merchantInterlocutor = (strpos($izbergThread->receiver->resource_uri, 'merchant') !== false) || (isset($izbergThread->merchant_order));
        if ($interlocutorId == $buyerIzbergId && $izbergThread->merchant_order) {
            $merchantOrder = $this->merchantOrderRepository->find($izbergThread->merchant_order->id);
            $interlocutorName = $merchantOrder->getMerchantName();
            $interlocutorId = $merchantOrder->getMerchantId();
        }
        $subject = $izbergThread->subject;
        if (isset($izbergThread->merchant_order)) {
            $subject = '[' . $this->translator->trans('orders.list.block.order_title', [], 'AppBundle') . ' ' . $izbergThread->merchant_order->id . '] ' . $subject;
        }

        $thread = (new Thread())
            ->setId($izbergThread->id)
            ->setCreatedAt(new \DateTimeImmutable($izbergThread->sent_at))
            ->setInterLocutorName($interlocutorName)
            ->setMerchantInterlocutor($merchantInterlocutor)
            ->setInterlocutorId($interlocutorId)
            ->setSubject($subject);

        $messages = $this->messageApi->getMessagesByRootId($thread->getId());

        $messages = array_map(
            function (\stdClass $izbergMessage): Message {
                return $this->buildMessageModelFromIzbergMessage($izbergMessage);
            },
            $messages->objects
        );

        $messages = array_reverse($messages);

        /** @var Message $message */
        foreach ($messages as $message) {
            $thread->addMessage($message);
            if ($message->isUnread() && $message->getSenderId() != $buyerIzbergId) {
                $thread->markAsUnread();
            }
        }

        return $thread;
    }

    private function buildLightThreadModelFromIzbergThread(\stdClass $izbergThread, int $buyerIzbergId)
    {
        // define merchant
        if (strpos($izbergThread->from_resource_uri, '/v1/merchant/') === 0) {
            $interlocutorName = $izbergThread->sender->from_display_name;
            $interlocutorId = $izbergThread->sender->id;
        } else {
            $interlocutorName = $izbergThread->receiver->to_display_name;
            $interlocutorId = $izbergThread->receiver->id;
        }
        $merchantInterlocutor = (strpos($izbergThread->receiver->resource_uri, 'merchant') !== false) || (isset($izbergThread->merchant_order));

        $subject = $izbergThread->subject;
        if (isset($izbergThread->merchant_order)) {
            $subject = '[' . $this->translator->trans('orders.list.block.order_title', [], 'AppBundle') . ' ' . $izbergThread->merchant_order->id . '] ' . $subject;
        }

        $thread = (new Thread())
            ->setId($izbergThread->id)
            ->setCreatedAt(new \DateTimeImmutable($izbergThread->sent_at))
            ->setInterLocutorName($interlocutorName)
            ->setMerchantInterlocutor($merchantInterlocutor)
            ->setInterlocutorId($interlocutorId)
            ->setSubject($subject);

        $threadAllmessages = $this->messageApi->getMessagesByRootId($izbergThread->id);
        if (count($threadAllmessages->objects) > 0 ) {
            $lastMessageObj = $this->buildMessageModelFromIzbergMessage($threadAllmessages->objects[0]);
            $thread->addMessage($lastMessageObj);
            if($lastMessageObj->isUnread() && $lastMessageObj->getSenderId() != $buyerIzbergId){
                $thread->markAsUnread();
            }
        }else{
            $thread->setLastMessageAt(new \DateTimeImmutable($izbergThread->sent_at));
            if($izbergThread->status=='unread' && $izbergThread->receiver->id == $buyerIzbergId){
                $thread->markAsUnread();
            }
        }
        $thread->setTotalMessages(count($threadAllmessages->objects));

        return $thread;
    }

    private function buildMessageModelFromIzbergMessage(\stdClass $izbergMessage): Message
    {
        $message = (new Message())
            ->setId($izbergMessage->id)
            ->setCreatedAt(new \DateTimeImmutable($izbergMessage->sent_at))
            ->setBody($izbergMessage->body)
            ->setSender($izbergMessage->from_display_name)
            ->setSenderId($izbergMessage->sender->id)
            ->setStatus($izbergMessage->status);

        if ($izbergMessage->attachment_count > 0) {
            $messageAttachments = $this->messageApi->getMessageAttachments($izbergMessage->id);

            /** @var \stdClass $messageAttachment */
            foreach ($messageAttachments as $izbergAttachment) {
                $messageAttachment = (new MessageAttachment())
                    ->setId($izbergAttachment->id)
                    ->setFileName($izbergAttachment->file_name);
                $message->addFile($messageAttachment);
            }
        }

        return $message;
    }

    public function convertToDatatableResponse($draw, $threads, $total, $local): DataTableResponse
    {
        $dataResponse = new DataTableResponse();
        $data = [];
        // @var Thread $thread
        foreach ($threads as $thread) {
            $data[] = $this->convertThreadToData($thread, $local);
        }
        $dataResponse->setDraw($draw);
        $dataResponse->setData($data);
        $dataResponse->setRecordFiltered($total);
        $dataResponse->setRecordTotal($total);

        return $dataResponse;
    }

    private function convertThreadToData(Thread $thread, $local)
    {
        $data = new MessageData();
        $data->setId($thread->getId())
            ->setCreationDate(DateUtil::printDate($thread->getCreatedAt(), $local))
            ->setLastThread(DateUtil::printDate($thread->getLastMessageAt(), $local))
            ->setSubject($thread->getSubject())
            ->setSupplierName($thread->getInterlocutorName())
            ->setThreadsCount($thread->getTotalMessages())
            ->setUnread($thread->isUnread());
        return $data;
    }
}
