<?php

namespace AppBundle\Services;

use AppBundle\Entity\Country;
use AppBundle\Entity\MarketPlace;
use AppBundle\Model\Search\Filter\SearchFilterQuery;
use AppBundle\Model\SearchRequest;
use AppBundle\Model\SearchResult;

interface SearchService
{
    public function search(SearchRequest $request, string $locale): SearchResult;

    public function searchMerchants(SearchRequest $request): SearchResult;

    public function update (array $data): void;

    public function findOfferUsingOfferId(int $offerId, MarketPlace $marketPlace, string $locale): array;

    public function findOffersUsingOfferIds(array $offerIds, MarketPlace $marketPlace, string $locale): array;

    public function setBranch(?string $branch): self;

    public function findDistinctValuesForField(string $fieldName,MarketPlace $marketPlace, ?SearchFilterQuery $filterQuery = null) : array ;

    public function enableActiveOfferFilters(SearchFilterQuery $filterQuery): void;

    public function enableActiveMerchantFilter(SearchFilterQuery $filterQuery): void;

    public function enableCountryOfDeliveryFilter(SearchFilterQuery $filterQuery, Country $country): void;

    public function disableSecondaryQuoteOffers(SearchFilterQuery $filterQuery);

    public function enableMerchantBranchesFilter(SearchFilterQuery $filterQuery): SearchFilterQuery;

    public function scan($size, $scroll, MarketPlace $marketPlace, string $locale, array $query = null): SearchResult;

    public function enableFilterEquipment (SearchFilterQuery $filterQuery, $user) : void;

    public function addMustBeEqualsIfExist (SearchFilterQuery $filterQuery, string $searchTerm, string $value);

    public function findSuggestions (array $body): array;

    public function scroll($scroll,$scrollId): SearchResult;

}
