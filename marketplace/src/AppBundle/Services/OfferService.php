<?php

namespace AppBundle\Services;

use AppBundle\Entity\Country;
use AppBundle\Entity\Favori;
use AppBundle\Entity\MarketPlace;
use AppBundle\Entity\User;
use AppBundle\Mapper\IzbergMapper;
use AppBundle\Mapper\OfferMapper;
use AppBundle\Model\BestOffer;
use AppBundle\Model\DetailedPrice;
use AppBundle\Model\Facet;
use AppBundle\Model\FacetValue;
use AppBundle\Model\InvalidSearchParametersException;
use AppBundle\Model\Merchant;
use AppBundle\Model\Offer;
use AppBundle\Model\OfferSearchResult;
use AppBundle\Model\Search\Filter\SearchFilter;
use AppBundle\Model\Search\Filter\SearchFilterQuery;
use AppBundle\Model\SearchRequest;
use AppBundle\Model\SearchResult;
use AppBundle\Model\SortOrderEnum;
use AppBundle\Repository\FavoriRepository;
use Open\IzbergBundle\Api\ApiException;
use Open\IzbergBundle\Api\ProductOfferApi;
use Open\IzbergBundle\Model\ApplicationCategory;
use Open\IzbergBundle\Service\CategoryService;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;

class OfferService
{
    private OfferMapper $offerMapper;
    private SearchService $searchService;
    private CurrencyExchangeRateService $currencyExchangeRateService;
    private CountryService $countryService;
    private LogService $logger;
    private FacetService $facetService;
    private CategoryService $categoryService;
    private MerchantService $merchantService;
    private ?User $user;
    private ProductOfferApi $offerApi;
    private IzbergMapper $izbergMapper;
    private array $messageCategories;
    private FavoriRepository $favoriRepository;

    public function __construct(
        array                       $messageCategories,
        OfferMapper                 $OfferMapper,
        SearchService               $searchService,
        CurrencyExchangeRateService $currencyExchangeRateService,
        CountryService              $countryService,
        LogService                  $logger,
        FacetService                $facetService,
        CategoryService             $categoryService,
        MerchantService             $merchantService,
        ProductOfferApi             $offerApi,
        IzbergMapper                $izbergMapper,
        FavoriRepository            $favoriRepository
    )
    {
        $this->offerMapper = $OfferMapper;
        $this->searchService = $searchService;
        $this->currencyExchangeRateService = $currencyExchangeRateService;
        $this->countryService = $countryService;
        $this->logger = $logger;
        $this->facetService = $facetService;
        $this->categoryService = $categoryService;
        $this->merchantService = $merchantService;
        $this->user = null;
        $this->offerApi = $offerApi;
        $this->izbergMapper = $izbergMapper;
        $this->messageCategories = $messageCategories;
        $this->favoriRepository = $favoriRepository;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        if ($user) {
            $this->searchService->setBranch($user->getBranch());
        }

        return $this;
    }

    /**
     * @param string|null            $text
     * @param SearchFilterQuery|null $filterQuery
     * @param Country                $country
     * @param int                    $hitsPerPage
     * @param int                    $page
     * @param string|null            $sortField
     * @param SortOrderEnum|null     $sortDir
     * @param string                 $locale
     * @param MarketPlace            $marketPlace
     * @param string                 $analyzer
     *
     * @return SearchResult|OfferSearchResult
     * @throws InvalidSearchParametersException
     */
    public function searchActiveOffers(
        ?string            $text,
        ?SearchFilterQuery $filterQuery,
        Country            $country,
        int                $hitsPerPage,
        int                $page,
        ?string            $sortField,
        ?SortOrderEnum     $sortDir,
        string             $locale,
        MarketPlace        $marketPlace,
        User               $buyer,
        string             $analyzer = ElasticSearchService::CUSTOM_ANALYZER
    ): SearchResult|OfferSearchResult
    {
        $filterQuery = $filterQuery ?? (new SearchFilterQuery());

        $this->searchService->enableActiveOfferFilters($filterQuery);
        $this->searchService->enableCountryOfDeliveryFilter($filterQuery, $country);


        if (!is_null($this->user)) {
            $this->searchService->addMustBeEqualsIfExist($filterQuery, 'merchant.branches.keyword', $this->user->getBranch());
            $this->searchService->enableFilterEquipment($filterQuery, $this->user);
        }


        //$filterQuery = $this->searchService->enableMerchantBranchesFilter($filterQuery);
        $this->searchService->disableSecondaryQuoteOffers($filterQuery);

        return $this->search(
            $text,
            $filterQuery,
            $hitsPerPage,
            $page,
            $analyzer,
            $sortField,
            $sortDir,
            $locale,
            $marketPlace,
            $buyer,
            $this->facetService->allFacets()
        );
    }

    /**
     * Method to search offers => use the search service
     *
     * @param null|string            $text        for full text search. Example "Wheel"
     * @param SearchFilterQuery|null $filterQuery list of filters. Example ["status" => "active"]
     * @param int                    $hitsPerPage number of result per page: Example 20
     * @param int                    $page        the requested page. For example: 1
     * @param string                 $analyze
     * @param string|null            $sortField   => Sort field. For example "price"
     * @param SortOrderEnum|null     $sortDir     => Sort direction.
     * @param string                 $locale      => locale to be used to retrieve data. For example: "fr"
     * @param MarketPlace            $marketPlace => marketplace
     * @param array|null             $facets      => optional list of facets. Must be an associative filter ["fieldName" => min_count]
     *
     * @return OfferSearchResult
     * @throws InvalidSearchParametersException
     */
    public function search(
        ?string            $text,
        ?SearchFilterQuery $filterQuery,
        int                $hitsPerPage,
        int                $page,
        string             $analyze,
        ?string            $sortField,
        ?SortOrderEnum     $sortDir,
        string             $locale,
        MarketPlace        $marketPlace,
        User               $buyer = null,
        array              $facets = null
    ): OfferSearchResult
    {


        ///////////////////////////////////////////////////////////////////
        //business rules: check parameters consistency
        ///////////////////////////////////////////////////////////////////
        if ($sortField !== null && $sortDir === null) {
            throw new InvalidSearchParametersException("You must specify sortDir parameter if sortField parameter is specified");
        }
        if ($sortDir !== null && $sortField === null) {
            throw new InvalidSearchParametersException("You must specify sortField parameter if sortOrder parameter is specified");
        }

        ///////////////////////////////////////////////////////////////////
        /// Call search service and translate objects
        ///////////////////////////////////////////////////////////////////

        return $this->offerMapper->indexerResultToSearchResult(
            $this->searchService->search(
                new SearchRequest(
                    $text,
                    $page,
                    $hitsPerPage,
                    $analyze,
                    $sortField,
                    $sortDir,
                    $filterQuery,
                    $facets,
                    $marketPlace,
                ),
                $locale
            ),
            $locale,
            $buyer
        );
    }

    /**
     * init a cursor for a full search
     *
     * @param       $size   integer page size
     * @param       $scroll string timeout window
     * @param array $elasticQuery
     * @param       $locale  string
     *
     * @return OfferSearchResult
     */
    public function scan($size, $scroll, ?array $elasticQuery, string $locale, MarketPlace $marketPlace)
    {
        $response = $this->searchService->scan($size, $scroll, $marketPlace, $locale, $elasticQuery);
        return $this->offerMapper->indexerResultToSearchResult($response, $locale, null);
    }

    /**
     * call a 'get next' results on a cursor
     *
     * @param $scroll_id
     * @param $scroll
     * @param $local
     *
     * @return OfferSearchResult
     */
    public function scroll($scroll_id, $scroll, $local)
    {
        $response = $this->searchService->scroll($scroll, $scroll_id);
        return $this->offerMapper->indexerResultToSearchResult($response, $local, null);
    }

    public function findOffer(int $offerId, string $locale, MarketPlace $marketPlace, ?User $buyer = null): ?Offer
    {
        if ($buyer instanceof User) {
            $favois = $this->favoriRepository->findByBuyer($buyer);
            $favOffers = array_map(
                function (Favori $favori) {
                    return $favori->getOfferId();
                },
                $favois
            );
        } else {
            $favOffers = [];
        }

        return $this->offerMapper->indexerOfferToTotalOffer(
            $this->searchService->findOfferUsingOfferId($offerId, $marketPlace, $locale),
            $locale,
            $favOffers
        );
    }

    public function findOfferForCountryOfDelivery(int $offerId, Country $country, string $locale, MarketPlace $marketPlace, ?User $buyer = null): ?Offer
    {
        $offer = $this->findOffer($offerId, $locale, $marketPlace, $buyer);

        if (!$offer) {
            try {
                $offer = $this->findOfferForCountryOfDeliveryIzberg($offerId, $locale);
            } catch (ApiException $exception) {
                $msg = sprintf('Error occurred while fetching offer - %s - from izberg', $offerId);
                $this->logger->error($msg, 'FETCHING_IZBERG_OFFER_API_ERROR');
            }
        }

        if ($offer) {
            $offer->useCurrency($country->getCurrency());
        }
        return $offer;
    }

    public function findOfferForCountryOfDeliveryIzberg(int $offerId, string $locale): ?Offer
    {
        $offerRaw = $this->offerApi->getProductOffer($offerId, $locale);
        return $this->izbergMapper->mapToOffer($offerRaw, $locale);
    }

    public function findOffers(array $offerIds, string $locale, MarketPlace $marketPlace, User $buyer): array
    {
        $offers = $this->offerMapper->indexerOffersToTotalOffers(
            $this->searchService->findOffersUsingOfferIds($offerIds, $marketPlace, $locale),
            $locale, $buyer
        );

        return (count($offers) === count($offerIds)) ? $offers : array_filter(array_map(function ($offerId) use ($locale){
            return $this->findOfferForCountryOfDeliveryIzberg($offerId, $locale);
        }, $offerIds));
    }

    /**
     * get all possible values for GTIN field
     *
     * @return array array of string with list of possible values for GTIN field
     */
    public function findAllDistinctGTIN(MarketPlace $marketPlace): array
    {
        return $this->searchService->findDistinctValuesForField($this->offerMapper->getGTINFieldName(), $marketPlace);
    }

    /**
     * get all country of delivery values for the specified gtin
     *
     * @param string $gtin
     *
     * @return array array of string with list of possible country of delivery for this gtin
     */
    public function findCountryOfDeliveryValuesForGTIN(string $gtin, MarketPlace $marketPlace): array
    {
        $filterQuery = (new SearchFilterQuery())
            ->addMust(
                (new SearchFilter($this->offerMapper->getGTINFieldName(), $gtin))
            );

        return $this->searchService->findDistinctValuesForField(
            $this->offerMapper->getCountryOfDeliveryFieldName(),
            $marketPlace,
            $filterQuery
        );
    }

    /**
     * find offers by country of delivery and GTIN ordered by price ASC
     *
     * @param string      $countryOfDelivery
     * @param string      $gtin
     * @param int         $hitsPerPage number of hits per page
     * @param int         $page        page number
     * @param MarketPlace $marketPlace
     *
     * @return array list of offers (Offer object)
     * @see Offer
     */
    public function findOffersByCountryOfDeliveryAndGTINOrderedByPriceASC(string $countryOfDelivery, string $gtin, int $hitsPerPage, int $page, MarketPlace $marketPlace): array
    {
        try {
            $filterQuery = (new SearchFilterQuery())
                ->addMust(...[
                    (new SearchFilter($this->offerMapper->getCountryOfDeliveryFieldName(), $countryOfDelivery)),
                    (new SearchFilter($this->offerMapper->getGTINFieldName(), $gtin)),
                ]);

            return $this->search(
                null,
                $filterQuery,
                $hitsPerPage,
                $page,
                ElasticSearchService::CUSTOM_ANALYZER,
                $this->offerMapper->getCountryPriceFieldName(),
                SortOrderEnum::asc(),
                "fr",
                $marketPlace
            )->getOffers();
        } catch (InvalidSearchParametersException $e) {
            $this->logger->error("Invalid search parameter: " . $e->getMessage(), EventNameEnum::SEARCH_ERROR);
            return [];
        }
    }

    /**
     * add or update detailed prices for an offer in the indexed data (not in the izberg api)
     *
     * @param Offer       $offer the offer
     * @param MarketPlace $marketPlace
     *
     * @throws \Exception
     */
    public function addOrUpdateDetailedPricesForOffer(Offer $offer, MarketPlace $marketPlace): void
    {
        $this->searchService->update(
            $this->offerMapper->buildIndexRequestForDetailedPrices(
                $offer->getId(),
                $marketPlace->getElasticSearchIndex(),
                $this->computeOfferDetailedPrices($offer)
            )
        );

        //get country of delivery and compute price for this. Also update price in indexer
        $countryOfDelivery = $this->countryService->getCountryByIzbergCode($offer->getCountryOfDelivery());
        if ($countryOfDelivery) {
            $offer = $this->computePriceForCountryOfDelivery($offer, $countryOfDelivery);

            //update document in index
            $this->searchService->update(
                $this->offerMapper->buildIndexRequestForUpdatingPrice(
                    $offer->getId(),
                    $marketPlace->getElasticSearchIndex(),
                    $offer
                )
            );
        } else {
            $this->logger->error("unable to compute price for country of delivery for offer " . $offer->getId() . ": Unknown country code: " . $offer->getCountryOfDelivery(), EventNameEnum::INDEX_ERROR);
        }
    }

    public function updateMerchantForOffer(Offer $offer, Merchant $merchant, MarketPlace $marketPlace): void
    {
        $this->logger->info("updateMerchantForOffer", "UPDATE_MERCHANT", null, array("offer_id" => $offer->getId(), "merchant_id" => $merchant->getId(), "merchant_name" => $merchant->getName()));
        foreach ($marketPlace->getLanguages() as $language) {
            $this->searchService->update(
                $this->offerMapper->buildIndexRequestForMerchant(
                    $offer->getId(),
                    sprintf('%s_%s', $marketPlace->getElasticSearchIndex(), $language),
                    $merchant
                )
            );
        }
    }

    /**
     * add a BestOffer attribute to the specified offer
     *
     * @param int         $offerId   the identifier of the offer to update
     * @param BestOffer   $bestOffer the best offer
     * @param MarketPlace $marketPlace
     */
    public function addOrUpdateBestOfferForOffer(int $offerId, BestOffer $bestOffer, MarketPlace $marketPlace): void
    {
        $this->searchService->update(
            $this->offerMapper->buildIndexRequestForBestOffer(
                $offerId,
                $marketPlace->getElasticSearchIndex(),
                $bestOffer
            )
        );
    }

    public function buyerLastOrderedProducts(User $user, int $limit)
    {
        // TODO refactor this method to implement the real logic TOTALMP-287
        try {
            /* @var OfferSearchResult $offerSearchResult */
            $offerSearchResult = $this->searchActiveOffers(
                null,
                null,
                $user->getCountryOfDelivery(),
                $limit,
                1,
                $this->offerMapper->getPriceFieldName(),
                SortOrderEnum::asc(),
                "fr",
                $user->getMarketPlace(),
                $user
            );
            return $offerSearchResult->getOffers();
        } catch (InvalidSearchParametersException $e) {
            $this->logger->error("Invalid search parameter: " . $e->getMessage(), EventNameEnum::SEARCH_ERROR);
            return [];
        }
    }

    /**
     * @param Offer $offer
     *
     * @return DetailedPrice a DetailedPrice object that contain prices for each currency
     * @throws \Exception
     */
    private function computeOfferDetailedPrices(Offer $offer): DetailedPrice
    {
        $detailedPrices = new DetailedPrice();
        $detailedPrices->setCreatedAt(new \DateTime());
        foreach ($this->currencyExchangeRateService->getSupportedCurrencies() as $currency) {
            if (strtoupper($currency) === strtoupper($offer->getCurrency())) {
                $detailedPrices->add(strtoupper($currency), $offer->getPrice(), true);
            } else {
                $detailedPrices->add(
                    strtoupper($currency),
                    $this->currencyExchangeRateService->computeExchange($offer->getPrice(),
                        strtoupper($currency),
                        strtoupper($offer->getCurrency())
                    ),
                    false
                );
            }
        }

        return $detailedPrices;
    }

    /**
     * @param Offer   $offer
     * @param Country $country
     *
     * @return Offer the updated offer
     */
    private function computePriceForCountryOfDelivery(Offer $offer, Country $country): Offer
    {
        $offer->setCountryCurrency($country->getCurrency());
        if (strtoupper($offer->getCountryCurrency()) != strtoupper($offer->getCurrency())) {

            $offer->setCountryPrice($this->currencyExchangeRateService->computeExchange($offer->getPrice(), strtoupper($offer->getCountryCurrency()), strtoupper($offer->getCurrency())));
            if ($offer->getThresholdPrice1()) {
                $offer->setCountryThresholdPrice1($this->currencyExchangeRateService->computeExchange($offer->getThresholdPrice1(), strtoupper($offer->getCountryCurrency()), strtoupper($offer->getCurrency())));
            }
            if ($offer->getThresholdPrice2()) {
                $offer->setCountryThresholdPrice2($this->currencyExchangeRateService->computeExchange($offer->getThresholdPrice2(), strtoupper($offer->getCountryCurrency()), strtoupper($offer->getCurrency())));
            }
            if ($offer->getThresholdPrice3()) {
                $offer->setCountryThresholdPrice3($this->currencyExchangeRateService->computeExchange($offer->getThresholdPrice3(), strtoupper($offer->getCountryCurrency()), strtoupper($offer->getCurrency())));
            }
            if ($offer->getThresholdPrice4()) {
                $offer->setCountryThresholdPrice4($this->currencyExchangeRateService->computeExchange($offer->getThresholdPrice4(), strtoupper($offer->getCountryCurrency()), strtoupper($offer->getCurrency())));
            }
        } else {
            $offer->setCountryPrice($offer->getPrice());
            $offer->setCountryThresholdPrice1($offer->getThresholdPrice1());
            $offer->setCountryThresholdPrice2($offer->getThresholdPrice2());
            $offer->setCountryThresholdPrice3($offer->getThresholdPrice3());
            $offer->setCountryThresholdPrice4($offer->getThresholdPrice4());
        }

        return $offer;
    }

    public function findOfferFacetValueById(SearchResult $searchResult, int $categoryId): ?FacetValue
    {
        if ($categoryId === 0) {
            return null;
        }

        /** @var Facet $facet */
        foreach ($searchResult->getFacets() as $facet) {
            /** @var FacetValue $facetValue */
            foreach ($facet->getValues() as $facetValue) {
                if ($facetValue->getValue() == $categoryId) {
                    return $facetValue;
                }
            }
        }

        return null;
    }

    public function getPriceFieldName(): string
    {

        return $this->offerMapper->getPriceFieldName();
    }

    public function getScoreFieldName(): string
    {

        return $this->offerMapper->getScoreFieldName();
    }

    public function getProductcategories(int $productId, MarketPlace $marketPlace)
    {
        try {
            $categories = [];

            $this->offerApi->configureApiConnection($marketPlace->getApiConfigurationKey());

            // get product categories
            $productApplicationCategories = $this->offerApi->getProduct($productId, $marketPlace->getLocale())->getApplicationCategories() ?? [];
            $categoriesIds = array_map(
                function (ApplicationCategory $category) {
                    return $category->getId();
                },
                $productApplicationCategories
            );
            foreach ($categoriesIds as $catId) {
                $categories [] = $this->getCategoriesTree($catId);
            }

            return $categories;

        } catch (ApiException $ex) {
            $this->logger->error("error getting offer:" . $ex->getMessage(), EventNameEnum::COMMAND_SYNC_ORDER_ITEM, null, ["offerId" => $productId]);
            return [];
        }
    }

    public function getOfferCategories(int $offerId, MarketPlace $marketPlace): array
    {
        try {
            $categories = [];
            $offer = $this->offerApi->getProductOffer($offerId, $marketPlace->getLocale());
            if ($offer != null) {
                $productId = $offer['product']['id'];
                $categories = $this->getProductcategories($productId, $marketPlace);
            }
            return $categories;
        } catch (ApiException $ex) {
            $this->logger->error("error getting offer:" . $ex->getMessage(), EventNameEnum::COMMAND_SYNC_ORDER_ITEM, null, ["offerId" => $offerId]);
            return [];
        }
    }

    public function getCategoriesTree(int $catId)
    {
        $category = $this->categoryService->find($catId);
        $tree = [];
        if (!is_null($category)) {
            foreach ($category->getParentIdsTree() as $catId) {
                $tree[] = $this->categoryService->find($catId);
            }
        } else {
            $this->logger->error("category no found:" . $catId, EventNameEnum::COMMAND_SYNC_ORDER_ITEM, null, []);
        }
        return $tree;

    }

    public function getOfferCategoriesByLevel(int $offerId, string $locale, int $level): array
    {
        $offer = null;
        // get offer from izberg
        try {
            $offer = $this->offerApi->getProductOffer($offerId, $locale);
        } catch (ApiException $ex) {
            $this->logger->error("error getting offer:" . $ex->getMessage(), EventNameEnum::COMMAND_SYNC_ORDER_ITEM, null, ["offerId" => $offerId]);
        }

        if ($offer != null) {
            // get offer product id
            $productId = $offer['product']['id'];

            // get product categories
            $productApplicationCategories = $this->offerApi->getProduct($productId)->getApplicationCategories() ?? [];
            $categoriesIds = array_map(
                function (ApplicationCategory $category) {
                    return $category->getId();
                },
                $productApplicationCategories
            );
            return $this->categoryService->filterByLevel($categoriesIds, $level);
        }

        return [];
    }

    /**
     * This function will build an array with trad key for informations messages to display to the user.
     *
     * @param array $facets
     *
     * @return array
     */
    public function buildInfosOffer(array $facets): array
    {
        //get Categorie facet
        $facetCat = $this->facetService->popFacet($facets, 'product.application_categories');
        $messagesToDisplay = [];
        foreach ($facetCat->getValues() as $cat) {
            if (array_key_exists($cat->getValue(), $this->messageCategories)) {
                $messagesToDisplay[] = $this->messageCategories[$cat->getValue()];
            }
        }
        return $messagesToDisplay;
    }
}
