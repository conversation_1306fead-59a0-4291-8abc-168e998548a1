<?php

namespace AppBundle\Services;

use AppBundle\Entity\Node;
use AppBundle\FilterQueryBuilder\PageQueryBuilder;
use Doctrine\ORM\EntityManagerInterface;
use Knp\Component\Pager\PaginatorInterface;

class PageService extends AbstractPaginatedService
{
    public function __construct(EntityManagerInterface $em, PaginatorInterface $paginator, PageQueryBuilder $filterQueryBuilder = null)
    {
        parent::__construct($em, Node::class, $paginator, $filterQueryBuilder);
    }
}