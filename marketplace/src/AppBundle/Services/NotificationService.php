<?php

namespace AppBundle\Services;

use AppBundle\Entity\Node;
use AppBundle\FilterQueryBuilder\FilterQueryBuilderInterface;
use Doctrine\ORM\EntityManagerInterface;
use Knp\Component\Pager\PaginatorInterface;

class NotificationService extends AbstractPaginatedService
{
    public function __construct(EntityManagerInterface $em, PaginatorInterface $paginator, FilterQueryBuilderInterface $filterQueryBuilder = null)
    {
        parent::__construct($em, Node::class, $paginator, $filterQueryBuilder);
    }

}