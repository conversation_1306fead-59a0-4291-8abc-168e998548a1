<?php

namespace AppBundle\Services;

use AppBundle\Provider\ExchangeRates;
use Money\Converter;
use Money\Currencies\ISOCurrencies;
use Money\Currency;
use Money\Exchange\FixedExchange;
use Money\Parser\DecimalMoneyParser;

class CurrencyExchangeRateService
{
    private ExchangeRates $exchangeRates;
    private array $supportedCurrencies;
    private string $marketPlaceCurrency;

    public function __construct(
        array $supportedCurrencies,
        string $marketPlaceCurrency,
        ExchangeRates $exchangeRates
    ) {
        $this->supportedCurrencies = $supportedCurrencies;
        $this->marketPlaceCurrency = $marketPlaceCurrency;
        $this->exchangeRates = $exchangeRates;
    }

    public function computeExchange(float $amount, string $toCurrency, string $fromCurrency = 'EUR'): float
    {
        if ($toCurrency == $fromCurrency) {
            return $amount;
        }

        $exchanges = $this->exchangeRates->getExchangeRate($fromCurrency);

        return $this->getExchangeAmount($amount, $toCurrency, $exchanges, $fromCurrency);
    }

    public function getCurrencyRate(string $toCurrency, string $fromCurrency = 'EUR'): float
    {
        if ($toCurrency == $fromCurrency) {
            return 1;
        }

        $rates = $this->exchangeRates->getExchangeRate($fromCurrency);
        if ($rates) {
            if (array_key_exists($toCurrency, $rates)) {
                return $rates[$toCurrency];
            }
        }
        return 0;
    }

    /**
     * compute exchage rate. (called from twig also)
     * @param float $amount
     * @param float $rate
     * @param string $toCurrency
     * @param string $fromCurrency
     * @return float|int
     */
    public static function getAmountWithRate(float $amount, float $rate, string $toCurrency, string $fromCurrency = 'EUR')
    {
        if ($rate == 0) {
            return 0;
        }
        if ($rate == 1) {
            return $amount;
        }
        $exchanges = [$toCurrency => $rate];
        return self::getExchangeAmount($amount, $toCurrency, $exchanges, $fromCurrency);

    }

    public function getExchangeRates(): ExchangeRates
    {
        return $this->exchangeRates;
    }

    /**
     * @param ExchangeRates $exchangeRates
     * @return CurrencyExchangeRateService
     */
    public function setExchangeRates(ExchangeRates $exchangeRates): CurrencyExchangeRateService
    {
        $this->exchangeRates = $exchangeRates;
        return $this;
    }

    /**
     * @return array
     */
    public function getSupportedCurrencies(): array
    {
        return $this->supportedCurrencies;
    }

    /**
     * @param array $supportedCurrencies
     * @return CurrencyExchangeRateService
     */
    public function setSupportedCurrencies(array $supportedCurrencies): CurrencyExchangeRateService
    {
        $this->supportedCurrencies = $supportedCurrencies;
        return $this;
    }

    private static function getExchangeAmount(float $amount, string $toCurrency, array $exchangeRates, string $fromCurrency = 'EUR'): float
    {

        $exchange = new FixedExchange([
            $fromCurrency => $exchangeRates
        ]);

        $currencies = new ISOCurrencies();

        $moneyParser = new DecimalMoneyParser($currencies);

        $money = $moneyParser->parse((string)$amount, $fromCurrency);
        $converter = new Converter(new ISOCurrencies(), $exchange);

        $convertedCurrency = $converter->convert($money, new Currency($toCurrency));

        $amount = $convertedCurrency->getAmount();

        return floatval(substr_replace($amount, '.', strlen($amount) - 2, 0));
    }

    /**
     * @return string
     */
    public function getMarketPlaceCurrency(): string
    {
        return $this->marketPlaceCurrency;
    }

    /**
     * @param string $marketPlaceCurrency
     */
    public function setMarketPlaceCurrency(string $marketPlaceCurrency): void
    {
        $this->marketPlaceCurrency = $marketPlaceCurrency;
    }
}

