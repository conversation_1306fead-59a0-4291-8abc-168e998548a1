<?php

namespace AppBundle\Services;

use AppBundle\Entity\MetaCart;
use Open\IzbergBundle\Api\CartApi;

class CartAddressService
{
    private CartApi $cartApi;
    private AddressService $addressService;

    public function __construct(
        CartApi $cartApi,
        AddressService $addressService
    ) {
        $this->cartApi = $cartApi;
        $this->addressService = $addressService;
    }

    public function configureShipping(MetaCart $metaCart): bool
    {
        $currencyCarts = $metaCart->getCarts();

        try {
            $izbergShippingAddressId = $this->addressService->createIzbergIfNotExistsBuyerAddress(
                $metaCart->getBuyerShippingAddress(), true
            );

            $izbergBillingAddressId = $this->addressService->createIzbergIfNotExistsBuyerAddress(
                $metaCart->getBuyerBillingAddress()
            );

        } catch (\Exception $exception) {
            return false;
        }

        try {
            $data = [
                'selected_payment_type' => 'prepayment',
                'selected_payment_method' => 'bankwire',
                'shipping_address' => '/v' . $this->cartApi->getVersion() . '/address/' . $izbergShippingAddressId . '/',
                'billing_address' => '/v' . $this->cartApi->getVersion() . '/address/' . $izbergBillingAddressId . '/',
            ];

            /** @var \AppBundle\Entity\Cart $currencyCart */
            foreach ($currencyCarts as $currencyCart) {
                $this->cartApi->patch(
                    $currencyCart->getId(),
                    $data
                );
            }
        } catch (\Exception $exception) {
            return false;
        }

        return true;
    }
}
