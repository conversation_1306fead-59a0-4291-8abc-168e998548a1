<?php

namespace AppBundle\Services;

use AppBundle\Entity\MarketPlace;
use AppBundle\Exception\MarketPlaceException;
use AppBundle\Repository\MarketPlaceRepository;
use Open\FrontBundle\Exception\TotalException;
use Open\IzbergBundle\Api;
use Open\IzbergBundle\Api\ApiClientManager;
use Open\IzbergBundle\Api\ApiConfigurator;
use Open\LogBundle\Service\LogService;
use Symfony\Component\HttpFoundation\Session\SessionInterface;

class MarketPlaceService
{
    private ApiConfigurator $apiConfigurator;
    private ApiClientManager $apiClientManager;
    private MarketPlaceRepository $repository;
    private LogService $logger;
    private SessionInterface $session;
    private array $marketPlaceBackOfficeUrls;
    public const DEFAULT_MARKETPLACE = 'fra';


    public function __construct(
        array $marketPlaceBackOfficeUrls,
        MarketPlaceRepository $repository,
        ApiConfigurator $apiConfigurator,
        ApiClientManager $apiClientManager,
        LogService $logger,
        SessionInterface $session
    ) {
        $this->marketPlaceBackOfficeUrls = $marketPlaceBackOfficeUrls;
        $this->repository = $repository;
        $this->apiConfigurator = $apiConfigurator;
        $this->apiClientManager = $apiClientManager;
        $this->logger = $logger;
        $this->session = $session;
    }

    public function getBackOfficeUrl(string $marketplaceName)
    {
        if (array_key_exists($marketplaceName, $this->marketPlaceBackOfficeUrls)) {
            return $this->marketPlaceBackOfficeUrls[$marketplaceName];
        } else {
            throw new TotalException("bad config izberg_merchant_base_urls for :" . $marketplaceName);
        }
    }

    /**
     * For now marketplace detection is based on info given by Total in buyer file
     *
     * @param String $countryCode
     *
     * @return MarketPlace
     */
    public function getMarketPlace(string $countryCode): ?MarketPlace
    {
        return $this->repository->findOneBy([
            "totalDiscriminator" => strtoupper($countryCode)
        ]);
    }

    /**
     * @param String $marketplaceName
     *
     * @return MarketPlace
     * @throws MarketPlaceException
     */
    public function getMarketPlaceByName(string $marketplaceName): MarketPlace
    {
        $marketplace = $this->repository->findOneBy(["name" => $marketplaceName]);
        if (is_null($marketplace)) {
            $this->logger->critical("MarketPlace name is unknown:" . $marketplaceName, "MARKET_PLACE_BY_NAME", null, []);
            throw new MarketPlaceException("MarketPlace name is unknown:" . $marketplaceName);
        }
        return $marketplace;
    }

    /**
     * @param int $marketPlaceId
     *
     * @return MarketPlace
     */
    public function getMarketPlaceById(int $marketPlaceId): MarketPlace
    {
        return $this->repository->findOneBy(["id" => $marketPlaceId]);
    }

    public function configureIzbergApi(string $marketPlaceName)
    {
        $marketPlace = $this->getMarketPlaceByName($marketPlaceName);
        $this->configureIzbergApiConnection($marketPlace);
    }

    public function configureIzbergApiConnection(MarketPlace $marketPlace)
    {
        $this->apiClientManager->useConnection($marketPlace->getApiConfigurationKey());
        $this->apiConfigurator->configure($this->apiClientManager);
    }

    public function configureIzbergApiByKey(string $key)
    {
        $this->apiClientManager->useConnection($key);
        $this->apiConfigurator->configure($this->apiClientManager);
    }

    public function getCurrentApiKey(): string
    {
        return $this->apiClientManager->inUseConnection();
    }

    public function configureIzbergApiForOperatorByName(string $marketPlaceName)
    {
        $this->configureIzbergApi($marketPlaceName);
        $this->setOperatorToken();
    }

    public function configureIzbergApiForOperator(MarketPlace $marketPlace)
    {
        $this->configureIzbergApiConnection($marketPlace);
        $this->setOperatorToken();
    }

    public function getAllMarketPlace()
    {
        return $this->repository->findAll();
    }

    private function setOperatorToken()
    {
        $this->session->set(Api::IZBERG_ACCESS_TOKEN, $this->apiClientManager->getConfiguration()->getAccessToken());
        $this->session->set(Api::IZBERG_USERNAME, $this->apiClientManager->getConfiguration()->getUsername());
    }

}
