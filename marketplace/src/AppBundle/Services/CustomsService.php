<?php

namespace AppBundle\Services;

use AppBundle\Entity\TvaRate;
use AppBundle\Model\Offer;
use AppBundle\Repository\TvaRateRepository;
use AppBundle\Entity\Country;
use Doctrine\ORM\EntityManagerInterface;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use AppBundle\Model\CustomsInfo;

class CustomsService
{
    public const EMPTY_LEGAL_NOTICE = '';

    private EntityManagerInterface $em;
    private MerchantService $merchantService;
    private LogService $logger;
    private SecurityService $securityService;
    private TvaRateRepository $tvaRepository;
    private IzbergCustomAttributes $customAttributes;
    private array $riskCategories;

    public function __construct(
        EntityManagerInterface $em,
        MerchantService $merchantService,
        LogService $logger,
        SecurityService $securityService,
        TvaRateRepository $tvaRateRepository,
        IzbergCustomAttributes $customAttributes,
        array $riskCategories
    ) {
        $this->em = $em;
        $this->merchantService = $merchantService;
        $this->securityService = $securityService;
        $this->tvaRepository = $tvaRateRepository;
        $this->logger = $logger;
        $this->customAttributes = $customAttributes;
        $this->riskCategories = $riskCategories;
    }


    /***
     * @param \AppBundle\Entity\Country|null $merchantCountry
     * @param \AppBundle\Entity\Country|null $buyerCountry
     *
     * @return bool
     */
    public function isInError(?Country $merchantCountry, ?Country $buyerCountry)
    {
        // Check consistency
        if (($merchantCountry == null) || ($buyerCountry == null)) {
            // Should not happen, LOG IT !
            $this->logger->critical('NULL company country', EventNameEnum::PAYMENT_GENERAL_ERROR);
            return true;
        }
        return false;
    }

    /***
     * @param \AppBundle\Entity\Country|null $merchantCountry
     * @param \AppBundle\Entity\Country|null $buyerCountry
     *
     * @return bool
     */
    public function isExport(?Country $merchantCountry, ?Country $buyerCountry)
    {
        if ($this->isInError($merchantCountry, $buyerCountry)) {
            return false;
        }

        if ($buyerCountry->getIzbergId() == $merchantCountry->getIzbergId()) {
            // same country
            return false;
        }

        return true;
    }

    /**
     * @param \AppBundle\Entity\Country|null $merchantCountry
     * @param \AppBundle\Entity\Country|null $buyerCountry
     *
     * @return bool
     */
    public function partnersAreInEU(?Country $merchantCountry, ?Country $buyerCountry)
    {
        if ($this->isInError($merchantCountry, $buyerCountry)) {
            return false;
        }

        return (($buyerCountry->isInEU()) && ($merchantCountry->isInEU()));
    }

    /***
     * @param \AppBundle\Entity\Country|null $merchantCountry
     * @param \AppBundle\Entity\Country|null $buyerCountry
     *
     * @return string
     */
    public function getAdditionalLegalNotices(?Country $merchantCountry, ?Country $buyerCountry)
    {

        if ($this->isInError($merchantCountry, $buyerCountry)) {
            return self::EMPTY_LEGAL_NOTICE;
        }

        if ($this->isExport($merchantCountry, $buyerCountry)) {
            if ($this->partnersAreInEU($merchantCountry, $buyerCountry)) {
                // Legal Notice for Export INTRA
                return empty($merchantCountry->getLegalNoticeProductExportEU()) ? self::EMPTY_LEGAL_NOTICE : $merchantCountry->getLegalNoticeProductExportEU();
            } else {
                // Legal Notice for Export NON EU
                return empty($merchantCountry->getLegalNoticeProductExportNonEU()) ? self::EMPTY_LEGAL_NOTICE : $merchantCountry->getLegalNoticeProductExportNonEU();
            }
        }

        return self::EMPTY_LEGAL_NOTICE;
    }

    /**
     *
     * Information to display in cart
     *
     * @param \AppBundle\Entity\Country|null $merchantCountry
     * @param \AppBundle\Entity\Country|null $buyerCountry
     *
     * @return \AppBundle\Model\CustomsInfo
     */
    public function getInfo(?Country $merchantCountry, ?Country $buyerCountry)
    {
        if (
            ($this->isInError($merchantCountry, $buyerCountry)) ||
            (!$this->isExport($merchantCountry, $buyerCountry))
        ) {
            return new CustomsInfo(true, false, false, false);
        } else {
            if ($this->partnersAreInEU($merchantCountry, $buyerCountry)) {
                return new CustomsInfo(false, true, false, true);
            } else {
                return new CustomsInfo(false, false, true, true);
            }
        }
    }


    /***
     * @param \AppBundle\Entity\Country|null $merchantCountry
     * @param \AppBundle\Entity\Country|null $buyerCountry
     *
     * @return bool
     */
    public function HaveToAddVAT(?Country $merchantCountry, ?Country $buyerCountry)
    {
        if ($this->isInError($merchantCountry, $buyerCountry)) {
            return true;
        }

        if (!$this->isExport($merchantCountry, $buyerCountry)) {
            // same country
            return true;
        }

        if ($this->partnersAreInEU($merchantCountry, $buyerCountry)) {
            // Both are in European Union AND countries are different
            return false; // INTRACOM
        }

        return true;
    }

    /***
     *
     * Calcule le taux de TVA à appliquer suivant les regles de l'art !
     *
     * @param $rawOffer
     *
     * @return mixed
     */
    public function taxRateToApply(Offer $offer)
    {
        $buyerCountry = null;
        $merchantCountry = $this->merchantService->getMerchantFiscalCountryByMerchantId($offer->getMerchant()->getId());
        $buyerCountry = $this->securityService->getFiscalCountry();

        if (!$merchantCountry || !$buyerCountry) {
            return 0;
        }

        // Business logic here
        if ($merchantCountry->getId() === $buyerCountry->getId() && $offer->getTaxRate()) {
            /** @var TvaRate $tva */
            $tva = $this->tvaRepository->getTaxRateFromTaxGroupAndDate($offer->getTaxRate(), new \DateTimeImmutable);
            if ($tva != null) {
                return $tva->getRate();
            }
        }

        return 0;
    }

    /***
     * @param array $categoriesIds
     * @return bool
     */
    public function categoriesIdsInRisk(array $categoriesIds): bool
    {
        return !empty(array_intersect($categoriesIds, $this->riskCategories));
    }

    /***
     * @param array $categoriesIds
     * @return int|null
     */
    public function categoriesIdsGetRisk(array $categoriesIds): ?int
    {
        $riskCats = array_intersect($categoriesIds, $this->riskCategories);
        return !empty($riskCats)?reset($riskCats):null;
    }
}