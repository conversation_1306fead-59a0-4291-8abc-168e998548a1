<?php

namespace AppBundle\Services\Cache;

use Symfony\Component\Cache\Adapter\AdapterInterface;
use Symfony\Component\Cache\Adapter\FilesystemAdapter;

class CacheService implements CacheInterface
{
    private const CACHE_NAMESPACE = 'reporting_product_export';
    private AdapterInterface $cache;

    public function __construct(string $cacheDirectory, int $cacheExpiration)
    {
        $this->cache = new FilesystemAdapter(
            self::CACHE_NAMESPACE,
            $cacheExpiration,
            $cacheDirectory
        );
    }

    public function keyExists(string $key): bool
    {
        return $this->cache->getItem($key)->isHit();
    }

    public function save(string $key, $value)
    {
        $cacheItem = $this->cache->getItem($key);
        $cacheItem->set($value);
        $this->cache->save($cacheItem);
    }

    public function get(string $key)
    {
        $cacheItem = $this->cache->getItem($key);
        return $cacheItem->isHit() ? $cacheItem->get() : null;
    }

    public function remove(string $key)
    {
        $this->cache->deleteItem($key);
    }

    public function clear()
    {
        $this->cache->clear();
    }

}
