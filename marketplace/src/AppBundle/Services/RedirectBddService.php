<?php

namespace AppBundle\Services;

use AppBundle\FilterQueryBuilder\RedirectQueryBuilder;
use Doctrine\ORM\EntityManagerInterface;
use Knp\Component\Pager\PaginatorInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;

class RedirectBddService extends AbstractPaginatedService
{
    private AuthorizationCheckerInterface $authorizationChecker;

    public function __construct(
        EntityManagerInterface        $em,
        PaginatorInterface            $paginator,
        RedirectQueryBuilder          $filterQueryBuilder,
        AuthorizationCheckerInterface $authorizationChecker
    )
    {
        parent::__construct($em, 'AppBundle:Redirect', $paginator, $filterQueryBuilder);
        $this->authorizationChecker = $authorizationChecker;
    }


    public function getCustomFilteredPaginator($page, $numberPerPage, $request, $data, $qualifier)
    {


        $qb = $this->getQueryBuilder($qualifier);

        $this->filterQueryBuilder->build($qb, $data);

        return $this->paginator->paginate(
            $qb->getQuery(),
            $request->query->getInt('page', 1),
            $numberPerPage,
            array('defaultSortFieldName' => 'e.id', 'defaultSortDirection' => 'desc')
        );

    }


    private function getQueryBuilder($qualifier)
    {
        $qb = $this->em->createQueryBuilder();

        $qb->select('e')
            ->from($this->entityName, 'e');

        return $qb;
    }

}