<?php

namespace AppBundle\Services;

use AppBundle\Entity\Cart as CurrencyCart;
use AppBundle\Entity\MetaCart;
use AppBundle\Model\Offer;
use AppBundle\Repository\CartRepository;
use Open\IzbergBundle\Api\CartApi;
use Open\IzbergBundle\Model\CartItem as IzbergCartItem;

class CurrencyCartService
{
    private CartRepository $cartRepository;
    private CartApi $cartApi;
    private CustomsService $customsService;
    private CartAddressService $cartAddressService;
    private MetaCartMerchantService  $metaCartMerchantService;

    public function __construct(
        CartRepository $cartRepository,
        CartApi $cartApi,
        CustomsService $customsService,
        CartAddressService $cartAddressService,
        MetaCartMerchantService $metaCartMerchantService
    ) {
        $this->cartRepository = $cartRepository;
        $this->cartApi = $cartApi;
        $this->customsService = $customsService;
        $this->cartAddressService = $cartAddressService;
        $this->metaCartMerchantService = $metaCartMerchantService;
    }

    public function findCurrencyCart(MetaCart $metaCart, string $currency): CurrencyCart
    {
        $currencyCart = $this->cartRepository->findOneBy(
            ['metaCart' => $metaCart, 'cartCurrency' => $currency]
        );
        if (!$currencyCart) {
            $izbergCart = $this->cartApi->createCart($currency);
            $currencyCart = new CurrencyCart();
            $currencyCart->setCartId($izbergCart->getId());
            $currencyCart->setStatus(CurrencyCart::STATUS_CREATE);
            $currencyCart->setCreatedUser($metaCart->getBuyer());
            $currencyCart->setCurrentUser($metaCart->getBuyer());
            $currencyCart->setCartCurrency($currency);
            $currencyCart->setMetaCart($metaCart);
            $currencyCart->setItemsCount(0);
            $this->cartRepository->save($currencyCart);

            $metaCart->addCart($currencyCart);

            //default cart is in france
            if($metaCart->getBuyerBillingAddress() && $metaCart->getBuyerShippingAddress()){
                $this->cartAddressService->configureShipping($metaCart);
            }
        }

        return $currencyCart;
    }

    /**
     * @param CurrencyCart $currencyCart
     * @param Offer $offer
     * @param int $quantity
     * @param bool $overwriteQty
     * @param array $extraInfo
     * @return int cart item id added
     */
    public function addOfferToCurrencyCart(CurrencyCart $currencyCart, Offer $offer, int $quantity, bool $overwriteQty = false, array $extraInfo = []): int
    {
       return  $this->addOfferToCurrencyCartPrivate($currencyCart,$offer,$quantity,$overwriteQty,$extraInfo);
    }

    /**
     * @param CurrencyCart $currencyCart
     * @param Offer $offer
     * @param int $quantity
     * @param bool $overwriteQty
     * @param array $extraInfo
     * @return int returns the number of items in the currency cart
     */
    private function addOfferToCurrencyCartPrivate(CurrencyCart $currencyCart, Offer $offer, int $quantity,bool $overwriteQty = false, array $extraInfoParam = []): int
    {
        $price = null;
        if ($offer->isRisk()) {
            $extraInfoParam["risk"] = "yes";
        }
        $izbergCart = $this->cartApi->fetchIzbergCart($currencyCart->getId());

        $cartItems = array_filter(
            $izbergCart->getItems()->toArray(),
            function(IzbergCartItem $cartItem) use ($offer) {
                return ($cartItem->getOffer()->getId() === $offer->getId());
            }
        );

        // if not overwrite quantity then add the input quantity to the existing quantity
        if (!$overwriteQty) {
            $quantity = array_reduce(
                $cartItems,
                function($quantity, IzbergCartItem $cartItem) {
                    $quantity += $cartItem->getQuantity();

                    return $quantity;
                },
                $quantity
            );
        }


        $extraInfo = [];

        //backup comment if any
        $comment =  $this->metaCartMerchantService->fetchMerchantComment($currencyCart->getMetaCart()->getId(),$offer->getMerchant()->getId());

        /** @var IzbergCartItem $cartItem */
        foreach ($cartItems as $cartItem) {
            $extraInfo = $this->cartApi->getCartItemExtraInfo($cartItem->getId());
            $this->cartApi->removeItem($cartItem->getId());

            $this->metaCartMerchantService->removeItem($currencyCart->getMetaCart(), $cartItem->getId());
        }

        $price = $offer->getReductionPrice($quantity);
        if (!$price) {
            $price = $offer->getPrice();
        }

        // CUSTOMS TAX CALCULATIONS
        $taxRate = $this->customsService->taxRateToApply($offer);

        //we can now add our product to the cart
        $cartItemId = $this->cartApi->addProductOffer(
            $currencyCart->getId(),
            $offer->getId(),
            $currencyCart->getCartCurrency(),
            $quantity,
            $price,
            $taxRate,
            array_merge($extraInfo, $extraInfoParam)
        );

        // add item to metaCart
        $merchantId = $offer->getMerchant()->getId();
        $this->metaCartMerchantService->addItem($currencyCart->getMetaCart(), $merchantId, $cartItemId, $comment);

        $numberOfItems = $this->cartApi->getNumberOfCartItems($currencyCart->getId());

        $currencyCart->setItemsCount($numberOfItems);
        $this->cartRepository->save($currencyCart);

        return $cartItemId;
    }

}
