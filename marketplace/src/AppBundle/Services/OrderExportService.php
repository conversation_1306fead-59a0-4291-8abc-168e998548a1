<?php

namespace AppBundle\Services;

use Open\IzbergBundle\Api\OrderApi;
use Open\IzbergBundle\Model\MerchantOrder;

class OrderExportService
{
    private OrderApi $orderApi;
    private SFTPService $sftpService;

    public function __construct(OrderApi $orderApi, SFTPService $sftpService)
    {
        $this->orderApi = $orderApi;
        $this->sftpService = $sftpService;
    }

    public function exportMerchantOrder(MerchantOrder $merchantOrder): bool
    {
        $xml = $this->orderApi->fetchMerchantOrderByIdXML($merchantOrder->getId());

        return $this->sftpService->uploadTo(
            'order-' . $merchantOrder->getId() . '.xml',
            $xml
        );
    }
}
