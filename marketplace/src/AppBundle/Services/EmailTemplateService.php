<?php

namespace AppBundle\Services;

use AppBundle\Exception\TemplateException;
use AppBundle\Model\EmailTemplate;
use Open\IzbergBundle\Service\RedisService;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Symfony\Component\Yaml\Yaml;

class EmailTemplateService
{
    private const CACHE_KEY = "EMAIL_TEMPLATES";

    private string $configFile;
    private LogService $logger;
    private RedisService $cacheService;

    public function __construct(
        string $configFile,
        RedisService $cacheService,
        LogService $logger
    ) {
        $this->configFile = $configFile;
        $this->logger = $logger;
        $this->cacheService = $cacheService;
        $templates = Yaml::parseFile($this->configFile);
        $this->cacheService->saveItem(self::CACHE_KEY, $templates);
    }

    /**
     * get a template from its name
     * @param string $templateName
     *
     * @return EmailTemplate|null the email template if found, null if the specified template doesn't exist
     */
    public function getTemplate (string $templateName){
        $templates = $this->cacheService->getItem(self::CACHE_KEY);

        if ($templates === null){
            $this->logger->error("enable to get template: Templates has not been loaded in cache", EventNameEnum::EMAIL_ERROR, null,
                [
                    "templateName" => $templateName
                ]);
            throw new TemplateException("enable to get template: Templates has not been loaded in cache");
        }

        if (array_key_exists($templateName, $templates)){
            return $this->parseTemplate($templateName, $templates[$templateName]);
        }
        else{
            $this->logger->error("enable to get template: it doesn't exist", EventNameEnum::EMAIL_ERROR, null,
                [
                    "templateName" => $templateName
                ]);
            return null;
        }

    }

    /**
     * get the list of the names of all the email templates
     * @return array the list of the names of all the email templates
     */
    public function fetchAllTemplateNames (){
        $templates = $this->cacheService->getItem(self::CACHE_KEY);

        if ($templates === null){
            $this->logger->error("error while fetching the list of templates: Templates has not been loaded in cache", EventNameEnum::EMAIL_ERROR);
            throw new TemplateException("error while fetching the list of templates: Templates has not been loaded in cache");
        }

        return array_keys($templates);
    }


    /**
     * parse a template from the cache and return a Template object
     * @param array $templateFromCache
     * @param string $templateName
     * @return EmailTemplate
     */
    public function parseTemplate (string $templateName, array $templateFromCache){
        $result = new EmailTemplate();
        $result->setTemplateName($templateName);
        $result->setContent($templateFromCache['body']);

        $variables = [];
        foreach ($templateFromCache['variables'] as $variable){
            if (array_key_exists("default", $variable)) {
                $variables[$variable['name']] = $variable['default'];
            }
            else{
                $items = [];
                //for testing template, just add 2 elements
                for ($i=0; $i<2; $i++){
                    $item = [];
                    foreach ($variable['children'] as $child) {
                        $item[$child['name']] = $child['default'];
                    }

                    $items [] = (object)$item;
                }
                $variables[$variable['name']] = $items;
            }
        }
        $result->setVariables($variables);

        return $result;
    }


}