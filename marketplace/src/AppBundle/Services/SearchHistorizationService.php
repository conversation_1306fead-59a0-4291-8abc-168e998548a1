<?php

namespace AppBundle\Services;

use AppBundle\FilterQueryBuilder\FilterQueryBuilderInterface;
use AppBundle\Entity\SearchHistorization;
use AppBundle\FilterQueryBuilder\SearchHistorizationQueryBuilder;
use Doctrine\ORM\EntityManagerInterface;
use Knp\Component\Pager\PaginatorInterface;
use Open\LogBundle\Service\LogService;
use Unirest\Exception;

class SearchHistorizationService extends AbstractPaginatedService
{
    private const REPO = 'AppBundle\entity\SearchHistorization';

    private SecurityService $securityService;
    private SimpleLogService $logger;

    public function __construct(
        EntityManagerInterface $em,
        PaginatorInterface $paginator,
        SearchHistorizationQueryBuilder $filterQueryBuilder,
        SecurityService $securityService,
        SimpleLogService $logger
    ) {
        parent::__construct($em, SearchHistorization::class, $paginator, $filterQueryBuilder);
        $this->securityService = $securityService;
        $this->logger = $logger;
    }

    /**
     * @param $id
     *
     * @return \AppBundle\Entity\SearchHistorization|null|object
     */
    public function get($id)
    {
        return $this->em->getRepository(self::REPO)->find($id);
    }

    /**
     * @param $nbItemPerPage
     * @param $request
     * @param $data
     * @return mixed
     */
    public function getAll($nbItemPerPage, $request, $data){
        $qb = $this->em->createQueryBuilder();
        $qb->select('e')
            ->from($this->entityName, 'e')
            ->addOrderBy('e.createdAt', 'desc');
        if(!empty($data)){
            $this->filterQueryBuilder->build($qb, $data);
        }

        return $this->paginator->paginate(
            $qb->getQuery(),
            $request->query->getInt('page', 1),
            $nbItemPerPage
        );
    }

    /**
     * @param $data
     * @return mixed
     */
    public function getAllByFilter($data){
        $qb = $this->em->createQueryBuilder();
        $qb->select('e')
            ->from($this->entityName, 'e')
            ->addOrderBy('e.createdAt', 'desc');
        if(!empty($data)){
            $this->filterQueryBuilder->build($qb, $data);
        }
        $query = $qb->getQuery();
        return $query->getResult();
    }
    /**
     * @param $search_term
     * @param $filter
     * @param $nb_hits
     *
     */
    public function trace($search_term, $filter, $nb_hits)
    {
        $entry = new SearchHistorization($this->securityService->isAnonymous(), $search_term, $filter, $nb_hits);
        try{
            $this->em->persist($entry);
            $this->em->flush();
        }
        catch (\Exception $e){
            $this->logger->simple_error($e);
        }
    }
}
