<?php

namespace AppBundle\Services;

use AppBundle\Domain\QuoteItemDomain;
use AppBundle\Entity\QuoteItem;
use AppBundle\Repository\QuoteItemRepository;
use Doctrine\ORM\EntityManagerInterface;

class QuoteItemService
{
    private QuoteItemRepository $quoteItemRepository;
    private EntityManagerInterface $em;

    public function __construct(QuoteItemRepository $quoteItemRepository, EntityManagerInterface $entityManager)
    {
        $this->quoteItemRepository = $quoteItemRepository;
        $this->em = $entityManager;
    }

    public function getQuoteItem(int $quoteItemId): ?QuoteItem
    {
        return $this->quoteItemRepository->find($quoteItemId);
    }

    public function saveQuoteItem(QuoteItem $quoteItem): QuoteItem
    {

        $this->em->persist($quoteItem);
        $this->em->flush();
        return $quoteItem;

    }

    public function updateQuoteItem(QuoteItemDomain $quoteItem, ?QuoteItem $quoteItemBdd = null): QuoteItem
    {
        if ($quoteItemBdd != null) {
            $quoteItemBdd = $this->getQuoteItem($quoteItem->getId());
        } else {
            $quoteItemBdd = new QuoteItem();
        }

        $quoteItemBdd->setUpdatedAt();
        $quoteItemBdd->setQuantity((string)$quoteItem->getQuantity());
        $quoteItemBdd->setTotalPrice($quoteItem->getQuantity() * $quoteItem->getUnitPrice());
        $quoteItemBdd->setUnitPrice($quoteItem->getUnitPrice());
        $quoteItemBdd->setVat($quoteItem->getVat());
        $quoteItemBdd->setDescription($quoteItem->getDescription());
        $this->em->flush();
        return $quoteItemBdd;

    }


}