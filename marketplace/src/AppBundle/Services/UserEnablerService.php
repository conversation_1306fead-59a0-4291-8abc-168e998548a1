<?php

namespace AppBundle\Services;

use AppBundle\Entity\Site;
use AppBundle\Entity\User;
use AppBundle\Entity\InvoiceEntity;
use AppBundle\Entity\UserEnabler;
use AppBundle\FilterQueryBuilder\UserEnablerQueryBuilder;
use AppBundle\Repository\UserEnablerRepository;
use AppBundle\Repository\UserRepository;
use Doctrine\ORM\QueryBuilder;
use Knp\Component\Pager\PaginatorInterface;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;

class UserEnablerService extends AbstractPaginatedService
{
    private UserEnablerRepository $userEnablerRepository;
    private UserRepository $userRepository;
    private BuyerService $buyerService;
    private LogService $logger;

    public function __construct(
        BuyerService $buyerService,
        UserEnablerRepository $userEnablerRepository,
        UserRepository $userRepository,
        LogService $logService,
        PaginatorInterface $paginator,
        UserEnablerQueryBuilder  $filterQueryBuilder
    ) {
        parent::__construct($userEnablerRepository->getEntityManager(),
            UserEnabler::class,
            $paginator,
            $filterQueryBuilder);
        $this->buyerService = $buyerService;
        $this->userEnablerRepository = $userEnablerRepository;
        $this->userRepository = $userRepository;
        $this->logger = $logService;
    }

    /**
     * Create simple UserEnabler Entity from it's properties
     *
     * @param string $name
     * @return UserEnabler
     */
    public function createEnabler(
        InvoiceEntity $invoiceEntity,
        Site    $site,
        ?string $userType
    ): UserEnabler
    {

        $userEnabler = $this->userEnablerRepository->findEnabler($invoiceEntity,$site,$userType);
        if(!$userEnabler) {
            // Create only unknown invoice entity
            $userEnabler = new UserEnabler();
            $userEnabler->setEntity($invoiceEntity);
            $userEnabler->setSite($site);
            $userEnabler->setUserType($userType);
            $this->userEnablerRepository->save($userEnabler);
        }
        return $userEnabler;
    }

    /**
     * @param string $name
     * @return UserEnabler|null
     */
    public function findEnabler(InvoiceEntity $invoiceEntity,Site $site, string $userType): ?UserEnabler
    {
        return $this->userEnablerRepository->findEnabler($invoiceEntity,$site,$userType);
    }

    /**
     * @param int $id
     * @return UserEnabler|null
     */
    public function findEnablerById(int $id): ?UserEnabler
    {

        return $this->userEnablerRepository->find($id);
    }


    /**
     * @param User $buyer
     * @param InvoiceEntity $invoiceEntity
     * @return User
     */
    public function attachBuyerToEnabler(User $buyer, UserEnabler $enabler): User
    {
        // if user creation or entity/site/type has change, update user enabled
        $updateEnable = (is_null($buyer->getUserEnabler()) ||  $buyer->getUserEnabler()->getId()!=$enabler->getId());
        $buyer->setUserEnabler($enabler);

        $this->logger->info(
            "Attach buyer to enabler",
            'UPDATE_USER_ENABLER',
            $buyer->getUsername(), [
                'ID' => $buyer->getId(),
                'ENABLER_ID' => $enabler->getId(),
                'USER_ENABLED' => $buyer->isEnabled(),
                'ENABLER_ENABLED' => $enabler->getEnabled(),
                'UPDATE_ENABLE' => $updateEnable
            ]
        );

        if($updateEnable){
            $buyer->setEnabled($enabler->getEnabled());
        }
        $this->userEnablerRepository->saveWithoutFlush($enabler);

        return $buyer;
    }

    /**
     * @param $page
     * @param $numberPerPage
     * @param $request
     * @param $userId
     */
    public function getBuyerFilteredPaginator($page, $numberPerPage, $request, $data, $qualifier = null)
    {
        $qb = $this->getQueryBuilder($data);

        // Filter with other data
        $this->filterQueryBuilder->build($qb, $data);
        $qb->addOrderBy('e.name','asc');
        $qb->addOrderBy('s.name','asc');
        $qb->addOrderBy('ue.userType','asc');

        return $this->paginator->paginate(
            $qb->getQuery(),
            $request->query->getInt('page', 1),
            $numberPerPage
        );
    }

    public function enable(User $operator,$data){
        $status = true;
        $userEnablers = $this->getUserEnablersWithFilter($data);
        /** @var UserEnabler $userEnabler */
        foreach ($userEnablers as $userEnabler){
            $this->changeEnablerStatus($operator, $userEnabler, $status);
        }
    }

    /**
     * @param User $operator
     * @param $userEnabler UserEnabler
     * @param $status
     */
    public function changeEnablerStatus(User $operator, $userEnabler, $status) {
        $this->logger->info(
            "Change enabler Status",
            $status ? EventNameEnum::USER_ENABLE : EventNameEnum::USER_DISABLE,
            $operator->getUsername(), ["ENABLER" => $userEnabler->getId()]
        );
        $userEnabler->setEnabled($status);
        $this->changeAllUserStatus($operator, $userEnabler, $status);
        $this->userRepository->activateAllAutoEnablePossible();
        $this->userRepository->deactivateAllNonRequiredAutoEnable();
        $this->userEnablerRepository->save($userEnabler);

    }



    public function disable (User $operator,$data){

        $status = false;
        $userEnablers = $this->getUserEnablersWithFilter($data);
        /** @var UserEnabler $userEnabler */
        foreach ($userEnablers as $userEnabler){
            $this->changeEnablerStatus($operator, $userEnabler, $status);
        }
    }

    private function changeAllUserStatus(User $operator,UserEnabler $userEnabler, bool $status) {


        $q = $this->em->createQuery('update AppBundle\Entity\User u set u.enabled = :status where u.userEnabler = :enabler and u.id != :me');
        $q->setParameter('enabler',$userEnabler->getId());
        $q->setParameter("me", $operator->getId());
        $q->setParameter("status",$status);
        $numUpdated = $q->execute();

    }

    private function getUserEnablersWithFilter ($data){
        $qb = $this->getQueryBuilder($data);
        // Filter with other data
        $this->filterQueryBuilder->build($qb, $data);
        return $qb->getQuery()->getResult();
    }

    private function getQueryBuilder(array $data): QueryBuilder
    {
        $qb = $this->em->createQueryBuilder();

        $qb->select('ue')
            ->from(UserEnabler::class, 'ue')
            ->join('ue.entity', 'e')
            ->join('ue.site', 's');

        return $qb;
    }

    private function updateQueryBuilder(array $data): QueryBuilder
    {
        $qb = $this->em->createQueryBuilder();

        $qbFilter = $this->em->createQueryBuilder();
        $qbFilter->select('ue.id')
            ->from(UserEnabler::class, 'ue')
            ->join('ue.entity', 'e')
            ->join('ue.site', 's');
        $this->filterQueryBuilder->build($qbFilter, $data);

        $qb->update('AppBundle\Entity\UserEnabler', 'user_enable_update')
            ->where ($qb->expr()->in('user_enable_update.id',$qbFilter->getDQL()));
        return $qb;
    }


}
