<?php

namespace AppBundle\Services;

use AppBundle\Entity\MarketPlace;
use AppBundle\Entity\OrderItem;
use AppBundle\Entity\User;
use AppBundle\Repository\OrderItemRepository;
use Symfony\Contracts\Translation\TranslatorInterface;

class OrderItemService
{
    private TranslatorInterface $translator;
    private OrderItemRepository $orderItemRepository;
    private CurrencyExchangeRateService $currencyRateService;
    private OfferService $offerService;

    public function __construct(
        TranslatorInterface $translator,
        OrderItemRepository $orderItemRepository,
        CurrencyExchangeRateService $currencyRateService,
        OfferService $offerService
    ) {
        $this->translator = $translator;
        $this->orderItemRepository = $orderItemRepository;
        $this->currencyRateService = $currencyRateService;
        $this->offerService = $offerService;
    }

    /**
     * return last  offer buy by user
     *
     * @param User $user
     * @param      $limit
     *
     * @return array
     */
    public function getlastOrderedOffer(User $user, int $limit): array
    {

        $offerIds = null;

        $orderItems = $this->orderItemRepository->findLastOrderedItem($user->getUsername(), $limit);
        if ($orderItems) {
            $offerIds = array_map(function (OrderItem $orderItem) {
                return $orderItem->getOfferId();
            }, $orderItems);
        }
        $offers = [];
        if ($offerIds) {
            foreach ($offerIds as $offerId) {
                $offer = $this->offerService->findOffer($offerId, $user->getLocale(), $user->getMarketPlace(), $user);
                if ($offer) {
                    $offers[] = $offer;
                }
            }
        }

        //some maybe inactive in ES, so we look for more elements than needed
        if (count($offers) < $limit) {
            $offerToCompletes = $this->getLastMarketplaceOrderedOffersExceptUser($user, 10);
            $offers = array_merge($offers, $offerToCompletes);
        }

        return array_slice($offers, 0, 4);

    }

    private function getLastMarketplaceOrderedOffersExceptUser(User $user, int $limit): array
    {
        $offerIds = array_map(
            function (OrderItem $orderItem) {
                return $orderItem->getOfferId();
            },
            $this->orderItemRepository->findLastOrderedItemExceptUser($user, $limit)
        );
        $offers = [];
        foreach ($offerIds as $offerId) {
            $offer = $this->offerService->findOffer($offerId, $user->getLocale(), $user->getMarketPlace(), $user);
            if ($offer) {
                $offers[] = $offer;
            }
        }

        return $offers;
    }

    public function getMerchantOrderItems(int $merchantOrderId): array
    {
        return $this->orderItemRepository->createQueryBuilder('oi')
            ->select('oi')
            ->where('oi.name != :name')
            ->andWhere('oi.merchantOrderId = :merchantOrder')
            ->setParameter('name', 'frais de livraison')
            ->setParameter('merchantOrder', $merchantOrderId)
            ->getQuery()
            ->getResult();
    }

    public function saveOrderItem(OrderItem $orderItem)
    {
        $this->orderItemRepository->saveOrderItem($orderItem);
    }

    public function syncOrderItemCategories(OrderItem $orderItem, MarketPlace $marketPlace)
    {
        $categories = $this->offerService->getOfferCategories($orderItem->getOfferId(), $marketPlace);

        $categoryL1 = (count($categories) > 0) ? ((count($categories[0]) > 0) ? $categories[0][0] : null) : null;
        $categoryL2 = (count($categories) > 0) ? ((count($categories[0]) > 1) ? $categories[0][1] : null) : null;
        $defaultCategory = $categoryL2 ?? $categoryL1;

        if (!is_null($defaultCategory)) {
            $orderItem->setCategoryId($defaultCategory->getId())
                ->setDefaultCategoryName($defaultCategory->getName())
                ->setCurrentCategoryName($defaultCategory->getName());
        }
        if (!is_null($categoryL1)) {
            $orderItem
                ->setRootCategoryId($categoryL1->getId())
                ->setRootCategoryName($categoryL1->getName());
        }

        $this->orderItemRepository->saveOrderItem($orderItem);
    }

    public function merchantOrderIdsForItemsWithoutCategories(): array
    {
        return $this->orderItemRepository->createQueryBuilder('oi')
            ->select('oi.merchantOrderId')
            ->where('oi.name != :name')
            ->andWhere('oi.rootCategoryName IS NULL OR oi.defaultCategoryName IS NULL OR oi.currentCategoryName IS NULL')
            ->setParameter('name', 'frais de livraison')
            ->distinct()
            ->getQuery()
            ->getResult();
    }
}
