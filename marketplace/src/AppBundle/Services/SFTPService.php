<?php

namespace AppBundle\Services;

use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use phpseclib3\Net\SFTP;
use Symfony\Component\DependencyInjection\ContainerInterface;

class SFTPService
{
    protected LogService $logger;
    private array $sftpParams;
    private array $sftpEpsa;

    public function __construct(
        LogService $logger,
        array $sftpParams,
        array $sftpEpsa
    ){
        $this->logger = $logger;
        $this->sftpParams = $sftpParams;
        $this->sftpEpsa = $sftpEpsa;
    }

    private function getBuyerImportSftp(): SFTP
    {
        $sftpParams = $this->sftpParams;

        $sftp = new SFTP($sftpParams['host']);
        $sftpLogin = $sftp->login($sftpParams['user'], $sftpParams['password']);

        if (!$sftpLogin) {
            $msg = 'Unable to login on sftp';
            $this->logger->error($msg, EventNameEnum::SECURITY_LOGOUT);
            throw new \Exception($msg);
        }

        return $sftp;
    }

    public function downloadFile(): ?string
    {
        $sftpParams = $this->sftpParams;

        try {
            $this->getBuyerImportSftp()->get(
                $sftpParams['sftp_filepath'],
                $sftpParams['local_filepath']
            );

            return $sftpParams['local_filepath'];
        } catch (\Exception $e) {
            $this->logger->error('Unable to fetch the file', $e->getMessage());
            throw new \Exception($e->getMessage());
        }
    }

    public function uploadTo(string $filename, string $xmlBody): bool
    {
        $sftpParams = $this->sftpEpsa;

        $sftp = new SFTP($sftpParams['host']);
        $sftpLogin = $sftp->login($sftpParams['user'], $sftpParams['password']);

        if (!$sftpLogin) {
            $msg = 'Unable to login on sftp';
            $this->logger->error($msg, EventNameEnum::SECURITY_LOGOUT);
        } else {
            try {
                $sftp->put(
                    $sftpParams['sftp_filepath'] . $filename,
                    $xmlBody
                );

                return true;
            } catch (\Exception $e) {
                $this->logger->error('Unable to put the file', $e->getMessage());
            }
        }
        return false;
    }
}
