<?php

namespace AppBundle\Services;

use AppBundle\Model\Merchant;
use Open\IzbergBundle\Api\KycApi;

class KycService
{
    private KycApi $kycApi;

    public function __construct(KycApi $kycApi)
    {
        $this->kycApi = $kycApi;
    }

    public function merchantHasGeneralSalesCondition(Merchant $merchant)
    {
        try {
            $generalSalesConditionKyc = $this->kycApi->findMerchantGeneralSalesCondition($merchant->getId());
        } catch (\Exception $e) {
            return false;
        }

        return ($generalSalesConditionKyc);
    }

    public function fetchMerchantGeneralSalesConditionUrl(int $merchantId): ?string
    {
        $generalSalesConditionKycList = $this->kycApi->findMerchantGeneralSalesCondition($merchantId);

        $generalSalesConditionKyc = array_shift($generalSalesConditionKycList);

        if (!$generalSalesConditionKyc) {
            return null;
        }

        if(!count($generalSalesConditionKyc->parts)){
            return null;
        }

        $kycPart = $generalSalesConditionKyc->parts[0];
        $kycPart = substr($kycPart, 0, -1);
        $kycPart = substr($kycPart, strrpos($kycPart, '/') + 1);
        $part = $this->kycApi->getKycPartById($kycPart);
        if(!$part){
            return null;
        }

        return $part->part;
    }
}
