<?php

namespace AppBundle\Services;

use AppBundle\Entity\OfferQuotationRequest;
use AppBundle\Entity\User;
use AppBundle\Repository\OfferQuotationRequestRepository;

class OfferQuotationService
{
    private OfferQuotationRequestRepository $offerQuotationRequestRepository;

    public function __construct(OfferQuotationRequestRepository $offerQuotationRequestRepository)
    {
        $this->offerQuotationRequestRepository = $offerQuotationRequestRepository;
    }

    public function askOfferQuotation(User $buyer, int $offerId): bool
    {
        $offerQuotationRequest = (new OfferQuotationRequest())
            ->setOfferId($offerId)
            ->setBuyer($buyer)
            ->setStatus(OfferQuotationRequest::STATUS_CREATED);

        $this->offerQuotationRequestRepository->save($offerQuotationRequest);

        return true;
    }
}
