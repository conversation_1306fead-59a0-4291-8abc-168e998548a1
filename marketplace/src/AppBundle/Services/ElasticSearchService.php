<?php


namespace AppBundle\Services;

use AppBundle\Entity\Country;
use AppBundle\Entity\MarketPlace;
use AppBundle\Entity\User;
use AppBundle\Model\Facet;
use AppBundle\Model\FacetValue;
use AppBundle\Model\Search\Filter\SearchFilter;
use AppBundle\Model\Search\Filter\SearchFilterExists;
use AppBundle\Model\Search\Filter\SearchFilterPath;
use AppBundle\Model\Search\Filter\SearchFilterQuery;
use AppBundle\Model\SearchRequest;
use AppBundle\Model\SearchResult;
use Elasticsearch\Client;
use Elasticsearch\ClientBuilder;

class ElasticSearchService implements SearchService
{
    public const AUTOCOMPLETE_ANALYZER = "autocomplete";
    public const CUSTOM_ANALYZER = "custom_analyzer_with_min";
    private const FACETS_VALUES_DEFAULT_SIZE = 100;
    private const MAX_HITS = 9900;
    private const RELEVANCE_SCORE_COEFFICIENT = 125;

    private Client $client;
    private string $index;
    private array $searchableFields;
    private string $offerIdField;
    private array $facetsFields;
    private IzbergCustomAttributes $izbergCustomAttributes;
    private FacetService $facetService;
    private ?string $branch;
    private string $epiFilterCategory;

    public function __construct(
        string $hostname,
        string $index,
        bool $ssl,
        string $certificate_path,
        array $searchableFields,
        array $facetsFields,
        string $epiFilterCategory,
        IzbergCustomAttributes $izbergCustomAttributes,
        FacetService $facetService
    ) {
        if ($ssl) {
            $this->client = ClientBuilder::create()
                ->setHosts([$hostname])
                ->setSSLVerification($certificate_path)
                ->build();
        } else {
            $this->client = ClientBuilder::create()->setHosts([$hostname])->build();
        }

        $this->index = $index;
        $this->searchableFields = $searchableFields;
        $this->facetsFields = $facetsFields;
        $this->offerIdField = '_id';
        $this->izbergCustomAttributes = $izbergCustomAttributes;
        $this->facetService = $facetService;
        $this->branch = null;
        $this->epiFilterCategory = $epiFilterCategory;
    }

    public function enableActiveOfferFilters(SearchFilterQuery $filterQuery): void
    {
        $activeOfferFilters = [
            (new SearchFilter('status', 'active')),
            (new SearchFilter('product.status', 'active')),
            (new SearchFilter('merchant.status', '10')),
        ];

        $filterQuery->addMust(...$activeOfferFilters);
    }

    public function enableActiveMerchantFilter(SearchFilterQuery $filterQuery): void
    {
        $activeMerchantFilter = [
            (new SearchFilter('merchant.status', '10')),
        ];

        $filterQuery->addMust(...$activeMerchantFilter);
    }

    public function enableMerchantBranchesFilter(SearchFilterQuery $filterQuery): SearchFilterQuery
    {
        $branchQuery = clone($filterQuery);

        $filterQuery->addMustNot(
            ...[
                (new SearchFilter('attributes.1193_offre_secondaire_devis', 'No')),
                (new SearchFilterExists('merchant.branches'))
            ]
        );

        if (!$this->branch) {
            return $filterQuery;
        }

        $branchQuery->addMust(
            ...[
                (new SearchFilter('merchant.branches', strtolower($this->branch)))
            ]
        );

        return (new SearchFilterQuery())
            ->addShould(
                $filterQuery,
                $branchQuery
            );
    }

    /**
     * enable filter for "Equipement de protections individuel"
     * @param SearchFilterQuery $filterQuery
     * @param User $user
     */
    private function enableSpecificCategoryFilter(SearchFilterQuery $filterQuery, User $user): void
    {

        $customFilter = [];
        $customFilter [] = 'attributes.' . $this->izbergCustomAttributes->getEpiInclusiveBranch();
        $customFilter [] = 'attributes.' . $this->izbergCustomAttributes->getEpiInclusiveOrganisation();
        $customFilter [] = 'attributes.' . $this->izbergCustomAttributes->getEpiExclusiveBranch();
        $customFilter [] = 'attributes.' . $this->izbergCustomAttributes->getEpiExclusiveOrganisation();
        $customFilter [] = 'attributes.' . $this->izbergCustomAttributes->getEpiInclusiveSite();
        $customFilter [] = 'attributes.' . $this->izbergCustomAttributes->getEpiExclusiveSite();
        $customFilter [] = 'attributes.' . $this->izbergCustomAttributes->getEpiInclusiveEntity();
        $customFilter [] = 'attributes.' . $this->izbergCustomAttributes->getEpiExclusiveEntity();
        $atleastOneExist = array_map(function ($key) {
            $searchExist = new SearchFilterQuery();
            $searchExist->addMust(...[(new SearchFilterExists($key))]);
            $searchExist->addMustNot(...[new SearchFilter($key . ".keyword", "")]);
            return $searchExist;
        }, $customFilter);
        $filterQuery->addShould(...$atleastOneExist);

    }

    private function filterEpi(SearchFilterQuery $filterQuery, User $user)
    {
        $mustQuery = new SearchFilterQuery();

        $this->addMustBeEqualsIfExist($mustQuery, 'attributes.' . $this->izbergCustomAttributes->getEpiInclusiveBranch(), $user->getBranch());
        $this->addMustBeginWithIfExist($mustQuery, 'attributes.' . $this->izbergCustomAttributes->getEpiInclusiveOrganisation(), $user->getOrganization());
        $this->addMustNotBeEqualsIfExist($mustQuery, 'attributes.' . $this->izbergCustomAttributes->getEpiExclusiveBranch(), $user->getBranch());
        $this->addMustNotBeginWithIfExist($mustQuery, 'attributes.' . $this->izbergCustomAttributes->getEpiExclusiveOrganisation(), $user->getOrganization());

        if (!is_null($user->getSite())) {
            $this->addMustBeEqualsIfExist($mustQuery, 'attributes.' . $this->izbergCustomAttributes->getEpiInclusiveSite(), $user->getSite()->getName());
            $this->addMustNotBeEqualsIfExist($mustQuery, 'attributes.' . $this->izbergCustomAttributes->getEpiExclusiveSite(), $user->getSite()->getName());
        }
        if (!is_null($user->getInvoiceEntity())) {
            $this->addMustBeEqualsIfExist($mustQuery, 'attributes.' . $this->izbergCustomAttributes->getEpiInclusiveEntity(), $user->getInvoiceEntity()->getName());
            $this->addMustNotBeEqualsIfExist($mustQuery, 'attributes.' . $this->izbergCustomAttributes->getEpiExclusiveEntity(), $user->getInvoiceEntity()->getName());
        }
        $filterQuery->addMust($mustQuery);

    }

    public function addMustBeEqualsIfExist(SearchFilterQuery $filterQuery, string $searchTerm, string $value)
    {

        $mustNotExistQuery = new SearchFilterQuery();
        $mustNotExistQuery->addMustNot(...[(new SearchFilterExists($searchTerm))]);
        $mustQuery = new SearchFilterQuery();
        $mustQuery->addMust(...[new SearchFilter($searchTerm, $value)]);

        $mustBeEmptyAlternative = new SearchFilterQuery();
        $mustBeEmptyAlternative->addMust(...[new SearchFilter($searchTerm, "")]);

        $shouldSubQuery = new SearchFilterQuery();
        $shouldSubQuery->addShould(
            $mustNotExistQuery,
            $mustQuery,
            $mustBeEmptyAlternative
        );
        return $filterQuery->addMust($shouldSubQuery);
    }

    private function addMustBeginWithIfExist(SearchFilterQuery $filterQuery, string $searchTerm, string $value, bool $negate = false)
    {

        $mustNotExistQuery = new SearchFilterQuery();
        $mustNotExistQuery->addMustNot(...[(new SearchFilterExists($searchTerm))]);
        $mustQuery = new SearchFilterQuery();

        if ($negate) {
            $mustQuery->addMustNot(...[new SearchFilterPath($searchTerm, $value)]);
        } else {
            $mustQuery->addMust(...[new SearchFilterPath($searchTerm, $value)]);
        }


        $mustBeEmptyAlternative = new SearchFilterQuery();
        $mustBeEmptyAlternative->addMust(...[new SearchFilter($searchTerm, "")]);

        $shouldSubQuery = new SearchFilterQuery();
        $shouldSubQuery->addShould(
            $mustNotExistQuery,
            $mustQuery,
            $mustBeEmptyAlternative
        );
        return $filterQuery->addMust($shouldSubQuery);
    }

    public function findSuggestions(array $body ) : array
    {
        return $this->client->msearch($body);
    }


    private function addMustNotBeginWithIfExist(SearchFilterQuery $filterQuery, string $searchTerm, string $value)
    {
        return $this->addMustBeginWithIfExist($filterQuery, $searchTerm, $value, true);
    }

    public function addMustNotBeEqualsIfExist(SearchFilterQuery $filterQuery, string $searchTerm, string $value)
    {
        $mustNotExistQuery = new SearchFilterQuery();
        $mustNotExistQuery->addMustNot(...[(new SearchFilterExists($searchTerm))]);
        $mustNotQuery = new SearchFilterQuery();
        $mustNotQuery->addMustNot(...[new SearchFilter($searchTerm, $value)]);

        $mustBeEmptyAlternative = new SearchFilterQuery();
        $mustBeEmptyAlternative->addMust(...[new SearchFilter($searchTerm, "")]);

        $shouldSubQuery = new SearchFilterQuery();
        $shouldSubQuery->addShould(
            $mustNotExistQuery,
            $mustNotQuery,
            $mustBeEmptyAlternative
        );
        return $filterQuery->addMust($shouldSubQuery);
    }

    public function enableFilterEquipment(SearchFilterQuery $filterQuery, $user): void
    {
        $mustNotQuery = new SearchFilterQuery();
        $mustNotQuery->addMustNot(...[new SearchFilter('product.application_categories', $this->epiFilterCategory)]);

        $mustQuery = new SearchFilterQuery();
        $this->enableSpecificCategoryFilter($mustQuery, $user);

        $filterQuery->addShould($mustNotQuery, $mustQuery);
        $this->filterEpi($filterQuery, $user);

    }


    public function enableCountryOfDeliveryFilter(SearchFilterQuery $filterQuery, Country $country): void
    {
        $filter = (new SearchFilter('attributes.' . $this->izbergCustomAttributes->getCountryOfDelivery(), $country->getIzbergCode()));

        $filterQuery->addMust($filter);
    }

    public function disableSecondaryQuoteOffers(SearchFilterQuery $filterQuery)
    {
        $filterQuery->addMustNot(
            new SearchFilter(
                sprintf('attributes.%s.keyword', $this->izbergCustomAttributes->getQuoteSecondaryOffer()), #term
                'Yes' #values
            )
        );
    }

    /**
     * Perform an elastic search request
     * @param SearchRequest $request
     * @param string $locale
     * @return SearchResult
     * @see SearchRequest for information about to build a request
     */
    public function search(SearchRequest $request, string $locale): SearchResult
    {
        //elastic search request parameters
        $params = [
            'index' => sprintf('%s_%s', $request->getMarketPlace()->getElasticSearchIndex(), $locale ?: '*'),
            'type' => '_doc',
            'size' => $request->getSize(),
            'from' => ($request->getPage() - 1) * $request->getSize(),
            "track_scores" => "true",
        ];

        //////////////////////////////////////////////////////////////////////////
        //body init
        //////////////////////////////////////////////////////////////////////////

        $body = [];

        //////////////////////////////////////////////////////////////////////////
        ///if request includes a full text search
        //////////////////////////////////////////////////////////////////////////
        if ($request->getSearch()) {
            $body['query']['bool']['should'] = [
                [
                    'simple_query_string' => [
                        'analyzer' => self::CUSTOM_ANALYZER,
                        'default_operator' => 'and',
                        'fields' => $this->searchableFields,
                        'query' => $request->getSearch()
                    ]
                ]
                ,
                [
                    'term' => [
                        'merchant.name.normalize' => [
                            'value' => $request->getSearch(),
                            'boost' => 100
                        ]
                    ]
                ]
            ];
            $body['query']['bool']['minimum_should_match'] = 1;
        }


        //////////////////////////////////////////////////////////////////////////
        //if filters are specified in the request, build and set filter request
        //////////////////////////////////////////////////////////////////////////

        if (!empty($request->getFilterQuery())) {
            $body['query']['bool']['filter'] = $request->getFilterQuery()->query();
        }

        //////////////////////////////////////////////////////////////////////////
        //sorting options
        //////////////////////////////////////////////////////////////////////////

        //always normal offer before Quote offer, and origin Quote offer before primary  (made by the marketplace).
        $body["sort"] = [
            'attributes.' . $this->izbergCustomAttributes->getPriceOnQuotation() . '.keyword' => ["missing" => "No", "order" => "asc"],
            'attributes.' . $this->izbergCustomAttributes->getQuoteSecondaryOffer() . '.keyword' => ["missing" => "AA", "order" => "asc", "unmapped_type" => "string"],
        ];

        // The search must always be by relevance, so we should set the sort by relevance score

        // Sort by price order if it's set
        if ($request->getSortField() !== null) {
            // Actually, search results order is based on a simple string field, for that we can't set multiple orders type
            // Sorting by relevance and by price ascending needs to set 2 orders (score and price) --> can't do it with simple string field
            // which explains the hard coded query

            if ($request->getSortField() == '_score') {
                $body["sort"] = array_merge($body["sort"], [
                    '_script' => [
                        "type" => "number",
                        "script" => [
                            "lang" => "painless",
                            "source" => sprintf("%s / %d", "_score.intValue()", self::RELEVANCE_SCORE_COEFFICIENT)
                        ],
                        "order" => "desc"
                    ],
                    'price_without_vat' => ["order" => "asc"]
                ]);
            } else {
                $body["sort"] = array_merge(
                    $body["sort"],
                    [
                        $request->getSortField()??'' => [
                            "order" => strtolower($request->getSortOrder()->getValue())
                        ]
                    ]);
            }
        }
        //////////////////////////////////////////////////////////////////////////
        //build and set aggregations request
        //////////////////////////////////////////////////////////////////////////
        if ($request->getAnalyzer() != self::AUTOCOMPLETE_ANALYZER) {
            if ($request->getFacets() !== null) {
                $aggregation = $request->getFacets();
            } else {
                $aggregation = $this->facetsFields;
            }

            array_walk($aggregation, function (&$value, $facet) {
                $value = ["terms" => [
                    "field" => $facet, "min_doc_count" => $value,
                    "size" => self::FACETS_VALUES_DEFAULT_SIZE
                ]];
            });

            $body["aggs"] = $aggregation;
        }

        //////////////////////////////////////////////////////////////////////////
        //set body request
        //////////////////////////////////////////////////////////////////////////
        $params['body'] = $body;

        //////////////////////////////////////////////////////////////////////////
        //perform request and return raw result
        //////////////////////////////////////////////////////////////////////////
        $indexerResult = $this->client->search($params);
        $result = new SearchResult();
        $result->setNbHits(($indexerResult["hits"]["total"]["value"] >= self::MAX_HITS) ? self::MAX_HITS : $indexerResult["hits"]["total"]["value"]);
        $result->setSize(count($indexerResult["hits"]["hits"]));
        if ($request->getSize() != 0) {
            $result->setNbPages(intval(ceil($result->getNbHits() / $request->getSize())));
        } else {
            $result->setNbPages(0);
        }
        $result->setHits($indexerResult["hits"]["hits"]);

        // We don't need facets for autocomplete
        if ($request->getAnalyzer() == self::CUSTOM_ANALYZER) {
            $result->setFacets($this->buildAggregations($indexerResult, $locale));
        }

        return $result;
    }

    /**
     * Perform an elastic search request
     * @param SearchRequest $request
     * @return SearchResult
     * @see SearchRequest for information about to build a request
     */
    public function searchMerchants(SearchRequest $request): SearchResult
    {
        //elastic search request parameters
        $params = ['size' => 0];

        //////////////////////////////////////////////////////////////////////////
        //body init
        //////////////////////////////////////////////////////////////////////////

        $body = [];

        //////////////////////////////////////////////////////////////////////////
        //if filters are specified in the request, build and set filter request
        //////////////////////////////////////////////////////////////////////////

        if (!empty($request->getFilterQuery())) {
            $body['query']['bool']['filter'] = $request->getFilterQuery()->query();
        }

        //////////////////////////////////////////////////////////////////////////
        //build and set aggregations request
        //////////////////////////////////////////////////////////////////////////

        $aggregation = [
            "merchant" =>[
                "filter"=>[
                    "match"=>[
                        "merchant.name.text"=>[
                            "query"=>$request->getSearch(),
                            "analyzer"=>"standard"
                        ]
                    ]
                ],
                "aggs"=>[
                    "merchant.id"=>[
                        "terms"=>[
                            "field"=>"merchant.id",
                            "min_doc_count"=>1,
                            "size"=>self::FACETS_VALUES_DEFAULT_SIZE
                        ],
                        "aggs"=>[
                            "merchant.name"=>[
                                "terms"=>[
                                    "field"=>"merchant.name",
                                    "min_doc_count"=>1,
                                    "size"=>self::FACETS_VALUES_DEFAULT_SIZE
                                ]
                            ]
                        ],

                    ]
                ]
            ]
        ];
        $body["aggs"] = $aggregation;

        //////////////////////////////////////////////////////////////////////////
        //set body request
        //////////////////////////////////////////////////////////////////////////
        $params['body'] = $body;

        //////////////////////////////////////////////////////////////////////////
        //perform request and return raw result
        //////////////////////////////////////////////////////////////////////////
        $indexerResult = $this->client->search($params);
        $result = new SearchResult();
        $result->setSize(count($indexerResult["aggregations"]["merchant"]["merchant.id"]["buckets"]));
        $result->setNbHits(count($indexerResult["aggregations"]["merchant"]["merchant.id"]["buckets"]));
        $result->setHits($indexerResult["aggregations"]["merchant"]["merchant.id"]["buckets"]);

        return $result;
    }

    public function scan($size, $scroll, MarketPlace $marketPlace, string $locale, array $query = null): SearchResult
    {


        if (is_null($query)) {
            $queryTmp = [
                'match_all' => new \stdClass()
            ];
        } else {
            $queryTmp = $query;
        }

        $params = [
            'index' => sprintf('%s_%s', $marketPlace->getElasticSearchIndex(), $locale),
            'scroll' => $scroll,          // how long between scroll requests. should be small!
            'size' => $size,             // how many results *per shard* you want back
            'body' => [
                'query' => $queryTmp
            ]
        ];
        $response = $this->client->search($params);

        $result = new SearchResult();
        $result->setNbHits(($response["hits"]["total"]["value"] >= self::MAX_HITS) ? self::MAX_HITS : $response["hits"]["total"]["value"]);
        $result->setSize(count($response["hits"]["hits"]));
        $result->setNbPages(0);
        $result->setHits($response["hits"]["hits"]);
        $result->setScrollId($response['_scroll_id']);

        return $result;
    }

    public function scroll($scroll, $scrollId): SearchResult
    {

        //doesn't seem to work without body as seen in doc and internet example
        $params = [

            'body' => [
                'scroll_id' => $scrollId,  //...using our previously obtained _scroll_id
                'scroll' => $scroll        // and the same timeout window
            ]
        ];
        $response = $this->client->scroll($params);

        $result = new SearchResult();
        $result->setNbHits(($response["hits"]["total"]["value"] >= self::MAX_HITS) ? self::MAX_HITS : $response["hits"]["total"]["value"]);
        $result->setSize(count($response["hits"]["hits"]));
        $result->setNbPages(0);
        $result->setHits($response["hits"]["hits"]);

        // You must always refresh your _scroll_id!  It can change sometimes
        $result->setScrollId($response['_scroll_id']);

        return $result;
    }


    /**
     * update data in the search service
     * @param array $data data to index
     */
    public function update(array $data): void
    {
        $existsParams = [
            'id' => $data['id'],
            'index' => $data['index'],
            'type' => $data['type'],
        ];

        if ($this->client->exists($existsParams)) {
            $this->client->update($data);
        }
    }


    /**
     * @inheritdoc
     */
    public function findDistinctValuesForField(string $fieldName, MarketPlace $marketPlace, ?SearchFilterQuery $filterQuery = null): array
    {
        //we perform a aggregation request
        $result = $this->search(new SearchRequest(null, 1, 0, ElasticSearchService::CUSTOM_ANALYZER, null, null, $filterQuery, [$fieldName => 1], $marketPlace), "fr");
        $values = [];

        if (count($result->getFacets()) === 1) {
            /** @var Facet $facet */
            $facet = $result->getFacets()[0];

            /** @var FacetValue $facetValue */
            foreach ($facet->getValues() as $facetValue) {
                $values [] = $facetValue->getValue();
            }
        }
        return $values;
    }

    /**
     * @return Client
     */
    public function getClient(): Client
    {
        return $this->client;
    }

    /**
     * @param Client $client
     * @return ElasticSearchService
     */
    public function setClient(Client $client): ElasticSearchService
    {
        $this->client = $client;
        return $this;
    }


    /**
     * @return array
     */
    public function getSearchableFields(): array
    {
        return $this->searchableFields;
    }

    /**
     * @param array $searchableFields
     * @return ElasticSearchService
     */
    public function setSearchableFields(array $searchableFields): ElasticSearchService
    {
        $this->searchableFields = $searchableFields;
        return $this;
    }

    /**
     * @return array
     */
    public function getFacetsFields(): array
    {
        return $this->facetsFields;
    }

    /**
     * @param array $facetsFields
     * @return ElasticSearchService
     */
    public function setFacetsFields(array $facetsFields): ElasticSearchService
    {
        $this->facetsFields = $facetsFields;
        return $this;
    }

    public function findOfferUsingOfferId(int $offerId, MarketPlace $marketPlace, string $locale): array
    {
        //elastic search request parameters
        $params = [
            'index' => sprintf('%s_%s', $marketPlace->getElasticSearchIndex(), $locale),
            'type' => '_doc',
            'size' => 1,
        ];

        //////////////////////////////////////////////////////////////////////////
        //body init
        //////////////////////////////////////////////////////////////////////////
        $body = [];
        $filterQuery = new SearchFilterQuery();
        $this->enableActiveOfferFilters($filterQuery);
        $this->disableSecondaryQuoteOffers($filterQuery);

        $body['query'] = [
            'bool' => [
                'must' => [
                    'multi_match' => [
                        'query' => $offerId,
                        'fields' => [$this->offerIdField]
                    ],
                ],
                'filter' => $filterQuery->query()
            ]
        ];

        //////////////////////////////////////////////////////////////////////////
        //set body request
        //////////////////////////////////////////////////////////////////////////
        $params['body'] = $body;

        //////////////////////////////////////////////////////////////////////////
        //perform request and return raw result
        //////////////////////////////////////////////////////////////////////////
        return $this->client->search($params)['hits']['hits'][0] ?? [];
    }

    public function findOffersUsingOfferIds(array $offerIds, MarketPlace $marketPlace, string $locale): array
    {
        //elastic search request parameters
        $params = [
            'index' => sprintf('%s_%s', $marketPlace->getElasticSearchIndex(), $marketPlace->getLocale()),
            'type' => '_doc',
            'size' => count($offerIds),
        ];

        //////////////////////////////////////////////////////////////////////////
        //body init
        //////////////////////////////////////////////////////////////////////////
        $body = [];

        $body['query'] = [
            "ids" => [
                'values' => $offerIds,
            ]
        ];

        //////////////////////////////////////////////////////////////////////////
        //set body request
        //////////////////////////////////////////////////////////////////////////
        $params['body'] = $body;

        //////////////////////////////////////////////////////////////////////////
        //perform request and return raw result
        //////////////////////////////////////////////////////////////////////////
        return $this->client->search($params)['hits']['hits'] ?? [];
    }

    public function setBranch(?string $branch): self
    {
        $this->branch = $branch;
        return $this;
    }

    private function buildAggregations(array $indexerResult, $locale): array
    {
        $facets = [];
        foreach ($indexerResult["aggregations"] as $fieldName => $aggregation) {

            $facet = new Facet();
            $facet->setFieldName($fieldName);
            $facet->setValues(array_map(function ($bucket) {
                $facetValue = new FacetValue();
                $facetValue->setHits($bucket['doc_count']);
                $facetValue->setValue($bucket['key']);
                $facetValue->setLabel($bucket['key']);
                return $facetValue;
            }, $aggregation["buckets"]));

            $this->facetService->buildFacet($facet, $locale);

            $facets [] = $facet;
        }

        return $facets;
    }

}
