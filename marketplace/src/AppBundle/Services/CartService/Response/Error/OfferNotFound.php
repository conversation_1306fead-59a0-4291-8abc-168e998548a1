<?php
namespace AppBundle\Services\CartService\Response\Error;

use AppBundle\Services\Response\Error;

class OfferNotFound extends Error
{
    public const ERROR_NOT_FOUND = 'offer_not_found';

    private $offerId;

    public function __construct(int $offerId)
    {
        $this->offerId = $offerId;
        $this->id = self::ERROR_NOT_FOUND;
    }

    /**
     * @return int
     */
    public function getOfferId(): int
    {
        return $this->offerId;
    }
}
