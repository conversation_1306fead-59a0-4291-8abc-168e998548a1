<?php

namespace AppBundle\Services;

use AppBundle\Entity\CxmlConf;
use AppBundle\Repository\CxmlConfRepository;
use AppBundle\Services\Export\CxmlExportInterface;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use phpseclib3\Crypt\Common\PrivateKey;
use phpseclib3\Crypt\PublicKeyLoader;
use phpseclib3\Net\SFTP;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Filesystem\Exception\IOException;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\Finder\Finder;
use Symfony\Component\Finder\SplFileInfo;

class CxmlExportService implements CxmlExportInterface
{
    private string $baseDirectory;
    private string $doneDirectory;
    private string $errorDirectory;
    private LogService $logger;
    private EventDispatcherInterface $eventDispatcher;

    private int $sftpTimeout;

    private string $logRequestID;

    private CxmlConfRepository $cxmlConfRepository;

    public function __construct(
        int $sftpTimeout,
        string $cxmlBaseDirectory,
        LogService $logger,
        EventDispatcherInterface $eventDispatcher,
        CxmlConfRepository $cxmlConfRepository
    ) {
        $this->baseDirectory = $cxmlBaseDirectory;
        $this->logger = $logger;
        $this->eventDispatcher = $eventDispatcher;
        $this->initDirectories();
        $this->sftpTimeout = $sftpTimeout;
        $this->cxmlConfRepository = $cxmlConfRepository;
    }

    public function store(int $merchantId, string $cxml, string $uniqId)
    {
        $filesystem = new Filesystem();
        $merchantDir = $this->getMerchantOrderDir($merchantId);
        $filename = $uniqId . "-order.xml";
        $filenamePath = $merchantDir . "/" . $filename;
        $this->logger->info("write cxml to :" . $filenamePath, EventNameEnum::CXML_EXPORT, null);
        try {
            $filesystem->dumpFile($filenamePath, $cxml);
        } catch (IOException $ex) {
            $this->logger->error("can't write cxml to file", EventNameEnum::CXML_EXPORT, null, ["filename" => $filename, "error" => $ex->getMessage()]);
            return false;
        }

        if ($filesystem->exists($filenamePath)) {
            $this->logger->info("file is present on disk:" . $filenamePath, EventNameEnum::CXML_EXPORT, null);
            return true;
        }
        return false;
    }

    public function send(MerchantOrderService $merchantOrderService)
    {
        $this->logRequestID = uniqid();

        $this->logger->info('Send orders start', EventNameEnum::SFTP_SEND_ORDER . '_START', null, ['logRequestID' => $this->logRequestID]);

        $cxmlConfCount = $this->cxmlConfRepository->count([]);
        $limit = 100;
        $offset = 0;
        while ($cxmlConfCount > $offset) {
            $cxmlConfs = $this->cxmlConfRepository->getAll($limit, $offset);
            foreach ($cxmlConfs as $cxmlConf) {
                try {
                    $this->sendFtp($cxmlConf, $merchantOrderService);
                } catch (\Throwable $t) {
                    $this->logger->error('SFTP exception: ' . $t->__toString(), EventNameEnum::SFTP_SEND_ORDER, null, ['logRequestID' => $this->logRequestID]);
                }
            }
            $offset += $limit;
        }
    }

    private function sendFtp(CxmlConf $cxmlConf, MerchantOrderService $merchantOrderService)
    {
        $merchantID = $cxmlConf->getMerchantId();
        
        $this->logger->info('Looking for order files for merchant ID: ' . $merchantID . '. cxmlConf ID: ' . $cxmlConf->getId(), EventNameEnum::SFTP_SEND_ORDER, null, ['logRequestID' => $this->logRequestID]);
        
        $merchantDir = $this->getMerchantOrderDir($merchantID);

        if (!file_exists($merchantDir) || !is_dir($merchantDir)) {
            $this->logger->error('Merchant directory not exists: ' . $merchantDir . '. Merchant ID: ' . $merchantID, EventNameEnum::SFTP_SEND_ORDER, null, ['logRequestID' => $this->logRequestID]);
            
            return false;
        }

        $finder = new Finder();
        $finder->files()->in($merchantDir);

        if (!$finder->hasResults()) {
            // No file to send
            $this->logger->info('Merchant directory empty: ' . $merchantDir . '. Merchant ID: ' . $merchantID, EventNameEnum::SFTP_SEND_ORDER, null, ['logRequestID' => $this->logRequestID]);

            return true;
        }

        $filesList = [];
        foreach ($finder as $file) {
            $filesList[] = $file->getRelativePathname();
        }

        $this->logger->info('Found ' . $finder->count() . ' files in merchant directory: ' . $merchantDir . '. Files list: ' . implode('; ', $filesList), EventNameEnum::SFTP_SEND_ORDER, null, ['logRequestID' => $this->logRequestID]);

        $sftp = new SFTP($cxmlConf->getSftpHostname(), $cxmlConf->getSftpPort() ?? 22);
        $sftp->setTimeout($this->sftpTimeout);
        $sftpLogin = $this->ftpLogin($sftp, $cxmlConf);

        if (!$sftpLogin) {
            $this->logger->error('Error login to SFTP for merchant ID: ' . $merchantID, EventNameEnum::SFTP_SEND_ORDER, null, ['logRequestID' => $this->logRequestID]);

            $merchantOrderService->sendFtpError($merchantID, $this->getFirstFile($finder)->getRelativePathname());
            $this->moveAllFileToError($finder);

            return false;
        }

        $serverDir = $cxmlConf->getSftpDir();
        if ($serverDir) {
            $serverDir = $serverDir . "/";
        }

        foreach ($finder as $file) {
            $this->logger->info('SFTP sending to: ' . $serverDir . $file->getRelativePathname() . '. File: ' . $file->getRealPath() . '. Order ID: ' . $merchantOrderService->getOrderIdFromFilename($file->getRelativePathname()), EventNameEnum::SFTP_SEND_ORDER, null, ['logRequestID' => $this->logRequestID]);

            if ($sftp->put($serverDir . $file->getRelativePathname(), $file->getRealPath(), SFTP::SOURCE_LOCAL_FILE)) {
                $this->logger->info("SFTP success for: " . $file->getRelativePathname(), EventNameEnum::SFTP_SEND_ORDER, null, ['logRequestID' => $this->logRequestID]);

                $this->moveFileToDone($file);
            } else {
                $this->logger->info("SFTP error for: " . $file->getRelativePathname(), EventNameEnum::SFTP_SEND_ORDER, null, ['logRequestID' => $this->logRequestID]);

                $merchantOrderService->sendFtpError($merchantID, $file->getRelativePathname());

                $this->moveFileToError($file);
            }
        }

        return true;
    }

    private function moveAllFileToError(Finder $finder)
    {
        foreach ($finder as $file) {
            $this->moveFileToError($file);
        }
    }

    private function ftpLogin(SFTP $sftp, CxmlConf $cxmlConf)
    {
        $merchantId = $cxmlConf->getMerchantId();
        if ($cxmlConf->getSftpRsa()) {
            return $this->ftpLoginWithRsa($sftp, $cxmlConf);
        }
        try {
            return $sftp->login($cxmlConf->getSftpLogin(), $cxmlConf->getSftpPassword());
        } catch (\Exception $exception) {
            $this->logger->error("error connecting to merchant ftp :" . $merchantId, EventNameEnum::SFTP_SEND_ORDER, null, array("error" => $exception->getMessage()));
        }
        return false;
    }

    private function ftpLoginWithRsa(SFTP $sftp, CxmlConf $cxmlConf)
    {
        $merchantId = $cxmlConf->getMerchantId();
        try {
            $password = $cxmlConf->getSftpRsaPassword();
            $keyFileName = $cxmlConf->getSftpRsa();
            $loadedKey = PublicKeyLoader::load(file_get_contents($keyFileName), $password);
            if (!$loadedKey) {
                $this->logger->error("error loading rsa key for :" . $merchantId, EventNameEnum::SFTP_SEND_ORDER, null, array("key_path" => $keyFileName));
                return false;
            }
            /** @var PrivateKey $loadedKey */
            if (!$sftp->login($cxmlConf->getSftpLogin(), $loadedKey)) {
                return false;
            }
            return true;
        } catch (\Exception $exception) {
            $this->logger->error("error connecting to merchant ftp :" . $merchantId, EventNameEnum::SFTP_SEND_ORDER, null, array("error" => $exception->getMessage()));
        }
        return false;
    }

    private function getFirstFile(Finder $finder)
    {
        // get first file, to have an order id, to send mail
        $iterator = $finder->getIterator();
        $iterator->rewind();
        return $iterator->current();
    }

    private function getMerchantOrderDir($merchantId): string
    {
        return $this->baseDirectory . "/" . $merchantId;
    }

    private function moveFileToDone(SplFileInfo $fileInfo)
    {
        $filesystem = new Filesystem();
        $filesystem->rename($fileInfo->getRealPath(), $this->doneDirectory . '/' . $fileInfo->getRelativePathname());
    }

    private function moveFileToError(SplFileInfo $fileInfo)
    {
        $filesystem = new Filesystem();
        $filesystem->rename($fileInfo->getRealPath(), $this->errorDirectory . '/' . $fileInfo->getRelativePathname());
    }

    private function initDirectories()
    {
        $this->doneDirectory = $this->baseDirectory . '/done';
        $this->errorDirectory = $this->baseDirectory . '/error';
        $this->initDirectory($this->doneDirectory);
        $this->initDirectory($this->errorDirectory);
    }

    private function initDirectory(string $dir)
    {
        $filesystem = new Filesystem();
        if (!$filesystem->exists($dir)) {
            $filesystem->mkdir($dir);
        }
    }
}
