<?php

namespace AppBundle\Services;

use AppBundle\Entity\MerchantOrder;
use AppBundle\Entity\MetaCart;
use AppBundle\Entity\MetaCartCheckout;
use AppBundle\Entity\User;
use AppBundle\Factory\CartFactory;
use AppBundle\Model\Merchant;
use AppBundle\Repository\MetaCartCheckoutRepository;
use AppBundle\Repository\MetaCartRepository;
use Open\IzbergBundle\Api\CartApi;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;

class CartCheckoutService
{
    private CartService $cartService;
    private BuyerService $buyerService;
    private MerchantService $merchantService;
    private OrderService $orderService;
    private MetaCartRepository $metaCartRepository;
    private MetaCartCheckoutRepository $metaCartCheckoutRepository;
    private CartApi $cartApi;
    private CartAddressService $cartAddressService;
    private CartFactory $cartFactory;
    private MailService $mailService;
    private LogService $logger;

    public function __construct(
        CartService $cartService,
        BuyerService $buyerService,
        MerchantService $merchantService,
        OrderService $orderService,
        MetaCartRepository $metaCartRepository,
        MetaCartCheckoutRepository $metaCartCheckoutRepository,
        CartApi $cartApi,
        CartAddressService $cartAddressService,
        CartFactory $cartFactory,
        MailService $mailService,
        LogService $logger
    ) {
        $this->cartService = $cartService;
        $this->buyerService = $buyerService;
        $this->merchantService = $merchantService;
        $this->orderService = $orderService;
        $this->metaCartRepository = $metaCartRepository;
        $this->metaCartCheckoutRepository = $metaCartCheckoutRepository;
        $this->cartApi = $cartApi;
        $this->cartAddressService = $cartAddressService;
        $this->cartFactory = $cartFactory;
        $this->mailService = $mailService;
        $this->logger = $logger;
    }

    public function checkoutCart(User $buyer, int $cartId, bool $autoValidation = false): MetaCartCheckout
    {
        $metaCart = $this->metaCartRepository->findBuyerMetaCart($buyer, $cartId);

        // Check if checkout has already been done
        $metaCartCheckout = $this->metaCartCheckoutRepository->findSuccessFulCheckoutByMetaCart($metaCart);

        if ($metaCartCheckout) {
            return $metaCartCheckout;
        }

        $manager = $this->buyerService->getBuyerSuperior($buyer);

        // Change status Checking out
        $metaCart->setStatus(MetaCart::STATUS_ORDERING);
        $metaCart->setIsCurrent(false);

        $metaCartCheckout = new MetaCartCheckout($metaCart, $manager);
        $this->metaCartCheckoutRepository->save($metaCartCheckout);

        if (!$this->processCartCheckout($metaCartCheckout)) {
            // Change status Checking out
            $metaCart->setStatus(MetaCart::STATUS_CREATED);
            $metaCart->setIsCurrent(true);

        }
        $metaCart->setAutoValidation($autoValidation);
        $this->metaCartRepository->save($metaCart);
        return $metaCartCheckout;
    }

    public function processCartCheckout(MetaCartCheckout $metaCartCheckout): bool
    {
        if ($metaCartCheckout->getState() !== MetaCartCheckout::STATE_PENDING) {
            return false;
        }

        $metaCartCheckout->setState(MetaCartCheckout::STATE_RUNNING);
        $this->metaCartCheckoutRepository->save($metaCartCheckout);

        $cart = $this->cartFactory->buildCartFromMetaCart($metaCartCheckout->getMetaCart());
        if ($cart->isTotalLimitOver()) {
            $this->logger->error('Cart limit is over', EventNameEnum::CART);
            return false;
        }

        // todo check merchant mandatory comments
        // todo check gift (if gift enabled then at least one item should be selected as gift)

        if (!$this->cartAddressService->configureShipping($metaCartCheckout->getMetaCart())) {
            $metaCartCheckout->setState(MetaCartCheckout::STATE_FAILED);
            $this->metaCartCheckoutRepository->save($metaCartCheckout);
            $this->logger->error('Cart shipping address update failed', EventNameEnum::CART);
            return false;
        }

        if (!$this->createMetaCartOrders($metaCartCheckout->getMetaCart())) {
            $metaCartCheckout->setState(MetaCartCheckout::STATE_FAILED);
            $this->metaCartCheckoutRepository->save($metaCartCheckout);
            $this->logger->error('Cart order processed failed', EventNameEnum::CART);
            return false;
        }

        if (
        !$this->assignMetaCartToManagerForValidation(
            $metaCartCheckout->getManager(),
            $metaCartCheckout->getMetaCart()
        )
        ) {
            $metaCartCheckout->setState(MetaCartCheckout::STATE_FAILED);
            $this->metaCartCheckoutRepository->save($metaCartCheckout);
            $this->logger->error('Cart manager assignation failed', EventNameEnum::CART);
            return false;
        }

        $metaCartCheckout->setState(MetaCartCheckout::STATE_FINISHED);
        $this->metaCartCheckoutRepository->save($metaCartCheckout);

        return true;
    }

    /**
     * @param User $managerOrDelegate
     * @param int $cartId
     * @return array|null
     */
    public function acceptCart(User $managerOrDelegate, int $cartId): ?array
    {
        $metaCart = $this->metaCartRepository->findOneBy([
            'buyerManager' => $this->getManagerFromManagerOrDelegate($managerOrDelegate),
            'id' => $cartId,
            'status' => MetaCart::STATUS_ASSIGNED,
        ]);

        if (!$metaCart instanceof MetaCart) {
            return null;
        }

        $acceptMerchantOrderSucceed = false;
        /** @var MerchantOrder $merchantOrder */
        foreach ($metaCart->getMerchantOrders() as $merchantOrder) {
            $acceptMerchantOrder = $this->orderService->acceptMerchantOrder($merchantOrder);
            $acceptMerchantOrderSucceed = $acceptMerchantOrderSucceed || $acceptMerchantOrder;
        }

        if (!$acceptMerchantOrderSucceed) {
            return null;
        }

        $metaCart->setStatus(MetaCart::STATUS_ORDERED);
        $metaCart->setValidatedBy($managerOrDelegate);

        $this->logger->info(
            sprintf(
                'Metacart %d accept by %s as %s',
                $metaCart->getId(),
                $managerOrDelegate->getUsername(),
                $managerOrDelegate === $metaCart->getBuyerManager() ? 'manager' : 'delegate of ' . $metaCart->getBuyerManager()->getUsername()
            ),
            'EVENT_CART_VALIDATION'
        );

        $metaCart->setValidatedAt(new \DateTimeImmutable());

        $this->metaCartRepository->save($metaCart);
        $merchantOrders = $metaCart->getMerchantOrders()->toArray();
        $merchantOrderIds = array_map(
            static function(MerchantOrder $item) {
                return [ 'id' => $item->getId() ];
            },
            $merchantOrders
        );


        return $merchantOrderIds;
    }

    public function hasCartAcceptedByDelegate(User $managerOrDelegate, int $cartId): bool {
        $metaCart = $this->metaCartRepository->findOneBy([
            'buyerManager' => $this->getManagerFromManagerOrDelegate($managerOrDelegate),
            'validatedBy' => $this->getManagerFromManagerOrDelegate($managerOrDelegate),
            'id' => $cartId,
            'status' => MetaCart::STATUS_ORDERED,
        ]);

        return empty($metaCart);
    }

    public function rejectCart(User $managerOrDelegate, int $cartId, string $reason): bool
    {
        $metaCart = $this->metaCartRepository->findOneBy([
            'buyerManager' => $this->getManagerFromManagerOrDelegate($managerOrDelegate),
            'id' => $cartId,
            'status' => MetaCart::STATUS_ASSIGNED,
        ]);

        if (!$metaCart instanceof MetaCart) {
            return false;
        }

        $rejectMerchantOrderSucceed = true;
        /** @var MerchantOrder $merchantOrder */
        foreach ($metaCart->getMerchantOrders() as $merchantOrder) {
            $rejectMerchantOrderSucceed = $rejectMerchantOrderSucceed && $this->orderService->rejectMerchantOrder($merchantOrder, $managerOrDelegate);
        }

        if (!$rejectMerchantOrderSucceed) {
            return false;
        }

        $metaCart->setStatus(MetaCart::STATUS_REJECTED);
        $metaCart->setValidatedBy($managerOrDelegate);

        $this->logger->info(
            sprintf(
                'Metacart %d rejected by %s as %s',
                $metaCart->getId(),
                $managerOrDelegate->getUsername(),
                $managerOrDelegate === $metaCart->getBuyerManager() ? 'manager' : 'delegate of ' . $metaCart->getBuyerManager()->getUsername()
            ),
            LogService::EVENT_CART_VALIDATION
        );


        $metaCart->setValidatedAt(new \DateTimeImmutable());
        $metaCart->setRejectReason($reason);

        $this->metaCartRepository->save($metaCart);

        return true;
    }

    public function cancelOrder (User $user, int $cartId , MerchantOrder $merchantOrder,string $reason){


        $needAMerchantMail = $merchantOrder->isPendingForSupplierValidation();
        $merchantEntity = $this->merchantService->findMerchantEntityByIzbergId($merchantOrder->getMerchantId());
        $cancelMerchantOrderSucceed = $this->orderService->cancelMerchantOrder($merchantOrder, $user);

        if($cancelMerchantOrderSucceed && $needAMerchantMail) {
            //cart was already ordered so a mail is sent to merchant
            /** @var Merchant $merchant * */
            $merchant = $this->merchantService->findMerchantById($merchantOrder->getMerchantId());

            $contactList = $this->merchantService->buildNotificationContacts($merchant, MailService::CART_CANCELLED_TO_MERCHANT);
            $this->mailService->sendEmailMessage(
                MailService::CART_CANCELLED_TO_MERCHANT,
                $merchantEntity->getLanguage(),
                $contactList,
                [
                    'firstName' => $merchant->getMainContactFirstName(),
                    'lastName' => $merchant->getMainContactLastName(),
                    'orderNumber' => $merchantOrder->getOrderId(),
                    'reason' => $reason,
                    'merchantSlug'=> $merchant->getSlug(),
                    'userFirstName' => $user->getFirstname(),
                    'userLastName' => $user->getLastname(),
                    'orderId' => $merchantOrder->getId()
                ],
                null,
                null,
                []
            );

            $usersToNotify = $this->buyerService->getValidator($user);
            /** @var User $managerOrDelegate */
            foreach ($usersToNotify as $managerOrDelegate) {

                if ($managerOrDelegate instanceOf User) {

                    $this->mailService->sendEmailMessage(
                        MailService::CART_CANCELLED_TO_MANAGER_OR_DELEGATE,
                        $user->getLocale(),
                        $managerOrDelegate->getEmail(),
                        [
                            'firstName' => $user->getFirstname(),
                            'lastName' => $user->getLastname(),
                            'firstNameValidator' => $managerOrDelegate->getFirstname(),
                            'lastNameValidator' => $managerOrDelegate->getLastname(),
                            'orderNumber' => $merchantOrder->getOrderId(),
                            'cartNumber' => '',
                            'reason' => $reason,
                        ]
                    );

                }
            }

            $this->mailService->sendEmailMessage(
                MailService::CART_CANCELLED_TO_BUYER,
                $user->getLocale(),
                $user->getEmail(),
                [
                    'firstName' => $user->getFirstname(),
                    'lastName' => $user->getLastname(),
                    'orderNumber' => $merchantOrder->getOrderId(),
                    'reason' => $reason

                ]
            );
        }

        /** @var MetaCart $metaCart */
        $metaCart = $this->metaCartRepository->findBuyerMetaCart($user,$cartId );

        if($this->checkMetaCartIsCancelled($metaCart)){

            $this->cancelCartEntity($metaCart,$user,$reason);

        }

        return $cancelMerchantOrderSucceed;

    }

    public function cancelCart(User $user, int $cartId , string $reason): bool
    {

        $metaCart = $this->metaCartRepository->findBuyerMetaCart($user,$cartId );

        if (!$metaCart->getStatus()==MetaCart::STATUS_ASSIGNED) {
            return false;
        }
        $cancelMerchantOrderSucceed = true;

        foreach ($metaCart->getMerchantOrders() as $merchantOrder) {
            $cancelMerchantOrderSucceed = $cancelMerchantOrderSucceed && $this->orderService->rejectMerchantOrder($merchantOrder, $user);
        }


        if (!$cancelMerchantOrderSucceed) {
            return false;
        }

        // cancel before validator's validation, we send a mail to them
        if($metaCart->getStatus()==MetaCart::STATUS_ASSIGNED){

            $buyer = $metaCart->getBuyer();
            $usersToNotify = $this->buyerService->getValidator($user);
            /** @var User $managerOrDelegate */
            foreach ($usersToNotify as $managerOrDelegate) {

                if ($managerOrDelegate instanceOf User) {

                    $this->mailService->sendEmailMessage(
                        MailService::CART_CANCELLED_TO_MANAGER_OR_DELEGATE,
                        $buyer->getLocale(),
                        $managerOrDelegate->getEmail(),
                        [
                            'firstName' => $buyer->getFirstname(),
                            'lastName' => $buyer->getLastname(),
                            'firstNameValidator' => $managerOrDelegate->getFirstname(),
                            'lastNameValidator' => $managerOrDelegate->getLastname(),
                            'orderNumber' => '',
                            'cartNumber' => $cartId,
                            'reason' => $reason,
                        ]
                    );

                }
            }
        }


        $metaCart->setStatus(MetaCart::STATUS_CANCELLED);
        $metaCart->setCancelReason($reason);
        $metaCart->setValidatedBy($user);

        $this->logger->info(
            sprintf(
                'Metacart %d cancelled by %s',
                $metaCart->getId(),
                $user->getUsername()
            ),
            LogService::EVENT_CART_VALIDATION
        );

        $this->mailService->sendEmailMessage(
            MailService::CART_CANCELLED_TO_BUYER,
            $buyer->getLocale(),
            $buyer->getEmail(),
            [
                'firstName' => $buyer->getFirstname(),
                'lastName' => $buyer->getLastname(),
                'cartNumber' => $cartId,
                'reason' => $reason,
            ]
        );

        $metaCart->setValidatedAt(new \DateTimeImmutable());

        $this->metaCartRepository->save($metaCart);

        return true;
    }

    public function hasCartRejectedByDelegate(User $managerOrDelegate, int $cartId): bool {
        $metaCart = $this->metaCartRepository->findOneBy([
            'buyerManager' => $this->getManagerFromManagerOrDelegate($managerOrDelegate),
            'validatedBy' => $this->getManagerFromManagerOrDelegate($managerOrDelegate),
            'id' => $cartId,
            'status' => MetaCart::STATUS_REJECTED,
        ]);

        return empty($metaCart);
    }

    private function createMetaCartOrders(MetaCart $metaCart): bool
    {
        $orderCreationFailed = false;
        $currencyCarts = $metaCart->getCarts();

        /** @var \AppBundle\Entity\Cart $currencyCart */
        foreach ($currencyCarts as $currencyCart) {
            if (!$this->orderService->createCurrencyCartOrder($currencyCart, $metaCart)) {
                $orderCreationFailed = true;
                break;
            }
        }

        if ($orderCreationFailed) {
            $this->abandonMetaCartOrders($metaCart);
            return false;
        }

        return true;
    }

    private function assignMetaCartToManagerForValidation(User $manager, MetaCart $metaCart): bool
    {
        try {
            $metaCart->setBuyerManager($manager);
            // change cart status to "assigned"
            $metaCart->setStatus(MetaCart::STATUS_ASSIGNED);
            $metaCart->setOrderCreatedAt(new \DateTimeImmutable());

            $this->metaCartRepository->save($metaCart);

        } catch (\Exception $exception) {
            return false;
        }

        return true;
    }

    private function abandonMetaCartOrders(MetaCart $metaCart): bool
    {
        // TODO
        return true;
    }

    private function getManagerFromManagerOrDelegate(User $managerOrDelegate): array {
        // $managerOrDelegate is manager, add $managerOrDelegate
        // Get managers who $managerOrDelegate have delegation

        $managerDelegation = $this->buyerService->getManagerWhoIAmDelegate($managerOrDelegate);
        return array_merge(
            [ $managerOrDelegate ],
            $managerDelegation
        );
    }

    private function checkMetaCartIsCancelled (MetaCart $metaCart ){


        /** @var MerchantOrder $merchantOrder */
        foreach($metaCart->getMerchantOrders() as $merchantOrder){
            if (!($merchantOrder->isCancelled() || $merchantOrder->isAbandoned())){
                // there is some ongoing merchantOrder, meta cart is not fully cancelled
               return false;
            }
        }
        return true;
    }

    private function cancelCartEntity (MetaCart $metaCart, User $user, $reason) {

        $metaCart->setStatus(MetaCart::STATUS_CANCELLED);
        $metaCart->setValidatedBy($user);

        $this->logger->info(
            sprintf(
                'Metacart %d cancelled by %s',
                $metaCart->getId(),
                $user->getUsername()
            ),
            LogService::EVENT_CART_VALIDATION
        );

        $metaCart->setValidatedAt(new \DateTimeImmutable());
        $metaCart->setCancelReason($reason);

        $this->metaCartRepository->save($metaCart);
    }


}
