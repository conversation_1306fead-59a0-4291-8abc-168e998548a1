<?php


namespace AppBundle\Services\Reporting\Model;


class BestOfferMerchantItem
{
    /**
     * @var int $offerId
     */
    private $offerId;

    /**
     * @var string $sku
     */
    private $sku;


    /**
     * @var string $gtin
     */
    private $gtin;

    /**
     * @var string $name
     */
    private $name;

    /**
     * @var float $price
     */
    private $price;

    /**
     * @var string $currency
     */
    private $currency;

    /**
     * @var float $bestOffer
     */
    private $bestOffer;

    /**
     * @return int
     */
    public function getOfferId(): int
    {
        return $this->offerId;
    }

    /**
     * @param int $offerId
     * @return BestOfferMerchantItem
     */
    public function setOfferId(int $offerId): self
    {
        $this->offerId = $offerId;
        return $this;
    }

    /**
     * @return string
     */
    public function getSku(): string
    {
        return $this->sku;
    }

    /**
     * @param string $sku
     * @return BestOfferMerchantItem
     */
    public function setSku(string $sku): self
    {
        $this->sku = $sku;
        return $this;
    }

    /**
     * @return string
     */
    public function getGtin(): string
    {
        return $this->gtin;
    }

    /**
     * @param string $gtin
     * @return BestOfferMerchantItem
     */
    public function setGtin(string $gtin): self
    {
        $this->gtin = $gtin;
        return $this;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @param string $name
     * @return BestOfferMerchantItem
     */
    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    /**
     * @return float
     */
    public function getPrice(): float
    {
        return $this->price;
    }

    /**
     * @param float $price
     * @return BestOfferMerchantItem
     */
    public function setPrice(float $price): self
    {
        $this->price = $price;
        return $this;
    }

    /**
     * @return float
     */
    public function getBestOffer(): float
    {
        return $this->bestOffer;
    }

    /**
     * @param float $bestOffer
     * @return BestOfferMerchantItem
     */
    public function setBestOffer(float $bestOffer): self
    {
        $this->bestOffer = $bestOffer;
        return $this;
    }

    /**
     * @return string
     */
    public function getCurrency(): string
    {
        return $this->currency;
    }

    /**
     * @param string $currency
     * @return BestOfferMerchantItem
     */
    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;
        return $this;
    }



}
