<?php


namespace AppBundle\Services\Reporting\Model;

use AppBundle\Model\BestOffer;

/**
 * Data report for best offer reporting to merchant
 * Class BestOfferMerchantReport
 * @package AppBundle\Services\Reporting
 */
class BestOfferMerchantReport implements DataReport
{
    /**
     * @var int $totalCountOffers
     */
    private $totalCountOffers;
    /**
     * @var int $countAnalysedOffers
     */
    private $countAnalysedOffers;
    /**
     * @var int $percentWithoutPicture
     */
    private $percentWithoutPicture;

    /**
     * @var int $percentBest
     */
    private $percentBest;

    /**
     * @var bool $descriptionTooShort
     */
    private $descriptionTooShort;

    /**
     * @var int $averageCharsInDescription
     */
    private $averageCharsInDescription;

    /**
     * @var array
     * @see BestOfferMerchantItem
     */
    private $offerItems;

    /**
     * @return int
     */
    public function getTotalCountOffers(): int
    {
        return $this->totalCountOffers;
    }

    /**
     * @param int $totalCountOffers
     * @return BestOfferMerchantReport
     */
    public function setTotalCountOffers(int $totalCountOffers): self
    {
        $this->totalCountOffers = $totalCountOffers;
        return $this;
    }

    /**
     * @return int
     */
    public function getCountAnalysedOffers(): int
    {
        return $this->countAnalysedOffers;
    }

    /**
     * @param int $countAnalysedOffers
     * @return BestOfferMerchantReport
     */
    public function setCountAnalysedOffers(int $countAnalysedOffers): self
    {
        $this->countAnalysedOffers = $countAnalysedOffers;
        return $this;
    }

    /**
     * @return int
     */
    public function getPercentWithoutPicture(): int
    {
        return $this->percentWithoutPicture;
    }

    /**
     * @param int $percentWithoutPicture
     * @return BestOfferMerchantReport
     */
    public function setPercentWithoutPicture(int $percentWithoutPicture): self
    {
        $this->percentWithoutPicture = $percentWithoutPicture;
        return $this;
    }

    /**
     * @return bool
     */
    public function isDescriptionTooShort(): bool
    {
        return $this->descriptionTooShort;
    }

    /**
     * @param bool $descriptionTooShort
     * @return BestOfferMerchantReport
     */
    public function setDescriptionTooShort(bool $descriptionTooShort): self
    {
        $this->descriptionTooShort = $descriptionTooShort;
        return $this;
    }

    /**
     * @return array
     */
    public function getOfferItems(): array
    {
        return $this->offerItems;
    }

    /**
     * @param array $offerItems
     * @return BestOfferMerchantReport
     */
    public function setOfferItems(array $offerItems): self
    {
        $this->offerItems = $offerItems;
        return $this;
    }

    public function addOfferItem(BestOfferMerchantItem $item){
        if (!$this->offerItems){
            $this->offerItems = [];
        }
        $this->offerItems [] = $item;
    }

    /**
     * @return int
     */
    public function getAverageCharsInDescription(): int
    {
        return $this->averageCharsInDescription;
    }

    /**
     * @param int $averageCharsInDescription
     * @return BestOfferMerchantReport
     */
    public function setAverageCharsInDescription(int $averageCharsInDescription): self
    {
        $this->averageCharsInDescription = $averageCharsInDescription;
        return $this;
    }

    /**
     * @return int
     */
    public function getPercentBest(): int
    {
        return $this->percentBest;
    }

    /**
     * @param int $percentBest
     */
    public function setPercentBest(int $percentBest): void
    {
        $this->percentBest = $percentBest;
    }

}
