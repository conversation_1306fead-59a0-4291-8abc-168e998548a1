<?php

namespace AppBundle\Services\Reporting;

use AppBundle\Entity\MarketPlace;
use AppBundle\Model\Merchant;
use AppBundle\Model\Offer;
use AppBundle\Model\OfferSearchResult;
use AppBundle\Model\Search\Filter\SearchFilter;
use AppBundle\Model\Search\Filter\SearchFilterQuery;
use AppBundle\Services\ElasticSearchService;
use AppBundle\Services\MailService;
use AppBundle\Services\MerchantService;
use AppBundle\Services\OfferService;
use AppBundle\Services\Reporting\Model\BestOfferMerchantItem;
use AppBundle\Services\Reporting\Model\BestOfferMerchantReport;
use AppBundle\Services\Reporting\Model\DataReport;
use AppBundle\Services\Reporting\Model\ReportingUser;
use AppBundle\Services\SearchService;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Symfony\Contracts\Translation\TranslatorInterface;

class MerchantOfferReporter extends ReporterJob
{
    private const TRANSLATION_BUNDLE = "AppBundle";

    private SearchService $searchService;
    private MerchantService $merchantService;
    private OfferService $offerService;
    private MailService $mailService;
    private TranslatorInterface $translator;
    private LogService $logger;
    private int $minAverageDescriptionLength;

    /**
     * MerchantOfferReporter constructor.
     * @param SearchService $searchService
     * @param OfferService $offerService
     * @param MerchantService $merchantService
     * @param MailService $mailService
     * @param TranslatorInterface $translator
     * @param LogService $logger
     * @param int $minAverageDescriptionLength
     */
    public function __construct(SearchService $searchService,
                                OfferService $offerService,
                                MerchantService $merchantService,
                                MailService $mailService,
                                TranslatorInterface $translator,
                                LogService $logger,
                                int $minAverageDescriptionLength
    ) {
        $this->searchService = $searchService;
        $this->offerService = $offerService;
        $this->merchantService = $merchantService;
        $this->mailService = $mailService;
        $this->minAverageDescriptionLength = $minAverageDescriptionLength;
        $this->logger = $logger;
        $this->translator = $translator;
    }


    /**
     * @inheritDoc
     */
    protected function getUsers(MarketPlace $marketPlace): array
    {
        $result = [];
        //iterate through merchant
        foreach ($this->searchService->findDistinctValuesForField("merchant.id", $marketPlace) as $merchantId){
            /** @var  Merchant $merchant */
            $merchant = $this->merchantService->findMerchantById($merchantId);

            if ($merchant->getMainContactEmail() && $merchant->getLanguage()) {

                $user = new ReportingUser();
                $user->setId(intval($merchantId))->setEmail($merchant->getMainContactEmail())->
                    setFirstName($merchant->getMainContactFirstName())->
                    setLastName($merchant->getMainContactLastName())->
                    setLanguage($merchant->getLanguage())->
                    setDisplayName($merchant->getName());

                $result[] = $user;
            }
        }

        return $result;
    }

    /**
     * @inheritDoc
     */
    protected function buildReportData(ReportingUser $user,MarketPlace  $marketPlace): DataReport
    {

        //pagination parameters
        $size = 50;
        $page = 1;
        $data = new BestOfferMerchantReport();

        $countAnalysedOffers = 0;
        $countWithoutPictures = 0;
        $countBestOffers = 0;
        $totalDescriptionLength = 0;
        $totalCountOffers = 0;

        //deal with pagination
        do {

            $filterQuery = (new SearchFilterQuery())
                ->addMust(
                    (new SearchFilter('merchant.id', $user->getId()))
                );

            /** @var OfferSearchResult $result */
            $result = $this->offerService->search(
                null,
                $filterQuery,
                $size,
                $page,
                ElasticSearchService::CUSTOM_ANALYZER,
                null,
                null,
                $user->getLanguage(),
                $marketPlace
                );

            /////////////////////////////////////
            //global stats
            /////////////////////////////////////

            $totalCountOffers += count($result->getOffers());



            /** @var Offer $offer */
            foreach ($result->getOffers() as $offer) {
                $totalDescriptionLength += $offer->getDescription() ? strlen($offer->getDescription()) : 0;
                //we analyse an offer only if:
                // - it has a bestOffer attribute
                // - the GTIN attribute of the offer is set
                // - the countryPrice attribute is set
                if ($offer->getBestOffer() && $offer->getProduct()->getGtin() && $offer->getCountryPrice() !== null) {
                    $countAnalysedOffers++;
                    $offerItem = new BestOfferMerchantItem();
                    $offerItem->setOfferId($offer->getId());
                    $offerItem->setSku($offer->getSku());
                    $offerItem->setGtin($offer->getProduct()->getGtin());
                    $offerItem->setName($offer->getName());
                    $offerItem->setPrice($offer->getCountryPrice());
                    $offerItem->setCurrency($offer->getCountryCurrency());
                    $offerItem->setBestOffer($offer->getBestOffer()->getPrice());

                    if ($offer->getCountryPrice() == $offer->getBestOffer()->getPrice()){
                        $countBestOffers++;
                    }
                    if (!$offer->getPicture()){
                        $countWithoutPictures++;
                    }
                    $data->addOfferItem($offerItem);
                }
            }
            //increment page
            $page++;

        }while(count($result->getOffers()) !== 0);

        //now include global data reporting

        $data->setTotalCountOffers($totalCountOffers);
        $data->setCountAnalysedOffers($countAnalysedOffers);
        $data->setPercentWithoutPicture($totalCountOffers != 0 ? (int)($countWithoutPictures / $totalCountOffers * 100) : 0);
        $data->setDescriptionTooShort(false);
        $data->setAverageCharsInDescription($totalCountOffers != 0 ? (int)($totalDescriptionLength / $totalCountOffers) : 0);

        if ($data->getAverageCharsInDescription() < $this->minAverageDescriptionLength){
            $data->setDescriptionTooShort(true);
        }
        $data->setPercentBest($data->getCountAnalysedOffers() != 0 ? (int)($countBestOffers / $data->getCountAnalysedOffers() * 100) : 0);
        return $data;

    }

    /**
     * @param ReportingUser $user
     * @param BestOfferMerchantReport $reportingData
     * @return bool true if report sent
     */
    protected function sendReport(ReportingUser $user, BestOfferMerchantReport $reportingData): bool
    {
        //we only want to send report if we have something to analyse...
        if ($reportingData->getCountAnalysedOffers() > 0) {

            $variables = [];
            $variables["firstName"] = $user->getFirstName();
            $variables["lastName"] = $user->getLastName();
            $variables["merchantName"] = $user->getDisplayName();

            $variables["totalCountOffers"] = $reportingData->getTotalCountOffers();
            $variables["countAnalysedOffers"] = $reportingData->getCountAnalysedOffers();
            $variables["descriptionTooShort"] = $reportingData->isDescriptionTooShort();
            $variables["averageCharsInDescription"] = $reportingData->getAverageCharsInDescription();
            $variables["percentWithoutPicture"] = $reportingData->getPercentWithoutPicture();
            $variables["percentBest"] = $reportingData->getPercentBest();

            $attachment = new \Swift_Attachment($this->buildCSVReport($user, $reportingData), 'statistics.csv', 'text/csv');
            $attachment->setDisposition("inline");
            $merchant = $this->merchantService->findMerchantById($user->getId());
            $merchantEntity = $this->merchantService->findMerchantEntityByIzbergId($merchant->getId());
            $contacts = $this->merchantService->buildNotificationContacts($merchant, MailService::BEST_OFFER_REPORTING_TO_VENDOR);

            $this->mailService->sendEmailMessage(MailService::BEST_OFFER_REPORTING_TO_VENDOR, $merchantEntity->getLanguage(), $contacts, $variables, null, null, [$attachment]);
            return true;
        }

        $this->logger->error("Merchant Reporting Command - unable to send reporting to merchant ".$user->getId().": possible causes: Best Offer Command has never been run / Merchant don't enter GTIN attributes on his offers", EventNameEnum::REPORTING_ERROR);
        return false;
    }

    private function buildCSVReport (ReportingUser $user, DataReport|BestOfferMerchantReport $report): string{

        $buffer = fopen('php://temp', 'r+');



        $header = [
            $this->translator->trans("reporting.best_offer.id_col", [],self::TRANSLATION_BUNDLE,  $user->getLanguage()),
            $this->translator->trans("reporting.best_offer.gtin_col", [],self::TRANSLATION_BUNDLE,  $user->getLanguage()),
            $this->translator->trans("reporting.best_offer.sku_col", [],self::TRANSLATION_BUNDLE,  $user->getLanguage()),
            $this->translator->trans("reporting.best_offer.name_col", [],self::TRANSLATION_BUNDLE,  $user->getLanguage()),
            $this->translator->trans("reporting.best_offer.price", [],self::TRANSLATION_BUNDLE,  $user->getLanguage())."(".$report->getOfferItems()[0]->getCurrency().")",
            $this->translator->trans("reporting.best_offer.best_price", [],self::TRANSLATION_BUNDLE,  $user->getLanguage())."(".$report->getOfferItems()[0]->getCurrency().")",
        ];

        fputcsv($buffer, $header, ";");


        /** @var BestOfferMerchantItem $item */
        foreach ($report->getOfferItems() as $item) {
            $data = [
                $item->getOfferId(),
                $item->getGtin(),
                $item->getSku(),
                $item->getName(),
                $item->getPrice(),
                $item->getBestOffer() == $item->getPrice() ? '' : $item->getBestOffer()
            ];
            fputcsv($buffer, $data, ";");
        }

        rewind($buffer);
        $csv = "";
        while(! feof($buffer)) {
            $csv .= fgets($buffer);
        }

        fclose($buffer);
        return $csv;
    }





}
