<?php


namespace AppBundle\Services\Reporting;

use AppBundle\Entity\MarketPlace;
use AppBundle\Services\Reporting\Model\BestOfferMerchantReport;
use AppBundle\Services\Reporting\Model\DataReport;
use AppBundle\Services\Reporting\Model\ReportingUser;

/**
 * Interface Reporter: Describe a reporting task:
 *  - How to get user to report
 *  - How to get data depending on user to report
 *  - How to send report to one user
 * @package AppBundle\Services\Reporting
 */
abstract class ReporterJob
{

    /**
     * Return the list (array of ReportingUser object) who need to receive reporting
     * @return array the list of user
     * @see ReportingUser
     */
    protected abstract function getUsers (MarketPlace $marketPlace): array;

    /**
     * build report data
     * @param ReportingUser $user
     * @return DataReport
     */
    protected abstract function buildReportData(ReportingUser $user, MarketPlace $marketPlace): DataReport;


    /**
     * @param ReportingUser $user
     * @param BestOfferMerchantReport $reportingData
     * @return bool if report sent
     */
    protected abstract function sendReport(ReportingUser $user, BestOfferMerchantReport $reportingData): bool;

    /**
     * Trigger the report process
     * @param MarketPlace $marketplace
     * @return array list of notified user
     */
    public function report(MarketPlace $marketplace){
        $notifiedUser = [];
        /** @var ReportingUser $user */
        foreach ($this->getUsers($marketplace) as $user){
            if ($this->sendReport($user, $this->buildReportData($user,$marketplace))){
                $notifiedUser []= $user->getId();
            }
        }
        return $notifiedUser;
    }

}
