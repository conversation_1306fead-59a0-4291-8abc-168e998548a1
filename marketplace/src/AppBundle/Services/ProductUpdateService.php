<?php


namespace AppBundle\Services;


use AppBundle\Entity\MarketPlace;
use AppBundle\Model\Offer;
use AppBundle\Model\Search\Filter\SearchFilter;
use AppBundle\Model\Search\Filter\SearchFilterQuery;
use Open\FrontBundle\Exception\TotalException;
use Open\IzbergBundle\Api\ProductApi;
use Open\LogBundle\Service\LogService;
use Symfony\Component\Console\Output\OutputInterface;

class ProductUpdateService
{
    private LogService $logger;
    private OfferService $offerService;
    private ProductApi $productApi;

    public function __construct(LogService $logger, OfferService $offerService, ProductApi $productApi)
    {
        $this->logger = $logger;
        $this->offerService = $offerService;
        $this->productApi = $productApi;
    }

    /**
     * @throws TotalException
     */
    public function updateIzbergQuoteProduct(MarketPlace $marketPlace, OutputInterface $output): void
    {
        // Get the offers from offer service

        $categoryId = $marketPlace->getQuoteCategoryId();
        if ($categoryId == null) {
            throw new TotalException("Quote category is not set in marketplace database table");
        }

        $filterQuery = (new SearchFilterQuery())
            ->addMust(
                (new SearchFilter('attributes.0110_price_on_quotation.keyword', "Yes"))
            )->addMustNot(
                new SearchFilter('product.application_categories', $categoryId)
            );

        foreach ($marketPlace->getLanguages() as $language) {
            $offerNb = 0;
            $pageSize = 30;
            //init the cursor
            $result = $this->offerService->scan(
                $pageSize,
                "30s",
                $filterQuery->query(),
                $language,
                $marketPlace
            );

            $nb = $result->getNbHits();
            while (count($result->getOffers()) !== 0) {
                /** @var Offer $offer */
                foreach ($result->getOffers() as $offer) {
                    $this->logger->info(
                        "update quote offer category",
                        "UPDATE_QUOTE_CATEGORY",
                        null,
                        ["offer" => $offer->getId(), "product" => $offer->getProduct()->getId()]
                    );
                    $offerNb++;
                    $output->writeln($offerNb . '/' . $nb . " offer:" . $offer->getId());
                    $this->productApi->updateProductAddCategory(
                        $offer->getProduct()->getId(),
                        $categoryId,
                        $marketPlace->getLocale()
                    );
                }

                //update cursor
                $result = $this->offerService->scroll($result->getScrollId(), "30s", $language);
            }
        }
    }
}
