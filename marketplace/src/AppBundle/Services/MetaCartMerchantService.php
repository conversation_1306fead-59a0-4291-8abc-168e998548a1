<?php

namespace AppBundle\Services;

use AppBundle\Entity\MetaCart;
use AppBundle\Entity\MetaCartMerchant;
use AppBundle\Entity\MetaCartMerchantItem;
use AppBundle\Entity\User;
use AppBundle\Repository\MetaCartMerchantItemRepository;
use AppBundle\Repository\MetaCartMerchantRepository;
use AppBundle\Repository\MetaCartRepository;
use Open\IzbergBundle\Api\MerchantApi;

class MetaCartMerchantService
{
    private MetaCartRepository $metaCartRepository;
    private MetaCartMerchantRepository $metaCartMerchantRepository;
    private MetaCartMerchantItemRepository $metaCartMerchantItemRepository;
    private MerchantApi $merchantApi;
    private IzbergCustomAttributes $customAttributes;

    public function __construct(
        MetaCartRepository $metaCartRepository,
        MetaCartMerchantRepository $metaCartMerchantRepository,
        MetaCartMerchantItemRepository $metaCartMerchantItemRepository,
        MerchantApi $merchantApi,
        IzbergCustomAttributes $customAttributes
    ) {
        $this->metaCartRepository = $metaCartRepository;
        $this->metaCartMerchantRepository = $metaCartMerchantRepository;
        $this->metaCartMerchantItemRepository = $metaCartMerchantItemRepository;
        $this->merchantApi = $merchantApi;
        $this->customAttributes = $customAttributes;
    }

    public function removeItem(MetaCart $metaCart, int $cartItemId)
    {
        // find metaCartMerchantItem
        /** @var MetaCartMerchantItem $item */
        $item = $this->metaCartMerchantItemRepository->findOneBy(['cartItemId' => $cartItemId]);

        if (!$item) {
            return;
        }

        $merchant = $item->getMetaCartMerchant();
        $this->metaCartMerchantItemRepository->remove($item);

        if(!$merchant->getItems()->count()) {
            $this->metaCartMerchantRepository->remove($merchant);
            $metaCart->setRisk(null);
            $this->metaCartRepository->save($metaCart);

        }
    }

    public function addItem(MetaCart $metaCart, int $merchantId, int $cartItemId, string $comment = null)
    {
        $merchant = $this->metaCartMerchantRepository->findOneBy([
            'izbergId' => $merchantId,
            'metaCart' => $metaCart,
        ]);

        if (!$merchant) {
            $commentPlaceholder = $this->fetchIzbergMerchantPlaceholder($merchantId);

            // create merchant and attach it to metacart
            $merchant = (new MetaCartMerchant())
                ->setIzbergId($merchantId)
                ->setMetaCart($metaCart)
                ->setCommentPlaceholder($commentPlaceholder)
                ->setComment($comment)
            ;

            $this->metaCartMerchantRepository->save($merchant);
        }

        // find metaCartMerchantItem
        /** @var MetaCartMerchantItem $item */
        $item = $this->metaCartMerchantItemRepository->findOneBy([
            'cartItemId' => $cartItemId,
            'metaCartMerchant' => $merchant,
        ]);

        // if does not exists
        // create it and add it
        if (!$item) {
            $item = (new MetaCartMerchantItem())
                ->setMetaCartMerchant($merchant)
                ->setCartItemId($cartItemId);

            $this->metaCartMerchantItemRepository->save($item);
        }
    }

    public function writeMerchantComment(User $buyer, int $cartId, int $merchantId, ?string $comment)
    {
        /** @var MetaCart $metaCart */
        $metaCart = $this->metaCartRepository->findOneBy([
            'id' => $cartId,
            'buyer' => $buyer,
            'status' => MetaCart::STATUS_CREATED,
        ]);

        if (!$metaCart) {
            return false;
        }

        // write comment
        // retrieve merchant if exists
        $metaCartMerchant = $this->metaCartMerchantRepository->findOneBy([
            'metaCart' => $metaCart,
            'izbergId' => $merchantId
        ]);

        if (!$metaCartMerchant) {
            return false;
        }

        $metaCartMerchant->setComment($comment);
        $this->metaCartMerchantRepository->save($metaCartMerchant);

        return true;
    }

    public function fetchMerchantComment(int $metaCartId, int $merchantId)
    {
        return $this->metaCartMerchantRepository->fetchMerchantComment($metaCartId, $merchantId);
    }

    public function fetchMerchantCommentPlaceholder(int $metaCartId, int $merchantId)
    {
        return $this->metaCartMerchantRepository->fetchMerchantCommentPlaceholder($metaCartId, $merchantId);
    }

    private function fetchIzbergMerchantPlaceholder(int $merchantId): ?string
    {
        return $this->merchantApi->getMerchantCustomAttribute($merchantId, $this->customAttributes->getMerchantCommentPlaceholder());
    }
}