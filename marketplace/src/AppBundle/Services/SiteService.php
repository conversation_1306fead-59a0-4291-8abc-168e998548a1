<?php

namespace AppBundle\Services;

use AppBundle\Entity\Site;
use AppBundle\FilterQueryBuilder\FilterQueryBuilderInterface;
use AppBundle\Repository\SiteRepository;
use Knp\Component\Pager\PaginatorInterface;

class SiteService extends AbstractPaginatedService
{
    private SiteRepository $siteRepository;

    public function __construct(
        SiteRepository $siteRepository,
        PaginatorInterface $paginator,
        ?FilterQueryBuilderInterface $filterQueryBuilder
    ) {
        parent::__construct($siteRepository->getEntityManager(),
            Site::class,
            $paginator,
            $filterQueryBuilder);
        $this->siteRepository = $siteRepository;
    }

    /**
     * Create simple Site Entity from it's properties
     *
     * @param string $name
     * @return Site
     */
    public function createSite(
        string $name
    ): Site
    {

        $site = $this->siteRepository->findByName($name);

        if(!$site) {
            // Create only unknown invoice entity
            $site = new Site();
            $site->setEmailRisk("");
            $site->setName($name);
            $this->siteRepository->save($site);
        }
        return $site;
    }

    /**
     * @param string $name
     * @return Site|null
     */
    public function findSiteByName(string $name): ?Site
    {
        return $this->siteRepository->findByName($name);
    }

    /**
     * @param int $id
     * @return Site|null
     */
    public function findInvoiceEntityById(int $id): ?Site
    {
        return $this->siteRepository->find($id);
    }


}