<?php

namespace AppBundle\Services;

use AppBundle\Entity\Country;
use AppBundle\Entity\MarketPlace;
use AppBundle\Entity\Merchant;
use AppBundle\Entity\User;
use AppBundle\Model\Invitation\DataTableResponse;
use AppBundle\Model\Invitation\InvitationData;
use AppBundle\Model\Invitation\UserData;
use AppBundle\Model\Merchant as ModelMerchant;
use AppBundle\Model\Search\Filter\SearchFilterQuery;
use AppBundle\Model\SearchRequest;
use AppBundle\Model\SearchResult;
use AppBundle\Repository\MerchantRepository;
use AppBundle\Repository\UserRepository;
use AppBundle\Services\SerializerService as Serializer;
use AppBundle\Util\DateUtil;
use DateInterval;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use DomainBundle\Event\MerchantModifiedEvent;
use DomainBundle\EventDispatcher\MerchantEventDispatcherInterface;
use Exception;
use Illuminate\Encryption\Encrypter;
use Knp\Component\Pager\Pagination\PaginationInterface;
use Open\IzbergBundle\Api\ApiClientManager;
use Open\IzbergBundle\Api\ApiConfigurator;
use Open\IzbergBundle\Api\ApiException;
use Open\IzbergBundle\Api\AttributeApi;
use Open\IzbergBundle\Api\IzbergUtils;
use Open\IzbergBundle\Api\MerchantApi;
use Open\IzbergBundle\Api\MerchantReviewApi;
use Open\IzbergBundle\Api\PermissionApi;
use Open\IzbergBundle\Factory\OperatorClientFactory;
use Open\IzbergBundle\IzbergUtil;
use Open\IzbergBundle\Model\Permission;
use Open\IzbergBundle\Service\RedisService;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Psr\Log\InvalidArgumentException;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class MerchantService
{
    const FIELD_COUNTRY = 'country';
    const FIELD_EMAIL = 'email';
    const FIELD_FIRSTNAME = 'firstname';
    const FIELD_IDENTIFICATION = 'identification';
    const FIELD_LASTNAME = 'lastname';
    const FIELD_MAIN_PHONE_NUMBER = 'mainPhoneNumber';
    const FIELD_PLAIN_MDP = 'plainPassword';
    const FIELD_RAISON_SOCIALE = 'raisonSociale';

    private const MERCHANT_CACHE_KEY = 'MERCHANT_';
    private const MERCHANT_REVIEWS_CACHE_KEY = 'MERCHANT_REVIEWS_';
    private const MERCHANT_TTL = 10;
    private const MERCHANT_REVIEWS_TTL = 1440;//24H time

    public const SOURCE_REGISTRATION_TOTAL = 'total';
    public const SOURCE_REGISTRATION_EPSA = 'epsa';
    public const SOURCE_REGISTRATION_OPERATOR = 'operator';
    public const SOURCE_REGISTRATION_DIRECT = 'direct';
    public const SOURCE_REGISTRATION_USER = 'user';

    public const REGISTRATION_STEP_ONBOARDING = 'onboarding_step';
    public const REGISTRATION_STEP_VALIDATED_BY_OPERATOR = 'validated_by_operator_step';
    public const REGISTRATION_STEP_REJECTED_BY_OPERATOR = 'rejected_by_operator_step';
    public const REGISTRATION_STEP_ACTIVATED_BY_OPERATOR = 'activated_by_operator_step';

    private const NOTIFICATION_ORDER_EMAILS = [
        MailService::CART_CANCELLED_TO_MERCHANT,
        MailService::ORDER_REJECTED_BY_VENDOR_TO_VENDOR,
        MailService::ORDER_PENDING_CONFIRMATION_TO_VENDOR,
        MailService::ORDER_AUTO_CONFIRMED_TO_VENDOR,
        MailService::CXML_ORDER_ERROR_TO_VENDOR,
        MailService::ORDER_PENDING_CONFIRMATION_TO_VENDOR,
        MailService::ORDER_AUTO_CONFIRMED_TO_VENDOR
    ];

    private const NOTIFICATION_QUOTE_EMAILS = [
        MailService::QUOTE_NEW_QUOTE_TO_VENDOR,
        MailService::QUOTE_SEND_QUOTE_TO_VENDOR,
        MailService::QUOTE_REFUSE_QUOTE_TO_VENDOR,
        MailService::QUOTE_ACCEPT_QUOTE_TO_VENDOR,
        MailService::QUOTE_CANCEL_QUOTE_TO_VENDOR,
        MailService::QUOTE_NEGOCIATE_QUOTE_TO_VENDOR,
        MailService::QUOTE_NEW_MESSAGE_TO_VENDOR
    ];

    private const NOTIFICATION_THREAD_EMAILS = [
        MailService::TICKET_THREAD_UPDATED_BY_BUYER_TO_VENDOR
    ];

    private const NOTIFICATION_COMMERCIAL_EMAILS = [
        MailService::BEST_OFFER_REPORTING_TO_VENDOR
    ];


    private MerchantApi $merchantApi;
    private MerchantRepository $merchantRepository;
    private LogService $logger;
    private MailService $mailer;
    private CountryService $countryService;
    private MarketPlaceService $marketPlaceService;
    private SecurityService $securityService;
    private RedisService $cache;
    private RouterInterface $router;
    private PermissionApi $permissionApi;
    private AttributeApi $attributeApi;
    private IzbergCustomAttributes $customAttributes;
    private TaxService $taxService;
    private MerchantReviewApi $merchantReviewApi;
    private Serializer $serializer;
    private UserRepository $userRepository;
    private UserBddService $userService;
    private TranslatorInterface $translator;
    private MerchantEventDispatcherInterface $merchantEventDispatcher;
    private EntityManagerInterface $em;
    private ApiClientManager $apiClientManager;
    private ApiConfigurator $apiConfigurator;
    private Encrypter $encryptor;
    private SearchService $searchService;
    private ?User $user;
    private OperatorClientFactory $operatorClientFactory;

    private array $middleBaseUrl;
    private array $commonEmailProviders;
    private string $supportOperatorEmail;

    public function __construct(
        string                           $supportOperatorEmail,
        array                            $middleBaseUrl,
        Encrypter                        $encryptor,
        EntityManagerInterface           $em,
        PermissionApi                    $permissionApi,
        MerchantApi                      $merchantApi,
        MerchantRepository               $merchantRepository,
        LogService                       $logger,
        MailService                      $mailer,
        CountryService                   $countryService,
        MarketPlaceService               $marketPlaceService,
        SecurityService                  $securityService,
        RedisService                     $cache,
        RouterInterface                  $router,
        AttributeApi                     $attributeApi,
        IzbergCustomAttributes           $customAttributes,
        TaxService                       $taxService,
        MerchantReviewApi                $merchantReviewApi,
        Serializer                       $serializer,
        UserRepository                   $userRepository,
        TranslatorInterface              $translator,
        UserBddService                   $userService,
        MerchantEventDispatcherInterface $merchantEventDispatcher,
        ApiClientManager                 $apiClientManager,
        ApiConfigurator                  $apiConfigurator,
        SearchService                    $searchService,
        OperatorClientFactory            $operatorClientFactory
    )
    {
        $this->merchantApi = $merchantApi;
        $this->merchantRepository = $merchantRepository;
        $this->logger = $logger;
        $this->mailer = $mailer;
        $this->countryService = $countryService;
        $this->securityService = $securityService;
        $this->middleBaseUrl = $middleBaseUrl;
        $this->cache = $cache;
        $this->encryptor = $encryptor;
        $this->permissionApi = $permissionApi;
        $this->router = $router;
        $this->attributeApi = $attributeApi;
        $this->customAttributes = $customAttributes;
        $this->taxService = $taxService;
        $this->merchantReviewApi = $merchantReviewApi;
        $this->serializer = $serializer;
        $this->userRepository = $userRepository;
        $this->translator = $translator;
        $this->merchantEventDispatcher = $merchantEventDispatcher;
        $this->em = $em;
        $this->commonEmailProviders = [];
        $this->userService = $userService;
        $this->apiClientManager = $apiClientManager;
        $this->apiConfigurator = $apiConfigurator;
        $this->supportOperatorEmail = $supportOperatorEmail;
        $this->marketPlaceService = $marketPlaceService;
        $this->searchService = $searchService;
        $this->operatorClientFactory = $operatorClientFactory;

    }

    /**
     * fetch a merchant from the izberg API and save it in cache from 10 minutes
     *
     * @param int $merchantId
     *
     * @return ModelMerchant|null
     * @throws ApiException
     */
    public function findMerchantById($merchantId, $resetCache = false): ?ModelMerchant
    {

        if ($resetCache) {
            $this->cache->removeItem(self::MERCHANT_CACHE_KEY . $merchantId);
        } else {
            $cacheItem = $this->cache->getItem(self::MERCHANT_CACHE_KEY . $merchantId);
            if ($cacheItem !== null) {
                return $cacheItem;
            }
        }

        try {
            $totalMerchant = $this->buildMerchantFromIzbergResponse($this->merchantApi->getMerchant($merchantId));
            $merchantAdaptedCompany = $this->attributeApi->getMerchantAttribute($this->customAttributes->getAdaptedCompany(), $merchantId) ?? false;
            $merchantBranches = $this->attributeApi->getMerchantAttribute($this->customAttributes->getMerchantBranch(), $merchantId);
            $merchantMinimumOrderAmount = $this->attributeApi->getMerchantAttribute($this->customAttributes->getMerchantMinimumOrderAmount(), $merchantId);

            $notificationOrder = $this->getCustomAddressList($this->customAttributes->getNotificationOrder(), $merchantId);
            $notificationQuote = $this->getCustomAddressList($this->customAttributes->getNotificationQuote(), $merchantId);
            $notificationThread = $this->getCustomAddressList($this->customAttributes->getNotificationThread(), $merchantId);
            $notificationCommercial = $this->getCustomAddressList($this->customAttributes->getNotificationCommercial(), $merchantId);
            $merchantBranches = !empty($merchantBranches) ? explode(';', $merchantBranches) : [];

            $totalMerchant
                ->setAdaptedCompany($merchantAdaptedCompany)
                ->setBranches($merchantBranches)
                ->setNotificationOrderContacts($notificationOrder)
                ->setNotificationQuoteContacts($notificationQuote)
                ->setNotificationThreadContacts($notificationThread)
                ->setNotificationCommercialContacts($notificationCommercial)
                ->setMinimumOrderAmount($merchantMinimumOrderAmount);

            $this->cache->saveItem(self::MERCHANT_CACHE_KEY . $merchantId, $totalMerchant, self::MERCHANT_TTL);
            return $totalMerchant;
        } catch (ApiException $e) {
            $this->logger->error("unable to load merchant from izberg API: " . $e->getMessage(), EventNameEnum::TECHNICAL_ERROR, null, ['id' => $merchantId]);
            return null;
        }
    }


    /**
     * This function will build the notification contacts list based on the emailIdentifier.
     *
     * @param ModelMerchant $merchant
     * @param string $emailIdentifier
     *
     * @return array
     */
    public function buildNotificationContacts(\AppBundle\Model\Merchant $merchant, string $emailIdentifier): array
    {
        $contacts = [];
        if (in_array($emailIdentifier, self::NOTIFICATION_ORDER_EMAILS)) {
            $contacts = $merchant->getNotificationOrderContacts();
        } else if (in_array($emailIdentifier, self::NOTIFICATION_COMMERCIAL_EMAILS)) {
            $contacts = $merchant->getNotificationCommercialContacts();
        } else if (in_array($emailIdentifier, self::NOTIFICATION_QUOTE_EMAILS)) {
            $contacts = $merchant->getNotificationQuoteContacts();
        } else if (in_array($emailIdentifier, self::NOTIFICATION_THREAD_EMAILS)) {
            $contacts = $merchant->getNotificationThreadContacts();
        }

        if (count($contacts) == 0) {
            $contacts = [$merchant->getMainContactEmail()];
        }

        return $contacts;
    }

    /**
     * @param string $categoryKey
     * @param string $sinceDate
     *
     * @return array
     */
    public function findUpdatedMerchantByCategory(string $categoryKey, string $sinceDate)
    {
        $attributes = $this->attributeApi->searchMerchantAttribute([
            "key" => $categoryKey,
            "last_modified__gte" => $sinceDate
        ]);

        $merchantId = array();

        // get merchant ids
        foreach ($attributes as $attribute) {
            $extractedId = $this->extractMerchantIdFromUrl($attribute->merchant);
            if (!is_null($extractedId)) {
                $merchantId[] = $extractedId;
            }
        }

        return $merchantId;
    }

    /**
     * @param array $merchantIds
     */
    public function notifyUpdatedMerchants(array $merchantIds)
    {
        foreach ($merchantIds as $merchantId) {
            $merchantModifiedEvent = new MerchantModifiedEvent($merchantId);
            $this->merchantEventDispatcher->dispatch($merchantModifiedEvent);
        }
    }


    public function findMerchantEntityById(int $id): ?Merchant
    {
        /** @var Merchant $merchant */
        $merchant = $this->merchantRepository->find($id);

        if (!$merchant instanceof Merchant) {
            return null;
        }

        if ($merchant->getInviteByIgg()) {
            /** @var User */
            $buyer = $this->userRepository->findOneBy(['username' => $merchant->getInviteByIgg()]);
            if (!$buyer) {
                throw new \InvalidArgumentException(sprintf('buyer not found with igg %s', $merchant->getInviteByIgg()));
            }

            $merchant
                ->setBuyerIgg($buyer->getUsername())
                ->setBuyerFirstname($buyer->getFirstname())
                ->setBuyerLastname($buyer->getLastname());
        }
        return $merchant;
    }

    public function findMerchantEntityByIzbergId(int $id): ?Merchant
    {
        /** @var Merchant $merchant */
        $merchant = $this->merchantRepository->findOneBy(array("izbergId" => $id));

        if (!$merchant) {
            return null;
        }
        return $merchant;
    }

    /**
     * @param $merchantId
     *
     * @return \AppBundle\Entity\Country|null
     */
    public function getMerchantFiscalCountryByMerchantId($merchantId)
    {
        $merchant = $this->findMerchantById($merchantId);
        if ($merchant !== null) {
            return $merchant->getCountry();
        }

        return null;
    }

    public function activateMerchant(Merchant $merchant, ?User $author = null, bool $tvaChecker = true): bool
    {

        try {
            $this->marketPlaceService->configureIzbergApiConnection($merchant->getMarketPlace());
            $this->createIzbergMerchant($merchant, $author, $tvaChecker);
            $merchant->setStatus(Merchant::STATUS_VALIDATION);
            $this->merchantRepository->update($merchant, $author);

        } catch (\Exception $e) {
            $this->logger->error(
                'error while registering a merchant with Izberg: ' . $e->getMessage(),
                EventNameEnum::TECHNICAL_ERROR,
                ($author) ? $author->getUsername() : null,
                [
                    'merchant_id' => $merchant->getId(),
                    'exception' => $e
                ]
            );

            return false;
        }

        $this->logger->info(
            "merchant has been validated",
            EventNameEnum::MERCHANT_VALIDATE,
            ($author) ? $author->getUsername() : null,
            [
                'merchant_id' => $merchant->getId(),
                'izberg_merchant_id' => $merchant->getIzbergId(),
            ]
        );


        $this->mailer->sendEmailMessage(
            MailService::MERCHANT_ACCEPTED,
            $merchant->getLanguage(),
            $merchant->getEmail(),
            [
                MailService::FIRST_NAME_VAR => $merchant->getFirstname(),
                MailService::LAST_NAME_VAR => $merchant->getLastname(),
                "companyName" => $merchant->getName(),
                "email" => $merchant->getEmail(),
                "link" => sprintf('%s%s', $this->getMarketplaceUrl($merchant), $merchant->getSlug()),
                "marketplace" => $merchant->getMarketplace()->getName()
            ]
        );

        if ($merchant->getInviteByIgg()) {
            $this->notifyBuyerToMerchantRegistrationSteps(
                $merchant->getInviteByIgg(),
                $merchant,
                self::REGISTRATION_STEP_VALIDATED_BY_OPERATOR
            );
        }

        return true;
    }

    /**
     * @param Merchant $merchant
     * @param string $reason
     * @param User|null $author
     * @param string|null $inviteByIgg
     *
     * @return bool
     */
    public function rejectMerchant(Merchant $merchant, string $reason, ?User $author = null): bool
    {
        $this->logger->info(
            'merchant has been rejected',
            EventNameEnum::MERCHANT_REJECT,
            ($author) ? $author->getUsername() : null,
            ['merchant_id' => $merchant->getId(), 'reason' => $reason]
        );

        try {
            $merchant->setStatus(Merchant::STATUS_REJECTED);
            $merchant->setRejectedReason($reason);
            $this->merchantRepository->update($merchant, $author);
        } catch (\Exception $e) {
            $this->logger->error(
                'error while rejecting merchant: ' . $e->getMessage(),
                EventNameEnum::TECHNICAL_ERROR,
                ($author) ? $author->getUsername() : null,
                [
                    'merchant_id' => $merchant->getId(),
                    'exception' => $e
                ]
            );

            return false;
        }

        $this->mailer->sendEmailMessage(
            MailService::MERCHANT_REJECTED,
            $merchant->getLanguage(),
            $merchant->getEmail(),
            [
                MailService::FIRST_NAME_VAR => $merchant->getFirstname(),
                MailService::LAST_NAME_VAR => $merchant->getLastname(),
                "companyName" => $merchant->getName(),
                'comment' => $reason,
                'marketplace' => $merchant->getMarketplace()->getName()]
        );

        if ($merchant->getInviteByIgg()) {
            $this->notifyBuyerToMerchantRegistrationSteps(
                $merchant->getInviteByIgg(),
                $merchant,
                self::REGISTRATION_STEP_REJECTED_BY_OPERATOR,
                $reason
            );
        }

        return true;
    }

    /**
     * @param Merchant $merchant
     * @param User|null $author
     *
     * @return bool
     */
    public function updateMerchantInformation(Merchant $merchant, ?User $author = null): bool
    {
        try {
            $this->merchantRepository->update($merchant, $author);
        } catch (\Exception $e) {
            $this->logger->error(
                'error while saving merchant: ' . $e->getMessage(),
                EventNameEnum::TECHNICAL_ERROR,
                ($author) ? $author->getUsername() : null,
                [
                    'merchant_id' => $merchant->getId(),
                    'exception' => $e
                ]
            );

            return false;
        }

        $this->logger
            ->info(
                'update merchant information',
                EventNameEnum::MERCHANT_SAVE,
                ($author) ? $author->getUsername() : null,
                [
                    'id' => $merchant->getId(),
                    'status' => $merchant->getStatus(),
                ]
            );

        return true;
    }

    public function registerMerchant(array $merchantDetails, bool $tvaChecked, ?string $source = null, bool $tvaChecker = true, Merchant $merchant = null): void
    {
        $canProceedToDirectActivation = $this->canProceedToDirectActivation($source);

        $source = self::sanatizeSource($source);
        $country = $this->countryService->getCountryById((int)$merchantDetails['country']->getViewData());


        if (!$merchant) {
            $merchant = new Merchant();
        }

        $marketplace = $this->marketPlaceService->getMarketPlaceById((int)$merchantDetails['marketplace']->getViewData());
        $merchant->setMarketplace($marketplace);
        $merchant->setCountry($country);
        $merchant->setCurrency($merchantDetails['currency']->getViewData());
        $merchant->setName($merchantDetails['raisonSociale']->getViewData());
        $merchant->setIdentification($merchantDetails['identification']->getViewData());
        $merchant->setLanguage($merchantDetails['language']->getViewData());
        $merchant->setFirstname($merchantDetails['firstname']->getViewData());
        $merchant->setLastname($merchantDetails['lastname']->getViewData());
        $merchant->setEmail($merchantDetails['email']->getViewData());
        $merchant->setRegistrationDate(new \DateTimeImmutable());

        $encrypted = $this->encryptor->encrypt($merchantDetails['plainPassword']->getViewData());

        $merchant->setPassword($encrypted);
        $merchant->setPhoneNumber($merchantDetails['mainPhoneNumber']->getViewData());
        $merchant->setStatus(Merchant::STATUS_REGISTRATION);
        $merchant->setTvaChecked($tvaChecked);
        $merchant->setSource($source);

        if (is_array($merchantDetails['categories']->getViewData())) {
            $merchant->setCategories(
                implode(',', $merchantDetails['categories']->getViewData())
            );
        }

        // Log merchant registration
        $this
            ->logger
            ->info(
                'New merchant register',
                EventNameEnum::MERCHANT_REGISTER,
                null,
                ['merchant_email' => $merchantDetails['email']->getViewData()]
            );
        // persist
        $this->merchantRepository->save($merchant);

        /////////////////////////////////////////////////////////////////////////////////////////////
        //notify merchant
        /////////////////////////////////////////////////////////////////////////////////////////////

        if (!$canProceedToDirectActivation) {
            if ($merchant->getCountry() !== null) {
                $this->mailer->sendEmailMessage(
                    MailService::MERCHANT_REGISTRATION_CONFIRMED,
                    strtolower($merchant->getLanguage()),
                    $merchant->getEmail(),
                    [
                        'companyName' => $merchant->getName(),
                        MailService::FIRST_NAME_VAR => $merchant->getFirstname(),
                        MailService::LAST_NAME_VAR => $merchant->getLastname(),
                        'marketplace' => $merchant->getMarketplace()->getName()
                    ]);
            } else {
                $this->logger->error("unable to notify merchant creation: Country doesn't exist",
                    EventNameEnum::MERCHANT_CREATION_NOTIFICATION_ERROR,
                    $merchant->getFirstname() . ' ' . $merchant->getLastname(),
                    [
                        'email' => $merchant->getEmail()
                    ]
                );
            }
        }

        /////////////////////////////////////////////////////////////////////////////////////////////
        //notify operators
        /////////////////////////////////////////////////////////////////////////////////////////////

        $operators = $this->securityService->getOperators();

        /** @var User $operator */
        foreach ($operators as $operator) {
            if ($operator->isEnabled()) {
                $this->mailer->sendEmailMessage(MailService::OPERATOR_MERCHANT_REGISTRATION_CONFIRMED,
                    "en", $operator->getEmail(), [
                        MailService::FIRST_NAME_VAR => $operator->getFirstname(),
                        MailService::LAST_NAME_VAR => $operator->getLastname(),
                        'merchantFirstName' => $merchant->getFirstname(),
                        'merchantLastName' => $merchant->getLastname(),
                        'merchantEmail' => $merchant->getEmail(),
                        'companyName' => $merchant->getName(),
                        'url' => $this->router->generate('admin.merchant.generalInfo', [
                            'id' => $merchant->getId()], UrlGeneratorInterface::ABSOLUTE_URL
                        ),
                        'marketplace' => $merchant->getMarketplace()->getName()

                    ]
                );
            }
        }

        if ($merchant->getInviteByIgg()) {
            $this->notifyBuyerToMerchantRegistrationSteps(
                $merchant->getInviteByIgg(),
                $merchant,
                self::REGISTRATION_STEP_ONBOARDING
            );
        }

        // Direct activation
        if ($canProceedToDirectActivation) {
            $this->activateMerchant($merchant, null, $tvaChecker);
        }

    }

    public function notifyBuyerToMerchantRegistrationSteps(string $buyerIgg, Merchant $merchant, string $merchantRegistrationStep, string $reason = "")
    {
        /** @var User */
        $buyer = $this->userRepository->findOneBy(['username' => $buyerIgg]);
        if (!$buyer) {
            throw new InvalidArgumentException(sprintf('buyer not found with igg %s', $buyerIgg));
        }

        $this->mailer->sendEmailMessage(
            MailService::INVITED_SUPPLIER_ONBOARDING_PROGRESS_TO_BUYER,
            $buyer->getLocale(),
            $buyer->getEmail(),
            [
                'supplierName' => $merchant->getName(),
                'registrationStep' => $this->translator->trans(
                    sprintf('buyerInvitation.%s', $merchantRegistrationStep),
                    [],
                    'AppBundle',
                    'fr'
                ),
                'reason' => $reason,
                'marketplace' => $merchant->getMarketplace()->getName()
            ]
        );
    }

    public function canProceedToDirectActivation(?string $source): bool
    {
        return in_array(
            $source,
            [
                self::SOURCE_REGISTRATION_TOTAL,
                self::SOURCE_REGISTRATION_OPERATOR,
            ], true
        );
    }

    public function registrationSourceIsValid(?string $source): bool
    {
        return in_array(
            $source,
            [
                null, // used for direct registration
                self::SOURCE_REGISTRATION_USER,
                self::SOURCE_REGISTRATION_OPERATOR,
                self::SOURCE_REGISTRATION_TOTAL
            ], true
        );
    }

    public static function sanatizeSource(?string $source): string
    {
        $sanatizedSource = self::SOURCE_REGISTRATION_DIRECT;

        if ($source === self::SOURCE_REGISTRATION_TOTAL) {
            $sanatizedSource = self::SOURCE_REGISTRATION_TOTAL;
        }

        if ($source === self::SOURCE_REGISTRATION_OPERATOR) {
            $sanatizedSource = self::SOURCE_REGISTRATION_OPERATOR;
        }

        if ($source === self::SOURCE_REGISTRATION_USER) {
            $sanatizedSource = self::SOURCE_REGISTRATION_USER;
        }

        return $sanatizedSource;
    }

    /**
     * @throws \Exception
     */
    protected function createIzbergMerchant(Merchant $merchant, ?User $author = null, bool $tvaChecker = true)
    {
        $izbergUserId = $this->findExistingMerchantUserId($merchant);

        //if we have found izberg user
        if ($izbergUserId !== null) {
            //here we can consider that the user exist
            $this->logger->info("merchant registration: the user already exists: Create the merchant and associate the existing user ", EventNameEnum::MERCHANT_REGISTER, null, [
                "izbergUserId" => $izbergUserId,
                "merchantEmail" => $merchant->getEmail()
            ]);

            $operatorClient = $this->operatorClientFactory
                ->buildOperatorClient($this->apiClientManager->getConfiguration());

            $merchantAPI = $operatorClient->merchantApi();
            //we create the merchant
            $responseJson = $merchantAPI->createMerchant(
                $merchant->getName(),
                $merchant->getCountry()->getIzbergId(),
                $merchant->getCountry()->getLocale()
            );
            $merchantArray = json_decode($responseJson, true);
            $merchantId = $merchantArray['id'];
            $this->setPermission($merchantId, $izbergUserId);

            $merchant->setIzbergUSerId($izbergUserId);
            $this->merchantRepository->update($merchant, $author);
            $merchantSlug = $merchantArray["slug"];
        } else {


            /////////////////////////////////////////////////////////////////////////////////////////////
            // Create Merchant
            /////////////////////////////////////////////////////////////////////////////////////////////
            $merchantArray = $this->createMerchantAndUser($merchant);
            $this->logger->info("merchant registration: the user doesn't exist. User and merchant has been created ", EventNameEnum::MERCHANT_REGISTER, null, [
                "merchantId" => $merchantArray["id"]
            ]);
            $merchantId = $merchantArray["id"];
            $merchantSlug = $merchantArray['slug'];
            $izbergUserId = $merchantArray['userId'];
        }


        $merchant->setIzbergId($merchantId);
        $merchant->setSlug($merchantSlug);
        $merchant->setStatus(Merchant::STATUS_VALIDATION);

        $this->setMerchantDefaultLanguage($merchantId,$merchant->getCountry()->getLocale());
        $this->setMerchantTaxRate($merchantId, $merchant->getCountry());

        $this->attributeApi->updateMerchantAttribute(
            $this->attributeApi->getMerchantAttributeId($this->customAttributes->getCompanyIdentificationNumber()),
            $merchantId,
            $merchant->getIdentification()
        );

        $this->attributeApi->updateMerchantAttribute(
            $this->attributeApi->getMerchantAttributeId($this->customAttributes->getCorporateName()),
            $merchantId,
            $merchant->getName()
        );

        $this->attributeApi->updateMerchantAttribute(
            $this->attributeApi->getMerchantAttributeId($this->customAttributes->getCategoryList()),
            $merchantId,
            $merchant->getCategories()
        );
        $merchant->setIzbergUSerId($izbergUserId);
        $this->merchantRepository->update($merchant, $author);

        // Log merchant creation
        $this
            ->logger
            ->info(
                'New merchant created with id ' . $merchantId,
                'izberg.merchant.created',
                ($author) ? $author->getUsername() : null,
                ['merchant_request' => $merchant->toArray()]
            );

        /////////////////////////////////////////////////////////////////////////////////////////////
        //create merchant address
        /////////////////////////////////////////////////////////////////////////////////////////////

        $this->merchantApi->addMerchantAddress(
            $merchantId,
            true,
            false,
            $merchant->getFirstname(),
            $merchant->getLastname(),
            '',
            '',
            $merchant->getCountry()->getId(), // TODO review addMerchantAddress
            $merchant->getEmail(),
            $merchant->getPhoneNumber(),
            1 // Authorized values -> mr: 1, mrs: 2, miss: 3
        );


        //set merchant as pending
        $this->merchantApi->checkActivation($merchantId);

        //set merchant currency
        $this->merchantApi->updateMerchantCurrency($merchantId, $merchant->getCurrency());

        $this->merchantApi->updateMerchantLanguage($merchantId, $merchant->getMarketplace()->getLocale());
    }

    private function findExistingMerchantUserId(Merchant $merchant): ?string
    {

        //first look for identity user, then for already used email in db
        $configuration = $this->apiClientManager->getConfiguration();
        $operatorClient = $this->operatorClientFactory
            ->buildOperatorClient($configuration);

        $userIdentity = $operatorClient->userIdentityApi()->findUser($configuration->getDomainId(), $merchant->getEmail());

        if ($userIdentity !== null) {
            return $userIdentity->getUuid();
        }
        return null;
    }

    private function setPermission(int $merchantId, string $userId): void
    {
        //set the permission for this merchant
        $this->logger->info("setting merchant permissions", EventNameEnum::MERCHANT_REGISTER, null,
            [
                "merchantId" => $merchantId,
                "userId" => $userId
            ]);
        if (!IzbergUtils::isIdIdentity($userId)) { //legacy method

            $this->permissionApi->setPermission($merchantId, $userId);

        } else { //identity method

            $operatorClient = $this->operatorClientFactory
                ->buildOperatorClient($this->apiClientManager->getConfiguration());
            $userIdentityApi = $operatorClient->userIdentityApi();
            $userIdentityApi->patchPermission($merchantId, $userId);
        }

    }

    private function createMerchantAndUser(Merchant $merchant): ?array
    {

        $password = $this->encryptor->decrypt($merchant->getPassword());
        $operatorClient = $this->operatorClientFactory
            ->buildOperatorClient($this->apiClientManager->getConfiguration());

        $merchantAPI = $operatorClient->merchantApi();
        $userIdentityApi = $operatorClient->userIdentityApi();
        $companyName = $merchant->getName() ?? "";
        $country = $merchant->getCountry();
        $countryCode = $merchant->getCountry()->getIzbergId();

        try {
            $responseJson = $merchantAPI->createMerchant($companyName, $countryCode,$merchant->getCountry()->getLocale());
            $merchantArray = json_decode($responseJson, true);
            $merchantId = $merchantArray['id'];
            $company = $merchantAPI->getMerchantCompany($merchantId);
            if ($country && $company) {
                $merchantAPI->updateCompany(
                    $company['id'],
                    $country->getIzbergId()
                );
            }

            $izbergMerchantUserData = $userIdentityApi->buildIzbergMerchantUser(
                $merchant->getEmail() ?? "",
                $password,
                $merchant->getFirstname() ?? "",
                $merchant->getLastname() ?? "",
                $merchantId
            );

            $userIdentity = $userIdentityApi->createMerchantUser($izbergMerchantUserData);
            $merchantArray['userId'] = $userIdentity->getUuid();
            return $merchantArray;
        } catch (Exception $exception) {
            $this->logger->error(
                'Merchant creation error with id ' . $merchant->getId(),
                'izberg.merchant.created',
                null,
                ["message" => $exception->getMessage(), "exception" => $exception->getTraceAsString(), 'merchant_request' => $merchant->toArray()]
            );
            throw new Exception("can't create merchant", 0, $exception);
        }
    }

    private function setMerchantDefaultLanguage(int $izbergMerchantId, string $locale)
    {
        $this->merchantApi->updateMerchant(
            $izbergMerchantId,
            [
                'languages' => [$locale],
                'prefered_language' => $locale
            ]
        );
    }

    private function setMerchantTaxRate(int $izbergMerchantId, Country $country)
    {
        $this->merchantApi->updateMerchantTaxRate($izbergMerchantId, $this->taxService->taxRateFromCountry($country));
    }

    /**
     * format an izberg merchant into a model merchant
     *
     * @param \stdClass $izbergMerchant
     *
     * @return ModelMerchant
     */
    private function buildMerchantFromIzbergResponse($izbergMerchant): ModelMerchant
    {
        $result = new ModelMerchant();
        $result->setId($izbergMerchant->id);
        $result->setName($izbergMerchant->name);
        $result->setShortDescription($izbergMerchant->description);
        $result->setRating($izbergMerchant->overall_score);
        $result->setSlug($izbergMerchant->slug);
        $result->setStatus($izbergMerchant->status);

        if (property_exists($izbergMerchant, "profile_image") &&
            $izbergMerchant->profile_image !== null &&
            property_exists($izbergMerchant->profile_image, "image_path")) {
            $result->setLogo($izbergMerchant->profile_image->image_path);
        }

        if (property_exists($izbergMerchant, "addresses")) {
            $addresses = $izbergMerchant->addresses;
            if (is_array($addresses) && isset($addresses[0])) {
                $mainAddress = $addresses[0];
                $result->setMainContactEmail($mainAddress->contact_email ?? null);
                $result->setMainContactFirstName($mainAddress->contact_first_name ?? null);
                $result->setMainContactLastName($mainAddress->contact_last_name ?? null);
                $result->setLanguage($mainAddress->language ?? null);
            }
        }


        $result->setCountry($this->getCountryFromIzbergResponse($izbergMerchant));

        return $result;
    }


    /***
     *
     * Returns a Country Object !
     *
     * @param $izbergMerchant
     *
     * @return \AppBundle\Entity\Country|null
     */
    public function getCountryFromIzbergResponse($izbergMerchant)
    {
        $additionalInformation = '';

        if (isset($izbergMerchant->addresses)) {
            $addresses = $izbergMerchant->addresses;
            if (is_array($addresses)) {
                foreach ($addresses as $address) {
                    if (isset($address->billing_address)) {
                        if ($address->billing_address == true) {
                            if (isset($address->country)) {
                                if (isset($address->country->id)) {
                                    $country = $this->countryService->getCountryByIzbergId($address->country->id);
                                    if ($country instanceof Country) {
                                        return $country;
                                    } else {
                                        $additionalInformation = 'country id = ' . $address->country->id;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // Here we do not have correct information in addresses, try the region
        if (isset($izbergMerchant->region)) {
            $region = $izbergMerchant->region; // 'FR' etc
            $country = $this->countryService->getCountryByIzbergCode($region);
            if ($country instanceof Country) {
                return $country;
            } else {
                $additionalInformation = 'region = ' . $region;
            }
        }

        $this->logger->critical('No usable country found for merchant id = ' . $izbergMerchant->id . ' [' . $additionalInformation . ']', EventNameEnum::PAYMENT_GENERAL_ERROR);
        return null;
    }

    public function paginatedMerchants($data, $page = 1, string $mkp = "FRA", $numberPerPage = 10): PaginationInterface
    {
        return $this->merchantRepository->paginatedMerchants($data, $page, $mkp, $numberPerPage);
    }

    public function getMerchantReviews(int $merchantId, int $offset, int $limit, bool $resetCache = false)
    {
        $cachekey = self::MERCHANT_REVIEWS_CACHE_KEY . $merchantId . '_' . $limit . '_' . $offset;

        if ($resetCache) {
            $this->cache->removeItem($cachekey);
        } else {
            $cacheItem = $this->cache->getItem($cachekey);
            if ($cacheItem !== null) {
                return $cacheItem;
            }
        }

        try {
            $filterString = IzbergUtils::buildQueryUriFromFilters([
                'merchant' => $merchantId,
                'offset' => $offset,
                'limit' => $limit,
                'order_by' => "-created_on"
            ]);

            $requestUrl = $this->merchantReviewApi->getUri() . $filterString;
            $response = $this->merchantReviewApi->sendApiRequest(MerchantReviewApi::HTTP_GET_OPERATION, $requestUrl);

            $merchantReviews = [
                'reviews' => array_map(function ($object) {
                    return $this->serializer->deserialize(json_encode($object), $this->merchantReviewApi->getItemClass());
                }, $response->body->objects),
                'totalCount' => $response->body->meta->total_count
            ];

            $this->cache->saveItem($cachekey, $merchantReviews, self::MERCHANT_REVIEWS_TTL);

            return $merchantReviews;

        } catch (ApiException $e) {
            $this->logger->error("unable to load merchant reviews from izberg API: " . $e->getMessage(), EventNameEnum::TECHNICAL_ERROR, null, ['id' => $merchantId]);
            return null;
        }
    }

    public function getBuyerInvitations(User $buyer)
    {
        return $this->merchantRepository->getBuyerInvitation($buyer);
    }

    public function paginatedAllInvitations(User $buyer, $data, $page = 1, $numberPerPage = 10)
    {
        return $this->merchantRepository->paginatedAllInvitation($buyer, $data, $page, $numberPerPage);
    }

    public function convertToDatatableResponse($draw, $paginator, $local): DataTableResponse
    {

        $dataResponse = new DataTableResponse();
        $data = [];
        /** @var Merchant $merchant */
        foreach ($paginator->getItems() as $merchant) {
            $creator = null;
            if ($merchant->getSource() == MerchantService::SOURCE_REGISTRATION_USER) {
                if (!is_null($merchant->getInviteByIgg())) {
                    $creator = $this->userService->findByUsername($merchant->getInviteByIgg());
                }
            }
            $data[] = $this->convertInvitationToData($merchant, $creator, $local);
        }
        $dataResponse->setDraw($draw);
        $dataResponse->setData($data);
        $dataResponse->setRecordFiltered($paginator->getTotalItemCount());
        $dataResponse->setRecordTotal($paginator->getTotalItemCount());

        return $dataResponse;

    }

    /**
     * @param $merchantId
     * @param $message
     *
     * @return bool
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function reSendInvitation(User $buyer, $merchantId, $message)
    {

        /** @var Merchant $merchant */
        $merchant = $this->merchantRepository->find($merchantId);
        if (!$merchant) {
            return false;
        }

        $this->sendSupplierInvitation($merchant, $buyer, null,$message, MailService::BUYER_INVITATION_REMINDER_TO_SUPPLIER);
        return true;

    }

    /**
     * @param string $invitationToken
     *
     * @return Merchant|null
     */
    public function fetchInvitationFromToken(string $invitationToken): ?Merchant
    {
        return $this->merchantRepository->findOneBy(['token' => $invitationToken]);
    }

    public function findMatchingInvitation(Merchant $merchant): array
    {
        $email = $merchant->getEmail();

        // retrieve email domain
        $pos = strpos($email, '@');
        if (!$pos) {
            return [];
        }
        $emailDomain = substr($email, $pos + 1);

        $qb = $this->em->createQueryBuilder();

        $whereExpr = $qb->expr()->andX(
            $qb->expr()->eq('m.email', ':email')
        );
        $queryParameters = ['email' => $email];

        if (!in_array($emailDomain, $this->commonEmailProviders)) {
            $whereExpr = $qb->expr()->andX(
                $qb->expr()->like('m.email', ':emailLikeDomain')
            );
            $queryParameters = ['emailLikeDomain' => sprintf('%%@%s', $emailDomain)];
        }

        $qb->select('m')
            ->from('AppBundle:Merchant', 'm')
            ->where($whereExpr)
            ->setParameters($queryParameters);

        $query = $qb->getQuery();

        return $query->getArrayResult();
    }

    public function setCommonEmailProviders(array $commonEmailProviders): self
    {
        $this->commonEmailProviders = $commonEmailProviders;
        return $this;
    }

    /**
     * @param boolean $sendEvent true if the supplierInvitationEvent must be throw
     * @param string $mailIdentifier BUYER_INVITIATION_TO_SUPPLIER or BUYER_INVITIATION_REMINDER_TO_SUPPLIER
     *
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function sendSupplierInvitation(Merchant $merchant, User $buyer, ?string $description, ?string $message, $mailIdentifier)
    {
        $this->merchantRepository->save($merchant);
        $receiverEmails = [$merchant->getEmail(), $this->supportOperatorEmail];
        $emailData = [
            'marketplace' => $this->translator->trans(
                sprintf('marketplace.%s', strtolower($buyer->getMarketPlace()->getName())),
                [],
                'AppBundle',
                $buyer->getLocale()
            ),
            'firstNameBuyer' => $buyer->getFirstname(),
            'lastNameBuyer' => $buyer->getLastname(),
            'emailBuyer' => $buyer->getEmail(),
            'firstNameSupplier' => $merchant->getFirstName(),
            'lastNameSupplier' => $merchant->getLastName(),
            'companyNameSupplier' => $merchant->getName(),
            'phoneSupplier' => $merchant->getPhoneNumber(),
            'emailSupplier' => $merchant->getEmail(),
            // The email format is in HTML
            // convert new line character to HTML br tag
            // make sure the template use "raw" to display the message variable : {{message|raw}}
            'message' => str_replace("\r\n", "<br />", $message),
            'urlRegister' => $this->router->generate(
                'front.merchant.registration.source',
                [
                    'source' => $merchant->getToken(),
                    'country_code' => $buyer->getMarketPlace()->getTotalDiscriminator()
                ],
                UrlGeneratorInterface::ABSOLUTE_URL
            )
        ];
        if ($description) {
            $emailData["userNeeds"] = str_replace("\r\n", "<br />", $description);
        }
        $this->mailer->sendEmailMessage(
            $mailIdentifier,
            $buyer->getLocale(),
            $receiverEmails,
            $emailData
        );
    }


    /**
     * extract merchant id from url like
     * "https://api.sandbox.iceberg.technology/v1/merchant/20982/",
     *
     * @param $url
     *
     * @return mixed
     */
    private function extractMerchantIdFromUrl($url)
    {
        $pattern = "#\/merchant\/([0-9]+)\/$#";
        preg_match($pattern, $url, $match);
        if (count($match)) {
            return $match[1];
        }
        return null;
    }


    private function convertInvitationToData(Merchant $merchant, ?User $creator, $local)
    {

        $status = $this->translator->trans(
            sprintf('invitation.status.%s', strtolower($merchant->getStatus())),
            [],
            'AppBundle',
            $local
        );
        $data = new InvitationData();
        $userData = new UserData();

        if ($creator) {
            $userData->setEmail($creator->getEmail())
                ->setLastName($creator->getLastname())
                ->setFirstName(($creator->getFirstname()))
                ->setUserName($creator->getUsername())
                ->setIsBuyer($merchant->getSource() == MerchantService::SOURCE_REGISTRATION_USER);
        } else {
            $userData->setIsBuyer(false);
        }

        $data->setCompanyName($merchant->getName())
            ->setCreator($userData)
            ->setEmail($merchant->getEmail())
            ->setStatus($status)
            ->setSource($merchant->getSource())
            ->setCreatedDate(DateUtil::printDate($merchant->getCreatedDate(), $local));
        return $data;
    }

    public function syncMerchant(bool $init, MarketPlace $marketPlace, ?int $forceMerchant)
    {
        // Filters for api call
        $filters = ['limit' => 100];
        $this->merchantApi->configureApiConnection($marketPlace->getApiConfigurationKey());
        if ($forceMerchant) {
            $this->forceSyncOneMerchant($forceMerchant);
            return;
        }
        // if not init look only last order item
        if (!$init) {
            $date = new DateTime('now');
            //P2D =  2 days
            $date->sub(new DateInterval('P7D'));
            $filters ['last_modified__gt'] = date_format($date, DateUtil::IZBERG_REQUEST_DATE_FORMAT);
        }

        // Synchronizing
        /** @var \Open\IzbergBundle\Model\Merchant $merchant */
        foreach ($this->merchantApi->findByFilters($filters, true) as $merchant) {
            $this->syncOneMerchant($merchant);

        }
    }

    public function forceSyncOneMerchant(int $forceMerchantId)
    {
        $filters ['id'] = $forceMerchantId;
        /** @var \Open\IzbergBundle\Model\Merchant $merchant */
        $merchant = $this->merchantApi->findOneByFilters($filters);
        if ($merchant) {
            $this->syncOneMerchant($merchant, true);
        }
    }

    private function syncOneMerchant(\Open\IzbergBundle\Model\Merchant $merchant, bool $force = false)
    {
        // get merchant id
        $merchantIzbId = $merchant->getId();

        $this->logger->info("detected change on merchantId:" . $merchantIzbId, EventNameEnum::COMMAND_SYNC_MERCHANT, null,
            array(
                "name" => $merchant->getName(),
                "id" => $merchant->getId(),
                "new_slug" => $merchant->getSlug()
            ));


        /** @var Merchant $merchantEntity */
        $merchantEntity = $this->merchantRepository->findOneBy(array("izbergId" => $merchant->getId()));
        // props init

        if ($force || (!is_null($merchantEntity) && $merchantEntity->getName() != $merchant->getName())) {

            $oldName = $merchantEntity->getName();
            $merchantEntity->setName($merchant->getName());
            $merchantEntity->setSlug($merchant->getSlug());
            $this->em->flush();

            $this->logger->info("merchantId:" . $merchantIzbId, 'MERCHANT_UPDATED', null,
                array("old_name" => $oldName,
                    "new_name" => $merchant->getName(),
                    "id" => $merchant->getId(),
                    "new_slug" => $merchant->getSlug()
                ));

            $this->notifyUpdatedMerchants(array($merchant->getId()));

        }
    }

    private function getCustomAddressList(string $customKey, int $merchantId): array
    {

        $list = [];
        $addressStr = $this->attributeApi->getMerchantAttribute($customKey, $merchantId, true);
        if (!is_null($addressStr)) {
            $addressStr = trim($addressStr);
            if (strlen($addressStr) > 0) {
                $list = explode(';', $addressStr);
            }
        }
        return $list;
    }


    /**
     *  as permission api can't see permission on other izbergPf we will store izbergUserId in db and retrieve it from there
     *
     * @param Merchant $merchant
     *
     * @return string|null
     */
    private function findIzbergUserIdFromDatabase(Merchant $merchant)
    {
        /** @var Merchant $merchant */
        $merchant = $this->merchantRepository->findMerchantByEmailOtherThanThisId($merchant->getEmail(), $merchant->getId());
        if ($merchant) {
            return $merchant->getIzbergUSerId();
        }
        return null;
    }

    public function getMarketplaceUrl(Merchant $merchant)
    {
        if (array_key_exists($merchant->getMarketplace()->getName(), $this->middleBaseUrl)) {
            return $this->middleBaseUrl[$merchant->getMarketplace()->getName()];
        }
        $this->logger->error('cant determine marketplace url', "ACTIVATE_MERCHANT", ["merchantId" => $merchant->getId()]);
        return "ERROR";
    }

    public function saveMerchant(Merchant $merchant)
    {
        $this->merchantRepository->save($merchant);
    }

    public function sendBuyerInvitationOperatorsNotification(Merchant $merchant, User $buyer, $description, ?string $message, $mailIdentifier)
    {
        $this->merchantRepository->save($merchant);
        $operatorsEmails = array_map(fn(User $operator) => $operator->getEmail(), $this->securityService->getOperators());
        $receiverEmails = array_merge([$merchant->getEmail()], $operatorsEmails);

        $urlRegister = $this->router->generate(
            'front.merchant.registration.source',
            [
                'source' => $merchant->getToken(),
                'country_code' => $buyer->getMarketPlace()->getTotalDiscriminator()
            ],
            UrlGeneratorInterface::ABSOLUTE_URL
        );

        $this->mailer->sendEmailMessage($mailIdentifier, $buyer->getLocale(), $receiverEmails, [
            'marketplace' => $this->translator->trans(
                sprintf('marketplace.%s', strtolower($buyer->getMarketPlace()->getName())),
                [],
                'AppBundle',
                $buyer->getLocale()
            ),
            'firstNameBuyer' => $buyer->getFirstname(),
            'lastNameBuyer' => $buyer->getLastname(),
            'emailBuyer' => $buyer->getEmail(),
            'firstNameSupplier' => $merchant->getFirstName(),
            'lastNameSupplier' => $merchant->getLastName(),
            'companyNameSupplier' => $merchant->getName(),
            'phoneSupplier' => $merchant->getPhoneNumber(),
            'emailSupplier' => $merchant->getEmail(),
            'message' => str_replace("\r\n", "<br />", $message),
            'userNeeds' => str_replace("\r\n", "<br />", $description),
            'urlRegister' => $urlRegister,
        ]);
    }

    /**
     * @param string|null $text
     * @param SearchFilterQuery|null $filterQuery
     * @param Country $country
     * @param MarketPlace $marketPlace
     *
     * @return SearchResult
     */
    public function searchActiveMerchants(
        ?string            $text,
        ?SearchFilterQuery $filterQuery,
        Country            $country,
        MarketPlace        $marketPlace
    ): SearchResult
    {
        $filterQuery = $filterQuery ?? (new SearchFilterQuery());

        $this->searchService->enableActiveMerchantFilter($filterQuery);
        $this->searchService->enableCountryOfDeliveryFilter($filterQuery, $country);


        if (!is_null($this->user)) {
            $this->searchService->addMustBeEqualsIfExist($filterQuery, 'merchant.branches.keyword', $this->user->getBranch());
            $this->searchService->enableFilterEquipment($filterQuery, $this->user);
        }

        return $this->search(
            $text,
            $filterQuery,
            $marketPlace
        );
    }

    /**
     * Method to search offers => use the search service
     *
     * @param null|string $text for full text search. Example "Wheel"
     * @param SearchFilterQuery|null $filterQuery list of filters. Example ["status" => "active"]
     * @param MarketPlace $marketPlace => marketplace
     *
     * @return SearchResult
     */
    public function search(
        ?string            $text,
        ?SearchFilterQuery $filterQuery,
        MarketPlace        $marketPlace
    ): SearchResult
    {
        ///////////////////////////////////////////////////////////////////
        /// Call search service and translate objects
        ///////////////////////////////////////////////////////////////////

        return
            $this->searchService->searchMerchants(
                new SearchRequest(
                    $text,
                    0,
                    0,
                    "",
                    null,
                    null,
                    $filterQuery,
                    null,
                    $marketPlace
                )
            );
    }

    public function searchResultToMerchantArray(SearchResult $searchResult, string $query): array
    {
        $merchantsArray = [];
        if ($searchResult->getSize()) {
            foreach ($searchResult->getHits() as $aggResult) {
                $merchantName = reset($aggResult["merchant.name"]["buckets"])["key"];
                if (stripos($this->removeAccents($merchantName), $this->removeAccents($query)) !== false) {
                    $merchantsArray[$aggResult["key"]] = $merchantName;
                }

            }
        }
        return $merchantsArray;
    }

    public function removeAccents(string $str): string
    {
        $a = array('À', 'Á', 'Â', 'Ã', 'Ä', 'Å', 'Æ', 'Ç', 'È', 'É', 'Ê', 'Ë', 'Ì', 'Í', 'Î', 'Ï', 'Ð', 'Ñ', 'Ò', 'Ó', 'Ô', 'Õ', 'Ö', 'Ø', 'Ù', 'Ú', 'Û', 'Ü', 'Ý', 'ß', 'à', 'á', 'â', 'ã', 'ä', 'å', 'æ', 'ç', 'è', 'é', 'ê', 'ë', 'ì', 'í', 'î', 'ï', 'ñ', 'ò', 'ó', 'ô', 'õ', 'ö', 'ø', 'ù', 'ú', 'û', 'ü', 'ý', 'ÿ', 'Ā', 'ā', 'Ă', 'ă', 'Ą', 'ą', 'Ć', 'ć', 'Ĉ', 'ĉ', 'Ċ', 'ċ', 'Č', 'č', 'Ď', 'ď', 'Đ', 'đ', 'Ē', 'ē', 'Ĕ', 'ĕ', 'Ė', 'ė', 'Ę', 'ę', 'Ě', 'ě', 'Ĝ', 'ĝ', 'Ğ', 'ğ', 'Ġ', 'ġ', 'Ģ', 'ģ', 'Ĥ', 'ĥ', 'Ħ', 'ħ', 'Ĩ', 'ĩ', 'Ī', 'ī', 'Ĭ', 'ĭ', 'Į', 'į', 'İ', 'ı', 'Ĳ', 'ĳ', 'Ĵ', 'ĵ', 'Ķ', 'ķ', 'Ĺ', 'ĺ', 'Ļ', 'ļ', 'Ľ', 'ľ', 'Ŀ', 'ŀ', 'Ł', 'ł', 'Ń', 'ń', 'Ņ', 'ņ', 'Ň', 'ň', 'ŉ', 'Ō', 'ō', 'Ŏ', 'ŏ', 'Ő', 'ő', 'Œ', 'œ', 'Ŕ', 'ŕ', 'Ŗ', 'ŗ', 'Ř', 'ř', 'Ś', 'ś', 'Ŝ', 'ŝ', 'Ş', 'ş', 'Š', 'š', 'Ţ', 'ţ', 'Ť', 'ť', 'Ŧ', 'ŧ', 'Ũ', 'ũ', 'Ū', 'ū', 'Ŭ', 'ŭ', 'Ů', 'ů', 'Ű', 'ű', 'Ų', 'ų', 'Ŵ', 'ŵ', 'Ŷ', 'ŷ', 'Ÿ', 'Ź', 'ź', 'Ż', 'ż', 'Ž', 'ž', 'ſ', 'ƒ', 'Ơ', 'ơ', 'Ư', 'ư', 'Ǎ', 'ǎ', 'Ǐ', 'ǐ', 'Ǒ', 'ǒ', 'Ǔ', 'ǔ', 'Ǖ', 'ǖ', 'Ǘ', 'ǘ', 'Ǚ', 'ǚ', 'Ǜ', 'ǜ', 'Ǻ', 'ǻ', 'Ǽ', 'ǽ', 'Ǿ', 'ǿ', 'Ά', 'ά', 'Έ', 'έ', 'Ό', 'ό', 'Ώ', 'ώ', 'Ί', 'ί', 'ϊ', 'ΐ', 'Ύ', 'ύ', 'ϋ', 'ΰ', 'Ή', 'ή');
        $b = array('A', 'A', 'A', 'A', 'A', 'A', 'AE', 'C', 'E', 'E', 'E', 'E', 'I', 'I', 'I', 'I', 'D', 'N', 'O', 'O', 'O', 'O', 'O', 'O', 'U', 'U', 'U', 'U', 'Y', 's', 'a', 'a', 'a', 'a', 'a', 'a', 'ae', 'c', 'e', 'e', 'e', 'e', 'i', 'i', 'i', 'i', 'n', 'o', 'o', 'o', 'o', 'o', 'o', 'u', 'u', 'u', 'u', 'y', 'y', 'A', 'a', 'A', 'a', 'A', 'a', 'C', 'c', 'C', 'c', 'C', 'c', 'C', 'c', 'D', 'd', 'D', 'd', 'E', 'e', 'E', 'e', 'E', 'e', 'E', 'e', 'E', 'e', 'G', 'g', 'G', 'g', 'G', 'g', 'G', 'g', 'H', 'h', 'H', 'h', 'I', 'i', 'I', 'i', 'I', 'i', 'I', 'i', 'I', 'i', 'IJ', 'ij', 'J', 'j', 'K', 'k', 'L', 'l', 'L', 'l', 'L', 'l', 'L', 'l', 'l', 'l', 'N', 'n', 'N', 'n', 'N', 'n', 'n', 'O', 'o', 'O', 'o', 'O', 'o', 'OE', 'oe', 'R', 'r', 'R', 'r', 'R', 'r', 'S', 's', 'S', 's', 'S', 's', 'S', 's', 'T', 't', 'T', 't', 'T', 't', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'W', 'w', 'Y', 'y', 'Y', 'Z', 'z', 'Z', 'z', 'Z', 'z', 's', 'f', 'O', 'o', 'U', 'u', 'A', 'a', 'I', 'i', 'O', 'o', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'A', 'a', 'AE', 'ae', 'O', 'o', 'Α', 'α', 'Ε', 'ε', 'Ο', 'ο', 'Ω', 'ω', 'Ι', 'ι', 'ι', 'ι', 'Υ', 'υ', 'υ', 'υ', 'Η', 'η');
        return str_replace($a, $b, $str);
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        if ($user) {
            $this->searchService->setBranch($user->getBranch());
        }

        return $this;
    }
}
