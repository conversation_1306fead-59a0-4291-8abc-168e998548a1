<?php

namespace AppBundle\Services;

use AppBundle\Entity\BuyerAddress;
use AppBundle\Entity\TotalAddress;
use AppBundle\Entity\User;
use AppBundle\Model\BuyerAddressModel;
use AppBundle\Repository\AddressRepository;
use AppBundle\Repository\BuyerAddressRepository;
use AppBundle\Repository\TotalAddressRepository;
use Doctrine\ORM\ORMException;
use Open\IzbergBundle\Api\AddressApi;
use Open\IzbergBundle\Model\Address;
use Open\IzbergBundle\Model\Country;
use Open\LogBundle\Service\LogService;

class AddressService
{
    private const API_COUNTRY_URL = '/v1/country/';

    private AddressApi $addressApi;
    private TotalAddressRepository $totalAddressRepository;
    private BuyerAddressRepository $buyerAddressRepository;
    private CountryService $countryService;
    private LogService $logService;

    /**
     * @deprecated
     */
    private AddressRepository $addressRepository;

    private string $shippingAddressHeader;

    public function __construct(
        string                 $shippingAddressHeader,
        AddressApi             $apiAddress,
        TotalAddressRepository $totalAddressRepository,
        BuyerAddressRepository $buyerAddressRepository,
        CountryService         $countryService,
        LogService             $logService,
        AddressRepository      $addressRepository
    )
    {
        $this->shippingAddressHeader = $shippingAddressHeader;
        $this->addressApi = $apiAddress;
        $this->totalAddressRepository = $totalAddressRepository;
        $this->buyerAddressRepository = $buyerAddressRepository;
        $this->countryService = $countryService;
        $this->logService = $logService;
        $this->addressRepository = $addressRepository;
    }
    /**
     * @param BuyerAddress $buyerAddress
     * @return BuyerAddressModel
     */
    public function fromBuyerAddressEntityToModel(?BuyerAddress $buyerAddress): BuyerAddressModel
    {
        $buyerAddressModel = new BuyerAddressModel();

        if ($buyerAddress) {
            $buyerAddressModel->setTechnicalId($buyerAddress->getTechnicalId());
            $buyerAddressModel->setType($buyerAddress->getType());
            $buyerAddressModel->setName($buyerAddress->getName());
            $buyerAddressModel->setAddress($buyerAddress->getAddress());
            $buyerAddressModel->setAddress2($buyerAddress->getAddress2());
            $buyerAddressModel->setZipCode($buyerAddress->getZipCode());
            $buyerAddressModel->setCity($buyerAddress->getCity());
            $buyerAddressModel->setCountry($buyerAddress->getCountry());
            $buyerAddressModel->setContact($buyerAddress->getContact());
            $buyerAddressModel->setPhone($buyerAddress->getPhone());
            $buyerAddressModel->setComment($buyerAddress->getComment());
        }

        return $buyerAddressModel;
    }

    /**
     * @param TotalAddress $totalAddress
     * @return BuyerAddressModel
     */
    public function fromTotalAddressEntityToModel(?TotalAddress $totalAddress): BuyerAddressModel
    {
        $buyerAddressModel = new BuyerAddressModel();

        if ($totalAddress) {
            $buyerAddressModel->setType(BuyerAddress::TYPE_TOTAL);
            $buyerAddressModel->setName($totalAddress->getName());
            $buyerAddressModel->setAddress($totalAddress->getAddress());
            $buyerAddressModel->setAddress2($totalAddress->getAddress2());
            $buyerAddressModel->setZipCode($totalAddress->getZipCode());
            $buyerAddressModel->setCity($totalAddress->getCity());
            $buyerAddressModel->setCountry($totalAddress->getCountry());
        }

        return $buyerAddressModel;
    }

    /**
     * @param User $user
     * @return array
     */
    public function getBuyerSiteAndPersonalAddressList(User $user, string $term): array
    {
        $addressList = [];

        $personalAddress = $this->buyerAddressRepository->findTermMultiColumn($user, $term);

        foreach ($personalAddress as $address) {
            $addressList[] = $this->fromBuyerAddressEntityToModel($address);
        }

        $totalAddress = $this->totalAddressRepository
            ->findTermMultiColumn($user->getCountryOfDelivery(), $term);
        foreach ($totalAddress as $address) {
            if ($address instanceof TotalAddress) {
                /** @var BuyerAddress $buyerAddress */
                $buyerAddress = $this->buyerAddressRepository
                    ->findOneBy([
                        'user' => $user,
                        'type' => BuyerAddress::TYPE_TOTAL,
                        'is_active' => true,

                        'name' => $address->getName(),
                        'address' => $address->getAddress(),
                        'address2' => $address->getAddress2(),
                        'zipCode' => $address->getZipCode(),
                        'city' => $address->getCity(),
                        'country' => $address->getCountry(),
                    ]);

                if ($buyerAddress) {
                    $addressList[] = $this->fromBuyerAddressEntityToModel($buyerAddress);
                } elseif ($user->getCountryOfDelivery() === $address->getCountry()) {
                    $addressList[] = $this->fromTotalAddressEntityToModel($address);
                }
            }
        }

        return $addressList;
    }

    /**
     * @param TotalAddress $totalAddress
     * @return TotalAddress
     * @throws ORMException
     */
    public function createTotalAddressIfNotExists(TotalAddress $totalAddress): TotalAddress
    {
        // Truncate address2 if it exceeds 100 characters
        if ($totalAddress->getAddress2() && strlen($totalAddress->getAddress2()) > 100) {
            $totalAddress->setAddress2(substr($totalAddress->getAddress2(), 0, 100));
            $this->logService->info(
                sprintf(
                    'Address2 truncated for total address %s: %s',
                    $totalAddress->getName(),
                    $totalAddress->getAddress2()
                ),
                self::class . '::createTotalAddressIfNotExists'
            );
        }

        /** @var TotalAddress $existingTotalAddress */
        $existingTotalAddress = $this->totalAddressRepository->findOneBy([
            'name' => $totalAddress->getName(),
            'address' => $totalAddress->getAddress(),
            'address2' => $totalAddress->getAddress2(),
            'zipCode' => $totalAddress->getZipCode(),
            'city' => $totalAddress->getCity(),
            'country' => $totalAddress->getCountry()
        ]);

        return $existingTotalAddress ?? $this->totalAddressRepository->save($totalAddress);
    }

    public function isTotalAddressExist(TotalAddress $totalAddress): bool
    {
        /** @var TotalAddress $existingTotalAddress */
        $countTotalAddress = $this->totalAddressRepository->count([
            'name' => $totalAddress->getName(),
            'address' => $totalAddress->getAddress(),
            'address2' => $totalAddress->getAddress2(),
            'zipCode' => $totalAddress->getZipCode(),
            'city' => $totalAddress->getCity(),
            'country' => $totalAddress->getCountry()
        ]);

        return $countTotalAddress > 0;
    }

    /**
     * @param User $user
     * @param string $type
     * @param BuyerAddressModel $buyerAddressModel
     * @return BuyerAddress
     */
    public function getBuyerAddressAndCreateItIfNotExists(
        User $user,
        string $type,
        BuyerAddressModel $buyerAddressModel,
        $batchMode = false
    ): ?BuyerAddress {
        try {
            /** @var BuyerAddress $existsBuyerAddress */
            $existsBuyerAddress = $this->buyerAddressRepository->findOneBy([
                'user' => $user,
                'type' => $type,

                'name' => $buyerAddressModel->getName(),
                'address' => $buyerAddressModel->getAddress(),
                'address2' => $buyerAddressModel->getAddress2(),
                'zipCode' => $buyerAddressModel->getZipCode(),
                'city' => $buyerAddressModel->getCity(),
                'country' => $buyerAddressModel->getCountry(),

                'contact' => $buyerAddressModel->getContact(),
                'phone' => $buyerAddressModel->getPhone(),
                'comment' => $buyerAddressModel->getComment(),
            ]);

            if (!$existsBuyerAddress) {
                $existsBuyerAddress = new BuyerAddress();

                $existsBuyerAddress->setUser($user);
                $existsBuyerAddress->setType($type);

                $existsBuyerAddress->setName($buyerAddressModel->getName());
                $existsBuyerAddress->setAddress(($buyerAddressModel->getAddress()));
                $existsBuyerAddress->setAddress2($buyerAddressModel->getAddress2());
                $existsBuyerAddress->setZipCode($buyerAddressModel->getZipCode());
                $existsBuyerAddress->setCity($buyerAddressModel->getCity());
                $existsBuyerAddress->setCountry($buyerAddressModel->getCountry());
                $existsBuyerAddress->setContact($buyerAddressModel->getContact());
                $existsBuyerAddress->setPhone($buyerAddressModel->getPhone());
                $existsBuyerAddress->setComment($buyerAddressModel->getComment());
            }

            $existsBuyerAddress->setIsActive(true);
            if ($batchMode) {
                $existsBuyerAddress = $this->buyerAddressRepository->saveWithoutFlush($existsBuyerAddress);
            } else {
                $existsBuyerAddress = $this->buyerAddressRepository->save($existsBuyerAddress);
            }


            return $existsBuyerAddress;
        } catch (ORMException $e) {
            $this->logService->error(
                $e->getMessage(),
                self::class . '::getBuyerAddressAndCreateItIfNotExists'
            );

            return null;
        }
    }

    /**
     * @param User $user
     * @param string $technicalId
     * @return BuyerAddress|null
     */
    public function findBuyerAddressFromPersonalAddresses(User $user, string $technicalId): ?BuyerAddress
    {
        /** @var BuyerAddress $personalBuyerAddress */
        $personalBuyerAddress = $this->buyerAddressRepository->findOneBy([
            'user' => $user,
            'technicalId' => $technicalId,
            'type' => BuyerAddress::TYPE_PERSONAL,
        ]);

        return $personalBuyerAddress;
    }

    public function getBuyerPersonalAddressList(User $buyer): array
    {
        return $this->buyerAddressRepository
            ->findBy([
                'user' => $buyer,
                'type' => BuyerAddress::TYPE_PERSONAL,
                'is_active' => true,
            ]);
    }

    /**
     * @param User $user
     * @param string $technicalId
     */
    public function setTotalBuyerAddressInactive(User $user, string $technicalId): void
    {
        /** @var BuyerAddress $buyerAddress */
        $buyerAddress = $this->buyerAddressRepository
            ->findOneBy([
                'user' => $user,
                'technicalId' => $technicalId,
            ]);

        if ($buyerAddress) {
            $buyerAddress->setIsActive(false);

            try {
                $this->buyerAddressRepository->save($buyerAddress);
            } catch (ORMException $e) {
                $this->logService->error(
                    $e->getMessage(),
                    self::class . '::setBuyerAddressInactive'
                );
            }
        }
    }

    /**
     * @param User $user
     * @param string $technicalId
     */
    public function setPersonalBuyerAddressInactive(User $user, string $technicalId): void
    {
        /** @var BuyerAddress */
        $buyerAddress = $this->buyerAddressRepository
            ->findOneBy([
                'user' => $user,
                'type' => BuyerAddress::TYPE_PERSONAL,
                'technicalId' => $technicalId,
            ]);

        if ($buyerAddress) {
            $buyerAddress->setIsActive(false);

            try {
                $this->buyerAddressRepository->save($buyerAddress);
            } catch (ORMException $e) {
                $this->logService->error(
                    $e->getMessage(),
                    self::class . '::setBuyerAddressInactive'
                );
            }
        }
    }

    /**
     * @param BuyerAddress $buyerAddress
     * @param bool $isShipping construct a shipping address or not
     * @return int|null
     */
    public function createIzbergIfNotExistsBuyerAddress(BuyerAddress $buyerAddress, bool $isShipping = false): ?int
    {
        if ($isShipping) {
            $izbergAddress = $this->buildShippingIzbergAddress($buyerAddress);
        } else {
            $izbergAddress = $this->buildIzbergAddress($buyerAddress);
        }

        if (!$buyerAddress->getIzbergAddressId()) {
            $buyerAddress->setIzbergAddressId($this->addressApi->createAddress($izbergAddress));
        } else {
            $buyerAddress->setIzbergAddressId(
                $this->addressApi->updateAddress($buyerAddress->getIzbergAddressId(), $izbergAddress)
            );
        }
        try {
            $buyerAddress = $this->buyerAddressRepository->save($buyerAddress);
        } catch (ORMException $e) {
            $this->logService->error(
                $e->getMessage(),
                self::class . '::createIzbergIfNotExistsShippingAddress'
            );
        }

        return $buyerAddress->getIzbergAddressId();
    }


    public function getLastUsedTotalRecipient(User $user, BuyerAddress $buyerAddress): ?BuyerAddress
    {
        /** @var BuyerAddress $lastBuyerAddress */
        $lastBuyerAddress = $this->buyerAddressRepository
            ->findOneBy([
                'user' => $user,
                'type' => BuyerAddress::TYPE_TOTAL,
                'is_active' => true,

                'name' => $buyerAddress->getName(),
                'address' => $buyerAddress->getAddress(),
                'address2' => $buyerAddress->getAddress2(),
                'zipCode' => $buyerAddress->getZipCode(),
                'city' => $buyerAddress->getCity(),
                'country' => $buyerAddress->getCountry(),
            ]);

        return $lastBuyerAddress;
    }

    /**
     * @param BuyerAddress $buyerAddress
     * @return Address
     */
    private function buildIzbergAddress(BuyerAddress $buyerAddress): Address
    {
        // Izberg Address
        $izbergAddress = new Address();

        $izbergAddress->setName($buyerAddress->getContact() ?? $buyerAddress->getName());
        $izbergAddress->setFirstName($buyerAddress->getContact() ?? $buyerAddress->getUser()->getFirstname());
        $izbergAddress->setLastName($buyerAddress->getContact() ? ' ' : $buyerAddress->getUser()->getLastname());
        $izbergAddress->setAddress($buyerAddress->getAddress());
        $izbergAddress->setAddress2($buyerAddress->getAddress2());
        $izbergAddress->setZipcode($buyerAddress->getZipcode());
        $izbergAddress->setCity($buyerAddress->getCity());
        $country = (new Country())->setResourceUri(
            self::API_COUNTRY_URL . $buyerAddress->getCountry()->getIzbergId() . '/'
        );
        $izbergAddress->setCountry($country);

        return $izbergAddress;
    }


// todo: delete with refactoring of invoice entities

    /**
     * @param string $address1
     * @param string|null $address2
     * @param string|null $zipCode
     * @param string|null $city
     * @param \AppBundle\Entity\Country|null $country
     * @return \AppBundle\Entity\Address
     * @deprecated
     *
     */
    public function createAddressIfNotExist(
        string $address1,
        ?string $address2 = '',
        ?string $zipCode = null,
        ?string $city = null,
        ?\AppBundle\Entity\Country $country = null
    ): \AppBundle\Entity\Address {
        $address = $this->addressRepository->findAddress(
            $address1,
            $address2,
            $zipCode,
            $city,
            null,
            $country
        );

        if (!$address) {
            $address = $this->createAddress(
                $address1,
                $address2,
                $zipCode,
                $city,
                null,
                $country
            );
        }

        return $address;
    }

    /** @deprecated */
    public function createAddress(
        string $address1,
        ?string $address2 = '',
        ?string $zipCode = null,
        ?string $city = null,
        ?string $regionText = null,
        ?\AppBundle\Entity\Country $country = null
    ): \AppBundle\Entity\Address {
        $address = new \AppBundle\Entity\Address();

        $address->setAddress($address1);
        $address->setAddress2($address2);
        $address->setZipCode($zipCode);
        $address->setCity($city);
        $address->setRegionText($regionText);

        if ($country) {
            $address->setCountry($country);
        } else {
            // TODO Remove this default address when Total Site address were fixed

            $defaultCountry = $this->countryService->getCountryByIzbergCode(['FR']);
            $address->setCountry($defaultCountry);
        }

        $this->addressRepository->save($address);
        return $address;
    }

    public function findTotalAddress(Address $address): ?TotalAddress
    {
        return $this->totalAddressRepository->findByIzbergAddress($address);
    }

    public function findBuyerAddress(Address $address): ?BuyerAddress
    {
        return $this->buyerAddressRepository->findOneBy(["izbergAddressId" => $address->getId()]);
    }

    /**
     * @param User $user
     * @return BuyerAddress|null
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function findLastUsedShippingAddress(User $user): ?BuyerAddress
    {
        $lastUsedAddressEntity = $this->buyerAddressRepository->getLastUsedAddress($user->getId());

        return $lastUsedAddressEntity;
    }

    public function findLastUsedShippingAddresses(User $user): array
    {
        $lastUsedAddressesEntity = $this->buyerAddressRepository->getLastUsedAddresses($user->getId());
        $addresses = $this->addOtherPersonalAddress($user, $lastUsedAddressesEntity);

        return array_map([$this, 'fromBuyerAddressEntityToModel'],
            $addresses
        );
    }

    private function addOtherPersonalAddress(User $user, array $lastUsedAddressesEntity): array
    {
        $personalAddresses = $this->buyerAddressRepository->findBy(
            array("user" => $user, "is_active" => true, "type" => BuyerAddress::TYPE_PERSONAL)
        );
        $personalAddresses = array_values(
            array_filter(
                $personalAddresses,
                function (BuyerAddress $buyerAddress) use ($lastUsedAddressesEntity) {
                    return !$this->addressListContains($buyerAddress, $lastUsedAddressesEntity);
                }
            )
        );
        return array_merge($lastUsedAddressesEntity, $personalAddresses);
    }

    private function addressListContains(BuyerAddress $addressTest, array $lastUsedAddressesEntity): bool
    {
        /** @var BuyerAddress $buyerAddress */
        foreach ($lastUsedAddressesEntity as $buyerAddress) {
            if ($buyerAddress->getId() == $addressTest->getId()) {
                return true;
            }
        }
        return false;
    }

    /**
     * TOTALMP-1872
     * @param BuyerAddress $buyerAddress
     * @return Address
     */
    private function buildShippingIzbergAddress(BuyerAddress $buyerAddress): Address
    {
        // Izberg Address
        /** @var Address $izbergAddress */
        $izbergAddress = $this->buildIzbergAddress($buyerAddress);

        $izbergAddress->setAddress2($izbergAddress->getAddress() . " " . $izbergAddress->getAddress2());
        $izbergAddress->setAddress(
            $this->shippingAddressHeader . " / " . $buyerAddress->getUser()->getInvoiceEntity()->getName()
        );
        $izbergAddress->setPhone($buyerAddress->getPhone());

        return $izbergAddress;
    }
}
