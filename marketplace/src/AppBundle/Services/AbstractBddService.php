<?php

namespace AppBundle\Services;

use Doctrine\ORM\EntityManagerInterface;

class AbstractBddService
{
    protected $em;
    protected $repository;
    protected $entityName;

    public function __construct(EntityManagerInterface $em, string $entityName)
    {
        $this->em = $em;
        $this->repository = $em->getRepository($entityName);
        $this->entityName = $entityName;
    }

    public function create($entity): void{
        $this->em->persist($entity);
    }

    public function findAll(){
        return $this->repository->findAll();
    }

    public function findById(int $id){
        return $this->repository->find($id);
    }
}
