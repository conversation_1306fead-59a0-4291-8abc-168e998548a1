<?php

namespace AppBundle\Services;

use AppBundle\Entity\MarketPlace;
use AppBundle\Entity\MetaCart;
use AppBundle\Entity\OrderItem as OrderItemEntity;
use AppBundle\Entity\Site;
use AppBundle\Entity\User;
use AppBundle\Repository\MerchantOrderRepository;
use AppBundle\Repository\OrderItemRepository;
use AppBundle\Util\DateUtil;
use DateInterval;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Exception;
use Monolog\Handler\StreamHandler;
use Open\IzbergBundle\Api\IzbergUtils;
use Open\IzbergBundle\Api\OrderApi;
use Open\IzbergBundle\Api\OrderItemApi;
use Open\IzbergBundle\Api\PaymentApi;
use Open\IzbergBundle\Model\Category;
use Open\IzbergBundle\Model\MerchantOrder;
use Open\IzbergBundle\Model\Order;
use Open\IzbergBundle\Model\OrderItem;
use Open\IzbergBundle\Model\OrderMerchant;
use Open\IzbergBundle\Service\AttributeService;
use Open\IzbergBundle\Service\CategoryService;
use Open\IzbergBundle\Service\RedisService;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Psr\Cache\InvalidArgumentException;
use Symfony\Bridge\Monolog\Logger;
use Symfony\Contracts\Translation\TranslatorInterface;

class OrderService
{
    private const ORDER_ITEM_DEFAULT_LOCALE = 'fr';
    private const ORDER_ITEM_DEFAULT_CATEGORY_LEVEL = 1;
    private const ORDER_ITEM_ROOT_CATEGORY_LEVEL = 0;
    private const ORDER_ITEM_CATEGORY_LEVEL = 2;
    private const TRANSLATION_BUNDLE = 'AppBundle';
    public const JOB_QUEUE_MERCHANT_ORDER_EXTRA_INFOS = 'merchant_order_extra_infos';

    private const ORDER_ITEM_TO_SYNC_STATUS = [OrderItem::STATUS_CONFIRMED, OrderItem::STATUS_PROCESSED, OrderItem::STATUS_INCIDENT_CLOSED ];

    private EntityManagerInterface $entityManager;
    private OrderApi $orderApi;
    private OrderItemApi $orderItemApi;
    private SerializerService $serializerService;
    private PaymentApi $paymentApi;
    private AttributeService $izbergAttributesService;
    private MerchantOrderRepository $merchantOrderRepository;
    private LogService $logger;
    private IzbergCustomAttributes $izbergCustomAttributes;
    private OrderItemRepository $orderItemRepository;
    private RedisService $cacheService;
    private OfferService $offerService;
    private CategoryService $categoryService;
    private CurrencyExchangeRateService $currencyRateService;
    private MetaCartMerchantService $metaCartMerchantService;
    private TranslatorInterface $translator;
    private Logger $consoleLogger;

    public function __construct(
        EntityManagerInterface $entityManager,
        OrderApi $orderApi,
        SerializerService $serializerService,
        PaymentApi $paymentApi,
        AttributeService $izbergAttributesService,
        MerchantOrderRepository $merchantOrderRepository,
        LogService $logger,
        IzbergCustomAttributes $izbergCustomAttributes,
        OrderItemApi $orderItemApi,
        OrderItemRepository $orderItemRepository,
        RedisService $cacheService,
        OfferService $offerService,
        CategoryService $categoryService,
        CurrencyExchangeRateService $currencyRateService,
        MetaCartMerchantService $metaCartMerchantService,
        TranslatorInterface $translator
    ) {
        $this->entityManager = $entityManager;
        $this->orderApi = $orderApi;
        $this->serializerService = $serializerService;
        $this->paymentApi = $paymentApi;
        $this->izbergAttributesService = $izbergAttributesService;
        $this->merchantOrderRepository = $merchantOrderRepository;
        $this->logger = $logger;
        $this->izbergCustomAttributes = $izbergCustomAttributes;
        $this->orderItemApi = $orderItemApi;
        $this->orderItemRepository = $orderItemRepository;
        $this->cacheService = $cacheService;

        $this->offerService = $offerService;

        $this->categoryService = $categoryService;
        $this->currencyRateService = $currencyRateService;
        $this->metaCartMerchantService = $metaCartMerchantService;
        $this->translator = $translator;
        $this->setConsoleLogger();
    }

    private function setConsoleLogger()
    {
        $this->consoleLogger = new Logger('console', [
            new StreamHandler('php://stdout', Logger::DEBUG),
        ]);
    }

    public function acceptMerchantOrder(\AppBundle\Entity\MerchantOrder $merchantOrder): bool
    {
        if ($merchantOrder->isPendingForSupplierValidation()) {
            return true;
        }

        if (!$merchantOrder->isPendingForManagerValidation()) {
            return false;
        }

        try {
            $this->orderApi->startUserConnection($merchantOrder->getBuyer());
            $izbergOrder = $this->fetchOrderById($merchantOrder->getOrderId());
            $this->orderApi->endUserConnection();

            $this->paymentApi->startUserConnection($merchantOrder->getBuyer());
            $izbergPayment = $this->paymentApi->authorizePaymentId($izbergOrder->getPayment()->getId());
            $this->paymentApi->endUserConnection();
        } catch (Exception $exception) {
            return false;
        }

        $merchanOrdersSharingSamePayment = $this->merchantOrderRepository->findBy(['orderId' => $merchantOrder->getOrderId()]);

        foreach ($merchanOrdersSharingSamePayment as $merchantOrder) {
            $merchantOrder->setIzbergStatus($izbergPayment->getStatus());
            $merchantOrder->setStatus(\AppBundle\Entity\MerchantOrder::STATUS_PENDING_SUPPLIER_VALIDATION);
            $merchantOrder->setManagerValidationDate(new \DateTimeImmutable());
            $this->merchantOrderRepository->save($merchantOrder);
        }

        return true;
    }

    public function cancelMerchantOrder(\AppBundle\Entity\MerchantOrder $merchantOrder, User $cancelBy): bool
    {

        if ($merchantOrder->isCancelled()) {
            return true;
        }

        if (!$merchantOrder->isPendingForSupplierValidation()) {
            return false;
        }

        try {
            $this->orderApi->startUserConnection($merchantOrder->getBuyer());
            /** @var MerchantOrder $MerchantOrderIzberg */
            $MerchantOrderIzberg = $this->orderApi->cancelMerchantOrder($merchantOrder->getId());

            $merchantOrder->setIzbergStatus($MerchantOrderIzberg->getStatus());
            $merchantOrder->setStatus(\AppBundle\Entity\MerchantOrder::STATUS_CANCELLED);
            $merchantOrder->setCancelledAt(new \DateTimeImmutable());
            $merchantOrder->setManagerValidationDate(new \DateTimeImmutable());
            $merchantOrder->setCancelBy($cancelBy);
            $this->merchantOrderRepository->save($merchantOrder);

        } catch (Exception $exception) {
            $this->logger->error($exception->getMessage(), 'CANCEL_MERCHANT_ORDER');
            return false;
        }

        return true;
    }

    public function rejectMerchantOrder(\AppBundle\Entity\MerchantOrder $merchantOrder, User $user): bool
    {

        if ($merchantOrder->isCancelled()) {
            return true;
        }

        if (!($merchantOrder->isPendingForManagerValidation())) {
            return false;
        }

        try {
            $this->orderApi->startUserConnection($merchantOrder->getBuyer());
            $izbergOrder = $this->fetchOrderById($merchantOrder->getOrderId());
            $this->orderApi->endUserConnection();

            $this->paymentApi->startUserConnection($merchantOrder->getBuyer());
            $izbergPayment = $this->paymentApi->cancelPaymentId($izbergOrder->getPayment()->getId());
            $this->paymentApi->endUserConnection();

        } catch (Exception $exception) {
            $this->logger->error($exception->getMessage(), 'REJECT_MERCHANT_ORDER');
            return false;
        }

        $merchanOrdersSharingSamePayment = $this->merchantOrderRepository->findBy(['orderId' => $merchantOrder->getOrderId()]);

        /** @var \AppBundle\Entity\MerchantOrder $merchantOrder */
        foreach ($merchanOrdersSharingSamePayment as $merchantOrder) {
            $merchantOrder->setIzbergStatus($izbergPayment->getStatus());
            $merchantOrder->setStatus(\AppBundle\Entity\MerchantOrder::STATUS_CANCELLED);
            $merchantOrder->setCancelledAt(new \DateTimeImmutable());
            $merchantOrder->setManagerValidationDate(new \DateTimeImmutable());
            $merchantOrder->setCancelBy($user);
            $this->merchantOrderRepository->save($merchantOrder);
        }

        return true;
    }

    public function createCurrencyCartOrder(\AppBundle\Entity\Cart $currencyCart, MetaCart $metaCart): bool
    {
        $commonAttributes = [
            $this->izbergCustomAttributes->getIgg() => $metaCart->getBuyer()->getUsername(),
            $this->izbergCustomAttributes->getInvoiceEntity() =>
                $metaCart->getBuyer()->getInvoiceEntity() ? $metaCart->getBuyer()->getInvoiceEntity()->getName() : null,
            $this->izbergCustomAttributes->getRecipientContact() =>
                $metaCart->getBuyerShippingAddress() ? $metaCart->getBuyerShippingAddress()->getContact() : null,
            $this->izbergCustomAttributes->getRecipientPhone() =>
                $metaCart->getBuyerShippingAddress() ? $metaCart->getBuyerShippingAddress()->getPhone() : null,
            $this->izbergCustomAttributes->getRecipientComment() =>
                $metaCart->getBuyerShippingAddress() ? $metaCart->getBuyerShippingAddress()->getComment() : null,
        ];

        // Make order for each merchant inside the cart
        try {
            $izbergOrder = $this->orderApi->createOrderFromCart($currencyCart->getId());
        } catch (Exception $exception) {
            $this->logger->error($exception->getMessage(), EventNameEnum::ORDER);
            return false;
        }

        // Update paiement to pending status to prevent cancellation after 20' of initial status
        try {
            $paiementId = $izbergOrder->getPayment()->getId();
            $this->orderApi->setOrderToPendingAuthorization($paiementId);
        } catch (Exception $exception) {
            $this->logger->error($exception->getMessage(), EventNameEnum::ORDER);
            return false;
        }

        // For each merchantOrder, update his info
        try {

            $specificAttributes = [];

            /** @var MerchantOrder $merchantOrder */
            foreach ($izbergOrder->getMerchantOrders() as $merchantOrder) {
                $izbergMerchantOrder = $this->fetchMerchantsOrderByOrderId($merchantOrder->getId());

                $specificAttributes[$merchantOrder->getId()] = [];

                // check for merchant comment
                $comment = $this->metaCartMerchantService->fetchMerchantComment($metaCart->getId(), $izbergMerchantOrder->getMerchant()->getId());

                if ($comment) {
                    $specificAttributes
                    [$izbergMerchantOrder->getId()]
                    [$this->izbergCustomAttributes->getMerchantComment()] = $comment;
                }
                $specificAttributes
                [$izbergMerchantOrder->getId()]
                [$this->izbergCustomAttributes->getCdatId()] = "CDAT" . $izbergMerchantOrder->getId();


                // clean current merchant order specific attributes if empty
                if (empty($specificAttributes[$izbergMerchantOrder->getId()])) {
                    unset($specificAttributes[$izbergMerchantOrder->getId()]);
                }
            };

            $this->updateMerchantOrdersExtraInfos(
                $metaCart->getBuyer()->getMarketPlaceName(),
                array_map(
                    function (MerchantOrder $merchantOrder) {
                        return $merchantOrder->getId();
                    },
                    $izbergOrder->getMerchantOrders()->toArray()
                ),
                $commonAttributes,
                $specificAttributes,
                $izbergOrder
            );

        } catch (Exception $exception) {
            $this->logger->error($exception->getMessage(), EventNameEnum::ORDER);
            return false;
        }


        $buyerCurrency = $metaCart->getBuyer()->getCountryOfDelivery()->getCurrency();

        $rateBuyer = $this->currencyRateService->getCurrencyRate($buyerCurrency, $izbergOrder->getCurrency());
        $rateMkp = $this->currencyRateService->getCurrencyRate($this->currencyRateService->getMarketPlaceCurrency(), $izbergOrder->getCurrency());


        try {
            /** @var MerchantOrder $izbergMerchantOrder */
            foreach ($izbergOrder->getMerchantOrders() as $izbergMerchantOrder) {
                $izbergMerchantOrder = $this->orderApi->fetchMerchantOrderById($izbergMerchantOrder->getId());

                $amountBuyer = CurrencyExchangeRateService::getAmountWithRate($izbergMerchantOrder->getAmount(), $rateBuyer, $buyerCurrency, $izbergOrder->getCurrency());
                $amountMkp = CurrencyExchangeRateService::getAmountWithRate($izbergMerchantOrder->getAmount(), $rateMkp, $this->currencyRateService->getMarketPlaceCurrency(), $izbergOrder->getCurrency());

                $hasGift = $this->merchantOrderHasGift($izbergMerchantOrder);
                $hasQuote = $this->merchantOrderHasQuote($izbergMerchantOrder);
                $userInvoiceEntity = isset($izbergMerchantOrder->getAttributes()[$this->izbergCustomAttributes->getInvoiceEntity()]) ? $izbergMerchantOrder->getAttributes()[$this->izbergCustomAttributes->getInvoiceEntity()] : null;
                $hasRisk = $this->merchantOrderHasRisk($izbergMerchantOrder);

                $merchantOrder = (new \AppBundle\Entity\MerchantOrder())
                    ->setId($izbergMerchantOrder->getId())
                    ->setMerchantId($izbergMerchantOrder->getMerchant()->getId())
                    ->setMerchantName($izbergMerchantOrder->getMerchant()->getName())
                    ->setAmount($izbergMerchantOrder->getAmount())
                    ->setAmountVatIncluded($izbergMerchantOrder->getAmountVatIncluded())
                    ->setCurrency($izbergOrder->getCurrency())
                    ->setIzbergStatus($izbergMerchantOrder->getStatus())
                    ->setStatus(\AppBundle\Entity\MerchantOrder::STATUS_PENDING_MANAGER_VALIDATION)
                    ->setMetaCart($metaCart)
                    ->setBuyer($metaCart->getBuyer())
                    ->setOrderId($izbergMerchantOrder->getOrder()->getId())
                    ->setCostCenter($metaCart->getBuyer()->getCostCenter())
                    ->setAmountCurrencyDelivery($amountBuyer)
                    ->setAmountCurrencyMarketPlace($amountMkp)
                    ->setCurrencyRateCountryOfdelivery($rateBuyer)
                    ->setCurrencyRateMarketPlace($rateMkp)
                    ->setGift($hasGift)
                    ->setQuote($hasQuote)
                    ->setShipping($izbergMerchantOrder->getShipping())
                    ->setUserInvoiceEntity($userInvoiceEntity)
                    ->setOrganisation($currencyCart->getCurrentUser()->getOrganization())
                    ->setSite($metaCart->getBuyer()->getSite())
                    ->setRisk($hasRisk);

                $this->merchantOrderRepository->save($merchantOrder);
            }
        } catch (Exception $exception) {
            $this->logger->error($exception->getMessage(), EventNameEnum::ORDER);
            return false;
        }

        return true;
    }

    public function merchantOrderHasGift(MerchantOrder $izbergMerchantOrder): bool
    {
        $hasGift = false;
        foreach ($izbergMerchantOrder->getItems() as $item) {

            $izbergOrderItem = $this->orderApi->fetchOrderItemById($item->getId());
            $extraInfo = $izbergOrderItem->getExtraInfo();

            if ($extraInfo) {
                if (strtolower($extraInfo->getGift()) == 'yes') {
                    $hasGift = true;
                    break;
                }
            }
        }

        return $hasGift;
    }

    public function merchantOrderHasRisk(MerchantOrder $izbergMerchantOrder): bool
    {
        $hasRisk = false;
        foreach ($izbergMerchantOrder->getItems() as $item) {

            $izbergOrderItem = $this->orderApi->fetchOrderItemById($item->getId());
            $extraInfo = $izbergOrderItem->getExtraInfo();

            if ($extraInfo) {
                if (strtolower($extraInfo->getRisk()) == 'yes') {
                    $hasRisk = true;
                    break;
                }
            }
        }

        return $hasRisk;
    }

    public function merchantOrderHasQuote(MerchantOrder $izbergMerchantOrder): bool
    {
        $hasQuote = false;
        foreach ($izbergMerchantOrder->getItems() as $item) {

            $izbergOrderItem = $this->orderApi->fetchOrderItemById($item->getId());
            $extraInfo = $izbergOrderItem->getExtraInfo();

            if ($extraInfo) {
                if (strtolower($extraInfo->getQuote()) == 'yes') {
                    $hasQuote = true;
                    break;
                }
            }
        }

        return $hasQuote;
    }

    public function updateMerchantOrdersExtraInfos(
        string $marketPlaceName,
        array $merchantOrdersIds,
        array $commonAttributes,
        array $specificAttributes,
        Order $order
    )
    {

        // Set order attributes
        $this->izbergAttributesService->setMerchantsOrdersExtraInfo(
            $merchantOrdersIds,
            $commonAttributes,
            $specificAttributes
        );

        return null;
    }

    public function fetchMerchantsOrderByOrderId($merchantOrderId): OrderMerchant
    {
        return $this->orderApi->fetchMerchantsOrderByOrderId($merchantOrderId);
    }

    public function fetchOrderById($orderId): Order
    {
        return $this->orderApi->fetchOrder($orderId);
    }

    public function getMerchantOrderInvoices($merchantOrderId, bool $operator = false)
    {
        return $this->orderApi->getMerchantOrderInvoices($merchantOrderId, $operator);
    }

    /**
     * @param int $merchantOrderId
     *
     * @return \AppBundle\Entity\MerchantOrder|null
     */
    public function getMerchantOrder(int $merchantOrderId): ?\AppBundle\Entity\MerchantOrder
    {
        $cacheKey = sprintf('MERCHANT_ORDER.%d', $merchantOrderId);

        $merchantOrder = $this->cacheService->getItem($cacheKey);

        if (null === $merchantOrder) {
            $merchantOrder = $this->merchantOrderRepository->find($merchantOrderId);

            if ($merchantOrder) {
                // needed for hydrating buyer to the merchantOrder object in cache
                $merchantOrder->getBuyer();

                if ( $merchantOrder->getBuyer()->getSite()) {
                    $merchantOrder->getBuyer()->getSite()->getName();
                }

                if ($merchantOrder->getSite()) {
                    $merchantOrder->getSite()->getName();
                }
            }
            $this->cacheService->saveItem($cacheKey, $merchantOrder, 10);
        }

        return $merchantOrder;
    }

    public function syncOrderItems(bool $init,MarketPlace $marketPlace, ?int $since = null, ?int $merchantOrderId = null): void
    {
        $logRequestID = uniqid();

        $pageSize = 50;
        $this->orderItemApi->configureApiConnection($marketPlace->getApiConfigurationKey());
        // Filters for api call
        $filters = ['limit' => $pageSize];
        $filters['status__in'] = implode(",", self::ORDER_ITEM_TO_SYNC_STATUS);
        if ($merchantOrderId!==null) {
            $filters['merchant_order'] = $merchantOrderId;

        }
        // default to 2 days.
        $nbHours = 48;
        if ($since) {
            $nbHours = $since;
        }
        // if not init look only last order item
        if (!$init && $merchantOrderId===null) {
            $date = new DateTime('now');
            $date->sub(new DateInterval('PT' . $nbHours . 'H'));
            $filters ['last_modified__gt'] = date_format($date, DateUtil::IZBERG_REQUEST_DATE_HOUR_FORMAT);
        }
        $index = 0;
        $offset = 0;

        do {
            $this->logger->info(sprintf("order item request offset: %s", $offset), EventNameEnum::COMMAND_SYNC_ORDER_ITEM, null, ['logRequestID' => $logRequestID]);

            $filters ['offset'] = $offset;
            $items = $this->orderItemApi->getOrderItems($filters, true);

            $this->logger->info(sprintf("order item request items counts: %s. Marketplace: %s.", count($items), $marketPlace->getName()), EventNameEnum::COMMAND_SYNC_ORDER_ITEM, null, ['logRequestID' => $logRequestID]);

            // Synchronizing
            /** @var OrderItem $orderItem */
            foreach ($items as $orderItem) {
                // get merchant order
                $merchantOrderId = IzbergUtils::parseIzbergResourceAndGetId($orderItem->getMerchantOrder());

                $this->logger->info(sprintf("index:%s, merchantOrderId: %s, last_modified: %s, offset: %s, marketplace: %s", $index, $merchantOrderId, $orderItem->getLastModified(), $offset, $marketPlace->getName()), EventNameEnum::COMMAND_SYNC_ORDER_ITEM, null, ["orderItem" => $orderItem->getId(), 'logRequestID' => $logRequestID]);
                // be careful this entity is in cache, not entirely hydrated
                $merchantOrder = $this->getMerchantOrder((int)$merchantOrderId);
                // props init
                $locale = self::ORDER_ITEM_DEFAULT_LOCALE;
                $supplier = '';
                /** @var Site $site */
                $site = null;
                $orderDate = null;
                $cartReference = null;
                $beneficiary = null;
                $beneficiaryName = null;

                // update props if merchant order gotten
                if ($merchantOrder && $merchantOrder->getStatus() == \AppBundle\Entity\MerchantOrder::STATUS_CONFIRMED_BY_SUPPLIER) {
                    $supplier = $merchantOrder->getMerchantName();
                    $orderDate = $merchantOrder->getCreatedAt();
                    $cartReference = $merchantOrder->getMetaCart()->getId();
                    $site = $merchantOrder->getSite();
                    if(is_null($site)){
                        $site = $merchantOrder->getBuyer()->getSite();
                    }

                    $buyer = $merchantOrder->getBuyer();
                    $beneficiary = $buyer->getUsername();
                    $beneficiaryName = sprintf(
                        '%s %s',
                        $buyer->getFirstname(),
                        $buyer->getLastname()
                    );
                    $locale = $buyer->getLocale();

                    $this->logger->info(sprintf("Get categories for product ID: %s. merchantOrderId: %s. Marketplace: %s.", $orderItem->getProductId(), $merchantOrderId, $marketPlace->getName()), EventNameEnum::COMMAND_SYNC_ORDER_ITEM, null, ["orderItem" => $orderItem->getId(), 'logRequestID' => $logRequestID]);

                    // for
                    $categories = $this->offerService->getProductcategories($orderItem->getProductId(), $marketPlace);

                    /** @var Category|null $categoryLevelOne */
                    $categoryLevelOne = null;
                    /** @var Category|null $categoryLevelTwo */
                    $categoryLevelTwo = null;

                    if (count($categories) > 0) {
                        if (count($categories[0]) > 1) {
                            $categoryLevelTwo = $categories[0][1];

                            $this->logger->info(sprintf("Subcategory for product ID %s: %s (%s). merchantOrderId: %s. Marketplace: %s.",
                                $orderItem->getProductId(), $categoryLevelTwo?->getId(), $categoryLevelTwo?->getName(),
                                $merchantOrderId, $marketPlace->getName()), EventNameEnum::COMMAND_SYNC_ORDER_ITEM, null, ["orderItem" => $orderItem->getId(), 'logRequestID' => $logRequestID]);
                        }

                        if (count($categories[0]) > 0) {
                            $categoryLevelOne = $categories[0][0];

                            $this->logger->info(sprintf("Top category for product ID %s: %s (%s). merchantOrderId: %s. Marketplace: %s.",
                                $orderItem->getProductId(), $categoryLevelOne?->getId(), $categoryLevelOne?->getName(),
                                $merchantOrderId, $marketPlace->getName()), EventNameEnum::COMMAND_SYNC_ORDER_ITEM, null, ["orderItem" => $orderItem->getId(), 'logRequestID' => $logRequestID]);
                        }
                    }
                    /** @var Category|null $defaultCategory */
                    $defaultCategory = $categoryLevelTwo ?? $categoryLevelOne;

                    if (is_null($categoryLevelOne)) {
                        $this->logger->info("No categories found for product: " . $orderItem->getProductId() . ". Merchant order ID: " . $merchantOrderId . ". Marketplace: " . $marketPlace->getName(), EventNameEnum::COMMAND_SYNC_ORDER_ITEM, null, array(
                                "orderItem" => $orderItem->getId(),
                                "offerId" => $orderItem->getOfferId(),
                                'logRequestID' => $logRequestID
                            )
                        );
                    }

                    $isGift = false;
                    $isQuote = false;
                    $isRisk = false;
                    $extraInfo = $orderItem->getExtraInfo();

                    if ($extraInfo) {
                        if (strtolower($extraInfo->getGift()) == 'yes') {
                            $isGift = true;
                        }
                        if (strtolower($extraInfo->getQuote()) == 'yes') {
                            $isQuote = true;
                        }
                        if (strtolower($extraInfo->getRisk()) == 'yes') {
                            $isRisk = true;
                        }
                    }


                    // update database
                    // 1. get order item from bdd
                    $orderItemBdd = $this->orderItemRepository->findOneBy(["id" => $orderItem->getId(), "merchantOrderId" => $merchantOrder->getId()]);

                    // 2. if null doesn't exists
                    if ($orderItemBdd == null) {
                        $orderItemBdd = new OrderItemEntity();
                    }

                    // 3. update fields
                    $orderItemBdd
                        ->setId($orderItem->getId())
                        ->setMerchantOrderId((int)$merchantOrderId)
                        ->setOfferId($orderItem->getOfferId())
                        ->setCategoryId($defaultCategory?->getId())
                        ->setRootCategoryId($categoryLevelOne?->getId())
                        ->setRootCategoryName($categoryLevelOne?->getName())
                        ->setDefaultCategoryName($defaultCategory?->getName())
                        ->setCurrentCategoryName($defaultCategory?->getName())
                        ->setName($orderItem->getName())
                        ->setPrice($orderItem->getPrice())
                        ->setQuantity($orderItem->getQuantity())
                        ->setTotal(round($orderItem->getPrice() * $orderItem->getQuantity(), 2))
                        ->setSupplier($supplier)
                        ->setOrderDate($orderDate)
                        ->setCartReference($cartReference)
                        ->setSite($site->getName())
                        ->setBeneficiary($beneficiary)
                        ->setBeneficiaryName($beneficiaryName)
                        ->setBeneficiaryInvoiceEntity($merchantOrder->getUserInvoiceEntity())
                        ->setCurrency($orderItem->getCurrency()->getCode())
                        ->setGift($isGift)
                        ->setQuote($isQuote)
                        ->setMarketplaceName($marketPlace->getName())
                        ->setRisk($isRisk);
                    // 4. save order item
                    $this->orderItemRepository->saveOrderItem($orderItemBdd);

                    $this->createOrUpdateShippingFakeOrderItem($merchantOrder, $beneficiary, $beneficiaryName, $merchantOrder->getUserInvoiceEntity(), $site, $marketPlace);

                    $message = sprintf('Order item ## %s ## updated for merchant order %s. Marketplace: %s.',
                        $orderItemBdd->getName(),
                        $merchantOrder->getId(),
                        $marketPlace->getName()
                    );

                    $this->logger->info($message, EventNameEnum::COMMAND_SYNC_ORDER_ITEM, null, ["orderItem" => $orderItem->getId(), 'logRequestID' => $logRequestID]);
                    $this->consoleLogger->info($message);
                } else {
                    if (!$merchantOrder) {
                        $this->logger->error(sprintf("Merchant order not found in DB. merchantOrderId: %s. Marketplace: %s.", $merchantOrderId, $marketPlace->getName()), EventNameEnum::COMMAND_SYNC_ORDER_ITEM, null, ["orderItem" => $orderItem->getId(), 'logRequestID' => $logRequestID]);
                    } else {
                        $this->logger->error(sprintf("Merchant order status wrong: %s. merchantOrderId: %s. Marketplace: %s.", $merchantOrder->getStatus(), $merchantOrderId, $marketPlace->getName()), EventNameEnum::COMMAND_SYNC_ORDER_ITEM, null, ["orderItem" => $orderItem->getId(), 'logRequestID' => $logRequestID]);
                    }
                }

                $index++;
            }
            $offset = $offset + $pageSize;
        } while (count($items) > 0);
    }

    public function initMerchantOrdersInvoiceEntity()
    {

        $merchantOrders = $this->merchantOrderRepository->findMerchantOrdersWithoutInvoiceEntity();

        $index = 1;
        $total = count($merchantOrders);
        /** @var \AppBundle\Entity\MerchantOrder $merchantOrder */
        foreach ($merchantOrders as $merchantOrder) {
            print(sprintf("merchant order %s/%s\n", $index, $total));
            $this->logger->info(sprintf("merchant order %s/%s", $index, $total), "INIT_MERCHANT_ORDER_INVOICE", null, ["merchantOrderId" => $merchantOrder->getId()]);
            try {
                /** @var MerchantOrder $merchantOrderIzb */
                $merchantOrderIzb = $this->orderApi->fetchMerchantOrderById($merchantOrder->getId());
                $merchantOrder->setUserInvoiceEntity($this->getInvoiceEntity($merchantOrderIzb));

                $this->merchantOrderRepository->save($merchantOrder);
            } catch (\Open\IzbergBundle\Api\ApiException $exception) {
                $this->logger->error("init merchant order invoice, can't find izberg merchant order", "INIT_MERCHANT_ORDER_INVOICE", null, ["merchantOrderId" => $merchantOrder->getId()]);
            }
            $index++;
        }
    }

    private function getInvoiceEntity(MerchantOrder $merchantOrderIzb)
    {

        $invoiceKey = $this->izbergCustomAttributes->getInvoiceEntity();
        return isset($merchantOrderIzb->getAttributes()[$invoiceKey]) ? $merchantOrderIzb->getAttributes()[$invoiceKey] : null;

    }

    private function createOrUpdateShippingFakeOrderItem(\AppBundle\Entity\MerchantOrder $merchantOrder, $beneficiary, $beneficiaryName, $beneficiaryInvoiceEntity,?Site $site, MarketPlace $marketPlace)
    {

        $orderItemBdd = $this->orderItemRepository->findOneBy(["id" => 0, "merchantOrderId" => $merchantOrder->getId()]);
        $locale = $merchantOrder->getBuyer()->getLocale();

        $shippinglabel = $this->translator->trans("report.csv.shipping", [], self::TRANSLATION_BUNDLE, $locale);


        // 2. if null doesn't exists
        if ($orderItemBdd == null) {
            $orderItemBdd = new OrderItemEntity();
        }

        $site = $site ?? $merchantOrder->getBuyer()->getSite() ?? null;

        $orderItemBdd
            ->setId(0)
            ->setMerchantOrderId($merchantOrder->getId())
            ->setOfferId(null)
            ->setCategoryId(null)
            ->setRootCategoryName($shippinglabel)
            ->setDefaultCategoryName($shippinglabel)
            ->setCurrentCategoryName($shippinglabel)
            ->setName($shippinglabel)
            ->setPrice($merchantOrder->getShipping())
            ->setQuantity(1)
            ->setTotal($merchantOrder->getShipping())
            ->setSupplier($merchantOrder->getMerchantName())
            ->setOrderDate($merchantOrder->getCreatedAt())
            ->setCartReference($merchantOrder->getMetaCart()->getId())
            ->setBeneficiary($beneficiary)
            ->setBeneficiaryName($beneficiaryName)
            ->setBeneficiaryInvoiceEntity($beneficiaryInvoiceEntity)
            ->setCurrency($merchantOrder->getCurrency())
            ->setGift(false)
            ->setQuote(false)
            ->setSite($site?->getName())
            ->setMarketplaceName($marketPlace->getName());
        // 4. save order item
        $this->orderItemRepository->saveOrderItem($orderItemBdd);
    }

    private function getCategoriesLevel(array $categories, int $level)
    {
        return array_map(function (array $tree) use ($level) {
            if (count($tree) >= $level) {
                return $tree[$level - 1];
            }
            return null;
        }, $categories);
    }


    public function syncOrderItemsSite(bool $init)
    {
        $ordersItems = $this->orderItemRepository->findAll();

        /** @var OrderItemEntity $orderItem */
        foreach ($ordersItems as $orderItem) {
            $merchantOrder = $this->getMerchantOrder($orderItem->getMerchantOrderId());
            if ($merchantOrder) {
                if (!$orderItem->getSite() || $init) {
                    $buyer = $this->entityManager->find(User::class, $merchantOrder->getBuyer()->getId());
                    $orderItem->setSite((string)$buyer->getSite());
                }

                $this->orderItemRepository->saveOrderItem($orderItem);

                $message = sprintf('Order item ## %s ## updated for merchant order %s',
                    $orderItem->getName(),
                    $merchantOrder->getId()
                );

                $this->logger->info($message, 'ORDER_ITEM_UPDATED');
                $this->consoleLogger->info($message);
            }
        }
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws Exception
     */
    public function syncOrderItemsCategory(string $date, MarketPlace $marketPlace)
    {
        $this->orderItemApi->configureApiConnection($marketPlace->getApiConfigurationKey());
        $items = $this->orderItemRepository
            ->createQueryBuilder('orderItem')
            ->select('orderItem')
            ->where('orderItem.orderDate >= :date')
            ->setParameter('date', (new DateTime($date))->format("Y-m-d"))
            ->andWhere('orderItem.offerId is not NULL')
            ->getQuery()
            ->getResult();

        /** @var OrderItemEntity $orderItem */
        foreach ($items as $orderItem) {
            $categories = $this->offerService->getOfferCategories($orderItem->getOfferId(), $marketPlace);
            $categoryL1 = (count($categories) > 0) ? ((count($categories[0]) > 0) ? $categories[0][0] : null) : null;
            $categoryL2 = (count($categories) > 0) ? ((count($categories[0]) > 1) ? $categories[0][1] : null) : null;
            $defaultCategory = $categoryL2 ?? $categoryL1;


            if (!is_null($defaultCategory)) {
                $orderItem->setCategoryId($defaultCategory->getId())
                    ->setDefaultCategoryName($defaultCategory->getName())
                    ->setCurrentCategoryName($defaultCategory->getName());
            }
            if (!is_null($categoryL1)) {
                $orderItem
                    ->setRootCategoryId($categoryL1->getId())
                    ->setRootCategoryName($categoryL1->getName());
            }

            $this->orderItemRepository->saveOrderItem($orderItem);
        }

    }
}
