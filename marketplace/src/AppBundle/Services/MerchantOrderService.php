<?php

namespace AppBundle\Services;

use AppBundle\Entity\BuyerAddress;
use AppBundle\Entity\MarketPlace;
use AppBundle\Entity\Merchant;
use AppBundle\Entity\MerchantOrder as MerchantOrderEntity;
use AppBundle\Entity\MetaCart;
use AppBundle\Entity\OrderItem;
use AppBundle\Entity\TotalAddress;
use AppBundle\Entity\User;
use AppBundle\Exception\FtpException;
use AppBundle\Factory\MerchantOrderFactory;
use AppBundle\Model\MerchantOrder;
use AppBundle\Repository\CxmlConfRepository;
use AppBundle\Repository\MerchantOrderRepository;
use AppBundle\Util\CurrencyUtil;
use DateTime;
use Exception;
use Open\IzbergBundle\Api\IzbergUtils;
use Open\IzbergBundle\Api\OrderApi;
use Open\IzbergBundle\Model\Address;
use Open\IzbergBundle\Model\MerchantOrder as MerchantOrderModel;
use Open\IzbergBundle\Model\MerchantOrderAll;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Twig\Environment;
use Twig\Error\Error;

class MerchantOrderService
{
    private const STATUS_OK = [OrderApi::MERCHANT_ORDER_STATUS_CONFIRMED, OrderApi::MERCHANT_ORDER_STATUS_PROCESSED, OrderApi::MERCHANT_ORDER_STATUS_FINALIZED];
    private const ORDER_BASE_URL = '%s%s/orders/details/%s/';

    private MerchantOrderRepository $merchantOrderRepository;
    private MerchantOrderFactory $merchantOrderFactory;
    private OrderApi $orderApi;
    private LogService $logService;
    private UserRelationshipService $userRelationshipService;
    private CurrencyExchangeRateService $currencyRateService;
    private UserBddService $userService;
    private Environment $templating;
    private IzbergCustomAttributes $customAttributes;
    private CxmlExportService $cxmlExportService;
    private MerchantService $merchantService;
    private MailService $mailService;
    private AddressService $addressService;
    private OrderItemService $orderItemService;
    private MarketPlaceService $marketPlaceService;
    private CxmlConfRepository $cxmlConfRepository;


    public function __construct(
        MailService                 $mailService,
        CxmlExportService           $cxmlExportService,
        MerchantOrderRepository     $merchantOrderRepository,
        MerchantOrderFactory        $merchantOrderFactory,
        OrderApi                    $orderApi,
        LogService                  $logService,
        UserRelationshipService     $userRelationshipService,
        CurrencyExchangeRateService $currencyRateService,
        UserBddService              $userService,
        Environment                 $templating,
        IzbergCustomAttributes      $izbergCustomAttributes,
        MerchantService             $merchantService,
        AddressService              $addressService,
        OrderItemService            $orderItemService,
        MarketPlaceService          $marketPlaceService,
        CxmlConfRepository          $cxmlConfRepository
    )
    {
        $this->merchantOrderRepository = $merchantOrderRepository;
        $this->merchantOrderFactory = $merchantOrderFactory;
        $this->orderApi = $orderApi;
        $this->logService = $logService;
        $this->userRelationshipService = $userRelationshipService;
        $this->currencyRateService = $currencyRateService;
        $this->userService = $userService;
        $this->templating = $templating;
        $this->customAttributes = $izbergCustomAttributes;
        $this->cxmlExportService = $cxmlExportService;
        $this->merchantService = $merchantService;
        $this->mailService = $mailService;
        $this->addressService = $addressService;
        $this->orderItemService = $orderItemService;
        $this->marketPlaceService = $marketPlaceService;
        $this->cxmlConfRepository = $cxmlConfRepository;
    }

    public function loadBuyerMerchantOrder(User $buyer, int $merchantOrderId): ?MerchantOrder
    {
        /** @var MerchantOrderEntity|null $merchantOrder */
        $merchantOrder = $this->merchantOrderRepository->find($merchantOrderId);
        if ($merchantOrder == null) {
            return null;
        }

        // get authorized buyers
        $merchantOrderBuyer = $merchantOrder->getBuyer();
        $authorizedBuyers = array_merge(
            [$merchantOrderBuyer->getId()],
            $this->userRelationshipService->fetchNXUserParents($merchantOrderBuyer->getId())
        );

        $buyerHasRightsToGetMerchantOrder = false;

        // get current buyer roles
        $roles = $buyer->getRoles();


        if (in_array(User::ROLE_MARKETPLACE_REPORTING, $roles)) {
            $buyerHasRightsToGetMerchantOrder = true;
        } elseif (in_array(User::ROLE_ENTITY_REPORTING, $roles)) {
            if ($merchantOrderBuyer->getInvoiceEntity()->getId()===$buyer->getInvoiceEntity()->getId()) {
                $buyerHasRightsToGetMerchantOrder = true;
            }
        } else {
            if (in_array($buyer->getId(), $authorizedBuyers)) {
                $buyerHasRightsToGetMerchantOrder = true;
            }
        }

        if ($buyerHasRightsToGetMerchantOrder) {
            return $this->merchantOrderFactory->buildMerchantOrderFromEntity($merchantOrder);
        }
        return null;
    }

    public function loadMerchantOrderEntity(int $merchantOrderId): ?\AppBundle\Entity\MerchantOrder
    {
        return $this->merchantOrderRepository->find($merchantOrderId);
    }

    public function loadMerchantOrderById(?\AppBundle\Entity\MerchantOrder $merchantOrderEntity): ?MerchantOrder
    {

        if (!$merchantOrderEntity) {
            return null;
        }
        return $this->merchantOrderFactory->buildMerchantOrderFromEntity($merchantOrderEntity);
    }


    public function cancelMerchantOrder(string $merchantOrderId)
    {
        /** @var MerchantOrderEntity $merchantOrderEntity */
        $merchantOrderEntity = $this->merchantOrderRepository->find($merchantOrderId);

        if ($merchantOrderEntity) {
            $merchantOrderEntity->setStatus(MerchantOrderEntity::STATUS_CANCELLED);
            $merchantOrderEntity->setCancelBy($merchantOrderEntity->getBuyer());
            $merchantOrderEntity->setMetaCart($merchantOrderEntity->getMetaCart()->setStatus(MetaCart::STATUS_CANCELLED));
            $merchantOrderEntity->setMetaCart($merchantOrderEntity->getMetaCart()->setCancelReason(MetaCart::STATUS_REASON));
            $this->merchantOrderRepository->save($merchantOrderEntity);

            return true;
        }

        return false;
    }

    public function merchantOrderSellerValidation(MerchantOrderModel $merchantOrder, MetaCart $metaCart): bool
    {
        /** @var MerchantOrderEntity $merchantOrderEntity */
        $merchantOrderEntity = $this->merchantOrderRepository->find($merchantOrder->getId());

        $merchantOrderIzb = $this->orderApi->fetchMerchantsOrderByOrderId($merchantOrder->getId());

        try {
            if ($merchantOrderIzb->getStatus() != OrderApi::MERCHANT_ORDER_STATUS_PROCESSED) {
                $this->orderApi->processedMerchantOrder($merchantOrder->getId());
            }

            if ($merchantOrderEntity) {
                $merchantOrderEntity->setStatus(MerchantOrderEntity::STATUS_CONFIRMED_BY_SUPPLIER);

                $amountBuyer = CurrencyExchangeRateService::getAmountWithRate($merchantOrder->getAmount(), $merchantOrderEntity->getCurrencyRateCountryOfdelivery(), $merchantOrderEntity->getBuyer()->getCountryOfDelivery()->getCurrency(), $merchantOrder->getCurrency()->getCode());
                $amountMkp = CurrencyExchangeRateService::getAmountWithRate($merchantOrder->getAmount(), $merchantOrderEntity->getCurrencyRateMarketPlace(), $this->currencyRateService->getMarketPlaceCurrency(), $merchantOrder->getCurrency()->getCode());

                // Update order amounts in case of partials supplier orders confirmation
                $merchantOrderEntity->setAmount($merchantOrder->getAmount());
                $merchantOrderEntity->setAmountVatIncluded($merchantOrder->getAmountVatIncluded());
                $merchantOrderEntity->setAmountCurrencyMarketPlace($amountMkp);
                $merchantOrderEntity->setAmountCurrencyDelivery($amountBuyer);
                $merchantOrderEntity->setSupplierConfirmAt(
                    new \DateTimeImmutable($merchantOrder->getConfirmationDate())
                );

                $this->merchantOrderRepository->save($merchantOrderEntity);

                $this->sendValidatedOrdToVendorMAil(MailService::ORDER_CONFIRMED_BY_VENDOR_TO_VENDOR, $merchantOrder, $metaCart);

                return true;
            }
        } catch (\Exception $exception) {
            $this->logService->error(
                'Process MerchantOrder generic error.',
                EventNameEnum::IZBERG_WEB_HOOK
            );
        }
        return false;
    }

    public function merchantOrderSellerCancellation(int $merchantOrderId, string $cancelReason = null): bool
    {
        /** @var MerchantOrderEntity $merchantOrderEntity */
        $merchantOrderEntity = $this->merchantOrderRepository->find($merchantOrderId);

        if ($merchantOrderEntity) {
            $merchantOrderEntity->setStatus(MerchantOrderEntity::STATUS_CANCELLED);
            $merchantOrderEntity->setCancelReason($cancelReason);
            $this->merchantOrderRepository->save($merchantOrderEntity);

            return true;
        }

        return false;
    }


    public function merchantOrderCostCenterSync(int $merchantOrderId = null): void
    {
        // Update specific merchant order
        if ($merchantOrderId) {
            $merchantOrder = $this->merchantOrderRepository->find($merchantOrderId);
            // if merchant order does not exist
            if (!$merchantOrder) {
                return;
            }
            // if merchant order exists and doesn't have a cost center
            if (!$merchantOrder->getCostCenter()) {
                $this->merchantOrderCostCenterUpdate($merchantOrder);
            }
            return;
        }

        // Update all merchant order
        $merchantOrders = $this->merchantOrderRepository->findBy([
            'costCenter' => null
        ]);
        foreach ($merchantOrders as $merchantOrder) {
            if (!$merchantOrder->getCostCenter()) {
                $this->merchantOrderCostCenterUpdate($merchantOrder);
            }
        }
    }

    public function merchantOrderSiteSync(int $merchantOrderId = null): void
    {
        // Update specific merchant order
        if ($merchantOrderId) {
            $merchantOrder = $this->merchantOrderRepository->find($merchantOrderId);
            // if merchant order does not exist
            if (!$merchantOrder) {
                return;
            }
            // if merchant order exists and doesn't have a site
            if (!$merchantOrder->getSite()) {
                $this->merchantOrderSiteUpdate($merchantOrder);
            }
            return;
        }

        // Update all merchant order
        $merchantOrders = $this->merchantOrderRepository->findBy([
            'site' => null
        ]);
        foreach ($merchantOrders as $merchantOrder) {
            if (!$merchantOrder->getSite()) {
                $this->merchantOrderSiteUpdate($merchantOrder);
            }
        }
    }

    public function merchantOrderItemsCategoriesSync(string $marketPlaceCode, int $merchantOrderId = null): void
    {
        // Update specific merchant order
        if ($merchantOrderId) {
            $this->syncMerchantOrderItemsCategories($merchantOrderId, $marketPlaceCode);
            return;
        }

        // Update all merchant order with empty items categories
        $merchantOrdersIds = $this->orderItemService->merchantOrderIdsForItemsWithoutCategories();

        foreach ($merchantOrdersIds as $merchantOrder) {
            $this->syncMerchantOrderItemsCategories($merchantOrder['merchantOrderId'], $marketPlaceCode);
        }
    }

    public function merchantOrderStatusSync(int $merchantOrderId = null): void
    {
        // Update specific merchant order
        if ($merchantOrderId) {
            $merchantOrder = $this->merchantOrderRepository->find($merchantOrderId);
            // if merchant order does not exist
            if (!$merchantOrder) {
                return;
            }
            // if merchant order exists and doesn't have a cost center
            $this->merchantOrderStatusUpdate($merchantOrder);
            return;
        }

        // Update all merchant order
        $merchantOrders = $this->merchantOrderRepository->findBy([
            'status' => \AppBundle\Entity\MerchantOrder::STATUS_PENDING_SUPPLIER_VALIDATION
        ]);
        foreach ($merchantOrders as $merchantOrder) {
            $this->merchantOrderStatusUpdate($merchantOrder);
        }
    }

    private function merchantOrderCostCenterUpdate(MerchantOrderEntity $merchantOrder): void
    {
        // get buyer cost center
        $buyerCostCenter = $merchantOrder->getBuyer()->getCostCenter();

        // Update merchant order const center
        $merchantOrder->setCostCenter($buyerCostCenter);
        $this->merchantOrderRepository->save($merchantOrder);

        // logs
        $msg = sprintf("assign cost-center {%s} to merchant-order %s", $buyerCostCenter, $merchantOrder->getId());
        $this->logService->info($msg, 'MERCHANT_ORDER_COST_CENTER_UPDATE');
    }

    private function merchantOrderSiteUpdate(MerchantOrderEntity $merchantOrder): void
    {
        // get buyer site
        $buyerSite = $merchantOrder->getBuyer()->getSite();

        // Update merchant order site
        $merchantOrder->setSite($buyerSite);
        $this->merchantOrderRepository->save($merchantOrder);

        // logs
        $msg = sprintf("assign site {%s} to merchant-order %s", $buyerSite->getName(), $merchantOrder->getId());
        $this->logService->info($msg, 'MERCHANT_ORDER_SITE_UPDATE');
    }

    private function syncMerchantOrderItemsCategories(int $merchantOrderId, string $marketplaceName): void
    {
        $marketPlace = $this->marketPlaceService->getMarketPlaceByName($marketplaceName);
        $merchantOrderItems = $this->orderItemService->getMerchantOrderItems($merchantOrderId);

        // if no items for merchant order
        if (count($merchantOrderItems) === 0) {
            return;
        }

        /** @var OrderItem $orderItem */
        foreach ($merchantOrderItems as $orderItem) {
            if (!$orderItem->getRootCategoryName() || !$orderItem->getRootCategoryId() ||
                !$orderItem->getDefaultCategoryName() || !$orderItem->getCurrentCategoryName()
            ) {
                $this->orderItemService->syncOrderItemCategories($orderItem, $marketPlace);

                // logs
                $msg = sprintf("sync categories for order item %s of merchant-order %s", $orderItem->getId(), $merchantOrderId);
                $this->logService->info($msg, 'MERCHANT_ORDER_ITEM_CATEGORIES_UPDATE');
            }
        }
    }

    private function merchantOrderStatusUpdate(MerchantOrderEntity $merchantOrder): void
    {
        try {
            $this->logService->info("Trying to fetch from Izberg merchant order #" . $merchantOrder->getId(), "MERCHANT_ORDER_STATUS_UPDATE", null, ["merchant_order_id" => $merchantOrder->getId()]);

            $this->orderApi->configureApiConnection($merchantOrder->getBuyer()->getMarketPlace()->getApiConfigurationKey());

            /** @var \Open\IzbergBundle\Model\MerchantOrder $merchantOrderIzb */
            if (!$merchantOrderIzb = $this->orderApi->fetchMerchantOrderById($merchantOrder->getId())) {
                $this->logService->error("Merchant order not found on Izberg side. ID: " . $merchantOrder->getId(), "MERCHANT_ORDER_STATUS_UPDATE", null, ["merchant_order_id" => $merchantOrder->getId()]);
            } else {
                $this->logService->info("Status of merchant order #" . $merchantOrder->getId() . " on Izberg: " . $merchantOrderIzb->getStatus(), "MERCHANT_ORDER_STATUS_UPDATE", null, ["merchant_order_id" => $merchantOrder->getId()]);

                if (in_array($merchantOrderIzb->getStatus(), self::STATUS_OK)) {
                    $merchantOrder->setStatus(\AppBundle\Entity\MerchantOrder::STATUS_CONFIRMED_BY_SUPPLIER);
                    $merchantOrder->setSupplierConfirmAt(new \DateTimeImmutable($merchantOrderIzb->getConfirmationDate()));
                    $this->merchantOrderRepository->save($merchantOrder);

                    $this->logService->info("merchantOrder updated: #" . $merchantOrder->getId(), "MERCHANT_ORDER_STATUS_UPDATE", null, ["merchant_order_id" => $merchantOrder->getId()]);
                } else {
                    $this->logService->info("Merchant order was not updated, because of status: " . $merchantOrderIzb->getStatus() . ". ID: " . $merchantOrder->getId(), "MERCHANT_ORDER_STATUS_UPDATE", null, ["merchant_order_id" => $merchantOrder->getId()]);
                }
            }
        } catch (\Exception $ex) {
            $this->logService->error("Error updating merchantOrder status. ID: " . $merchantOrder->getId() . ". Exception: " . $ex->__toString(), "MERCHANT_ORDER_STATUS_UPDATE", null, ["merchant_order_id" => $merchantOrder->getId()]);
        }
    }

    /**
     * export order with batch
     *
     * @param int $merchantOrderId
     */
    public function exportOrder(MarketPlace $marketPlace, int $merchantOrderId)
    {
        /** @var \AppBundle\Entity\MerchantOrder $merchantOrderEntity */
        $merchantOrderEntity = $this->merchantOrderRepository->find($merchantOrderId);
        $merchantEntity = $this->merchantService->findMerchantEntityByIzbergId($merchantOrderEntity->getMerchantId());
        $this->orderApi->configureApiConnection($marketPlace->getApiConfigurationKey());
        /** @var MerchantOrderAll $merchantOrderAll */
        $merchantOrderAll = $this->orderApi->fetchMerchantOrderByIdAll($merchantOrderId);
        $this->exportCxml($merchantOrderAll, $merchantOrderEntity);

    }

    /**
     * webhook export order to cxml
     *
     * @param $webHookJson
     */
    public function merchantOrderAuthorizedActions(MerchantOrderAll $merchantOrder)
    {
        if ($this->isCxmlMerchant($merchantOrder->getMerchant()->getId())) {
            /** @var \AppBundle\Entity\MerchantOrder $merchantOrderEntity */
            $merchantOrderEntity = $this->merchantOrderRepository->find($merchantOrder->getId());

            $this->logService->info("merchantOrderAuthorizedActions is cxmlMerchant order: " . $merchantOrder->getId(), EventNameEnum::CXML_EXPORT, null, ["merchant_id" => $merchantOrder->getMerchant()->getId()]);

            if (!$this->exportCxml($merchantOrder, $merchantOrderEntity)) {
                $this->logService->error("merchantOrderAuthorizedActions export error. cxmlMerchant order: " . $merchantOrder->getId(), EventNameEnum::CXML_EXPORT, null, ["merchant_id" => $merchantOrder->getMerchant()->getId()]);

                $this->sendcxmlErrorToMerchant($merchantOrder->getMerchant()->getId(), $merchantOrder);
            } else {
                $this->sendNewOrdToVendorMAil(MailService::ORDER_AUTO_CONFIRMED_TO_VENDOR, $merchantOrder);
            }
        } else {
            $this->logService->info("merchantOrderAuthorizedActions CxmlConf not found for merchant ID: " . $merchantOrder->getMerchant()->getId() . ". cxmlMerchant order: " . $merchantOrder->getId(), EventNameEnum::CXML_EXPORT, null, ["merchant_id" => $merchantOrder->getMerchant()->getId()]);

            $this->sendNewOrdToVendorMAil(MailService::ORDER_PENDING_CONFIRMATION_TO_VENDOR, $merchantOrder);
        }
    }

    public function isCxmlMerchant(int $merchantId) : bool
    {
        return !is_null($this->cxmlConfRepository->findMerchantConf($merchantId));
    }

    public function sendFtpError(int $merchantId, string $filename)
    {

        /** @var MerchantOrderAll $merchantOrderAll */
        $merchantOrderAll = $this->orderApi->fetchMerchantOrderByIdAll($this->getOrderIdFromFilename($filename));
        $this->sendcxmlErrorToMerchant($merchantId, $merchantOrderAll);
    }

    public function sendMerchantOrders()
    {
        $this->cxmlExportService->send($this);
    }

    public function getOrderIdFromFilename(string $filename)
    {
        $tab = explode("_", $filename);
        if (count($tab) > 1) {
            return $tab[1];
        }
        return null;
    }

    public function getOrderUrl(Merchant $merchant, int $OrderId): string
    {
        return sprintf(self::ORDER_BASE_URL, $this->merchantService->getMarketplaceUrl($merchant), $merchant->getSlug(), $OrderId);
    }

    private function exportCxml(MerchantOrderAll $merchantOrder, MerchantOrderEntity $merchantOrderEntity)
    {
        try {
            $merchantId = $merchantOrder->getMerchant()->getId();
            $date = new DateTime('now');

            if (MerchantOrderAll::CONFIRMED != $merchantOrder->getStatus() && MerchantOrderAll::PROCESSED != $merchantOrder->getStatus()) {
                $this->orderApi->confirmOrder($merchantOrder->getId());
                $this->logService->info("exportCxml: status changed :" . $merchantOrder->getId(), EventNameEnum::CXML_EXPORT, null, ["merchant_id" => $merchantId]);
            }
            $payloadId = $this->getPayloadId($date);
            $cxml = $this->generateOrderFileContent($payloadId, $merchantOrder, $merchantOrderEntity);

            if (strlen($cxml) > 0) {
                return $this->cxmlExportService->store($merchantId, $cxml, $this->getOrderUniqueId($merchantOrder, $date));
            } else {
                $this->logService->error("exportCxml: cxml is empty " . $merchantOrder->getId(), EventNameEnum::CXML_EXPORT, null, []);
            }
        } catch (Exception $exception) {
            $this->logService->error("Unknown error exporting cxml order: " . $merchantOrder->getId(), EventNameEnum::CXML_EXPORT, null, array("error" => $exception->getTrace()));
        }

        return false;
    }


    private function getOrderUniqueId(MerchantOrderAll $merchantOrder, DateTime $date)
    {
        $params = [];
        $params[] = $merchantOrder->getMerchant()->getId();
        $params[] = $merchantOrder->getId();
        $params[] = $date->getTimestamp();
        return implode("_", $params);
    }


    private function generateOrderFileContent(string $payloadId, MerchantOrderAll $merchantOrderAll, MerchantOrderEntity $merchantOrderEntity): string
    {
        $date = new DateTime('now');
        $timestamp = $date->format(DateTime::ATOM);

        $shippingAddress = $merchantOrderAll->getShippingAddress();
        $shippingAddressId = $shippingAddress->getId();

        $totalAddressEntity = $this->addressService->findTotalAddress($shippingAddress);
        $buyerAddressEntity = $this->addressService->findBuyerAddress($shippingAddress);
        $streets = $this->getStreets($shippingAddress, $buyerAddressEntity, $totalAddressEntity);
        $this->logService->debug("street found",EventNameEnum::CXML_EXPORT,null,["street"=>$streets]);
        if (!is_null($totalAddressEntity)) {
            $shippingAddressId = $totalAddressEntity->getId();
        }
        try {
            $this->logService->info("generateOrderFileContent: generating cxml" . $merchantOrderAll->getID(), EventNameEnum::CXML_EXPORT, null, []);
            $cxmlConf = $this->cxmlConfRepository->findMerchantConf($merchantOrderAll->getMerchant()->getId());
            // Generate description
            $merchantOrderXml = $this->templating->render("@OpenFront/template/merchantOrderExport.twig.html",
                array(
                    "entity" => $merchantOrderEntity->getBuyer()->getInvoiceEntity()->getName(),
                    "merchantOrder" => $merchantOrderAll,
                    "shippingAddress" => $merchantOrderAll->getShippingAddress(),
                    "shippingAddressId" => $shippingAddressId,
                    "billingAddress" => $merchantOrderAll->getBillingAddress(),
                    "streets" => $streets,
                    "payloadId" => $payloadId,
                    "timestamp" => $timestamp,
                    "toDomain" => $cxmlConf?$cxmlConf->getCxmlToDomain():'',
                    "toIdentity" => $cxmlConf?$cxmlConf->getCxmlToIdentity():'',
                    "senderDomain" => $cxmlConf?$cxmlConf->getCxmlSenderDomain():'',
                    "senderIdentity" => $cxmlConf?$cxmlConf->getCxmlSenderIdentity():'',
                    "senderSecret" => $cxmlConf?$cxmlConf->getCxmlSenderSecret():'',
                    "customerPhone" => $this->getAttribute($merchantOrderAll, $this->customAttributes->getRecipientPhone()),
                    'customAttributes' => $merchantOrderAll->getAttributes(),
                    'cdatId' => $this->getAttribute($merchantOrderAll, $this->customAttributes->getCdatId())

                )
            );
        } catch (Error $err) {

            $this->logService->error("cxml file generation error", EventNameEnum::CXML_EXPORT, null, ["error" => $err->getMessage()]);
            return "";
        }
        $this->logService->info("generateOrderFileContent: cxml generated" . $merchantOrderAll->getID(), EventNameEnum::CXML_EXPORT, null, ["cxml" => $merchantOrderXml]);
        return $merchantOrderXml;

    }

    private function getAttribute(MerchantOrderAll $merchantOrderAll, $key)
    {
        $attribute = "";
        $attributes = $merchantOrderAll->getAttributes();
        if (array_key_exists($key, $attributes)) {
            $value = $attributes[$key];
            if (!empty($value)) {
                $attribute = $value;
            }
        }
        return $attribute;
    }

    private function getPayloadId(DateTime $date)
    {
        return sprintf("%s.%s.%s@%s", $date->getTimestamp(), getmypid(), uniqid(), gethostname());
    }

    private function sendcxmlErrorToMerchant(int $merchantId, ?MerchantOrderAll $merchantOrder)
    {

        /** @var \AppBundle\Model\Merchant $merchant */
        $merchant = $this->merchantService->findMerchantById($merchantId);

        $merchantEntity = $this->merchantService->findMerchantEntityByIzbergId($merchant->getId());
        $contactBccList = $this->merchantService->buildNotificationContacts($merchant, MailService::CXML_ORDER_ERROR_TO_VENDOR);

        $orderUrl = $this->getOrderUrl($merchantEntity, $merchantOrder->getId());
        $this->mailService->sendEmailMessage(
            MailService::CXML_ORDER_ERROR_TO_VENDOR,
            $merchantEntity->getLanguage(),
            $merchant->getMainContactEmail(),
            [
                'firstName' => $merchant->getMainContactFirstName(),
                'lastName' => $merchant->getMainContactLastName(),
                'orderNumber' => $merchantOrder->getId(),
                'orderUrl' => $orderUrl,
                'items' => IzbergUtils::getAttributeItemForMail($merchantOrder->getItems()->toArray())
            ],
            null,
            null,
            [],
            $contactBccList
        );

        $admins = $this->userService->getAdmin();
        if ($admins && count($admins) > 0) {
            $adminMail = array_map(function (User $admin) {
                return $admin->getEmail();
            }, $admins);
            $this->mailService->sendEmailMessage(
                MailService::CXML_ORDER_ERROR_TO_VENDOR,
                "en",
                $adminMail,
                [
                    'firstName' => $merchant->getMainContactFirstName(),
                    'lastName' => $merchant->getMainContactLastName(),
                    'orderNumber' => $merchantOrder->getId(),
                    'orderUrl' => $orderUrl,
                    'items' => IzbergUtils::getAttributeItemForMail($merchantOrder->getItems()->toArray())
                ]
            );
        }


    }

    private function sendNewOrdToVendorMAil(string $mailTemplate, MerchantOrderAll $merchantOrder)
    {

        /** @var \AppBundle\Model\Merchant $merchant */
        $merchant = $this->merchantService->findMerchantById($merchantOrder->getMerchant()->getId());
        $merchantEntity = $this->merchantService->findMerchantEntityByIzbergId($merchant->getId());

        /** @var Address $shippingAddress */
        $shippingAddress = $merchantOrder->getShippingAddress();
        /** @var Address $billingAddress */
        $billingAddress = $merchantOrder->getBillingAddress();

        $contactList = $this->merchantService->buildNotificationContacts($merchant, $mailTemplate);

        $this->mailService->sendEmailMessage(
            $mailTemplate,
            $merchantEntity->getLanguage(),
            $contactList,
            [
                'firstName' => $merchant->getMainContactFirstName(),
                'lastName' => $merchant->getMainContactLastName(),
                'merchant_slug' => $merchant->getSlug(),
                'order_id' => $merchantOrder->getId(),
                'orderUrl' => $this->getOrderUrl($merchantEntity, $merchantOrder->getId()),
                'order_date' => $merchantOrder->getCreatedOn(),
                'price_ht' => CurrencyUtil::printCurrency($merchantOrder->getPrice(), $merchant->getLanguage(), $merchantOrder->getCurrency()->getCode()),
                'shipping_price' => CurrencyUtil::printCurrency($merchantOrder->getShipping(), $merchant->getLanguage(), $merchantOrder->getCurrency()->getCode()),
                'total_order_price_ht' => CurrencyUtil::printCurrency($merchantOrder->getAmount(), $merchant->getLanguage(), $merchantOrder->getCurrency()->getCode()),
                'shipping_first_name' => $shippingAddress->getFirstName(),
                'shipping_last_name' => $shippingAddress->getLastName(),
                'shipping_address' => $shippingAddress->getAddress(),
                'shipping_address2' => $shippingAddress->getAddress2(),
                'shipping_zipcode' => $shippingAddress->getZipcode(),
                'shipping_city' => $shippingAddress->getCity(),
                'shipping_country' => $shippingAddress->getCountry()->getName(),
                'shipping_state' => $shippingAddress->getState(),
                'billing_address' => $billingAddress->getAddress(),
                'billing_address2' => $billingAddress->getAddress2(),
                'billing_zipcode' => $billingAddress->getZipcode(),
                'billing_city' => $billingAddress->getCity(),
                'billing_state' => $billingAddress->getState(),
                'billing_country' => $billingAddress->getCountry()->getName(),
                'shippingAmountHt' => $merchantOrder->getShipping(),
                'products' => IzbergUtils::getAttributeProductForMail($merchantOrder->getItems()->toArray())
            ],
            null,
            null,
            []
        );
    }

    private function sendValidatedOrdToVendorMAil(string $mailTemplate, MerchantOrderModel $merchantOrder, MetaCart $metaCart)
    {

        /** @var \AppBundle\Model\Merchant $merchant */
        $merchant = $this->merchantService->findMerchantById($merchantOrder->getMerchant()->getId());
        $merchantEntity = $this->merchantService->findMerchantEntityByIzbergId($merchant->getId());
        if ($merchantEntity->getMarketPlace()->getName() == "france") {
            /** @var BuyerAddress $shippingAddress */
            $shippingAddress = $metaCart->getBuyerShippingAddress();

            $contactList = $this->merchantService->buildNotificationContacts($merchant, $mailTemplate);

            $this->mailService->sendEmailMessage(
                $mailTemplate,
                $merchantEntity->getLanguage(),
                $contactList,
                [
                    'firstName' => $merchant->getMainContactFirstName(),
                    'lastName' => $merchant->getMainContactLastName(),
                    'merchant_slug' => $merchant->getSlug(),
                    'order_id' => $merchantOrder->getId(),
                    'orderUrl' => $this->getOrderUrl($merchantEntity, $merchantOrder->getId()),
                    'order_date' => $metaCart->getOrderCreatedAt()->format("d/m/y"),
                    'price_ht' => CurrencyUtil::printCurrency((float)$merchantOrder->getAmount() - (float)$merchantOrder->getShipping(), $merchant->getLanguage(), $merchantOrder->getCurrency()->getCode()),
                    'shipping_price' => CurrencyUtil::printCurrency($merchantOrder->getShipping(), $merchant->getLanguage(), $merchantOrder->getCurrency()->getCode()),
                    'total_order_price_ht' => CurrencyUtil::printCurrency($merchantOrder->getAmount(), $merchant->getLanguage(), $merchantOrder->getCurrency()->getCode()),
                    'shipping_contact' => $shippingAddress->getContact(),
                    'shipping_address' => $shippingAddress->getAddress(),
                    'shipping_address2' => $shippingAddress->getAddress2(),
                    'shipping_zipcode' => $shippingAddress->getZipcode(),
                    'shipping_city' => $shippingAddress->getCity(),
                    'shipping_country' => $shippingAddress->getCountry()?->getCode(),
                    'shippingAmountHt' => $merchantOrder->getShipping(),
                    'products' => IzbergUtils::getAttributeProductForMail($merchantOrder->getItems()->toArray())
                ],
                null,
                null,
                []
            );
        }
    }

    private function getStreets(Address $izbergAddress, ?BuyerAddress $buyerAddress, ?TotalAddress $totalAddress): array
    {

        if ($totalAddress !== null) {
            return [ $totalAddress->getAddress(), $totalAddress->getAddress2()];
        }
        if ($buyerAddress !== null) {
            return [ $buyerAddress->getAddress(),  $buyerAddress->getAddress2()];
        }
        return str_split($izbergAddress->getAddress2(), 35);

    }


}
