<?php

namespace AppBundle\Services;

use AppBundle\Model\Cart\Cart;
use AppBundle\Model\Cart\CartNotification;
use Open\IzbergBundle\Api\CartNotificationApi;

class CartNotificationService
{
    private CartNotificationApi $cartNotificationApi;
    private CartService $cartService;

    public function __construct(CartNotificationApi $cartNotificationApi, CartService $cartService)
    {
        $this->cartNotificationApi = $cartNotificationApi;
        $this->cartService = $cartService;
    }

    public function checkCartNotification(Cart $cart)
    {
        $izbergCartNotifications = $this->cartNotificationApi->getUnreadNotifications($cart->getId());

        /** @var \Open\IzbergBundle\Model\CartNotification $izbergCartNotification */
        foreach ($izbergCartNotifications as $izbergCartNotification) {
            $cart->addNotification(
                (new CartNotification())
                    ->setMessage($izbergCartNotification->getCustomMessage())
                    ->setId($izbergCartNotification->getId())
                    ->setCartItemId($izbergCartNotification->getCartItem()->getId())
                    ->setType($izbergCartNotification->getNotifType())
            );
        }
    }

    public function applyCartNotification(CartNotification $cartNotification)
    {
        $this->cartNotificationApi->readNotification($cartNotification->getId());
    }
}
