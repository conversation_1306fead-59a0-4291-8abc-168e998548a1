<?php

namespace AppBundle\Services;

use AppBundle\Entity\MarketPlace;
use AppBundle\Entity\Merchant;
use AppBundle\Entity\MerchantOrder;
use AppBundle\Entity\OrderItem;
use AppBundle\Entity\Site;
use AppBundle\Entity\User;
use AppBundle\FilterQueryBuilder\FilterQueryBuilderInterface;
use AppBundle\Model\Invitation\DataTableResponse;
use AppBundle\Model\Order\MerchantOrderData;
use AppBundle\Repository\MerchantOrderRepository;
use AppBundle\Repository\MerchantRepository;
use AppBundle\Repository\OrderItemRepository;
use AppBundle\StreamFilter\StreamFilterNewlines;
use AppBundle\Util\DateUtil;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\QueryBuilder;
use Knp\Component\Pager\Pagination\PaginationInterface;
use Knp\Component\Pager\PaginatorInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use AppBundle\Services\Cache\CacheInterface;

class ReportService extends AbstractPaginatedService
{
    private const TRANSLATION_BUNDLE = 'AppBundle';

    private TranslatorInterface $translator;
    private MerchantRepository $merchantRepository;
    private MerchantOrderRepository $merchantOrderRepository;
    private OrderItemRepository $orderItemRepository;
    private UserRelationshipService $userRelationshipService;
    private CurrencyExchangeRateService $currencyRateService;
    private CacheInterface $cache;

    public function __construct(
        EntityManagerInterface $entityManager,
        PaginatorInterface $paginator,
        TranslatorInterface $translator,
        UserRelationshipService $userRelationshipService,
        CurrencyExchangeRateService $currencyRateService,
        MerchantRepository $merchantRepository,
        MerchantOrderRepository $merchantOrderRepository,
        OrderItemRepository $orderItemRepository,
        CacheInterface $cache,
        ?FilterQueryBuilderInterface $filterQueryBuilder = null
    ) {
        parent::__construct($entityManager, 'AppBundle:MerchantOrder', $paginator, $filterQueryBuilder);
        $this->translator = $translator;
        $this->userRelationshipService = $userRelationshipService;
        $this->merchantRepository = $merchantRepository;
        $this->merchantOrderRepository = $merchantOrderRepository;
        $this->orderItemRepository = $orderItemRepository;
        $this->currencyRateService = $currencyRateService;
        $this->cache = $cache;
    }

    public function getMerchantOrderItemsWithoutShipping(int $merchantOrderId): array
    {
        $queryBuilder = $this->em->getRepository(OrderItem::class)->createQueryBuilder('orderItem');
        // orderItem.id == 0 when it's fake shipping order item
        $queryBuilder
            ->select('orderItem')
            ->where('orderItem.id <>0 and orderItem.merchantOrderId = :id')
            ->setParameter('id', $merchantOrderId);

        return $queryBuilder->getQuery()->getResult();
    }


    public function exportProductsToCsv(iterable $filteredOrdersItems, bool $writeFileHeader = false, string $locale = 'fr')
    {
        $yes = $this->translate('report.list.block.yes', $locale);
        $no = $this->translate('report.list.block.no', $locale);
        $noInfo = $this->translate('report.list.block.no_info', $locale);
        $fileHandle = fopen('php://output', 'w');
        stream_filter_register('newlines', StreamFilterNewlines::class);
        stream_filter_append($fileHandle, 'newlines');

        if($writeFileHeader){

            fwrite($fileHandle, $bom = (chr(0xEF) . chr(0xBB) . chr(0xBF)));
            $header = [
                $this->translate('report.list.block.order_number', $locale),
                $this->translate('report.list.block.cart_reference', $locale),
                $this->translate('report.list.block.order_date', $locale),
                $this->translate('report.list.block.beneficiary_name', $locale),
                $this->translate('report.list.block.beneficiary_firstname', $locale),
                $this->translate('report.list.block.beneficiaries', $locale),
                $this->translate('report.list.block.invoice_entity', $locale),
                $this->translate('report.list.block.organisational_entity', $locale),
                $this->translate('report.list.block.root_categories', $locale),
                $this->translate('report.list.block.categories', $locale),
                $this->translate('report.list.block.designation', $locale),
                $this->translate('report.list.block.gift', $locale),
                $this->translate('report.list.block.risk', $locale),
                $this->translate('report.list.block.supplier', $locale),
                $this->translate('report.list.block.vat_num', $locale),
                $this->translate('report.list.block.unit_price', $locale),
                $this->translate('report.list.block.quantity', $locale),
                $this->translate('report.list.block.amount', $locale),
                $this->translate('report.list.block.currency', $locale),
                $this->translate('report.list.block.address', $locale),
                $this->translate('report.list.block.cost_center', $locale),
                $this->translate('report.list.block.site_name', $locale),
                $this->translate('report.list.block.marketplace', $locale),
            ];
            fputcsv($fileHandle, $header, ';');

        }

        /** @var OrderItem $orderItem */
        foreach ($filteredOrdersItems as $orderItem) {
            /** @var MerchantOrder $merchantOrder */
            $merchantOrderCacheKey = sprintf("merchantOrder_%s",$orderItem->getMerchantOrderId());
            if (!$this->cache->keyExists($merchantOrderCacheKey)) {
                $merchantOrder = $this->merchantOrderRepository->find($orderItem->getMerchantOrderId());
                $buyer = $merchantOrder->getBuyer();
                $buyerInformation = ['firstname' => $buyer->getFirstname(),
                    'lastname' => $buyer->getLastname(),
                    'siteBuyer' => $buyer->getSite()->getName(),
                    'siteMerchantOrder' =>$merchantOrder->getSite()->getName()];
                $this->cache->save('buyer'.$merchantOrderCacheKey, $buyerInformation);
                $this->cache->save($merchantOrderCacheKey, $merchantOrder);
                $address = $merchantOrder->getMetaCart()->getBuyerShippingAddress();
                $shippingAddress = (null != $address ? sprintf("%s, %s %s", $address->getName(), $address->getZipCode(), $address->getCity()) : '');
                $this->cache->save('shippingAddress'.$merchantOrderCacheKey, $shippingAddress);
            } else {
                $merchantOrder = $this->cache->get($merchantOrderCacheKey);
                $buyerInformation = $this->cache->get('buyer'.$merchantOrderCacheKey);
                $shippingAddress = $this->cache->get('shippingAddress'.$merchantOrderCacheKey);
            }


            /** @var Merchant $merchant */
            $merchantCacheKey = sprintf("merchant_%s",$merchantOrder->getMerchantId());
            if (!$this->cache->keyExists($merchantCacheKey)) {
                $merchant = $this->merchantRepository->findOneBy(['izbergId' => $merchantOrder->getMerchantId()]);
                $this->cache->save($merchantCacheKey, $merchant);
            } else {
                $merchant = $this->cache->get($merchantCacheKey);
            }

            $buyerCurrency = 'EUR';
            $vendorCurrency = $orderItem->getCurrency();
            $rate = $merchantOrder->getCurrencyRateCountryOfdelivery();
            $siteName = $buyerInformation['siteMerchantOrder'] ??  $buyerInformation['siteBuyer'];

            $afterRisk = $orderItem->getOrderDate() && $orderItem->getOrderDate() > new \DateTimeImmutable("2023-01-01");

            $data = [
                trim((string)$orderItem->getMerchantOrderId()),
                $orderItem->getCartReference(),
                ($orderItem->getOrderDate()) ? $orderItem->getOrderDate()->format('Y-m-d') : null,
                $buyerInformation['lastname'] ,
                $buyerInformation['firstname'],
                sprintf("%s %s", $buyerInformation['lastname'], $buyerInformation['firstname']),
                $orderItem->getBeneficiaryInvoiceEntity(),
                $merchantOrder->getOrganisation(),
                $orderItem->getRootCategoryName(),
                $orderItem->getDefaultCategoryName(),
                $orderItem->getName(),
                ($orderItem->isGift()) ?  $yes: $no,
                $afterRisk ? ($orderItem->isRisk()) ?  $yes : $no : $noInfo,
                $orderItem->getSupplier(),
                $merchant ? $merchant->getIdentification() : null,
                number_format(CurrencyExchangeRateService::getAmountWithRate($orderItem->getPrice(), $rate,$buyerCurrency,$vendorCurrency) , 2, ',', ''),
                $orderItem->getQuantity(),
                number_format(CurrencyExchangeRateService::getAmountWithRate($orderItem->getTotal(), $rate,$buyerCurrency,$vendorCurrency), 2, ',', ''),
                $buyerCurrency,
                $shippingAddress,
                $merchantOrder->getCostCenter() ?? $merchantOrder->getBuyer()->getCostCenter(),
                $siteName,
                $orderItem->getMarketplaceName()
            ];

            fputcsv($fileHandle, $data, ';');
            flush();
        }
        fclose($fileHandle);
    }

    public function getOrdersItemsFromFilteredOrders(array $filteredOrdersIds, $page, $pageSize): array
    {
        if (count($filteredOrdersIds) === 0) {
            return [];
        }
        $queryBuilder = $this->em->getRepository(OrderItem::class)->createQueryBuilder('orderItem');
        $queryBuilder
            ->select('orderItem')
            ->where($queryBuilder->expr()->in('orderItem.merchantOrderId', $filteredOrdersIds))
            ->setMaxResults($pageSize)->setFirstResult($page * $pageSize);

        return $queryBuilder->getQuery()->getResult();
    }

    public function exportOrdersToCsv(array $orders, string $locale = 'fr')
    {
        $fileHandle = fopen('php://output', 'w');
        stream_filter_register('newlines', StreamFilterNewlines::class);
        stream_filter_append($fileHandle, 'newlines');

        fwrite($fileHandle, $bom = (chr(0xEF) . chr(0xBB) . chr(0xBF)));
        $header = [
            $this->translate('report.list.block.order_number', $locale),
            $this->translate('report.list.block.supplier', $locale),
            $this->translate('report.list.block.order_date', $locale),
            $this->translate('report.list.block.cart_reference', $locale),
            $this->translate('report.list.block.site', $locale),
            $this->translate('report.list.block.beneficiaries', $locale),
            $this->translate('report.list.block.amount', $locale),
        ];

        fputcsv($fileHandle, $header, ';');

        /** @var MerchantOrderData $order */
        foreach ($orders as $order) {
            $data = [
                $order->getOrderNumber(),
                $order->getSupplier(),
                $order->getOrderDate(),
                $order->getCartReference(),
                $order->getSiteName(),
                $order->getBeneficiary(),
                $order->getAmount(),
            ];
            fputcsv($fileHandle, $data, ';');
        }

        fclose($fileHandle);
    }

    public function getFilteredOrders(User $buyer, array $filter): array
    {
        $filteredQueryBuilder = $this->getFilteredReportsQueryBuilder($buyer, $filter);
        $filteredQueryBuilder->select('mo');
        return array_map(
            function (MerchantOrder $merchantOrder) use ($buyer) {
                return $this->convertMerchantOrderToData($merchantOrder, $buyer);
            },
            $filteredQueryBuilder->getQuery()->getResult()
        );
    }

    public function getPaginatedFilteredOrders(User $buyer, array $filter, int $page, int $pageSize): PaginationInterface
    {
        $filteredQueryBuilder = $this->getFilteredReportsQueryBuilder($buyer, $filter);
        $filteredQueryBuilder->select('mo');
        return $this->paginator->paginate($filteredQueryBuilder, $page, $pageSize);
    }

    public function getFilteredOrdersIds(User $buyer, array $filter): array
    {
        $filteredQueryBuilder = $this->getFilteredReportsQueryBuilder($buyer, $filter);
        $filteredQueryBuilder->select('mo.id')->getQuery()->getResult();
        return  array_map(
            function (array  $merchantOrder) {
                return $merchantOrder['id'];
            },
            $filteredQueryBuilder->select('mo.id')->getQuery()->getResult()
        );
    }

    public function getReportStats(User $buyer, array $filter): array
    {
        $amountInfo = $this->chooseCurrencyAmoutProperty($buyer);
        $filteredQueryBuilder = $this->getFilteredReportsQueryBuilder($buyer, $filter);
        $filteredQueryBuilder->select(
            'count(mo.id) as ordersCount, SUM(mo.'.$amountInfo.') as totalAmount, AVG(mo.'.$amountInfo.') as averageCartAmount'
        );

        return $filteredQueryBuilder->getQuery()->getArrayResult()[0];
    }

    public function getReportPaginated(User $buyer, array $filter, int $page, int $length): PaginationInterface
    {
        $qb = $this->getFilteredReportsQueryBuilder($buyer, $filter);
        $qb->select('mo');

        return $this->paginator->paginate(
            $qb->getQuery(),
            $page,
            $length,
            []
        );
    }

    public function convertToDatatableResponse($draw, PaginationInterface $pagination, User $buyer, array $reportsStats = [], array $filters = [])
    {
        $dataResponse = new DataTableResponse();
        $data = array_map(
            function (MerchantOrder $merchantOrder) use ($buyer) {
                return $this->convertMerchantOrderToData($merchantOrder, $buyer);
            },
            (array)$pagination->getItems()
        );

        $dataResponse->setDraw($draw);
        $dataResponse->setData($data);
        $dataResponse->setRecordFiltered($pagination->getTotalItemCount());
        $dataResponse->setRecordTotal($pagination->getTotalItemCount());

        if (array_key_exists('ordersCount', $reportsStats)) {
            $dataResponse->setOrdersCount($reportsStats['ordersCount']);
        }

        if (array_key_exists('totalAmount', $reportsStats)) {
            $dataResponse->setTotalAmount(number_format(round($reportsStats['totalAmount'], 2), 2, '.', ' '));
        }

        if (array_key_exists('averageCartAmount', $reportsStats)) {
            $dataResponse->setAverageCartAmount(number_format(round($reportsStats['averageCartAmount'], 2), 2, '.', ' '));
        }

        if (array_key_exists('dateStart', $filters)) {
            /** @var DateTime $startDate */
            $startDate = $filters['dateStart'];
            $dataResponse->setStartDate($startDate->format("Y-m-d"));
        }

        if (array_key_exists('dateEnd', $filters)) {
            /** @var DateTime $startDate */
            $endDate = $filters['dateEnd'];
            $dataResponse->setEndDate($endDate->format("Y-m-d"));
        }

        return $dataResponse;
    }

    private function translate(string $key, string $locale): ?string
    {
        return $this->translator->trans($key, [], self::TRANSLATION_BUNDLE, $locale);
    }

    /**
     * @param User $buyer
     * @param array $filter
     * @return QueryBuilder
     */
    private function getFilteredReportsQueryBuilder(User $buyer, array $filter): QueryBuilder
    {
        $qb = $this->em->createQueryBuilder()->from($this->entityName, 'mo');

        $this->filterMerchantOrdersByKeyword($qb, $filter); # must be the first due to orWhere clauses
        $this->filterMerchantOrdersByStartDate($qb, $filter);
        $this->filterMerchantOrdersByEndDate($qb, $filter);
        $this->filterMerchantOrdersByUserRoles($qb, $buyer, $filter);
        $this->filterMerchantOrdersWithConfirmedBySupplierStatus($qb);
        //$this->filterMerchantOrdersByMarketpLace($qb, $buyer->getMarketPlace());
        // order-item are synchronized 1 time in the night. data are not complete until there.
        $qb->andWhere('mo.supplierConfirmAt < :midnight');
        $qb->setParameter('midnight', date('Y-m-d 00:00:00'));

        $this->orderMerchantOrdersByDate($qb, 'DESC');

        return $qb;
    }

    private function orderMerchantOrdersByDate(QueryBuilder $qb, $order)
    {
        $qb->orderBy('mo.createdAt', $order);
    }

    private function filterMerchantOrdersByUserRoles(QueryBuilder $qb, User $buyer, array $filter)
    {
        $availableFilters = array_merge(['n-1','n-x'] , $buyer->getRoles());
        $reportBuyers = [$buyer->getId()];
        $getAllOrders = false;
        if (array_key_exists('role', $filter)) {
            if (in_array($filter['role'], $availableFilters)) {
                switch ($filter['role']) {
                    case 'n-1':
                        $reportBuyers = $this->userRelationshipService->fetchNXUserChildren($buyer->getId(), true);
                        $reportBuyers = array_filter($reportBuyers, function ($buyerId) use ($buyer) {
                            return $buyer->getId() != $buyerId;
                        });
                        break;
                    case User::ROLE_MARKETPLACE_REPORTING:
                        $getAllOrders = true;
                        break;
                    case User::ROLE_ENTITY_REPORTING:
                        $qb->andWhere($qb->expr()->in('mo.userInvoiceEntity', [$buyer->getInvoiceEntity()->getName()]));
                        return;
                    case 'n-x':
                        $reportBuyers = $this->userRelationshipService->fetchNXUserChildren($buyer->getId());
                        $reportBuyers = array_filter($reportBuyers, function ($buyerId) use ($buyer) {
                            return $buyer->getId() != $buyerId;
                        });
                        break;
                }
            }
        }

        if ($getAllOrders) {
            return;
        }

        if (empty($reportBuyers)) {
            $reportBuyers = [$buyer->getId()];
        }
        // buyer can see its order and his children's ones (n-x) depending of its roles -> if MANAGER get All
        $qb->andWhere($qb->expr()->in('mo.buyer', $reportBuyers));
    }

    private function filterMerchantOrdersByKeyword(QueryBuilder $qb, array $filter)
    {
        if (array_key_exists('search', $filter)) {
            $orderItemQueryBuilder = $this->orderItemRepository->createQueryBuilder('orderItem');
            $orderItemQueryBuilder
                ->select('orderItem.merchantOrderId')
                ->where('orderItem.defaultCategoryName LIKE :search')
                ->orWhere('orderItem.currentCategoryName LIKE :search')
                ->setParameter('search', '%' . $filter['search'] . '%')
                ->distinct();
            $merchantOrderIds = array_map(
                function (array $merchantOrderAsArray) {
                    return $merchantOrderAsArray['merchantOrderId'];
                },
                $orderItemQueryBuilder->getQuery()->getResult() ?? []
            );

            $qb
                ->join('mo.buyer', 'buyer')
                ->andWhere('mo.id LIKE :search')
                ->orWhere('mo.merchantName LIKE :search')
                ->orWhere('mo.createdAt LIKE :search')
                ->orWhere('buyer.lastname LIKE :search')
                ->orWhere('buyer.firstname LIKE :search')
                ->orWhere('mo.userInvoiceEntity LIKE :search')
                ->orWhere('mo.organisation LIKE :search')
                ->orWhere("CONCAT(buyer.lastname, ' ' ,buyer.firstname) LIKE :search")
                ->orWhere("CONCAT(buyer.firstname, ' ' ,buyer.lastname) LIKE :search")
                ->orWhere("CONCAT(buyer.firstname, buyer.lastname) LIKE :search")
                ->orWhere("CONCAT(buyer.lastname, buyer.firstname) LIKE :search")
                ->setParameter('search', '%' . $filter['search'] . '%');

            if (count($merchantOrderIds) > 0) {
                $qb->orWhere($qb->expr()->in('mo.id', $merchantOrderIds));
            }
        }
    }

    private function filterMerchantOrdersByStartDate(QueryBuilder $qb, array $filter)
    {
        if (array_key_exists('dateStart', $filter)) {
            $qb
                ->andWhere('mo.createdAt >= :startDate')
                ->setParameter('startDate', $filter['dateStart']);
        }
    }

    private function filterMerchantOrdersByEndDate(QueryBuilder $qb, array $filter)
    {
        if (array_key_exists('dateEnd', $filter)) {
            $dateEnd = clone $filter['dateEnd'];
            date_add($dateEnd, date_interval_create_from_date_string('1 days'));
            $qb
                ->andWhere('mo.createdAt < :endDate')
                ->setParameter('endDate', $dateEnd);
        }
    }

    private function filterMerchantOrdersWithConfirmedBySupplierStatus(QueryBuilder $qb)
    {
        $qb
            ->andWhere('mo.status = :status')
            ->setParameter('status', MerchantOrder::STATUS_CONFIRMED_BY_SUPPLIER);
    }
    /*
    private function filterMerchantOrdersByMarketplace(QueryBuilder $qb, MarketPlace $marketplace)
    {
        $orderItemQueryBuilder = $this->orderItemRepository->createQueryBuilder('orderItem');
        $orderItemQueryBuilder
            ->select('orderItem.merchantOrderId')
            ->where('orderItem.marketplaceName = :marketplaceName')
            ->setParameter('marketplaceName',$marketplace->getName())
            ->distinct();
        $merchantOrderIds = array_map(
            function (array $merchantOrderAsArray) {
                return $merchantOrderAsArray['merchantOrderId'];
            },
            $orderItemQueryBuilder->getQuery()->getResult() ?? []
        );

        if (count($merchantOrderIds) > 0) {
            $qb->andWhere($qb->expr()->in('mo.id', $merchantOrderIds));
        }
    }
    */

    public function convertMerchantOrderToData(MerchantOrder $merchantOrder, User $buyer)
    {
        $siteName ="";
        $site = $merchantOrder->getSite();
        if($site instanceof Site){
            $siteName = $site->getName();
        }

        return (new MerchantOrderData())
            ->setOrderNumber((string)$merchantOrder->getId())
            ->setSupplier($merchantOrder->getMerchantName())
            ->setOrderDate(DateUtil::printDate($merchantOrder->getCreatedAt(), $buyer->getLocale()))
            ->setCartReference((string)$merchantOrder->getMetaCart()->getId())
            ->setCategories($this->getMerchantOrderCategories($merchantOrder->getId()))
            ->setBeneficiary(
                sprintf("%s %s",
                    $merchantOrder->getBuyer()->getLastname(),
                    $merchantOrder->getBuyer()->getFirstname()
                )
            )
            ->setAmount($this->getAmount($merchantOrder, $buyer))
            ->setOrganisation($merchantOrder->getOrganisation())
            ->setSiteName($siteName);

    }

    private function getMerchantOrderCategories(int $merchantOrderId): string
    {
        $categoriesString = "";
        $merchantOrderItems = $this->getMerchantOrderItemsWithoutShipping($merchantOrderId);
        /** @var OrderItem $orderItem */
        foreach ($merchantOrderItems as $orderItem) {
            if (!str_contains($categoriesString, $orderItem->getCurrentCategoryName() ?? "")) {
                $categoriesString = sprintf("%s, %s", $categoriesString, $orderItem->getCurrentCategoryName());
            }
        }
        return substr($categoriesString, 2);
    }

    private function chooseCurrencyAmoutProperty (User $reader) : string{
        if($reader->hasRole(User::ROLE_MARKETPLACE_REPORTING)){
            return "amountCurrencyMarketPlace";
        }
        return "amountCurrencyDelivery";
    }

    private function getAmount(MerchantOrder $merchantOrder,User $reader): string{

        if($reader->hasRole(User::ROLE_MARKETPLACE_REPORTING)){
            $amount = $merchantOrder->getAmountCurrencyMarketPlace();
            $currency = $this->currencyRateService->getMarketPlaceCurrency();
        }else{
            $amount = $merchantOrder->getAmountCurrencyDelivery();
            $currency = $merchantOrder->getBuyer()->getCountryOfDelivery()->getCurrency();
        }
        $labelCurrency = $currency;
        if($currency=="EUR"){
            $labelCurrency = "€";
        }else if($currency=="USD") {
            $labelCurrency = "$";
        }

       return sprintf("%s %s", number_format($amount, 2, '.', ' '), $labelCurrency);
    }
}
