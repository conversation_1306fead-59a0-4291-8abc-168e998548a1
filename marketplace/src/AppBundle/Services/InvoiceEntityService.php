<?php


namespace AppBundle\Services;

use AppBundle\Entity\User;
use AppBundle\Entity\InvoiceEntity;
use AppBundle\FilterQueryBuilder\InvoiceEntityQueryBuilder;
use AppBundle\Repository\InvoiceEntityRepository;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Doctrine\ORM\QueryBuilder;
use Knp\Component\Pager\Pagination\PaginationInterface;
use Knp\Component\Pager\PaginatorInterface;
use Open\LogBundle\Service\LogService;

class InvoiceEntityService extends AbstractPaginatedService
{
    private LogService $logger;
    private InvoiceEntityRepository $invoiceEntityRepository;
    private AddressService $addressService;
    private CountryService $countryService;

    public function __construct(
        LogService $logger,
        InvoiceEntityRepository $invoiceEntityRepository,
        AddressService $addressService,
        CountryService $countryService,
        PaginatorInterface $paginator,
        ?InvoiceEntityQueryBuilder $filterQueryBuilder
    ) {
        parent::__construct($invoiceEntityRepository->getEntityManager(),
            InvoiceEntity::class,
            $paginator,
            $filterQueryBuilder);
        $this->logger = $logger;
        $this->invoiceEntityRepository = $invoiceEntityRepository;
        $this->addressService = $addressService;
        $this->countryService = $countryService;
    }

    /**
     * Create simple Invoice Entity from it's properties
     *
     * @param string $name
     * @param string|null $countryCode
     * @param string|null $street
     * @param string|null $street2
     * @param string|null $zipCode
     * @param string|null $locality
     * @return InvoiceEntity
     */
    public function createInvoiceEntity(
        string $name,
        ?string $countryCode,
        ?string $street = null,
        ?string $street2 = null,
        ?string $zipCode = null,
        ?string $locality = null
    ): InvoiceEntity
    {
        $invoiceEntity = $this->invoiceEntityRepository->findByName($name);

        if (!$invoiceEntity) {
            // Create only unknown invoice entity

            $invoiceEntity = new InvoiceEntity();
            $invoiceEntity->setName($name);

            if ($street !== null || $street2 !== null || $zipCode !== null || $locality !== null) {
                $address = $this->addressService->createAddressIfNotExist(
                    $street,
                    $street2,
                    $zipCode,
                    $locality
                );

                $invoiceEntity->setMainAddress($address);
            } else if (!$invoiceEntity->getMainAddress()) {
                $invoiceEntity->setMainAddress(null);
            }

            $countryCodeIzb = $this->countryService->getIzbergCountryCode($countryCode);
            // get country code
            if (!$country = $this->countryService->getCountryByIzbergCode($countryCodeIzb)) {
                throw new \Exception('Country code not found: ' . $countryCodeIzb);
            }
            $invoiceEntity->setCountry($country);

            $this->invoiceEntityRepository->save($invoiceEntity);
        }

//        if($invoiceEntity){
//            $countryCodeIzb = $this->countryService->getIzbergCountryCode($countryCode);
//            // get country code
//            $country = $this->countryService->getCountryByIzbergCode($countryCodeIzb);
//            $invoiceEntity->setCountry($country);
//            $this->invoiceEntityRepository->save($invoiceEntity);
//        }

        return $invoiceEntity;
    }

    /**
     * @param string $name
     * @return InvoiceEntity|null
     */
    public function findInvoiceEntityByName(string $name): ?invoiceEntity
    {
        return $this->invoiceEntityRepository->findByName($name);
    }

    /**
     * @param int $id
     * @return InvoiceEntity|null
     */
    public function findInvoiceEntityById(int $id): ?invoiceEntity
    {
        return $this->invoiceEntityRepository->find($id);
    }


    /**
     * @param User $buyer
     * @param InvoiceEntity $invoiceEntity
     * @return User
     */
    public function attachBuyerToInvoiceEntity(User $buyer, InvoiceEntity $invoiceEntity): User
    {

        $this->invoiceEntityRepository->saveWithoutFlush($invoiceEntity);

        return $buyer;
    }

    public function getCustomFilteredPaginator($numberPerPage, $request, $data): PaginationInterface
    {
        $qb = $this->getQueryBuilder();

        $this->filterQueryBuilder->build($qb, $data);

        return $this->paginator->paginate(
            $qb->getQuery(),
            $request->query->getInt('page', 1),
            $numberPerPage,
            array('defaultSortFieldName' => 'e.validationThreshold', 'defaultSortDirection' => $data['order'])
        );
    }

    private function getQueryBuilder(): QueryBuilder
    {
        $qb = $this->em->createQueryBuilder();
        $qb->select('e')
            ->from(InvoiceEntity::class, 'e')
            ->leftJoin('e.mainAddress', 'a')
            ->leftJoin('a.country', 'co');

        return $qb;
    }

    /**
     * @param InvoiceEntity $invoiceEntity
     */
    public function save(InvoiceEntity $invoiceEntity)
    {
        try {
            $this->em->persist($invoiceEntity);
            $this->em->flush();
            $this->em->clear();
        } catch ( ORMException | OptimisticLockException $exception) {
            $msg = sprintf("Error while persisting InvoiceEntity %s", $invoiceEntity->getId());
            $this->logger->error($msg, 'INVOICE_ENTITY_PERSISTING', [
                'message' => $exception->getMessage()
            ]);
        }

    }
}
