<?php

namespace AppBundle\Services;

class IzbergCustomAttributes
{
    private string $countryOfDelivery;
    private string $threshold1;
    private string $threshold1Price;
    private string $threshold2;
    private string $threshold2Price;
    private string $threshold3;
    private string $threshold3Price;
    private string $threshold4;
    private string $threshold4Price;
    private string $moq;
    private string $batchSize;
    private string $stockAvailability;
    private string $stockManagement;
    private string $madeIn;
    private string $zoneRestriction;
    private string $zoneRestrictionDescription;
    private string $technicalCharacteristics;
    private string $vatRate;
    private string $companyIdentificationNumber;
    private string $corporateName;
    private string $categoryList;
    private string $priceOnQuotation;
    private string $igg;
    private string $recipientContact;
    private string $recipientPhone;
    private string $recipientComment;
    private string $invoiceEntity;
    private string $adaptedCompany;
    private string $quoteSecondaryOffer;
    private string $merchantComment;
    private array $ignoredAttributes;
    private string $merchantBranch;
    private string $merchantMinimumOrderAmount;
    private string $merchantCommentPlaceholder;
    private string $notificationOrder;
    private string $notificationQuote;
    private string $notificationThread;
    private string $notificationCommercial;
    private string $epiInclusiveBranch;
    private string $epiInclusiveOrganisation;
    private string $epiInclusiveSite;
    private string $epiInclusiveEntity;
    private string $epiExclusiveBranch;
    private string $epiExclusiveOrganisation;
    private string $epiExclusiveSite;
    private string $epiExclusiveEntity;
    private string $cdatId;
    private string $data_sheet;
    private string $attached_sheet;
    private string $color;

    public function __construct(array $customAttributes)
    {
        $this->countryOfDelivery = $customAttributes['country_of_delivery'];
        $this->threshold1 = $customAttributes['threshold_1'];
        $this->threshold1Price = $customAttributes['threshold_1_price'];
        $this->threshold2 = $customAttributes['threshold_2'];
        $this->threshold2Price = $customAttributes['threshold_2_price'];
        $this->threshold3 = $customAttributes['threshold_3'];
        $this->threshold3Price = $customAttributes['threshold_3_price'];
        $this->threshold4 = $customAttributes['threshold_4'];
        $this->threshold4Price = $customAttributes['threshold_4_price'];
        $this->moq = $customAttributes['moq'];
        $this->batchSize = $customAttributes['batch_size'];
        $this->stockManagement = $customAttributes['stock_management'];
        $this->stockAvailability = $customAttributes['stock_availability'];
        $this->madeIn = $customAttributes['made_in'];
        $this->zoneRestriction = $customAttributes['zone_restriction'];
        $this->zoneRestrictionDescription = $customAttributes['zone_restriction_description'];
        $this->technicalCharacteristics = $customAttributes['technical_characteristics'];
        $this->vatRate = $customAttributes['vat_rate'];
        $this->companyIdentificationNumber = $customAttributes['company_identification_number'];
        $this->corporateName = $customAttributes['corporate_name'];
        $this->categoryList = $customAttributes['category_list'];
        $this->priceOnQuotation = $customAttributes['price_on_quotation'];
        $this->invoiceEntity = $customAttributes['invoice_entity'];
        $this->adaptedCompany = $customAttributes['adapted_company'];
        $this->igg = $customAttributes['igg'];
        $this->merchantComment = $customAttributes['merchant_comment'];
        $this->merchantCommentPlaceholder = $customAttributes['merchant_comment_placeholder'];

        $this->recipientContact = $customAttributes['recipient_contact'];
        $this->recipientPhone = $customAttributes['recipient_phone'];
        $this->recipientComment = $customAttributes['recipient_comment'];
        $this->quoteSecondaryOffer = $customAttributes['offre_secondaire_devis'];
        $this->merchantBranch = $customAttributes['merchant_branch'];
        $this->merchantMinimumOrderAmount = $customAttributes['merchant_minimum_order_amount'];
        $this->notificationOrder = $customAttributes['notification_order'];
        $this->notificationQuote = $customAttributes['notification_quote'];
        $this->notificationThread = $customAttributes['notification_thread'];
        $this->notificationCommercial = $customAttributes['notification_commercial'];

        $this->epiExclusiveBranch = $customAttributes['epi_exclusive_branch'];
        $this->epiExclusiveOrganisation = $customAttributes['epi_exclusive_organisation'];
        $this->epiExclusiveSite = $customAttributes['epi_exclusive_site'];
        $this->epiExclusiveEntity = $customAttributes['epi_exclusive_entity'];
        $this->epiInclusiveBranch = $customAttributes['epi_inclusive_branch'];
        $this->epiInclusiveOrganisation = $customAttributes['epi_inclusive_organisation'];
        $this->epiInclusiveSite = $customAttributes['epi_inclusive_site'];
        $this->epiInclusiveEntity = $customAttributes['epi_inclusive_entity'];
        $this->cdatId = $customAttributes['cdat_id'];
        $this->color = $customAttributes['color'];

        $this->data_sheet = $customAttributes['data_sheet'];
        $this->attached_sheet = $customAttributes['attached_sheet'];


        $this->ignoredAttributes = [];
    }

    /**
     * @return string
     */
    public function getColor()
    {
        return $this->color;
    }

    /**
     * @param string $color
     */
    public function setColor($color): void
    {
        $this->color = $color;
    }

    public function setIgnoredAttributes(array $ignoredAttributes)
    {
        $this->ignoredAttributes = $ignoredAttributes;
    }

    public function fetchIgnoredAttributes()
    {
        return $this->ignoredAttributes;
    }

    public static function createFullAttributeName($attributeName): string
    {
        return sprintf('attributes.%s', $attributeName);
    }

    public function fetchAllAttributes(): array
    {
        $attributes = [];

        $properties = get_object_vars($this);
        foreach($properties as $attribute) {
            if (is_array($attribute)) {
                $attributes = array_merge($attributes, $attribute);
            }

            if (is_string($attribute)) {
                $attributes[] = $attribute;
            }
        }

        return $attributes;
    }

    public function getCountryOfDelivery(): string
    {
        return $this->countryOfDelivery;
    }

    public function getThreshold1(): string
    {
        return $this->threshold1;
    }

    public function getThreshold1Price(): string
    {
        return $this->threshold1Price;
    }

    public function getThreshold2(): string
    {
        return $this->threshold2;
    }

    public function getThreshold2Price(): string
    {
        return $this->threshold2Price;
    }

    public function getThreshold3(): string
    {
        return $this->threshold3;
    }

    public function getThreshold3Price(): string
    {
        return $this->threshold3Price;
    }

    public function getThreshold4(): string
    {
        return $this->threshold4;
    }

    public function getThreshold4Price(): string
    {
        return $this->threshold4Price;
    }

    public function getMoq(): string
    {
        return $this->moq;
    }

    public function getBatchSize()
    {
        return $this->batchSize;
    }

    public function getStockAvailability(): string
    {
        return $this->stockAvailability;
    }

    public function getTechnicalCharacteristics(): string
    {
        return $this->technicalCharacteristics;
    }

    public function getStockManagement(): string
    {
        return $this->stockManagement;
    }

    public function getMadeIn(): string
    {
        return $this->madeIn;
    }

    public function getZoneRestriction(): string
    {
        return $this->zoneRestriction;
    }

    public function getZoneRestrictionDescription(): string
    {
        return $this->zoneRestrictionDescription;
    }

    public function getVatRate(): string
    {
        return $this->vatRate;
    }

    public function getCompanyIdentificationNumber(): string
    {
        return $this->companyIdentificationNumber;
    }

    public function getCorporateName(): string
    {
        return $this->corporateName;
    }

    public function getCategoryList(): string
    {
        return $this->categoryList;
    }

    public function getPriceOnQuotation(): string
    {
        return $this->priceOnQuotation;
    }

    public function getIgg(): string
    {
        return $this->igg;
    }

    public function getRecipientContact(): string
    {
        return $this->recipientContact;
    }

    public function getRecipientPhone(): string
    {
        return $this->recipientPhone;
    }

    public function getRecipientComment(): string
    {
        return $this->recipientComment;
    }

    public function getInvoiceEntity(): string
    {
        return $this->invoiceEntity;
    }

    public function getAdaptedCompany(): string
    {
        return $this->adaptedCompany;
    }

    /**
     * @return string
     */
    public function getQuoteSecondaryOffer(): string
    {
        return $this->quoteSecondaryOffer;
    }

    /**
     * @return string
     */
    public function getMerchantBranch(): string
    {
        return $this->merchantBranch;
    }


    public function getMerchantMinimumOrderAmount(): string
    {
        return $this->merchantMinimumOrderAmount;
    }

    public function getMerchantComment(): string
    {
        return $this->merchantComment;
    }

    public function getMerchantCommentPlaceholder(): string
    {
        return $this->merchantCommentPlaceholder;
    }

    /**
     * @return string
     */
    public function getNotificationOrder(): string
    {
        return $this->notificationOrder;
    }

    /**
     * @return string
     */
    public function getNotificationQuote(): string
    {
        return $this->notificationQuote;
    }

    /**
     * @return string
     */
    public function getNotificationThread(): string
    {
        return $this->notificationThread;
    }

    /**
     * @return string
     */
    public function getNotificationCommercial(): string
    {
        return $this->notificationCommercial;
    }

    /**
     * @return string
     */
    public function getEpiInclusiveBranch(): string
    {
        return $this->epiInclusiveBranch;
    }

    /**
     * @return string
     */
    public function getEpiInclusiveOrganisation(): string
    {
        return $this->epiInclusiveOrganisation;
    }

    /**
     * @return string
     */
    public function getEpiInclusiveSite(): string
    {
        return $this->epiInclusiveSite;
    }

    /**
     * @return string
     */
    public function getEpiInclusiveEntity(): string
    {
        return $this->epiInclusiveEntity;
    }

    /**
     * @return string
     */
    public function getEpiExclusiveBranch(): string
    {
        return $this->epiExclusiveBranch;
    }

    /**
     * @return string
     */
    public function getEpiExclusiveOrganisation(): string
    {
        return $this->epiExclusiveOrganisation;
    }

    /**
     * @return string
     */
    public function getEpiExclusiveSite(): string
    {
        return $this->epiExclusiveSite;
    }

    /**
     * @return string
     */
    public function getEpiExclusiveEntity(): string
    {
        return $this->epiExclusiveEntity;
    }

    /**
     * @return string
     */
    public function getCdatId(): string
    {
        return $this->cdatId;
    }

    /**
     * @param string $cdatId
     */
    public function setCdatId(string $cdatId): void
    {
        $this->cdatId = $cdatId;
    }

    /**
     * @return string
     */
    public function getDataSheet(): string
    {
        return $this->data_sheet;
    }

    /**
     * @param string $data_sheet
     */
    public function setDataSheet(string $data_sheet): void
    {
        $this->data_sheet = $data_sheet;
    }

    /**
     * @return string
     */
    public function getAttachedSheet(): string
    {
        return $this->attached_sheet;
    }

    /**
     * @param string $attached_sheet
     */
    public function setAttachedSheet(string $attached_sheet): void
    {
        $this->attached_sheet = $attached_sheet;
    }
}
