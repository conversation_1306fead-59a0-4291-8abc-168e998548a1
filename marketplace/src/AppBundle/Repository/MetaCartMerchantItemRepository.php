<?php

namespace AppBundle\Repository;

use AppBundle\Entity\MetaCartMerchantItem;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class MetaCartMerchantItemRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, MetaCartMerchantItem::class);
    }

    public function save(MetaCartMerchantItem $metaCartMerchantItem): MetaCartMerchantItem
    {
        $this->_em->persist($metaCartMerchantItem);
        $this->_em->flush();

        return $metaCartMerchantItem;
    }

    public function remove(MetaCartMerchantItem $metaCartMerchantItem)
    {
        $this->_em->remove($metaCartMerchantItem);
        $this->_em->flush();
    }
}
