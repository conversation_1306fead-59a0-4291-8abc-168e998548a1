<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Country;
use AppBundle\Entity\TvaRate;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class TvaRateRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TvaRate::class);
    }

    public function getTaxRateFromTaxGroupAndDate($taxGroup, $date): ?TvaRate
    {
        $qb = $this->createQueryBuilder('rate');
        $qb->leftJoin('rate.group', 'g');
        $qb->where('g.groupName = :taxGroup');
        $qb->andWhere('rate.fromDate < :date');
        $qb->setParameter('taxGroup', $taxGroup);
        $qb->setParameter('date', $date);
        $qb->orderBy('rate.fromDate', 'DESC');
        $result = $qb->getQuery()->getResult();
        if (count($result) > 0) {
            return $result[0];
        }

        return null;
    }

    public function findTaxRate(Country $country)
    {
        $qb = $this->createQueryBuilder('rate');
        $qb->leftJoin('rate.group', 'g')
            ->where('g.country = :country')
            ->setParameter('country', $country->getId());
        return $qb->getQuery()->getResult();

    }
}
