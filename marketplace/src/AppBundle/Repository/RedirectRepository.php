<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Redirect;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class RedirectRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Redirect::class);
    }

    /**
     * Find a content by its slug
     *
     * @param $origin
     *
     * @return mixed
     */
    public function findByOrigin($origin)
    {
        $query =
            $this->createQueryBuilder('r')
                ->where('r.origin = :origin')
                ->setMaxResults(1)
                ->setParameter('origin', $origin);

        return $query->getQuery()->getOneOrNullResult();
    }

    /**
     * Find all redirections going to $destination (Many To One)
     *
     * @param $destination
     *
     * @return array
     */
    public function findByDestination($destination)
    {
        $query =
            $this->createQueryBuilder('r')
                ->where('r.destination = :destination')
                ->setMaxResults(1)
                ->setParameter('destination', $destination);

        return $query->getQuery()->getResult();
    }


}