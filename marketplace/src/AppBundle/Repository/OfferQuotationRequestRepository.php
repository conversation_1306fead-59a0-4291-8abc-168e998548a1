<?php

namespace AppBundle\Repository;

use AppBundle\Entity\OfferQuotationRequest;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class OfferQuotationRequestRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, OfferQuotationRequest::class);
    }

    public function save(OfferQuotationRequest $offerQuotationRequest): OfferQuotationRequest
    {
        $this->_em->persist($offerQuotationRequest);
        $this->_em->flush();

        return $offerQuotationRequest;
    }
}
