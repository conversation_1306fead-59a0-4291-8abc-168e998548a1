<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Cart;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class CartRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Cart::class);
    }

    public function getCartByWPSTransactionId($transactionId)
    {
        return $this->getEntityManager()->createQueryBuilder()
            ->select("c")
            ->from("AppBundle:Cart", "c")
            ->where("c.wpsTransactionId = :transactionId")
            ->setParameter("transactionId", $transactionId)
            ->getQuery()->getOneOrNullResult();
    }

    public function getCartByOrderId($orderId)
    {
        return $this->getEntityManager()->createQueryBuilder()
            ->select("c")
            ->from("AppBundle:Cart", "c")
            ->where("c.orderId = :orderId")
            ->setParameter("orderId", $orderId)
            ->getQuery()->getOneOrNullResult();
    }


    public function getPendingCartByCompanyId($companyId)
    {
        return $this->getEntityManager()->createQueryBuilder()
            ->select("c")
            ->from("AppBundle:Cart", "c")
            ->leftJoin('c.currentUser', 'u')
            ->leftJoin('u.company', 'co')
            ->where('co.id = :companyId')
            ->andWhere('c.status = :statusAssign OR c.status = :statusRejected')
            ->setParameter('companyId', $companyId)
            ->setParameter('statusAssign', Cart::STATUS_ASSIGN)
            ->setParameter('statusRejected', Cart::STATUS_REJECTED)
            ->getQuery()->getResult();
    }

    public function getPendingCartByUserId($userId)
    {
        return $this->getEntityManager()->createQueryBuilder()
            ->select("c")
            ->from("AppBundle:Cart", "c")
            ->innerJoin('c.currentUser', 'u')
            ->where('u.id = :userId')
            ->andWhere('c.status = :statusAssign or c.status = :statusRejected')
            ->setParameter('userId', $userId)
            ->setParameter('statusAssign', Cart::STATUS_ASSIGN)
            ->setParameter('statusRejected', Cart::STATUS_REJECTED)
            ->getQuery()->getResult();
    }

    public function saveCartOrder(\AppBundle\Model\Cart\Cart $cart): bool
    {
        /** @var Cart $cartEntity */
        $cartEntity = $this->find($cart->getId());

        if (!$cartEntity || !$cart->getOrder()) {
            return false;
        }

        $cartEntity->setOrderId($cart->getOrder()->getIzbergId());
        $this->getEntityManager()->merge($cartEntity);
        $this->getEntityManager()->flush();

        return true;
    }

    public function save(Cart $cart)
    {
        $this->_em->persist($cart);
        $this->_em->flush();
    }
}
