<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Address;
use AppBundle\Entity\Country;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @deprecated
 */
class AddressRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Address::class);
    }

    public function save(Address $address): Address
    {
        $this->_em->persist($address);
        $this->_em->flush();

        return $address;
    }

    public function remove(Address $address): void
    {
        $this->_em->remove($address);
        $this->_em->flush();
    }

    public function findAddress(
        string   $address1,
        ?string  $address2 = '',
        ?string  $zipCode = null,
        ?string  $city = null,
        ?string  $regionText = null,
        ?Country $country = null
    ): ?Address
    {
        $criteria = [
            'address' => $address1,
            'address2' => $address2,
            'city' => $city,
            'zipCode' => $zipCode,
            'regionText' => $regionText,
            'country' => $country,
        ];

        return $this->findOneBy($criteria);
    }
}