<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Setting;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;


class SettingRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Setting::class);
    }

    const DOMAIN = 'domain';
    const DOMAIN_PARAM = 's.domain = :domain';


    public function findByDomain($domain)
    {
        $query =
            $this
                ->createQueryBuilder('s')
                ->where(self::DOMAIN_PARAM)
                ->orderBy('s.id', 'asc')
                ->setParameter(self::DOMAIN, $domain); // Use a constant Node::Status_Published or something

        return $query->getQuery()->getResult();
    }

    public function findByDomainAndKey($domain, $key)
    {
        $query =
            $this
                ->createQueryBuilder('s')
                ->where(self::DOMAIN_PARAM)
                ->andWhere('s.name = :name')
                ->setMaxResults(1)
                ->setParameter(self::DOMAIN, $domain)
                ->setParameter('name', $key);

        return $query->getQuery()->getOneOrNullResult();
    }

    public function findByDomainAndKeys($domain, $keys = array())
    {
        $query =
            $this
                ->createQueryBuilder('s')
                ->where(self::DOMAIN_PARAM)
                ->andWhere('s.name in (:names)')
                ->orderBy('s.id', 'asc')
                ->setParameter(self::DOMAIN, $domain)
                ->setParameter('names', $keys);

        return $query->getQuery()->getResult();
    }

    public function findByDomainAndTag($domain, $tag)
    {
        $query =
            $this
                ->createQueryBuilder('s')
                ->where(self::DOMAIN_PARAM)
                ->andWhere('s.tag LIKE %:tag%')
                ->orderBy('s.id', 'asc')
                ->setParameter(self::DOMAIN, $domain)
                ->setParameter('names', $tag);

        return $query->getQuery()->getResult();
    }
}