<?php

namespace AppBundle\Repository;

use AppBundle\Entity\CxmlConf;
use AppBundle\Entity\User;
use AppBundle\Model\Merchant;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;


class CxmlConfRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CxmlConf::class);
    }

    /**
     *
     * @param int $merchantId
     * @return CxmlConf|null
     */

    public function findMerchantConf(int $merchantId) : ?CxmlConf
    {
        $cxmlConf = $this->findOneBy(["merchantId"=>$merchantId]);
        if (!$cxmlConf instanceof CxmlConf) {
            return null;
        }
        return $cxmlConf;
    }

    /**
     *
     * @param int $limit
     * @param int $offset
     * @return CxmlConf[]
     */

    public function getAll(int $limit = 100, int $offset = 0) : array
    {
        return $this->findBy([], null, $limit, $offset);
    }

}