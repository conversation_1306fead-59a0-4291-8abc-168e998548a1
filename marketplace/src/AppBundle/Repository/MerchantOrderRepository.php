<?php

namespace AppBundle\Repository;

use AppBundle\Entity\MerchantOrder;
use AppBundle\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class MerchantOrderRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, MerchantOrder::class);
    }

    public function findBuyerOrders(User $buyer, string $status, int $page = null, int $pageSize = null): array
    {
        $offset = null;
        $limit = null;

        if ($page > 0 && $pageSize > 0) {
            $offset = ($page - 1) * $pageSize;
            $limit = $pageSize;
        }

        return $this->findBy(
            [
                'buyer' => $buyer,
                'status' => $status,
            ],
            ['createdAt' => 'desc'],
            $limit,
            $offset
        );
    }

    public function findMerchantOrdersWithoutInvoiceEntity(): array
    {
        return $this
            ->createQueryBuilder('mo')
            ->select('mo')
            ->where('mo.userInvoiceEntity IS NULL')
            ->getQuery()
            ->getResult();
    }

    public function save(MerchantOrder $merchantOrder): MerchantOrder
    {
        $this->_em->persist($merchantOrder);
        $this->_em->flush();

        return $merchantOrder;
    }

    public function fixMerchantOrderCurrencyRate()
    {

        $this->_em->getConnection()->executeUpdate("
        update merchant_order set amount_delivery= amount, amount_mkp = amount , currency_rate_country = 1 , currency_rate_mkp = 1 where currency = 'EUR';
    ");
    }

    public function getNotShippedCommands()
    {
        return $this
            ->createQueryBuilder('mo')
            ->select('mo')
            ->where('mo.shipping = :shipping')
            ->setParameter('shipping', 0)
            ->getQuery()
            ->getResult();
    }
}
