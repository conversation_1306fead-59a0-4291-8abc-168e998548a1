<?php

namespace AppBundle\Repository;

use AppBundle\Entity\UserToUserRelationship;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;


class UserToUserRelationshipRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, UserToUserRelationship::class);
    }

    public function createRelationship(UserToUserRelationship $relationship, $withFlush = true): void
    {
        $alreadyLinked = $this->count([
                "childUser" => $relationship->getChildUser(),
                "parentUser" => $relationship->getParentUser(),
                "isDelegate" => $relationship->getIsDelegate(),
            ]
        );

        // To repect unicity of link
        if ($alreadyLinked > 0) {
            return;
        }

        if (!$relationship->getIsDelegate()) {
            // Remove all existing manager relationships between child<PERSON>uy<PERSON> and any parentBuyer before adding the new one

            $existingManagerRelationShips = $this->findBy(
                [
                    "childUser" => $relationship->getChildUser(),
                    "isDelegate" => false,
                ]
            );

            foreach ($existingManagerRelationShips as $relationShip) {
                $this->_em->remove($relationShip);
            }
            if ($withFlush) {
                $this->_em->flush();
            }
        }

        $this->_em->persist($relationship);
        if ($withFlush) {
            $this->_em->flush();
        }
    }

    public function deleteRelationship(UserToUserRelationship $relationship): void
    {
        $relationShip = $this->findOneBy([
            "childUser" => $relationship->getChildUser(),
            "parentUser" => $relationship->getParentUser(),
            "isDelegate" => $relationship->getIsDelegate(),
        ]);

        $this->_em->remove($relationShip);
        $this->_em->flush();
    }

    public function deleteManager(int $userId)
    {
        $relationShip = $this->findOneBy([
            "childUser" => $userId,
            "isDelegate" => false,
        ]);
        if (!is_null($relationShip)) {
            $this->_em->remove($relationShip);
            $this->_em->flush();
            return true;
        }
        return false;
    }
}
