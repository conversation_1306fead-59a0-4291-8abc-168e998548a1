<?php

namespace AppBundle\Repository;

use AppBundle\Entity\MetaCart;
use AppBundle\Entity\MetaCartCheckout;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class MetaCartCheckoutRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, MetaCartCheckout::class);
    }

    public function findSuccessFulCheckoutByMetaCart(MetaCart $metaCart): ?MetaCartCheckout
    {
        return $this->findOneBy(['metaCart' => $metaCart, 'state' => MetaCartCheckout::STATE_FINISHED]);
    }

    public function save(MetaCartCheckout $metaCart): MetaCartCheckout
    {
        $this->_em->persist($metaCart);
        $this->_em->flush();

        return $metaCart;
    }
}
