<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Favori;
use AppBundle\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class FavoriRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Favori::class);
    }

    /**
     *
     * @param User $buyer
     *
     * @return array
     */
    public function findByBuyer(User $buyer): ?array
    {
        return $this->findBy([
            "buyer"=>$buyer->getId(),
            "marketPlace"=>$buyer->getMarketPlace()
        ]);
    }

    /**
     *
     * @param User $buyer
     * @param int $offerId
     *
     * @return Favori
     */
    public function addForBuyer(User $buyer, int $offerId): Favori
    {
        $favoriExists = $this->findBuyerOffer($buyer, $offerId);
        if ($favoriExists) {
            return $favoriExists;
        } else {
            $favori = new Favori();
            $favori->setBuyer($buyer);
            $favori->setOfferId($offerId);
            $favori->setMarketPlace($buyer->getMarketPlace());
            $this->_em->persist($favori);
            $this->_em->flush();
            return $favori;
        }
    }

    /**
     *
     * @param User $buyer
     * @param int $offerId
     *
     * @return Favori|null
     */
    public function findBuyerOffer(User $buyer, int $offerId) : ?Favori
    {
        $favori = $this->findOneBy([
            "buyer"=>$buyer,
            "offerId"=>$offerId,
            "marketPlace"=>$buyer->getMarketPlace()
        ]);
        if (!$favori instanceof Favori) {
            return null;
        }
        return $favori;
    }

    public function removeForBuyer(User $buyer, array $offerIds)
    {
        $qb = $this->_em->createQueryBuilder();
        $query = $qb->delete('AppBundle:Favori', 'f')
            ->where('f.buyer = :buyer')
            ->andWhere('f.offerId in (:offer_ids)')
            ->setParameters([
                "buyer" => $buyer,
                "offer_ids" =>implode(",",$offerIds)
            ])
            ->getQuery();
        return $query->execute();
    }

    public function delete(array $ids)
    {
        return $this->_em->createQueryBuilder()
            ->delete('AppBundle:Favori', 'f')
            ->where('f.id in (:ids)')
            ->setParameter("ids", $ids)
            ->getQuery()
            ->execute();
    }

    public function remove(Favori $favori): void
    {
        $this->_em->remove($favori);
        $this->_em->flush();
    }
}
