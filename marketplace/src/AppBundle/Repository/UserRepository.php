<?php

namespace AppBundle\Repository;

use AppBundle\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

class UserRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, User::class);
    }

    /**
     * get list of enabled operators
     * @return mixed
     */
    public function findOperator()
    {
        /**
         * @var QueryBuilder
         */
        $qb =
            $this->createQueryBuilder('u')
                ->select('u')
                ->where("u.roles like '%OPERATOR%' and u.enabled = 1");

        return $qb->getQuery()->getResult();
    }

    /**
     * get list of enabled admins
     * @return mixed
     */
    public function findAdmin()
    {
        /**
         * @var QueryBuilder
         */
        $qb =
            $this->createQueryBuilder('u')
                ->select('u')
                ->where("u.roles like '%SUPER_ADMIN%' and u.enabled = 1");

        return $qb->getQuery()->getResult();
    }

    public function findPotentialDelegateByNameOrIgg(string $token, User $user)
    {

        $subQb = $this->getEntityManager()->createQueryBuilder()
            ->select('Identity(delegate.parentUser)')
            ->from("AppBundle:UserToUserRelationship", "delegate")
            ->where('delegate.isDelegate = true and delegate.childUser = :user ');

        /**
         * @var QueryBuilder
         */
        $qb =
            $this->createQueryBuilder('u')
                ->select('u')
                ->Where("u.enabled = true and u != :user and u.roles like '%BUYER%'")
                ->andWhere("( CONCAT(u.firstname,' ',u.lastname) LIKE :token OR u.username LIKE :token)")
                ->andWhere("u.id not in (" . $subQb->getDQL() . ")");
        $qb->setParameter('token', '%' . $token . '%')
            ->setParameter("user", $user->getId());

        return $qb->getQuery()->getResult();
    }

    /**
     * find all users that are not operators neither administrator
     */
    public function findNonAdministratorUsers()
    {
        return $this->createQueryBuilder('u')
            ->where("u.roles NOT LIKE '%OPERATOR%'")
            ->andWhere("u.roles NOT LIKE '%SUPER_ADMIN%'");
    }

    public function getUpdateIsCurrentMetaCartToUsers()
    {
        $em = $this->getEntityManager();
        $sql = "update users manager set manager.auto_enabled = true"
            . "  where manager.enabled = false and exists (" // sub table to select manager with nb children > 0
            . "      select parent_user_id from  user_to_user_relationship r"
            . "      join (select * from users) as child on child.id = r.child_user_id"
            . "      where manager.id = parent_user_id"
            . "      and r.isDelegate = false and child.enabled = true group by  parent_user_id having count(*) > 0 ) ";
        $stmt = $em->getConnection()->prepare($sql);
        $stmt->execute();
    }

    public function activateAllAutoEnablePossible()
    {

        $em = $this->getEntityManager();
        $sql = "update users manager set manager.auto_enabled = true"
            . "  where manager.enabled = false and exists (" // sub table to select manager with nb children > 0
            . "      select parent_user_id from  user_to_user_relationship r"
            . "      join (select * from users) as child on child.id = r.child_user_id"
            . "      where manager.id = parent_user_id"
            . "      and r.isDelegate = false and child.enabled = true group by  parent_user_id having count(*) > 0 ) ";
        $stmt = $em->getConnection()->prepare($sql);
        $stmt->execute();

    }

    public function deactivateAllNonRequiredAutoEnable()
    {
        $em = $this->getEntityManager();
        $sql = "update users manager set manager.auto_enabled = false"
            . "  where manager.enabled = true or not exists (" // sub table to select manager with nb children > 0
            . "      select parent_user_id from  user_to_user_relationship r"
            . "      join (select * from users) as child on child.id = r.child_user_id"
            . "      where manager.id = parent_user_id"
            . "      and r.isDelegate = false and child.enabled = true group by  parent_user_id having count(*) > 0 ) ";
        $stmt = $em->getConnection()->prepare($sql);
        $stmt->execute();
    }

    /**
     * Find users authorized to buy and valid carts for a company
     */
    public function findBuyersForCompany($companyId)
    {
        $qb = $this->createQueryBuilder('u')
            ->join('u.company', 'c')
            ->where("u.roles LIKE '%BUYER_PAYER%'")
            ->orWhere("u.roles LIKE '%BUYER_ADMIN%'")
            ->andWhere('c.id = :companyId');
        $qb->setParameter('companyId', $companyId);
        return $qb->getQuery()->getResult();
    }

    /**
     * @param $siteId
     * @param $userId
     * Find users authorized to buy and valid carts for a code center (other than current user)
     *
     * @return mixed
     */
    public function findUsersForSite($siteId, $userId)
    {
        $qb = $this->createQueryBuilder('u')
            ->join('u.sites', 's')
            ->where('s.id = :siteId')
            ->andWhere('u.id != :userId')
            ->andWhere('u.enabled <> 0');
        $qb->setParameter('siteId', $siteId);
        $qb->setParameter('userId', $userId);

        return $qb->getQuery()->getResult();
    }

    public function save(User $user): User
    {
        $this->_em->persist($user);
        $this->_em->flush();

        return $user;
    }

    public function findUserByUsername(string $username)
    {
        /**
         * @var QueryBuilder
         */
        $qb = $this->createQueryBuilder('u')
            ->select('u')
            ->Where("u.username = :username")
            ->setParameter('username', $username);
        return $qb->getQuery()->getOneOrNullResult();
    }

    public function findUserByUsernameOrEmail(string $username)
    {
        /**
         * @var QueryBuilder
         */
        $qb = $this->createQueryBuilder('u')
            ->select('u')
            ->where("u.email = :email")
            ->orWhere("u.username = :username")
            ->setParameter('email', $username)
            ->setParameter('username', $username);

        return $qb->getQuery()->getOneOrNullResult();
    }

    public function findUserByPasswordToken(string $token)
    {
        /**
         * @var QueryBuilder
         */
        $qb = $this->createQueryBuilder('u')
            ->select('u')
            ->where("u.resetPasswordToken = :token")
            ->setParameter('token', $token);

        return $qb->getQuery()->getOneOrNullResult();
    }

    public function disableNotActiveBuyers()
    {

        $sub = $this->_em->createQueryBuilder()
            ->select('du.igg')
            ->from("AppBundle:DistantUser", 'du');


        $qb = $this->createQueryBuilder('u');
        $qb->update()
            ->set("u.enabled", 0)
            ->where($qb->expr()->notIn('u.username', $sub->getDQL()))
            ->andWhere("u.roles LIKE '%ROLE_BUYER%'")
            ->getQuery()->execute();


    }
}
