<?php

namespace AppBundle\Repository;

use AppBundle\Entity\MetaCartMerchant;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class MetaCartMerchantRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, MetaCartMerchant::class);
    }

    public function save(MetaCartMerchant $metaCartMerchant): MetaCartMerchant
    {
        $this->_em->persist($metaCartMerchant);
        $this->_em->flush();

        return $metaCartMerchant;
    }

    public function remove(MetaCartMerchant $metaCartMerchant)
    {
        $this->_em->remove($metaCartMerchant);
        $this->_em->flush();
    }

    /**
     * @param int $metaCartId
     * @param int $merchantId
     *
     * @return string|null
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function fetchMerchantComment(int $metaCartId, int $merchantId): ?string
    {
        $qb = $this->createQueryBuilder('m')
            ->leftJoin('m.metaCart', 'mc')
            ->where('mc.id=:metaCartId')
            ->andWhere('m.izbergId=:merchantId')
            ->setParameter('metaCartId', $metaCartId)
            ->setParameter('merchantId', $merchantId);

        /** @var MetaCartMerchant $merchant */
        $merchant = $qb->getQuery()->getOneOrNullResult();

        if (!$merchant) {
            return null;
        }

        $comment = $merchant->getComment();

        return (!empty($comment)) ? $comment : null;
    }

    public function fetchMerchantCommentPlaceholder(int $metaCartId, int $merchantId): ?string
    {
        $qb = $this->createQueryBuilder('m')
            ->leftJoin('m.metaCart', 'mc')
            ->where('mc.id=:metaCartId')
            ->andWhere('m.izbergId=:merchantId')
            ->setParameter('metaCartId', $metaCartId)
            ->setParameter('merchantId', $merchantId);

        /** @var MetaCartMerchant $merchant */
        $merchant = $qb->getQuery()->getOneOrNullResult();

        if (!$merchant) {
            return null;
        }

        $commentPlaceholder = $merchant->getCommentPlaceholder();

        return (!empty($commentPlaceholder)) ? $commentPlaceholder : null;
    }
}
