<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Menu;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class MenuRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Menu::class);
    }

    public function findByName($name)
    {
        $query =
            $this
                ->createQueryBuilder('m')
                ->where('m.name = :name')
                ->setMaxResults(1)
                ->setParameter('name', $name);

        return $query->getQuery()->getOneOrNullResult();
    }

}