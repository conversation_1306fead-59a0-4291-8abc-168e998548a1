<?php

namespace AppBundle\Repository;

use AppBundle\Entity\ShippingRecipient;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;


/**
 * @deprecated
 */
class ShippingRecipientRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ShippingRecipient::class);
    }

    public function save(ShippingRecipient $recipient): ShippingRecipient
    {
        $this->_em->persist($recipient);
        $this->_em->flush();

        return $recipient;
    }

    public function remove(ShippingRecipient $recipient): void
    {
        $this->_em->remove($recipient);
        $this->_em->flush();
    }
}