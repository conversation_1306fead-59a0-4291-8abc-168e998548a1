<?php

namespace AppBundle\Repository;

use AppBundle\Entity\BuyerAddress;
use AppBundle\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Open\IzbergBundle\Model\Address;

class BuyerAddressRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, BuyerAddress::class);
    }

    public function save(BuyerAddress $buyerAddress): BuyerAddress
    {
        $this->_em->persist($buyerAddress);
        $this->_em->flush();

        return $buyerAddress;
    }

    public function saveWithoutFlush(BuyerAddress $buyerAddress): BuyerAddress
    {
        $this->_em->persist($buyerAddress);

        return $buyerAddress;
    }

    public function getLastUsedAddresses(int $userId): array
    {
        $sub = $this->_em->createQueryBuilder()->select('max(m1.validatedAt)')
            ->from("AppBundle:BuyerAddress", "a1")
            ->join('a1.metaCart', 'm1')
            ->where("a1.is_active=true and a1.user = :userId")
            ->groupBy('a1.address')
            ->addGroupBy('a1.address2')
            ->addGroupBy('a1.zipCode')
            ->addGroupBy('a1.city')
            ->addGroupBy('a1.country');
        $qb =
            $this->createQueryBuilder('a')
                ->select('a')
                ->join('a.metaCart', 'm');
        $qb
            ->where("a.is_active=true and a.user = :userId")
            ->andWhere("m.status= 'ORDERED'")
            ->andWhere($qb->expr()->in('m.validatedAt', $sub->getDQL()))
            ->setParameter('userId', $userId)
            ->orderBy("m.validatedAt", "desc");

        return $qb->getQuery()->getResult();
    }

    public function getLastUsedAddress(int $userId): ?BuyerAddress
    {
        $sub = $this->_em->createQueryBuilder()->select('max(m1.orderCreatedAt)')
            ->from("AppBundle:BuyerAddress", "a1")
            ->join('a1.metaCart', 'm1')
            ->where("a1.is_active=true and a1.user = :userId");

        $qb =
            $this->createQueryBuilder('a')
                ->select('a')
                ->join('a.metaCart', 'm');
        $qb
            ->where("a.is_active=true and a.user = :userId")
            ->andWhere($qb->expr()->in('m.orderCreatedAt', $sub->getDQL()))
            ->setParameter('userId', $userId)
            ->orderBy("m.orderCreatedAt", "desc");

        return $qb->getQuery()->getOneOrNullResult();
    }

    public function findTermMultiColumn(User $user, string $term)
    {
        $qb =
            $this->createQueryBuilder('a');
        $qb->where("a.country = :country");
        $qb->andWhere("a.type = :type");
        $qb->andWhere("a.is_active = true");
        $qb->andWhere("a.user = :user");
        $qb->andWhere($qb->expr()->orX()->addMultiple( // nested condition
            [$qb->expr()->like("a.name", ":term"),
            $qb->expr()->like("a.address", ":term"),
            $qb->expr()->like("a.address2", ":term"),
            $qb->expr()->like("a.city", ":term"),
            $qb->expr()->like("a.zipCode", ":term")]
        ))
            ->setParameter("term", "%" . $term . "%")
            ->setParameter("country", $user->getCountryOfDelivery())
            ->setParameter("type", BuyerAddress::TYPE_PERSONAL)
            ->setParameter("user", $user->getId());

        return $qb->getQuery()->getResult();
    }

    public function resetBuyerAddressIzbergId(User $user)
    {

        $queryBuilder = $this->createQueryBuilder('b');
        $query = $queryBuilder->update('AppBundle\Entity\BuyerAddress', 'b')
            ->set('b.izbergAddressId', ':value')
            ->where('b.user = :userId')
            ->setParameter('value', null)
            ->setParameter('userId', $user->getId())
            ->getQuery();
        $result = $query->execute();
    }

    public function unsetBuyerAddresses(User $user)
    {
        return $this->createQueryBuilder('b')
            ->update('AppBundle:BuyerAddress', 'b')
            ->set('b.is_active', ':active')
            ->set('b.izbergAddressId', ':address')
            ->where('b.user=:user')
            ->setParameter('active', 0)
            ->setParameter('address', null)
            ->setParameter('user', $user->getId())
            ->getQuery()
            ->execute();
    }

    public function findByIzbergAddress(Address $address)
    {
        //address are create with a header
        //Address2 = Address." ".Address2;
        //Address = AddressHeader." / ".invoiceEntityName;
        $qb =
            $this->createQueryBuilder('a');
        $qb->where('a.city =:city')
            ->andWhere('a.zipCode =:zipcode')
            ->andWhere('CONCAT(a.address, \' \', a.address2) =:address2')
            ->setParameter("city", $address->getCity())
            ->setParameter("zipcode", $address->getZipcode())
            ->setParameter("address2", $address->getAddress2());
        return $qb->getQuery()->getOneOrNullResult();
    }
}
