<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Merchant;
use AppBundle\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Knp\Component\Pager\Pagination\PaginationInterface;
use Knp\Component\Pager\PaginatorInterface;

class MerchantRepository extends ServiceEntityRepository
{
    private PaginatorInterface $paginator;

    public function __construct(ManagerRegistry $registry, PaginatorInterface $paginator)
    {
        parent::__construct($registry, Merchant::class);
        $this->paginator = $paginator;
    }

    /**
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function save(Merchant $merchant)
    {
        $this->getEntityManager()->persist($merchant);
        $this->getEntityManager()->flush();
    }

    /**
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function update(Merchant $merchant, ?User $author = null)
    {
        if ($author) {
            $merchant->setActionBy($author);
        }
        $merchant->setUpdatedAt(new \DateTimeImmutable());
        $this->getEntityManager()->merge($merchant);
        $this->getEntityManager()->flush();
    }

    public function paginatedMerchants( $data, int $page = 0, string $mkp = "FRA", int $numberPerPage = 10): PaginationInterface
    {
        $qb = $this->getEntityManager()->createQueryBuilder();
        $qb->select('e')
            ->from($this->getEntityName(), 'e')
            ->join('e.marketPlace', 'mkp')
            ->where("1=1")
            ->andWhere('mkp.totalDiscriminator = :mkp')
            ->addOrderBy("e.status", 'DESC')
            ->addOrderBy("e.registrationDate", "DESC")
            // if filter is 'invitation' registration date is null and no order is really set.
            // so 'limit' pagination don't work well.
            ->addOrderBy("e.createdDate", "DESC")
            ->setParameter("mkp", $mkp);


        if (!empty($data['status']) && $data['status'] != 'all') {
            $qb->andWhere('e.status = :status');
            $qb->setParameter('status', $data['status']);
        }

        if (!empty($data['id'])) {
            $qb->andWhere('e.id = :id');
            $qb->setParameter('id', $data['id']);
        }

        if (!empty($data['name'])) {
            $qb->andWhere('e.name LIKE :name');
            $qb->setParameter('name', '%' . $data['name'] . '%');
        }

        if (!empty($data['identification'])) {
            $qb->andWhere('e.identification LIKE :identification');
            $qb->setParameter('identification', '%' . $data['identification'] . '%');
        }

        if (!empty($data['source'])) {
            $qb->andWhere('e.source LIKE :source');
            $qb->setParameter('source', '%' . $data['source'] . '%');
        }

        if (!empty($data['activites'])) {
            $qb->andWhere('e.activites LIKE :activites');
            $qb->setParameter('activites', '%' . $data['activites'] . '%');
        }

        if (!empty($data['email'])) {
            $qb->andWhere('e.email LIKE :email');
            $qb->setParameter('email', '%' . $data['email'] . '%');
        }


        if (!empty($data['firstname'])) {
            $qb->andWhere('e.firstname LIKE :firstname');
            $qb->setParameter('firstname', '%' . $data['firstname'] . '%');
        }

        if (!empty($data['lastname'])) {
            $qb->andWhere('e.lastname LIKE :lastname');
            $qb->setParameter('lastname', '%' . $data['lastname'] . '%');
        }

        return $this->paginator->paginate($qb->getQuery(), $page, $numberPerPage, []);
    }

    public function searchMerchants(string $searchKey, User $buyer)
    {
        $qb = $this->createQueryBuilder('m');
        $qb->where("m.marketPlace = :mpk")
            ->andWhere("m.name LIKE :name")
            ->andWhere("m.izbergUSerId IS NOT NULL")
            ->andWhere("m.izbergId IS NOT NULL")
            ->orderBy("m.name")
            ->setParameter('mpk', $buyer->getMarketPlace())
            ->setParameter('name', '%' . $searchKey . '%');
        return $qb->getQuery()->getResult();
    }

    public function getBuyerInvitation(User $buyer)
    {

        $unwanted = [
            Merchant::STATUS_ACCOUNT_VALIDATION,
            Merchant::STATUS_REJECTED
        ];

        $qb = $this->createQueryBuilder('i');
        $qb->where('i.inviteByIgg = :igg AND i.status not in ( :unwanted ) and i.marketPlace = :mkp')
            ->orderBy('i.createdDate', 'DESC')
            ->setParameter('igg', $buyer->getUsername())
            ->setParameter('unwanted', $unwanted)
            ->setParameter('mkp', $buyer->getMarketPlace());
        return $qb->getQuery()->getResult();
    }

    public function paginatedAllInvitation(User $buyer, $data, int $page = 0, int $numberPerPage = 10)
    {
        $finishStatus = Merchant::STATUS_ACCOUNT_VALIDATION;
        $rejectedStatus = Merchant::STATUS_REJECTED;

        $qb = $this->getEntityManager()->createQueryBuilder();
        $qb->select('e')
            ->from($this->getEntityName(), 'e');
        $qb
            ->where('e.marketPlace = :mkp and ((e.inviteByIgg = :igg AND (e.status = :validated or e.status = :rejected) ) or e.inviteByIgg is null or e.inviteByIgg != :igg )')
            ->setParameter('igg', $buyer->getUsername())
            ->setParameter('validated', $finishStatus)
            ->setParameter('rejected', $rejectedStatus)
            ->setParameter('mkp', $buyer->getMarketPlace());

        if (!empty($data['search'])) {
            $qb->andWhere('e.name like :search or e.email like :search');
            $qb->setParameter('search', '%' . $data['search'] . '%');
        }

        $qb->orderBy('e.createdDate', 'DESC');

        return $this->paginator->paginate($qb->getQuery(), $page, $numberPerPage, []);

    }

    /**
     * @param \Open\IzbergBundle\Model\Merchant $izbergMerchant
     *
     * @return Merchant|null
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function findByIzbergMerchant(\Open\IzbergBundle\Model\Merchant $izbergMerchant): ?Merchant
    {
        return $this->findOneBy(['izbergId' => $izbergMerchant->getId()]);
    }

    /**
     * @param string $email
     * @param int    $merchantId
     *
     * @return Merchant|null
     */
    public function findMerchantByEmailOtherThanThisId(string $email, int $merchantId): ?Merchant
    {
        $qb = $this->createQueryBuilder('m');
        $qb->where("m.email = :email AND m.id != :id  AND m.status in (:statuses)")
            ->setParameter('email', $email)
            ->setParameter('id', $merchantId)
            ->setParameter('statuses', [Merchant::STATUS_VALIDATION, Merchant::STATUS_ACCOUNT_VALIDATION])
            ->setMaxResults(1);
        return $qb->getQuery()->getOneOrNullResult();

    }
}
