<?php

namespace AppBundle\Repository;

use AppBundle\Entity\MetaCart;
use AppBundle\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class MetaCartRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, MetaCart::class);
    }

    public function findMetaCart(int $cartId): ?MetaCart
    {
        return $this->find($cartId);
    }

    public function findBuyerMetaCart(User $buyer, int $cartId): ?MetaCart
    {
        $metaCart = $this->findOneBy(['buyer' => $buyer, 'id' => $cartId]);
        if (!$metaCart instanceof MetaCart) {
            return null;
        }

        return $metaCart;
    }

    public function findBuyerCurrentMetaCart(User $buyer): ?MetaCart
    {
        $metaCart = $this->findOneBy(['buyer' => $buyer, 'isCurrent' => true]);
        if (!$metaCart instanceof MetaCart) {
            return null;
        }

        return $metaCart;
    }

    public function findLastBuyerMetaCart(User $buyer): ?MetaCart
    {
        $metaCart = $this->findOneBy(
            [
                'buyer' => $buyer,
                'status' => [
                    MetaCart::STATUS_ORDERED,
                    MetaCart::STATUS_ORDERING,
                    MetaCart::STATUS_ASSIGNED,
                ],
            ],
            ['id' => 'desc']
        );
        if (!$metaCart instanceof MetaCart) {
            return null;
        }

        return $metaCart;
    }

    public function save(MetaCart $metaCart): MetaCart
    {
        $this->_em->persist($metaCart);
        $this->_em->flush();

        return $metaCart;
    }

    public function showResumeFix()
    {

        $qb = $this->getEntityManager()->createQueryBuilder();
        return $qb->select("m.id meta_id, old_address.id current_ship_id, default_address.id default_ship_id")
            ->from('AppBundle:MetaCart', 'm')
            ->leftJoin("m.buyerShippingAddress", "old_address")
            ->leftJoin("m.buyer", "buyer")
            ->leftJoin("buyer.defaultBuyerAddress", "default_address")
            ->where('m.isCurrent = true')
            ->andWhere("(m.status = 'CREATED' or m.status = 'EMPTIED')")
            ->andWhere("old_address.type = 'total'")
            ->andWhere("old_address.id <> default_address.id")
            ->getQuery()
            ->getScalarResult();
    }

    /**
     * one shot after TOTALTMA_120 fix in hotfix 3.7.6
     */
    public function fixShippingAddress()
    {
        $em = $this->getEntityManager();
        $sql = "update meta_cart m "
            . "join buyer_address old_address on old_address.id = m.buyer_shipping_address_id "
            . "set m.buyer_shipping_address_id = (select u.default_buyer_address_id from users u  where m.buyer_id = u.id) "
            . "where  m.is_current = 1 "
            . "and (m.status = 'CREATED' or m.status = 'EMPTIED') "
            . "and old_address.type = 'total'";
        $stmt = $em->getConnection()->prepare($sql);
        $stmt->execute();
    }

    public function fixMetaCartIsCurrent(User $user)
    {
        return $this->createQueryBuilder('m')
            ->update('AppBundle:MetaCart', 'm')
            ->set('m.isCurrent', 0)
            ->where('m.buyer=:buyer')
            ->setParameter('buyer', $user->getId())
            ->getQuery()
            ->execute();
    }

    public function resetCurrentUserMetaCart(User $user)
    {
        return $this->createQueryBuilder('m')
            ->update('AppBundle:MetaCart', 'm')
            ->set('m.isCurrent', ':current')
            ->where('m.buyer=:buyer')
            ->setParameter('current', 0)
            ->setParameter('buyer', $user->getId())
            ->getQuery()
            ->execute();
    }

}
