<?php

namespace AppBundle\Repository;

use AppBundle\Entity\InvoiceEntity;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\EntityManager;
use Doctrine\Persistence\ManagerRegistry;

class InvoiceEntityRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, InvoiceEntity::class);
    }

    public function getEntityManager(): EntityManager
    {
        return $this->_em;
    }

    public function save(InvoiceEntity $invoiceEntity)
    {
        try {
            $this->_em->persist($invoiceEntity);
            $this->_em->flush();
        } catch (\Exception $e) {
            throw $e;
        }
    }

    public function saveWithoutFlush(InvoiceEntity $invoiceEntity)
    {
        $this->_em->persist($invoiceEntity);
    }

    public function findByName(?string $name): ?InvoiceEntity
    {
        return $this->findOneBy(['name' => $name]);
    }
}
