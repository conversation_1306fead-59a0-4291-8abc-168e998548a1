<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Company;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class CompanyRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Company::class);
    }

    public function isUserExistsInCompany($companyId, $userId)
    {
        $qb =
            $this->createQueryBuilder('c')
                ->select('c')
                ->join('c.users', 'u')
                ->where("u.id = :userId")
                ->andWhere("u.company = :companyId")
                ->setParameter('userId', $userId)
                ->setParameter('companyId', $companyId);


        return $qb->getQuery()->getOneOrNullResult();
    }

    public function getOwners($companyId)
    {
        $qb =
            $this->getEntityManager()
                ->createQueryBuilder()
                ->select('u')
                ->from("AppBundle:User", 'u')
                ->where("u.company = :companyId")
                ->andWhere("u.roles LIKE :role")
                ->setParameter('companyId', $companyId)
                ->setParameter('role', '%OWNER%');


        return $qb->getQuery()->getResult();

    }

    public function getOwnerCompanyNotPending($Xdays)
    {
        $date = date('Ymd', strtotime("-$Xdays day"));

        $qb =
            $this->getEntityManager()->createQueryBuilder()
                ->select('u')
                ->from("AppBundle:User", 'u')
                ->leftJoin('u.company', 'c')
                ->where("c.status = :status")
                ->andWhere("c.createdAt < :date")
                ->andWhere("u.roles LIKE :roleBuyer OR u.roles LIKE :roleMerchant")
                ->setParameter('status', 'draft')
                ->setParameter('date', $date)
                ->setParameter('roleBuyer', '%BUYER_ADMIN%')
                ->setParameter('roleMerchant', '%MERCHANT_ADMIN%');

        return $qb->getQuery()->getResult();
    }

    public function findCompanyByIzbergUserId(int $izbergUserId)
    {
        return $this->getEntityManager()->createQueryBuilder()
            ->select("c")
            ->from("AppBundle:Company", "c")
            ->where("c.izbergUserId = :izbergUserId")
            ->setParameter("izbergUserId", $izbergUserId)
            ->getQuery()->getOneOrNullResult();
    }

}
