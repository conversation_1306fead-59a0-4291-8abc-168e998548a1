<?php

namespace AppBundle\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Illuminate\Support\Facades\Notification;

class NotificationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Notification::class);
    }

    public function findByKeyAndLocale(string $key, string $locale)
    {
        $query =
            $this->createQueryBuilder('n')
                ->where('n.key = :key and n.locale = :locale')
                ->setMaxResults(1)
                ->setParameter('key', $key)
                ->setParameter('locale', $locale);

        return $query->getQuery()->getOneOrNullResult();
    }
}