<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Node;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\Persistence\ManagerRegistry;
use Generator;

class NodeRepository extends ServiceEntityRepository
{
    const YM = 'Ym';

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Node::class);
    }

    public function findAllByType($type): Generator
    {
        $iterator = $this->createQueryBuilder("n")->where("n.type = :type")
            ->setParameter("type", $type)->getQuery()->iterate();
        foreach ($iterator as $row) {
            yield $row[0];
        }
    }

    /**
     * Find a content by its slug
     *
     * @param $slug
     *
     * @return mixed
     */
    public function findBySlug($slug)
    {
        $query =
            $this->createQueryBuilder('n')
                ->where('n.slug = :slug')
                ->setMaxResults(1)
                ->setParameter('slug', $slug);
        return $query->getQuery()->getOneOrNullResult();
    }

    /**
     * Find a content by its slug
     *
     * @param $slug
     *
     * @return mixed
     */
    public function findPublishedBySlug($slug)
    {
        $query =
            $this->createQueryBuilder('n')
                ->where('n.slug = :slug')
                ->andWhere("n.status = 'published'")
                ->setMaxResults(1)
                ->setParameter('slug', $slug);
        return $query->getQuery()->getOneOrNullResult();
    }

    /**
     * Find nodes by the type
     *
     * @param $node_type
     *
     * @return array
     */
    public function findByType($node_type)
    {
        $query =
            $this->createQueryBuilder('n')
                ->where('n.type = :type')
                ->setParameter('type', $node_type); // Use a constant Node::Status_Published or something

        return $query->getQuery()->getResult();
    }

    /**
     * find slides to display on the homepage
     * @return array
     */
    public function findSlidesHomepage()
    {
        $query =
            $this->createQueryBuilder('n')
                ->select('n')
                ->where('n.type = :type and n.status = :status')
                ->setParameter('type', 'slider')
                ->setParameter('status', Node::STATUS_PUBLISHED)
                ->orderBy('n.orderNode', 'ASC');
        return $query->getQuery()->getResult();
    }

    /**
     * Find nodes of type 'testimonial', ordered by date, filtered by max_age, only the max_items one
     *
     * @param $max_age
     * @param $max_items
     *
     * @return array
     */
    public function findTestimonialsByAgeAndMax($max_age, $max_items)
    {
        /* Get published + testimonials types Nodes */
        $query =
            $this->createQueryBuilder('n')
                ->leftJoin('n.testimonialDetails', 'd')
                ->where(' n.type = :type and n.status = :status and ( d.publishedDate >= DATE_SUB(CURRENT_DATE(), :max_age, \'MONTH\') )')
                ->setParameter('type', Node::TYPE_TESTIMONIAL)
                ->setParameter('status', Node::STATUS_PUBLISHED)
                ->setParameter('max_age', $max_age)
                ->orderBy('d.publishedDate', 'DESC')
                ->setMaxResults($max_items);

        return $query->getQuery()->getResult();
    }


    /**
     * find a node object that represents an email
     *
     * @param string $slug the identifier of the email
     * @param string $lang the language of the email
     *
     * @return mixed Node if found one result, null otherwise (if no result or more than one result)
     */
    public function findEmailBySlugAndLanguage($slug, $lang)
    {
        $qb = $this->createQueryBuilder('n');
        $qb->leftJoin('n.content', 'c')
            ->where('n.slug = :slug and c.lang = :lang and n.type = :type')
            ->setParameter('slug', $slug)
            ->setParameter('lang', $lang)
            ->setParameter('type', Node::TYPE_EMAIL);

        try {
            return $qb->getQuery()->getOneOrNullResult();
        } catch (NonUniqueResultException $e) {
            return null;
        }

    }

    public function findByIdAndLang(int $id, array $lang)
    {
        $qb = $this->createQueryBuilder('n');
        $qb->leftJoin('n.content', 'c')
            ->addSelect('c')
            ->where('n.id = :id and c.lang IN (:lang) AND n.type = :type')
            ->setParameter('id', $id)
            ->setParameter('lang', $lang)
            ->setParameter('type', Node::TYPE_EMAIL)
            ->orderBy('c.lang', 'DESC');

        try {
            return $qb->getQuery()->getOneOrNullResult();
        } catch (NonUniqueResultException $e) {
            return null;
        }

    }
}
