<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Country;
use AppBundle\Entity\TotalAddress;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Doctrine\Persistence\ManagerRegistry;
use Open\IzbergBundle\Model\Address;

class TotalAddressRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TotalAddress::class);
    }
    /**
     * @param TotalAddress $totalAddress
     * @return TotalAddress
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function save(TotalAddress $totalAddress): TotalAddress
    {
        $this->_em->persist($totalAddress);
        $this->_em->flush();

        return $totalAddress;
    }

    public function findTermMultiColumn(Country $country, string $term)
    {
        $qb =
            $this->createQueryBuilder('a');
        $qb->where("a.country = :country");
        $qb->andWhere("a.enable = true");
        $qb->andWhere(
            $qb->expr()->orX()->addMultiple([ // nested condition
                $qb->expr()->like("a.name", ":term"),
                $qb->expr()->like("a.address", ":term"),
                $qb->expr()->like("a.address2", ":term"),
                $qb->expr()->like("a.city", ":term"),
                $qb->expr()->like("a.zipCode", ":term")]
            )
        )
            ->setParameter("term", "%" . $term . "%")
            ->setParameter("country", $country->getId());

        return $qb->getQuery()->getResult();
    }

    public function findByIzbergAddress(Address $address)
    {
        //address are create with a header
        //Address2 = Address." ".Address2;
        //Address = AddressHeader." / ".invoiceEntityName;
        $qb =
            $this->createQueryBuilder('a');
        $qb->where('a.city =:city')
            ->andWhere('a.zipCode =:zipcode')
            ->andWhere('CONCAT(a.address, \' \', a.address2) =:address2')
            ->setParameter("city", $address->getCity())
            ->setParameter("zipcode", $address->getZipcode())
            ->setParameter("address2", $address->getAddress2());
        return $qb->getQuery()->getOneOrNullResult();
    }
}
