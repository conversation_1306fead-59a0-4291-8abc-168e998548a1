<?php

namespace AppBundle\Repository;

use AppB<PERSON>le\Entity\BuyerAddress;
use AppBundle\Entity\MarketPlace;
use AppBundle\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Doctrine\Persistence\ManagerRegistry;

class MarketPlaceRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, MarketPlace::class);
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function save(MarketPlace $marketPlace)
    {
        $this->_em->persist($marketPlace);
        $this->_em->flush($marketPlace);
    }

    public function saveWithoutFlush(MarketPlace $marketPlace): MarketPlace
    {
        $this->_em->persist($marketPlace);

        return $marketPlace;
    }

    public function getLastUsedMarketPlace(int $userId): array
    {

        $sub = $this->_em->createQueryBuilder()->select('max(m1.validatedAt)')
            ->from("AppBundle:BuyerAddress", "a1")
            ->join('a1.metaCart', 'm1')
            ->where("a1.is_active=true and a1.user = :userId")
            ->groupBy('a1.address')
            ->addGroupBy('a1.address2')
            ->addGroupBy('a1.zipCode')
            ->addGroupBy('a1.city')
            ->addGroupBy('a1.country');
        $qb =
            $this->createQueryBuilder('a')
                ->select('a')
                ->join('a.metaCart', 'm');
        $qb
            ->where("a.is_active=true and a.user = :userId")
            ->andWhere("m.status= 'ORDERED'")
            ->andWhere($qb->expr()->in('m.validatedAt', $sub->getDQL()))
            ->setParameter('userId', $userId)
            ->orderBy("m.validatedAt", "desc");

        return $qb->getQuery()->getResult();
    }

    /**
     * @param int $userId
     *
     * @return BuyerAddress|null
     * @throws NonUniqueResultException
     */
    public function getLastUsedAddress(int $userId): ?BuyerAddress
    {
        $sub = $this->_em->createQueryBuilder()->select('max(m1.orderCreatedAt)')
            ->from("AppBundle:BuyerAddress", "a1")
            ->join('a1.metaCart', 'm1')
            ->where("a1.is_active=true and a1.user = :userId");

        $qb =
            $this->createQueryBuilder('a')
                ->select('a')
                ->join('a.metaCart', 'm');
        $qb
            ->where("a.is_active=true and a.user = :userId")
            ->andWhere($qb->expr()->in('m.orderCreatedAt', $sub->getDQL()))
            ->setParameter('userId', $userId)
            ->orderBy("m.orderCreatedAt", "desc");

        return $qb->getQuery()->getOneOrNullResult();
    }

    public function findTermMultiColumn(User $user, string $term)
    {
        $qb =
            $this->createQueryBuilder('a');
        $qb->where("a.country = :country");
        $qb->andWhere("a.type = :type");
        $qb->andWhere("a.is_active = true");
        $qb->andWhere("a.user = :user");
        $qb->andWhere($qb->expr()->orX()->addMultiple([ // nested condition
            $qb->expr()->like("a.name", ":term"),
            $qb->expr()->like("a.address", ":term"),
            $qb->expr()->like("a.address2", ":term"),
            $qb->expr()->like("a.city", ":term"),
            $qb->expr()->like("a.zipCode", ":term")]
        ))
            ->setParameter("term", "%" . $term . "%")
            ->setParameter("country", $user->getCountryOfDelivery())
            ->setParameter("type", BuyerAddress::TYPE_PERSONAL)
            ->setParameter("user", $user->getId());

        return $qb->getQuery()->getResult();
    }

    public function resetBuyerAddressIzbergId(User $user)
    {

        $queryBuilder = $this->createQueryBuilder('b');
        $query = $queryBuilder->update('AppBundle\Entity\BuyerAddress', 'b')
            ->set('b.izbergAddressId', ':value')
            ->where('b.user = :userId')
            ->setParameter('value', null)
            ->setParameter('userId', $user->getId())
            ->getQuery();
        $result = $query->execute();
    }
}
