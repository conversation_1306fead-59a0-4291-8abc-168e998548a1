<?php

namespace AppBundle\Repository;

use AppBundle\Entity\InvoiceEntity;
use AppBundle\Entity\Site;
use AppBundle\Entity\UserEnabler;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\EntityManager;
use Doctrine\Persistence\ManagerRegistry;

class UserEnablerRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, UserEnabler::class);
    }

    public function getEntityManager(): EntityManager
    {
        return $this->_em;
    }

    public function save(UserEnabler $userEnabler)
    {
        $this->_em->persist($userEnabler);
        $this->_em->flush();
    }

    public function saveWithoutFlush(UserEnabler $userEnabler)
    {
        $this->_em->persist($userEnabler);
    }

    public function findEnabler(InvoiceEntity $invoiceEntity, Site $site, ?string $userType): ?UserEnabler
    {
        return $this->findOneBy(['entity' => $invoiceEntity, 'site' => $site, 'userType' => $userType]);
    }

}
