<?php

namespace AppBundle\Repository;

use AppBundle\Entity\CreditNote;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Doctrine\Persistence\ManagerRegistry;

class CreditNoteRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CreditNote::class);
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function save(CreditNote $creditNote)
    {
        $this->_em->persist($creditNote);
        $this->_em->flush($creditNote);
    }
}
