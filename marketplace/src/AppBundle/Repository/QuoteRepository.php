<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Quote;
use AppBundle\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class QuoteRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Quote::class);
    }

    public function countUnreadBuyerMessage(User $buyer)
    {
        $qb = $this->getEntityManager()->createQueryBuilder();
        return $qb->select("COUNT(q.buyerUnread) as nb, q.status")
            ->from('AppBundle:Quote', 'q')
            ->where('q.buyer = :buyer')
            ->andWhere('q.buyerUnread = true')
            ->groupBy('q.status')
            ->setParameter('buyer', $buyer->getId())
            ->getQuery()
            ->getScalarResult();
    }

    /**
     * Count total unread message
     *
     * @param User $buyer
     *
     * @return array
     */
    public function countTotalUnreadBuyerMessage(User $buyer)
    {
        $qb = $this->getEntityManager()->createQueryBuilder();
        return $qb->select("COUNT(q.buyerUnread) as nb")
            ->from('AppBundle:Quote', 'q')
            ->where('q.buyer = :buyer')
            ->andWhere('q.buyerUnread = true')
            ->setParameter('buyer', $buyer->getId())
            ->getQuery()
            ->getScalarResult();
    }

    public function countUnreadVendorMessage($merchantId)
    {
        $qb = $this->getEntityManager()->createQueryBuilder();
        return $qb->select("COUNT(q.vendorUnread) as nb, q.status")
            ->from('AppBundle:Quote', 'q')
            ->where('q.vendor = :merchantId')
            ->andWhere('q.vendorUnread = true')
            ->groupBy('q.status')
            ->setParameter('merchantId', $merchantId)
            ->getQuery()
            ->getScalarResult();
    }

    public function countTotalVendorMessage($merchantId)
    {
        $qb = $this->getEntityManager()->createQueryBuilder();
        return $qb->select("COUNT(q.vendorUnread) as nb")
            ->from('AppBundle:Quote', 'q')
            ->where('q.vendor = :merchantId')
            ->andWhere('q.vendorUnread = true')
            ->setParameter('merchantId', $merchantId)
            ->getQuery()
            ->getScalarResult();
    }

    public function countQuoteNumberByVendor($quoteNumber, $merchantId)
    {
        $qb = $this->getEntityManager()->createQueryBuilder();
        return $qb->select("COUNT(q) as nb")
            ->from('AppBundle:Quote', 'q')
            ->where('q.vendor = :merchantId and q.quoteNumber = :quoteNumber')
            ->setParameter('merchantId', $merchantId)
            ->setParameter('quoteNumber', $quoteNumber)
            ->getQuery()
            ->getScalarResult();
    }
}