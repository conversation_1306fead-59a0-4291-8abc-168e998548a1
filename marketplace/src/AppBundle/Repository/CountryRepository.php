<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Country;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class CountryRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Country::class);
    }

    public function findByCode(string $code): ?Country
    {
        return $this->findOneBy(['code' => $code]);
    }

    public function findAvailableCountryOfDelivery(): array
    {
        return $this->findBy(['buyer' => true]);
    }
}
