<?php

namespace AppBundle\Repository;

use AppBundle\Entity\OrderItem;
use AppBundle\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Doctrine\Persistence\ManagerRegistry;

class OrderItemRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, OrderItem::class);
    }

    /**
     * @param int $orderItemId
     *
     * @return OrderItem|null
     */
    public function getOrderItemById(int $orderItemId): ?OrderItem
    {
        return $this->find($orderItemId);
    }

    /**
     * @param OrderItem $orderItem
     *
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function saveOrderItem(OrderItem $orderItem): void
    {
        $this->_em->persist($orderItem);
        $this->_em->flush();
    }

    public function findLastOrderedItem(string $username, int $limit)
    {
        $qb = $this->getEntityManager()->createQueryBuilder()
            ->select("o")
            ->from("AppBundle:OrderItem", "o")
            ->where("o.id <> 0 and o.beneficiary = :username")
            ->orderBy("o.orderDate", "desc")
            ->setParameter("username", $username)
            ->setMaxResults($limit);


        return $qb->getQuery()->getResult();
    }

    public function findLastOrderedItemExceptUser(User $user, int $limit): array
    {
        $qb = $this->getEntityManager()->createQueryBuilder()
            ->select("o")
            ->from("AppBundle:OrderItem", "o")
            ->where("o.beneficiary != :username")
            ->andWhere('o.offerId IS NOT NULL')
            ->orderBy("o.orderDate", "desc")
            ->setParameter("username", $user->getUsername())
            ->setMaxResults($limit);

        return $qb->getQuery()->getResult();
    }
}
