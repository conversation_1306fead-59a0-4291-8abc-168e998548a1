<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Country;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class CityRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Country::class);
    }

    public function findByCityLike($term, $country)
    {
        return $this->createQueryBuilder('z')
            ->select('distinct z.city, z.label, z.zipcode')
            ->where('z.label LIKE :label and z.country =:country')
            ->setParameter('label', "%" . $term . '%')
            ->setParameter('country', strtolower($country))
            ->getQuery()
            ->getResult();

    }

    public function findByCity($city)
    {
        return $this->createQueryBuilder('z')
            ->select('distinct z.city, z.label, z.zipcode')
            ->where('z.label LIKE :label OR z.city =:city')
            ->setParameter('label', "%" . $city . '%')
            ->setParameter('city', "%" . $city . '%')
            ->getQuery()
            ->getResult();
    }

    public function deleteCountry($country)
    {
        return $this->createQueryBuilder('z')
            ->delete()
            ->where('z.country LIKE :country')
            ->setParameter('country', $country)
            ->getQuery()
            ->getResult();
    }
}