<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Node;
use AppBundle\Entity\NodeContent;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\Persistence\ManagerRegistry;

class NodeContentRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, NodeContent::class);
    }

    public function findPageBySlugAndLanguage($slug, $lang)
    {
        $qb = $this->createQueryBuilder('c');
        $qb->leftJoin('c.node', 'n')
            ->where('n.slug = :slug and c.lang = :lang and n.type = :type')
            ->setParameter('slug', $slug)
            ->setParameter('lang', $lang)
            ->setParameter('type', Node::TYPE_PAGE);

        try {
            return $qb->getQuery()->getOneOrNullResult();
        } catch (NonUniqueResultException $e) {
            return null;
        }

    }
}