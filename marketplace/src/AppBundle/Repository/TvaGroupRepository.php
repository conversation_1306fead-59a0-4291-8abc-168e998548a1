<?php

namespace AppBundle\Repository;

use AppBundle\Entity\TvaGroup;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class TvaGroupRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TvaGroup::class);
    }

    public function findByGroupName($groupName)
    {
        $qb = $this->getEntityManager()->createQueryBuilder()
            ->select('t')
            ->from('AppBundle:TvaGroup', 't')
            ->where("t.groupName = :groupName")
            ->setParameter('groupName', $groupName);
        return $qb->getQuery()->getOneOrNullResult();
    }
}