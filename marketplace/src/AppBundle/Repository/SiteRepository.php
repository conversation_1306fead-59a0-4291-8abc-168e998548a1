<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Site;
use AppBundle\Util\TransportService;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\EntityManager;
use Doctrine\Persistence\ManagerRegistry;


class SiteRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Site::class);
    }


    public function getEntityManager(): EntityManager
    {
        return $this->_em;
    }

    public function save(Site $site)
    {
        $this->_em->persist($site);
        $this->_em->flush();
    }

    public function saveWithoutFlush(Site $site)
    {
        $this->_em->persist($site);

    }

    public function findByName(?string $name): ?Site
    {
        return $this->findOneBy(['name' => $name]);
    }


}
