<?php

namespace AppBundle\Repository;

use AppBundle\Entity\DistantUser;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Generator;

class DistantUserRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, DistantUser::class);
    }

    public function fetchAll(): Generator
    {

        $offset = 0;
        $limit = 100;

        do {
            $distantUsers = $this->findBy([], null, $limit, $offset);
            foreach ($distantUsers as $distantUser) {
                yield $distantUser;
            }
            $offset = $offset + $limit;
        } while (count($distantUsers) == $limit);

    }

    public function clearAll(): void
    {
        $this->createQueryBuilder('u')
            ->delete()
            ->getQuery()
            ->execute();
    }


}