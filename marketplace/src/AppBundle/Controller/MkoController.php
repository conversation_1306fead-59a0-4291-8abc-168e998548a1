<?php

namespace AppBundle\Controller;

use AppBundle\Entity\Company;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Contracts\Translation\TranslatorInterface;

class Mko<PERSON>ontroller extends AbstractController
{
    const ROLE_BUYER = "ROLE_BUYER";

    const ROLE_OPERATOR = 'ROLE_OPERATOR';
    const ROLE_SUPER_ADMIN = 'ROLE_SUPER_ADMIN';

    const TRANSLATOR = 'translator';
    const TRANSLATION_DOMAIN = 'AppBundle';

    const NOT_FOUND_EXCEPTION = 'exception.controller.not_found';

    const ACCESS_DENIED_EXCEPTION = 'exception.controller.access_denied';
    const ACCESS_DENIED_NOT_DRAFT_EXCEPTION = 'exception.controller.access_denied_draft';
    const ACTION_DENIED_EXCEPTION = 'exception.controller.action_denied';
    const ACCESS_DENIED_COMPANY_NOT_VALID = "exception.controller.access_denied_company_not_valid";

    const VALIDATION_GROUPS = 'validation_groups';

    protected const SERVICE_SETTINGS_PROVIDER = 'system.settings_provider';
    protected const SERVICE_OFFER = 'service.offer';
    protected const SERVICE_CART = 'service.cart';
    protected const SERVICE_CATEGORIES = 'izberg.service.categories';
    protected const SESSION = 'session';

    const SESSION_DEPARTMENT_SEARCH = 'front.search.department_type';
    const SESSION_ACTIVE_SEARCH_TYPE = 'front.search.active_search_type';
    const SESSION_TEXT_SEARCH = 'front.search.text';
    const SESSION_FILTER_SEARCH = 'front.search.filter';
    const SESSION_ACCOUNT_CREATION = 'front.account.creation';


    /**
     * get the identifier of the current user
     */
    protected function getUsername(): string
    {
        return ($this->getUser()) ? $this->getUser()->getUsername() : 'anonymous';
    }

    public static function getSubscribedServices(): array
    {
        $services = parent::getSubscribedServices();
        $services['translator'] = TranslatorInterface::class;

        return $services;
    }
}
