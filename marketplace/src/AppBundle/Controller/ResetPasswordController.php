<?php

namespace AppBundle\Controller;

use AppBundle\Entity\User;
use AppBundle\Form\NewPasswordForm;
use AppBundle\Services\MailService;
use AppBundle\Services\SecurityService;
use AppBundle\Services\UserBddService;
use Open\FrontBundle\Form\EmailForm;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

class ResetPasswordController extends AbstractController
{
    /**
     * @Route(path="/profile/password/reset", name="send.user.password.reset")
     * @param Request $request
     * @param UserBddService $userBddService
     * @return Response
     */
    public function sendResetPasswordAction(Request $request, UserBddService $userBddService, MailService $mailService): Response
    {
        $form = $this->createForm(EmailForm::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $user = $userBddService->findUserByUsernameOrEmail($data['email']);
            if (!$user instanceof User) {
                throw new \InvalidArgumentException();
            }
            $userBddService->setNewTokenPassword($user);
            $mailService->sendResettingEmailMessage($user);

            return $this->render('@App/security/check_email.html.twig');
        }

        return $this->render(
            '@OpenFront/security/reset_password.html.twig',
            [
                'form' => $form->createView(),
            ]
        );
    }


    /**
     * @Route(path="/password/reset/{id}", name="send.mail.reset.password")
     */
    public function sendEmailResetPasswordAction(User $user, UserBddService $userBddService, MailService $mailService, TranslatorInterface $translator)
    {
        try {
            $userBddService->setNewTokenPassword($user);
            $mailService->sendResettingEmailMessage($user);
            $this->addFlash('success', $translator->trans('back.user.form.resetingPassword.ok', [], 'AppBundle'));
        } catch (\Exception $e) {
            $this->addFlash('error', $translator->trans('back.user.form.resetingPassword.ko', [], 'AppBundle'));
        }

        return $this->redirectToRoute('admin.user.info', ['id' => $user->getId()]);
    }

    /**
     * @Route(path="/profile/password/reset/{token}", name="user.password.reset")
     *
     * @param string $token
     * @param Request $request
     * @param UserBddService $userBddService
     * @param SecurityService $securityService
     *
     * @return Response
     */
    public function resetPasswordAction(string $token, Request $request, UserBddService $userBddService, SecurityService $securityService): Response
    {
        //Todo => Security check token validity ?
        /*if (!$securityService->checkPasswordTokenValidity($token)) {
            return $this->render('AppBundle:security:invalid-token-password.html.twig');
        }*/

        $form = $this->createForm(NewPasswordForm::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $user = $userBddService->findUserByPasswordToken($token);
            $securityService->reinitUserPassword($user, $data['password']);

            return $this->redirectToRoute('admin.login');
        }

        return $this->render(
            '@App/security/reset_password_content.html.twig',
            [
                'form' => $form->createView(),
            ]
        );
    }
}
