<?php

namespace AppBundle\Controller;

use AppBundle\Repository\ImageRepository;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;


class ImageController extends MkoController
{
    /**
     * @Route("/image/{id}",name="front.get.image")
     */
    public function getImageById(ImageRepository $imageRepository, $id): Response
    {
        $img = $imageRepository->getImgById($id);
        $file = stream_get_contents($img->getBlob());

        return new Response($file, 200, [
            'Content-Type' => $img->getMime(),
            'Content-Disposition' => 'inline; filename="' . $img->getName() . '"'
        ]);
    }
}
