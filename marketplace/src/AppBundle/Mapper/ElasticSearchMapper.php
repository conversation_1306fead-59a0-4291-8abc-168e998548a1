<?php

namespace AppBundle\Mapper;

use AppBundle\Entity\Favori;
use AppBundle\Entity\User;
use AppBundle\Model\BestOffer;
use AppBundle\Model\DetailedPrice;
use AppBundle\Model\Merchant;
use AppBundle\Model\Offer;
use AppBundle\Model\OfferSearchResult;
use AppBundle\Model\Price;
use AppBundle\Model\Product;
use AppBundle\Model\Reduction;
use AppBundle\Model\SearchResult;
use AppBundle\Model\TechnicalDetails;
use AppBundle\Model\TechnicalProperty;
use AppBundle\Repository\FavoriRepository;
use AppBundle\Services\CustomsService;
use AppBundle\Services\IzbergCustomAttributes;
use Open\IzbergBundle\Dto\AttributeDTO;
use Open\IzbergBundle\Service\AttributeService;

class ElasticSearchMapper implements OfferMapper
{
    private IzbergCustomAttributes $izbergCustomAttributes;
    private AttributeService $attributeService;
    private int $stockMinimum;
    private FavoriRepository $favoriRepository;
    private CustomsService $customsService;

    public function __construct(IzbergCustomAttributes $izbergCustomAttributes, AttributeService $attributeService, FavoriRepository $favoriRepository, CustomsService $customsService)
    {
        $this->izbergCustomAttributes = $izbergCustomAttributes;
        $this->attributeService = $attributeService;
        $this->stockMinimum = 0;
        $this->favoriRepository = $favoriRepository;
        $this->customsService = $customsService;
    }

    /**
     * Setter use when initializing the service
     *
     * @param int $stockMinimum
     * @return $this
     */
    public function setStockMinimum(int $stockMinimum): self
    {
        $this->stockMinimum = $stockMinimum;
        return $this;
    }

    /**
     * Indexer result to SearchResult.
     *
     * @param SearchResult $indexerResult
     * @param string $locale
     *
     * @return OfferSearchResult
     */
    public function indexerResultToSearchResult(SearchResult $indexerResult, string $locale, ?User $buyer = null): OfferSearchResult
    {
        $result = new OfferSearchResult();
        $result->setNbHits($indexerResult->getNbHits());
        $result->setSize($indexerResult->getSize());
        $result->setNbPages($indexerResult->getNbPages());
        $result->setFacets($indexerResult->getFacets());
        $result->setScrollId($indexerResult->getScrollId());

        $offers = [];
        if ($buyer) {
            $favois = $this->favoriRepository->findByBuyer($buyer);
            $favOffers = array_map(
                function (Favori $favori) {
                    return $favori->getOfferId();
                },
                $favois
            );
        } else {
            $favOffers = [];
        }

        foreach ($indexerResult->getHits() as $elasticSearchOffer) {
            $offers[] = $this->indexerOfferToTotalOffer($elasticSearchOffer, $locale, $favOffers);
        }

        $result->setOffers($offers);
        return $result;
    }

    /**
     * build a request to be sent to the indexer to add DetailedPrice to an offer
     * @param int $offerId the identifier of the offer
     * @param string $index
     * @param DetailedPrice $detailedPrice the detailed prices
     * @return array the indexer request
     */
    public function buildIndexRequestForDetailedPrices(int $offerId, string $index, DetailedPrice $detailedPrice): array
    {
        $request = [];
        $request["index"] = $index;
        $request["id"] = $offerId;
        $request["type"] = "_doc";
        $request["body"]["doc"]["detailed_prices"]["created_at"] = $detailedPrice->getCreatedAt();
        /** @var Price $price */
        foreach ($detailedPrice->getPrices() as $price){
            $request["body"]["doc"]["detailed_prices"]["prices"][$price->getCurrency()] = ["value" => $price->getPrice(), "offer_currency" => $price->isOfferCurrency()];
        }

        return $request;
    }


    public function buildIndexRequestForUpdatingPrice (int $offerId, string $index, Offer $offer): array
    {

        $request = [];
        $request["index"] = $index;
        $request["id"] = $offerId;
        $request["type"] = "_doc";
        $request["body"]["doc"]["country_price"] = $offer->getCountryPrice();
        $request["body"]["doc"]["country_currency"] = $offer->getCountryCurrency();

        if ($offer->getCountryThresholdPrice1()){
            $request["body"]["doc"]["country_threshold_price1"] = $offer->getCountryThresholdPrice1();
        }
        if ($offer->getCountryThresholdPrice2()){
            $request["body"]["doc"]["country_threshold_price2"] = $offer->getCountryThresholdPrice2();
        }
        if ($offer->getCountryThresholdPrice3()){
            $request["body"]["doc"]["country_threshold_price3"] = $offer->getCountryThresholdPrice3();
        }
        if ($offer->getCountryThresholdPrice4()){
            $request["body"]["doc"]["country_threshold_price4"] = $offer->getCountryThresholdPrice4();
        }

        return $request;

    }

    public function buildIndexRequestForMerchant(int $offerId, string $index, Merchant $merchant): array
    {
        $request = [];
        $request["index"] = $index;
        $request["id"] = $offerId;
        $request["type"] = "_doc";
        $request["body"]["doc"]["merchant"]["name"] = $merchant->getName();
        $request["body"]["doc"]["merchant"]["rating"] = $merchant->getRating();
        $request["body"]["doc"]["merchant"]["adapted_company"] = $merchant->isAdaptedCompany();
        $request["body"]["doc"]["merchant"]["branches"] = $merchant->getBranches();
        $request["body"]["doc"]["merchant"]["status"] = $merchant->getStatus();
//        $request["body"]["doc"]["merchant"]["contrat_cadre"] = $merchant->hasContratCadre();

        return $request;
    }

    /**
     * @inheritdoc
     */
    public function buildIndexRequestForBestOffer(int $offerId, string $index, BestOffer $bestOffer): array{
        $request = [];
        $request["index"] = $index;
        $request["id"] = $offerId;
        $request["type"] = "_doc";
        $request["body"]["doc"]["best_offer"]["created_at"] = $bestOffer->getCreatedAt();
        $request["body"]["doc"]["best_offer"]["offer_id"] = $bestOffer->getOfferId();
        $request["body"]["doc"]["best_offer"]["price"] = $bestOffer->getPrice();
        $request["body"]["doc"]["best_offer"]["currency"] = $bestOffer->getCurrency();

        return $request;

    }

    /**
     * ElasticSearch offer to total offer.
     *
     * @param array $elasticSearchOffer
     * @param $locale
     *
     * @return Offer
     */
    public function indexerOfferToTotalOffer(?array $indexerOffer, string $locale, array $favOffers): ?Offer
    {
        if(!$indexerOffer) {
            return null;
        }
        $elasticSearchOffer = $indexerOffer;

        $convertToBool = function(string $value) {
            $return = false;

            if (in_array(strtolower(trim($value)), ['yes', 'oui'])) {
                return true;
            }

            return $return;
        };

        $dataSheetUrl =[];
        $dataSheetUrl[] = $this->getExistsStringAttributeOrNull($elasticSearchOffer["_source"]["attributes"],$this->izbergCustomAttributes->getDataSheet());
        $dataSheetUrl[] = $this->getExistsStringAttributeOrNull($elasticSearchOffer["_source"]["attributes"],$this->izbergCustomAttributes->getAttachedSheet());
        $result = new Offer();
        $deliveryRestrictionDescription = array_key_exists($this->izbergCustomAttributes->getZoneRestrictionDescription(),$elasticSearchOffer["_source"]["attributes"]) ?
        $elasticSearchOffer["_source"]["attributes"][$this->izbergCustomAttributes->getZoneRestrictionDescription()] : null;
        $deliveryRestrictionDescription = is_array($deliveryRestrictionDescription) ? array_shift($deliveryRestrictionDescription) : $deliveryRestrictionDescription;
        $result
            ->setId($elasticSearchOffer["_id"])
            ->setScore($elasticSearchOffer["_score"])
            ->setName($elasticSearchOffer["_source"]["name"] ?? "")
            ->setDescription($elasticSearchOffer["_source"]["description"] ?? "")
            ->setPicture($elasticSearchOffer["_source"]["default_image"] ?? null)
            ->setPrice($elasticSearchOffer["_source"]["price_without_vat"] ?? null)
            ->setPreviousPrice($elasticSearchOffer["_source"]["previous_price_without_vat"] ?? null)
            ->setCurrency($elasticSearchOffer["_source"]["currency"] ?? '')
            ->setTaxRate($elasticSearchOffer["_source"]["attributes"][$this->izbergCustomAttributes->getVatRate()] ?? null)
            ->setStock($elasticSearchOffer["_source"]["stock"] ?? null)
            ->setStockMinimum($this->stockMinimum)
            ->setStockManagement(call_user_func($convertToBool,
                    $elasticSearchOffer["_source"]["attributes"][$this->izbergCustomAttributes->getStockManagement()] ?? ''
                )
            )
            ->setAvailability($elasticSearchOffer["_source"]["attributes"][$this->izbergCustomAttributes->getStockAvailability()] ?? null)
            ->setIsNew($elasticSearchOffer["_source"]["isnew"] ?? null)
            ->setExclusive($elasticSearchOffer["_source"]["exclusive"] ?? null)
            ->setStatus($elasticSearchOffer["_source"]["status"] ?? null)
            ->setCreatedOn($elasticSearchOffer["_source"]["created_on"] ?? null)
            ->setEndSellingDate($elasticSearchOffer["_source"]["end_selling_date"] ?? null)
            ->setDeliveryTime($elasticSearchOffer["_source"]["delivery_time"] ?? null)
            ->setNumberOfUnits($elasticSearchOffer["_source"]["number_of_units"] ?? null)
            ->setMerchant($this->elasticSearchMerchantToMerchant($elasticSearchOffer["_source"]["merchant"] ?? null))
            ->setProduct($this->elasticSearchProductToProduct($elasticSearchOffer["_source"]["product"] ?? null) )
            ->setDetailedPrices($this->elasticSearchDetailedPricesToDetailedPrice($elasticSearchOffer["_source"]["detailed_prices"] ?? null))
            ->setReductions($this->elasticSearchAttributesToReductions($elasticSearchOffer["_source"]["attributes"] ?? [], $elasticSearchOffer["_source"]["price_without_vat"] ?? null))
            ->setTechnicalDetails($this->elasticSearchAttributesToTechnicalDetails($elasticSearchOffer["_source"]["attributes"] ?? [], $locale))
            ->setSku($elasticSearchOffer["_source"]["sku"] ?? null)
            ->setAssociatedPictures(
                array_filter(array_map(
                    function(array $assignedImage) {
                        return $assignedImage['image_path'] ?? null;
                    },
                    $elasticSearchOffer["_source"]['assigned_images'] ?? []
                ))
            )
            ->setMoq($this->elasticSearchAttributesToMoq($elasticSearchOffer["_source"]["attributes"] ?? []))
            ->setBatchSize($elasticSearchOffer["_source"]["attributes"][$this->izbergCustomAttributes->getBatchSize()] ?? 1)
            ->setDeliveryRestricted($elasticSearchOffer["_source"]["attributes"][$this->izbergCustomAttributes->getZoneRestriction()] ?? false)
            ->setDeliveryRestrictionDescription($deliveryRestrictionDescription ?? null)
            ->setCountryOfDelivery($elasticSearchOffer["_source"]["attributes"][$this->izbergCustomAttributes->getCountryOfDelivery()] ?? null)
            ->setBestOffer($this->indexBestOfferToTotalBestOffer($elasticSearchOffer["_source"]["best_offer"] ?? null))

            ->setThreshold1($this->getExistsIntAttributeOrNull($elasticSearchOffer["_source"]["attributes"], $this->izbergCustomAttributes->getThreshold1()))
            ->setThreshold2($this->getExistsIntAttributeOrNull($elasticSearchOffer["_source"]["attributes"], $this->izbergCustomAttributes->getThreshold2()))
            ->setThreshold3($this->getExistsIntAttributeOrNull($elasticSearchOffer["_source"]["attributes"], $this->izbergCustomAttributes->getThreshold3()))
            ->setThreshold4($this->getExistsIntAttributeOrNull($elasticSearchOffer["_source"]["attributes"], $this->izbergCustomAttributes->getThreshold4()))

            ->setThresholdPrice1($this->getExistsFloatAttributeOrNull($elasticSearchOffer["_source"]["attributes"],$this->izbergCustomAttributes->getThreshold1Price()))
            ->setThresholdPrice2($this->getExistsFloatAttributeOrNull($elasticSearchOffer["_source"]["attributes"],$this->izbergCustomAttributes->getThreshold2Price()))
            ->setThresholdPrice3($this->getExistsFloatAttributeOrNull($elasticSearchOffer["_source"]["attributes"],$this->izbergCustomAttributes->getThreshold3Price()))
            ->setThresholdPrice4($this->getExistsFloatAttributeOrNull($elasticSearchOffer["_source"]["attributes"],$this->izbergCustomAttributes->getThreshold4Price()))

            ->setCountryPrice($elasticSearchOffer["_source"]["country_price"] ?? null)
            ->setCountryCurrency($elasticSearchOffer["_source"]["country_currency"] ?? null)
            ->setCountryThresholdPrice1($elasticSearchOffer["_source"]["country_threshold_price1"] ?? null)
            ->setCountryThresholdPrice2($elasticSearchOffer["_source"]["country_threshold_price2"] ?? null)
            ->setCountryThresholdPrice3($elasticSearchOffer["_source"]["country_threshold_price3"] ?? null)
            ->setCountryThresholdPrice4($elasticSearchOffer["_source"]["country_threshold_price4"] ?? null)
            ->setDataSheetUrls($dataSheetUrl)
            ->setLanguage($elasticSearchOffer["_source"]["language"] ?? ($locale ?: 'fr'))
            ->setIsFavorit(in_array($elasticSearchOffer["_id"], $favOffers));

        $this->setQuoteInfo($result,$elasticSearchOffer );
        $result->setRisk($this->customsService->categoriesIdsInRisk($result->getProduct()->getCategoriesIds()));
        return $result;
    }

    private function setQuoteInfo (Offer $result, ?array $elasticSearchOffer){

        $result->setPriceGivenOnQuotation($elasticSearchOffer["_source"]["attributes"][$this->izbergCustomAttributes->getPriceOnQuotation()] ?? false);
        if($result->isPriceGivenOnQuotation()){
            if(!isset($elasticSearchOffer["_source"]["attributes"][$this->izbergCustomAttributes->getQuoteSecondaryOffer()])){
                $result->setOriginQuoteOffer(true);
            }else{
                $result->setSecondaryQuoteOffer($elasticSearchOffer["_source"]["attributes"][$this->izbergCustomAttributes->getQuoteSecondaryOffer()] ?? false);
                if(!$result->isSecondaryQuoteOffer()){
                    $result->setPrimaryQuoteOffer(true);
                }
            }
        }
    }

    /**
     * @param array $attributes
     * @param string $attributeName
     * @return string|null
     */
    private function getExistsStringAttributeOrNull(array $attributes, string $attributeName): ?string
    {
        if (!array_key_exists($attributeName, $attributes)) {
            return null;
        }
        $stringAttribute = is_array($attributes[$attributeName]) ? array_shift($attributes[$attributeName]) : $attributes[$attributeName];
        return empty($stringAttribute) ? null : (string)$stringAttribute;
    }

    private function indexBestOfferToTotalBestOffer(?array $esBestOffer): ?BestOffer{
        if ($esBestOffer) {
            $bestOffer = new BestOffer();
            $bestOffer->setCurrency($esBestOffer['currency']);
            $bestOffer->setPrice($esBestOffer['price']);
            $bestOffer->setOfferId($esBestOffer['offer_id']);
            return $bestOffer;
        }
        return null;
    }

    private function getExistsIntAttributeOrNull(array $attributes, string $attributeName): ?int
    {
        if(empty($attributes[$attributeName])) {
            return null;
        }

        return (int)$attributes[$attributeName];
    }
    private function getExistsFloatAttributeOrNull(array $attributes, string $attributeName): ?float
    {
        if(empty($attributes[$attributeName])) {
            return null;
        }

        return (float)$attributes[$attributeName];
    }

    public function indexerOffersToTotalOffers(array $indexerOffers, string $locale, User $buyer): array
    {
        $favois = $this->favoriRepository->findByBuyer($buyer);
        $favOffers = array_map(
          function (Favori $favori) {
              return $favori->getOfferId();
          },
          $favois
        );
        return array_map(
            function(array $elasticSearchOffer) use ($locale, $favOffers){
                return $this->indexerOfferToTotalOffer($elasticSearchOffer, $locale, $favOffers);
            },
            $indexerOffers
        );
    }


    /////////////////////////////////////////////////////////////////////////
    // Function to work with fieldNames
    /////////////////////////////////////////////////////////////////////////

    /**
     * @inheritdoc
     */
    public function getGTINFieldName(): string{
        return "product.gtin";
    }

    /**
     * @inheritdoc
     */
    public function getCountryOfDeliveryFieldName(): string{
        return "attributes.".$this->izbergCustomAttributes->getCountryOfDelivery();
    }

    /**
     * @inheritdoc
     */
    public function getPriceFieldName(): string{
        return "price";
    }

    /**
     * @inheritDoc
     */
    public function getCountryPriceFieldName(): string
    {
        return "country_price";
    }

    public function getScoreFieldName(): string
    {
        return "_score";
    }


    /**
     * build a detailed price from the result from the elastic search
     * @param array|null $elasticDetailedPrice the elastic search detailed price
     * @return DetailedPrice
     */
    private function elasticSearchDetailedPricesToDetailedPrice(?array $elasticDetailedPrice): DetailedPrice
    {
        $detailedPrice = new DetailedPrice();
        if ($elasticDetailedPrice !== null){
            $detailedPrice->setCreatedAt(\DateTime::createFromFormat('Y-m-d H:i:s+',
                $elasticDetailedPrice["created_at"]["date"],
                new \DateTimeZone($elasticDetailedPrice["created_at"]["timezone"])));

            foreach ($elasticDetailedPrice["prices"] as $currency => $value){
                $detailedPrice->add($currency, $value["value"], $value["offer_currency"]);
            }
        }

        return $detailedPrice;
    }

    private function elasticSearchAttributesToTechnicalDetails(?array $elasticAttributes, string $locale): TechnicalDetails
    {
        $technicalDetails = new TechnicalDetails();
        $technicalDetails->setDescription($elasticAttributes[$this->izbergCustomAttributes->getTechnicalCharacteristics()] ?? "");

        $allCustomAttributesRegistered = $this->izbergCustomAttributes->fetchAllAttributes();
        $ignoredAttributes = $this->izbergCustomAttributes->fetchIgnoredAttributes();

        $elasticAttributesKeys = array_keys($elasticAttributes);
        $matchAttributes = array_intersect($allCustomAttributesRegistered, $elasticAttributesKeys, $ignoredAttributes);
        $technicalAttributes = array_diff($elasticAttributesKeys, $matchAttributes);

        foreach ($technicalAttributes as $technicalAttribute) {
            $attribute = $this->attributeService->getAttribute($technicalAttribute, $locale);
            $value = $elasticAttributes[$technicalAttribute];

            if (is_string($value)) {
                $value = trim($value);
            }

            if (
                in_array(
                    $attribute->getType(),
                    [AttributeDTO::TYPE_BOOL, AttributeDTO::TYPE_STRING, AttributeDTO::TYPE_TEXT]
                )
                && empty($value)
            ) {
                break;
            }

            if($attribute->getKey() !== $this->izbergCustomAttributes->getTechnicalCharacteristics()) {
                $technicalDetails->addTechnicalProperty(
                    (new TechnicalProperty())
                        ->setLabel($attribute->getLabel())
                        ->setValue(
                            call_user_func(
                                function ($attributeValue) use ($attribute) {
                                    if (is_array($attributeValue)) {
                                        $attributeValue = implode(', ', $attributeValue);
                                    }

                                    if ($attribute->getType() === AttributeDTO::TYPE_BOOL) {
                                        return 'oui';
                                    }

                                    return $attributeValue;
                                },
                                $value
                            )
                        )
                );
            }
        }

        return $technicalDetails;
    }

    private function elasticSearchAttributesToReductions(?array $elasticAttributes, ?float $refencePrice): array
    {
        if (!$refencePrice) {
            return [];
        }

        $reductions = [];

        $thresholdAttributes = [
            $this->izbergCustomAttributes->getThreshold1() => $this->izbergCustomAttributes->getThreshold1Price(),
            $this->izbergCustomAttributes->getThreshold2() => $this->izbergCustomAttributes->getThreshold2Price(),
            $this->izbergCustomAttributes->getThreshold3() => $this->izbergCustomAttributes->getThreshold3Price(),
            $this->izbergCustomAttributes->getThreshold4() => $this->izbergCustomAttributes->getThreshold4Price(),
        ];

        foreach ($thresholdAttributes as $thresholdAttribute => $thresholdPriceAttribute) {
            $threshold = (!empty($elasticAttributes[$thresholdAttribute])) ? (int)$elasticAttributes[$thresholdAttribute] : null;
            $thresholdPrice = $elasticAttributes[$thresholdPriceAttribute] ?? null;

            if ($threshold === null || $thresholdPrice === null) {
                break;
            }

            $reductions[] = new Reduction($threshold, $thresholdPrice, $refencePrice);
        }

        return $reductions;
    }

    private function elasticSearchAttributesToMoq(?array $elasticAttributes): int
    {
        $moq = $elasticAttributes[$this->izbergCustomAttributes->getMoq()] ?? 1;

        // $batchSize must be a strict positive integer value
        $batchSize = max($elasticAttributes[$this->izbergCustomAttributes->getBatchSize()] ?? 1, 1);

        if ($moq < $batchSize) {
            $moq = $batchSize;
        }

        if ($diff = $moq % $batchSize) {
            $moq = $moq + ($batchSize - $diff);
        }

        return $moq;
    }

    /**
     * ElasticSearch result to Merchant.
     *
     * @param array|null $elasticSearchMerchant
     *
     * @return Merchant
     */
    private function elasticSearchMerchantToMerchant(?array $elasticSearchMerchant): Merchant
    {
        $merchant = new Merchant();
        if ($elasticSearchMerchant === null){
            return $merchant;
        }
        $merchant->setId($elasticSearchMerchant["id"]);
        $merchant->setName($elasticSearchMerchant["name"]);
        $merchant->setBranches(!empty($elasticSearchMerchant["branches"]) ? $elasticSearchMerchant["branches"] : []);

        // todo nb review is missing
        if (isset($elasticSearchMerchant["rating"])) {
            $merchant->setRating($elasticSearchMerchant["rating"]);
        }
        $merchant->setAdaptedCompany($elasticSearchMerchant["adapted_company"] ?? false);

        return $merchant;
    }

    /**
     * ElasticSearch result to Product.
     *
     * @param array|null $elasticSearchProduct
     * @param string $locale
     *
     * @return Product
     */
    private function elasticSearchProductToProduct(?array $elasticSearchProduct): Product
    {
        $product = new Product();
        if ($elasticSearchProduct === null){
            return $product;
        }
        $product
            ->setId($elasticSearchProduct["id"])
            ->setName($elasticSearchProduct["name"] ?? "")
            ->setDescription($elasticSearchProduct["description"] ?? "")
            ->setBrand($elasticSearchProduct["brand"]["name"] ?? null)
            ->setMadeIn($elasticSearchProduct["made_in"] ?? null)
            ->setGtin($elasticSearchProduct["gtin"] ?? null)
            ->setCategoriesIds($elasticSearchProduct["application_categories"] ?? [])
        ;
        return $product;
    }

    public function buildIndexRequestForMerchantRating(int $offerId, string $index,float $rating): array
    {
        $request = [];
        $request["index"] = $index;
        $request["id"] = $offerId;
        $request["type"] = "_doc";
        $request["body"]["doc"]["merchant"]["rating"] = $rating;

        return $request;
    }
}
