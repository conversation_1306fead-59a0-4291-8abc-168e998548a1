<?php

namespace AppBundle\Mapper;

use AppB<PERSON>le\Entity\User;
use AppBundle\Model\BestOffer;
use AppBundle\Model\DetailedPrice;
use AppBundle\Model\Merchant;
use AppBundle\Model\Offer;
use AppBundle\Model\OfferSearchResult;
use AppBundle\Model\SearchResult;

interface OfferMapper
{
    /**
     * Map result of an indexer to a Total Offer object.
     *
     * @param SearchResult $indexerResult
     * @param string $locale locale to be used to map the data (example: "fr")
     * @param User|null $buyer
     * @return OfferSearchResult
     */
    public function indexerResultToSearchResult (SearchResult $indexerResult, string $locale, ?User $buyer) : OfferSearchResult;


    /**
     * build a request to be sent to the indexer to add DetailedPrice to an offer
     * @param int $offerId the identifier of the offer
     * @param string $index the name of the index to update
     * @param DetailedPrice $detailedPrice the detailed prices
     * @return array the indexer request
     */
    public function buildIndexRequestForDetailedPrices(int $offerId, string $index, DetailedPrice $detailedPrice): array;

    /**
     * build a request to be sent to the indexer to add BestOffer to an offer
     * @param int $offerId the identifier of the offer
     * @param string $index the name of the index to update
     * @param BestOffer $bestOffer the best offer
     * @return array
     */
    public function buildIndexRequestForBestOffer(int $offerId, string $index, BestOffer $bestOffer): array;

    /**
     * build a request to be sent to the indexer to update prices
     * @param int $offerId
     * @param string $index
     * @param Offer $offer
     * @return array
     */
    public function buildIndexRequestForUpdatingPrice(int $offerId, string $index, Offer $offer): array;

    public function indexerOfferToTotalOffer(array $indexerOffer, string $locale, array $favOffers): ?Offer;

    public function indexerOffersToTotalOffers(array $indexerOffers, string $locale, User $buyer): array;

    /////////////////////////////////////////////////////////////////////////
    // Function to work with fieldNames
    /////////////////////////////////////////////////////////////////////////

    /**
     * get the name of the gtin field
     * @return string
     */
    public function getGTINFieldName(): string;

    /**
     * get the name of the country of delivery field
     * @return string
     */
    public function getCountryOfDeliveryFieldName(): string;

    /**
     * get the name of the price field
     * @return string
     */
    public function getPriceFieldName(): string;

    public function getScoreFieldName(): string;

    /**
     * @return string the name of the country price field name
     */
    public function getCountryPriceFieldName(): string;

    public function buildIndexRequestForMerchantRating(int $offerId, string $index, float $rating): array;

    public function buildIndexRequestForMerchant(int $offerId, string $index, Merchant $merchant): array;
}
