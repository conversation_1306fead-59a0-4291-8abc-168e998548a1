<?php

namespace AppBundle\Mapper;

use AppBundle\Model\BestOffer;
use AppBundle\Model\DetailedPrice;
use AppBundle\Model\Merchant;
use AppBundle\Model\Offer;
use AppBundle\Model\OfferSearchResult;
use AppBundle\Model\Price;
use AppBundle\Model\Product;
use AppBundle\Model\Reduction;
use AppBundle\Model\SearchResult;
use AppBundle\Model\TechnicalDetails;
use AppBundle\Model\TechnicalProperty;
use AppBundle\Services\IzbergCustomAttributes;
use Open\IzbergBundle\Dto\AttributeDTO;
use Open\IzbergBundle\Service\AttributeService;

class IzbergMapper
{
    /**
     * @var IzbergCustomAttributes
     */
    private $izbergCustomAttributes;

    /**
     * @var AttributeService
     */
    private $attributeService;


    public function __construct(IzbergCustomAttributes $izbergCustomAttributes, AttributeService $attributeService)
    {
        $this->izbergCustomAttributes = $izbergCustomAttributes;
        $this->attributeService = $attributeService;
    }

    public function mapToOffer (?array $izbergOffer, $locale="fr"){

        if(!$izbergOffer) {
            return null;
        }

        $convertToBool = function(string $value) {
            if (in_array(strtolower(trim($value)), ['yes', 'oui'])) {
                return true;
            }

            return false;
        };

        $dataSheetUrl =[];

        $dataSheetUrl[] = $this->getExistsStringAttributeOrNull($izbergOffer["attributes"],$this->izbergCustomAttributes->getDataSheet());
        $dataSheetUrl[] = $this->getExistsStringAttributeOrNull($izbergOffer["attributes"],$this->izbergCustomAttributes->getAttachedSheet());
        $result = new Offer();
        $result
            ->setId($izbergOffer["id"])
            ->setName($izbergOffer["name"]?? "")
            ->setDescription($izbergOffer["description"] ?? "")
            ->setPicture($izbergOffer["default_image"] ?? null)
            ->setPrice($izbergOffer["price_without_vat"] ?? null)
            ->setPreviousPrice($izbergOffer["previous_price_without_vat"] ?? null)
            ->setCurrency($izbergOffer["currency"] ?? '')
            ->setTaxRate($izbergOffer["attributes"][$this->izbergCustomAttributes->getVatRate()] ?? null)
            ->setStock($izbergOffer["stock"] ?? null)
            ->setStockManagement(call_user_func($convertToBool,
                    $izbergOffer["attributes"][$this->izbergCustomAttributes->getStockManagement()] ?? ''
                )
            )
            ->setAvailability($izbergOffer["availability"] ?? null)
            ->setIsNew($izbergOffer["isnew"] ?? null)
            ->setExclusive($izbergOffer["exclusive"] ?? null)
            ->setStatus($izbergOffer["status"] ?? null)
            ->setCreatedOn($izbergOffer["created_on"] ?? null)
            ->setEndSellingDate($izbergOffer["end_selling_date"] ?? null)
            ->setDeliveryTime($izbergOffer["delivery_time"] ?? null)
            ->setNumberOfUnits($izbergOffer["number_of_units"] ?? null)
            ->setMerchant($this->elasticSearchMerchantToMerchant($izbergOffer["merchant"] ?? null))
            ->setProduct($this->elasticSearchProductToProduct($izbergOffer["product"] ?? null, $locale) )
            ->setDetailedPrices($this->elasticSearchDetailedPricesToDetailedPrice($izbergOffer["detailed_prices"] ?? null))
            ->setReductions($this->elasticSearchAttributesToReductions($izbergOffer["attributes"] ?? [], $izbergOffer["price_without_vat"] ?? null))
            ->setTechnicalDetails($this->elasticSearchAttributesToTechnicalDetails($izbergOffer["attributes"] ?? [], $locale))
            ->setSku($izbergOffer["sku"] ?? null)
            ->setAssociatedPictures(
                array_filter(array_map(
                    function(array $assignedImage) {
                        return $assignedImage['image_path'] ?? null;
                    },
                    $izbergOffer['assigned_images'] ?? []
                ))
            )
            ->setMoq($this->elasticSearchAttributesToMoq($izbergOffer["attributes"] ?? []))
            ->setBatchSize($izbergOffer["attributes"][$this->izbergCustomAttributes->getBatchSize()] ?? 1)
            ->setPriceGivenOnQuotation($izbergOffer["attributes"][$this->izbergCustomAttributes->getPriceOnQuotation()] ?? false)
            ->setDeliveryRestricted($izbergOffer["attributes"][$this->izbergCustomAttributes->getZoneRestriction()] ?? false)
            ->setDeliveryRestrictionDescription($izbergOffer["attributes"][$this->izbergCustomAttributes->getZoneRestrictionDescription()] ?? null)
            ->setCountryOfDelivery($izbergOffer["attributes"][$this->izbergCustomAttributes->getCountryOfDelivery()] ?? null)
            ->setBestOffer($this->indexBestOfferToTotalBestOffer($izbergOffer["best_offer"] ?? null))

            ->setThreshold1($this->getExistsIntAttributeOrNull($izbergOffer["attributes"], $this->izbergCustomAttributes->getThreshold1()))
            ->setThreshold2($this->getExistsIntAttributeOrNull($izbergOffer["attributes"], $this->izbergCustomAttributes->getThreshold2()))
            ->setThreshold3($this->getExistsIntAttributeOrNull($izbergOffer["attributes"], $this->izbergCustomAttributes->getThreshold3()))
            ->setThreshold4($this->getExistsIntAttributeOrNull($izbergOffer["attributes"], $this->izbergCustomAttributes->getThreshold4()))

            ->setThresholdPrice1($this->getExistsFloatAttributeOrNull($izbergOffer["attributes"],$this->izbergCustomAttributes->getThreshold1Price()))
            ->setThresholdPrice2($this->getExistsFloatAttributeOrNull($izbergOffer["attributes"],$this->izbergCustomAttributes->getThreshold2Price()))
            ->setThresholdPrice3($this->getExistsFloatAttributeOrNull($izbergOffer["attributes"],$this->izbergCustomAttributes->getThreshold3Price()))
            ->setThresholdPrice4($this->getExistsFloatAttributeOrNull($izbergOffer["attributes"],$this->izbergCustomAttributes->getThreshold4Price()))

            ->setCountryPrice($izbergOffer["country_price"] ?? null)
            ->setCountryCurrency($izbergOffer["country_currency"] ?? null)
            ->setCountryThresholdPrice1($izbergOffer["country_threshold_price1"] ?? null)
            ->setCountryThresholdPrice2($izbergOffer["country_threshold_price2"] ?? null)
            ->setCountryThresholdPrice3($izbergOffer["country_threshold_price3"] ?? null)
            ->setCountryThresholdPrice4($izbergOffer["country_threshold_price4"] ?? null)
            ->setDataSheetUrls($dataSheetUrl)
            ->setLanguage($izbergOffer["language"] ?? null);

        return $result;
    }

    private function indexBestOfferToTotalBestOffer(?array $esBestOffer): ?BestOffer{
        if ($esBestOffer) {
            $bestOffer = new BestOffer();
            $bestOffer->setCurrency($esBestOffer['currency']);
            $bestOffer->setPrice($esBestOffer['price']);
            $bestOffer->setOfferId($esBestOffer['offer_id']);
            return $bestOffer;
        }
        return null;
    }

    /**
     * @param array $attributes
     * @param string $attributeName
     * @return string|null
     */
    private function getExistsStringAttributeOrNull(array $attributes, string $attributeName): ?string
    {
        if(empty($attributes[$attributeName])) {
            return null;
        }

        return (string)$attributes[$attributeName];
    }

    private function getExistsIntAttributeOrNull(array $attributes, string $attributeName): ?int
    {
        if(empty($attributes[$attributeName])) {
            return null;
        }

        return (int)$attributes[$attributeName];
    }
    private function getExistsFloatAttributeOrNull(array $attributes, string $attributeName): ?float
    {
        if(empty($attributes[$attributeName])) {
            return null;
        }

        return (float)$attributes[$attributeName];
    }

    /**
     * build a detailed price from the result from the elastic search
     * @param array|null $elasticDetailedPrice the elastic search detailed price
     * @return DetailedPrice
     */
    private function elasticSearchDetailedPricesToDetailedPrice(?array $elasticDetailedPrice): DetailedPrice
    {
        $detailedPrice = new DetailedPrice();
        if ($elasticDetailedPrice !== null){
            $detailedPrice->setCreatedAt(\DateTime::createFromFormat('Y-m-d H:i:s+',
                $elasticDetailedPrice["created_at"]["date"],
                new \DateTimeZone($elasticDetailedPrice["created_at"]["timezone"])));

            foreach ($elasticDetailedPrice["prices"] as $currency => $value){
                $detailedPrice->add($currency, $value["value"], $value["offer_currency"]);
            }
        }

        return $detailedPrice;
    }

    private function elasticSearchAttributesToTechnicalDetails(?array $elasticAttributes, string $locale): TechnicalDetails
    {
        $technicalDetails = new TechnicalDetails();
        $technicalDetails->setDescription($elasticAttributes[$this->izbergCustomAttributes->getTechnicalCharacteristics()] ?? "");

        $allCustomAttributesRegistered = $this->izbergCustomAttributes->fetchAllAttributes();
        $ignoredAttributes = $this->izbergCustomAttributes->fetchIgnoredAttributes();

        $elasticAttributesKeys = array_keys($elasticAttributes);
        $matchAttributes = array_intersect($allCustomAttributesRegistered, $elasticAttributesKeys, $ignoredAttributes);
        $technicalAttributes = array_diff($elasticAttributesKeys, $matchAttributes);

        foreach ($technicalAttributes as $technicalAttribute) {
            $attribute = $this->attributeService->getAttribute($technicalAttribute, $locale);
            $value = $elasticAttributes[$technicalAttribute];

            if ($attribute->getType() === AttributeDTO::TYPE_BOOL && !$value) {
                break;
            }

            if($attribute->getKey() !== $this->izbergCustomAttributes->getTechnicalCharacteristics()) {
                $technicalDetails->addTechnicalProperty(
                    (new TechnicalProperty())
                        ->setLabel($attribute->getLabel())
                        ->setValue(
                            call_user_func(
                                function ($attributeValue) use ($attribute) {
                                    if (is_array($attributeValue)) {
                                        $attributeValue = implode(', ', $attributeValue);
                                    }

                                    if ($attribute->getType() === AttributeDTO::TYPE_BOOL) {
                                        return 'oui';
                                    }

                                    return $attributeValue;
                                },
                                $elasticAttributes[$technicalAttribute]
                            )
                        )
                );
            }
        }

        return $technicalDetails;
    }

    private function elasticSearchAttributesToReductions(?array $elasticAttributes, ?float $refencePrice): array
    {
        if (!$refencePrice) {
            return [];
        }

        $reductions = [];

        $thresholdAttributes = [
            $this->izbergCustomAttributes->getThreshold1() => $this->izbergCustomAttributes->getThreshold1Price(),
            $this->izbergCustomAttributes->getThreshold2() => $this->izbergCustomAttributes->getThreshold2Price(),
            $this->izbergCustomAttributes->getThreshold3() => $this->izbergCustomAttributes->getThreshold3Price(),
            $this->izbergCustomAttributes->getThreshold4() => $this->izbergCustomAttributes->getThreshold4Price(),
        ];

        foreach ($thresholdAttributes as $thresholdAttribute => $thresholdPriceAttribute) {
            $threshold = (!empty($elasticAttributes[$thresholdAttribute])) ? (int)$elasticAttributes[$thresholdAttribute] : null;
            $thresholdPrice = $elasticAttributes[$thresholdPriceAttribute] ?? null;

            if ($threshold === null || $thresholdPrice === null) {
                break;
            }

            $reductions[] = new Reduction($threshold, $thresholdPrice, $refencePrice);
        }

        return $reductions;
    }

    private function elasticSearchAttributesToMoq(?array $elasticAttributes): int
    {
        $moq = $elasticAttributes[$this->izbergCustomAttributes->getMoq()] ?? 1;

        // $batchSize must be a strict positive integer value
        $batchSize = max($elasticAttributes[$this->izbergCustomAttributes->getBatchSize()] ?? 1, 1);

        if ($moq < $batchSize) {
            $moq = $batchSize;
        }

        if ($diff = $moq % $batchSize) {
            $moq = $moq + ($batchSize - $diff);
        }

        return $moq;
    }

    /**
     * ElasticSearch result to Merchant.
     *
     * @param array|null $elasticSearchMerchant
     *
     * @return Merchant
     */
    private function elasticSearchMerchantToMerchant(?array $elasticSearchMerchant): Merchant
    {
        $merchant = new Merchant();
        if ($elasticSearchMerchant === null){
            return $merchant;
        }
        $merchant->setId($elasticSearchMerchant["id"]);
        $merchant->setName($elasticSearchMerchant["name"]);

        // todo nb review is missing
        if (isset($elasticSearchMerchant["rating"])) {
            $merchant->setRating($elasticSearchMerchant["rating"]);
        }
        $merchant->setAdaptedCompany($elasticSearchMerchant["adapted_company"] ?? false);

        return $merchant;
    }

    /**
     * ElasticSearch result to Product.
     *
     * @param array|null $elasticSearchProduct
     * @param string $locale
     *
     * @return Product
     */
    private function elasticSearchProductToProduct(?array $elasticSearchProduct, string $locale): Product
    {
        $product = new Product();
        if ($elasticSearchProduct === null){
            return $product;
        }
        $product
            ->setId($elasticSearchProduct["id"])
            ->setName($elasticSearchProduct["name"][$locale] ?? "")
            ->setDescription($elasticSearchProduct["description"] ?? "")
            ->setBrand($elasticSearchProduct["brand"]["name"] ?? null)
            ->setMadeIn($elasticSearchProduct["made_in"] ?? null)
            ->setGtin($elasticSearchProduct["gtin"] ?? null);
        return $product;
    }
}
