<?php

namespace AppBundle\Security\Checker;

use AppBundle\Entity\User;
use Symfony\Component\Security\Core\Exception\CustomUserMessageAuthenticationException;
use Symfony\Component\Security\Core\User\UserCheckerInterface;
use Symfony\Component\Security\Core\User\UserInterface;

class UserStatusChecker implements UserCheckerInterface
{

    public function checkPreAuth(UserInterface $user)
    {
        $this->checkUser($user);
    }

    public function checkPostAuth(UserInterface $user)
    {
        $this->checkUser($user);
    }

    private function checkUser(UserInterface $user)
    {
        if (!$user instanceof User) {
            new CustomUserMessageAuthenticationException('Invalid user type !');
        }

        /** @var User $user */
        if (!$user->isEnabled()) {
            throw new CustomUserMessageAuthenticationException('User disabled !');
        }
    }
}
