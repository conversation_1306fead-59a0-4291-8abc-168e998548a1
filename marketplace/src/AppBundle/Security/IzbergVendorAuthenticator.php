<?php

namespace AppBundle\Security;

use AppBundle\Entity\Country;
use AppBundle\Entity\Merchant;
use AppBundle\Entity\Quote;
use AppBundle\Repository\CountryRepository;
use AppBundle\Repository\MerchantRepository;
use AppBundle\Repository\QuoteRepository;
use AppBundle\Security\Model\IzbergVendor;
use Doctrine\ORM\EntityManagerInterface;
use KnpU\OAuth2ClientBundle\Client\ClientRegistry;
use KnpU\OAuth2ClientBundle\Security\Authenticator\SocialAuthenticator;
use Open\IzbergBundle\Api\AuthenticationApi;
use Open\LogBundle\Service\LogService;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\User\UserProviderInterface;

/**
 * @deprecated use AppBundle\Security\Authenticator\IzbergVendorAuthenticator instead
 */
class IzbergVendorAuthenticator extends SocialAuthenticator
{
    private $clientRegistry;
    private $em;

    /**
     * @var MerchantRepository
     */
    private $merchantRepository;

    /**
     * @var CountryRepository
     */
    private $countryRepository;

    /**
     * @var QuoteRepository
     */
    private $quoteRepository;


    /**
     * @var RouterInterface $router
     */
    private $router;

    /**
     * @var string $marketPlaceName
     */
    private $marketPlaceName;

    /** @var AuthenticationApi */
    private $authenticationApi;

    private LogService $logger;

    private const VENDOR_AUTHENTICATOR = "VENDOR_AUTHENTICATOR";


    public function __construct(ClientRegistry $clientRegistry, EntityManagerInterface $em, RouterInterface $router, AuthenticationApi $api, LogService $logger, string $marketPlaceName)
    {
        $this->clientRegistry = $clientRegistry;

        $this->em = $em;

        $this->merchantRepository = $this->em->getRepository(Merchant::class);
        $this->countryRepository = $this->em->getRepository(Country::class);
        $this->quoteRepository = $this->em->getRepository(Quote::class);

        $this->router = $router;
        $this->marketPlaceName = $marketPlaceName;
        $this->authenticationApi = $api;
        $this->logger = $logger;
    }

    public function supports(Request $request)
    {

        if ($request->attributes->get('_route') === 'front_supplier_connect_check') {
            if ($request->query->get('marketplace') === $this->marketPlaceName) {
                return true;
            }

            return false;
        }
        if ($request->getSession()->get("izberg_access_token")) {
            return true;
        }

        $quoteId = null;
        if (!is_null($request->attributes->get("id"))) {
            $quoteId = $request->attributes->get("id");
        } else {
            return false;
        }
        if (is_null($quoteId)) {
            $this->logger->error("Can't find quote :" . $quoteId, self::VENDOR_AUTHENTICATOR, null, []);
            return false;
        }

        /** @var Quote $quote */
        $quote = $this->quoteRepository->find($quoteId);
        return $quote->getVendor()->getCountryCode() === $this->marketPlaceName;


        // continue ONLY if the current ROUTE matches the check ROUTE
        //  return $request->attributes->get('_route') === 'front_supplier_connect_check';
    }

    public function getCredentials(Request $request)
    {
        if (!$this->supports($request)) {
            return null;
        }

        return $this->fetchAccessToken($this->getIzbergVendorClient());
    }

    public function getUser($credentials, UserProviderInterface $userProvider)
    {

        /** @var IzbergVendor $izbergVendor */
        $izbergVendor = $this->getIzbergVendorClient()->fetchUserFromToken($credentials);

        /** @var Merchant $merchant */
        $merchant = $this->merchantRepository->findOneBy(array("izbergId" => $izbergVendor->getMerchantId()));

        if (!$merchant) {
            $country = $this->countryRepository->findByCode(Country::CODE_FRANCE);
            $merchant = (new Merchant())
                ->setFirstname('')
                ->setLastname('')
                ->setEmail('NOT MENTIONNED')
                ->setStatus(Merchant::STATUS_ACCOUNT_VALIDATION)
                ->setIzbergId($izbergVendor->getMerchantId())
                ->setName($izbergVendor->getCompanyName())
                ->setSlug($izbergVendor->getSlug())
                ->setLanguage($izbergVendor->getPreferredLanguage())
                ->setCountry($country);

            $this->merchantRepository->save($merchant);
        }


        if ($merchant) {
            $merchant->setIzbergUsername($izbergVendor->getUserName());
            return $merchant;
        }


        return null;
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, $providerKey)
    {
        return null;
    }

    public function onAuthenticationFailure(Request $request, AuthenticationException $exception)
    {
        return null;
    }

    public function start(Request $request, AuthenticationException $authException = null)
    {
        $marketPlaceName = null;
        if ($request->attributes) {
            if (!is_null($request->attributes->get("marketplace_name"))) {
                $marketPlaceName = $request->attributes->get("marketplace_name");
            } else {
                if (!is_null($request->attributes->get("id"))) {
                    $quoteId = $request->attributes->get("id");
                    $quote = $this->quoteRepository->find($quoteId);
                    $marketPlaceName = $quote->getVendor()->getMarketPlace()->getName();
                }
            }
        }
        if (is_null($marketPlaceName)) {
            $this->logger->critical("Can't determine marketplace", self::VENDOR_AUTHENTICATOR, null, ["request" => $request]);
            die();
        }

        return new RedirectResponse(
            $this->router->generate('front_supplier_connect_start', ['marketplaceName' => $marketPlaceName]),
            Response::HTTP_TEMPORARY_REDIRECT
        );
    }

    private function getIzbergVendorClient()
    {
        return $this->clientRegistry->getClient('izberg_vendor_client_' . $this->marketPlaceName);
    }

}
