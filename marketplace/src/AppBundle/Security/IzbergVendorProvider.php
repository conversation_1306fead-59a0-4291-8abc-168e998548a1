<?php

namespace AppBundle\Security;

use AppBundle\Security\Model\IzbergVendor;
use AppBundle\Util\JWT;
use Firebase\JWT\Key;
use League\OAuth2\Client\Provider\GenericProvider;
use League\OAuth2\Client\Provider\GenericResourceOwner;
use League\OAuth2\Client\Token\AccessToken;

/**
 * @deprecated use AppBundle\Security\Provider\IzbergVendorProvider
 */
class IzbergVendorProvider extends GenericProvider
{
    /**
     * @var string
     */
    protected $izbergApiDomain;

    public function getResourceOwner(AccessToken $token): ?GenericResourceOwner
    {
        $izbergVendor = null;

        $request = $this->getRequest('GET', 'https://api.izberg.me/.well-known/valid-keys.json');
        $keys = $this->getParsedResponse($request);
        $key = array_shift($keys['keys']);
        $decoded = JWT::decode($token->getToken(), new Key($key['n'],  'RS256'));


        $me = $this->getParsedResponse($this->getRequest('GET', 'https://api.izberg.me/v1/user/me/', [
            'headers' => [
                'Authorization' => 'Bearer ' . $token,
                'Content-Type'        => 'application/json',
            ]
        ]));

        $profil = $this->getParsedResponse($this->getRequest('GET', 'https://'.$this->izbergApiDomain.'/v1/merchant/'.$decoded->merchant_id.'/', [
            'headers' => [
                'Authorization' => 'Bearer ' . $token,
                'Accept'        => 'application/json',
            ]
        ]));


        $izbergVendor = (new IzbergVendor())
            ->setName($decoded->name)
            ->setMerchantId($decoded->merchant_id)
            ->setPreferredLanguage($profil['prefered_language'])
            ->setSlug($profil['slug'])
            ->setCompanyName($profil['name'])
            ->setUserName($me['username']??"")
            ->setFirstName($me['first_name'])
            ->setLastName($me['last_name'])
            ->setEmail($me['email']);

        return new GenericResourceOwner($izbergVendor->toArray(), (string)$izbergVendor->getMerchantId());
    }

    protected function getRequiredOptions()
    {
        return [
            'urlAuthorize',
            'urlAccessToken',
            'izbergApiDomain',
        ];
    }

    /**
     * @inheritdoc
     */
    public function getIzbergApiDomain(array $params)
    {
        return $this->izbergApiDomain;
    }
}
