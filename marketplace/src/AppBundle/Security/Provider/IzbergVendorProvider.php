<?php

namespace AppBundle\Security\Provider;

use AppBundle\Entity\Merchant;
use AppBundle\Exception\MarketPlaceException;
use AppBundle\Security\Model\IzbergVendor;
use AppBundle\Services\CountryService;
use AppBundle\Services\MarketPlaceService;
use AppBundle\Services\MerchantService;
use AppBundle\Util\JWT;
use Firebase\JWT\Key;
use Open\IzbergBundle\Api\MerchantApi;
use Open\LogBundle\Service\LogService;
use RuntimeException;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\DecodingExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class IzbergVendorProvider
{
    private HttpClientInterface $httpClient;
    private ParameterBagInterface $parameterBag;
    private LogService $logger;
    private MerchantApi $merchantApi;
    private MerchantService $merchantService;
    private CountryService $countryService;
    private MarketPlaceService $marketplaceService;
    private UrlGeneratorInterface $urlGenerator;


    public function __construct(
        ParameterBagInterface $parameterBag,
        LogService            $logger,
        MerchantApi           $merchantApi,
        MerchantService       $merchantService,
        CountryService        $countryService,
        MarketPlaceService    $marketplaceService,
        UrlGeneratorInterface $urlGenerator
    )
    {
        $this->httpClient = HttpClient::create();
        $this->parameterBag = $parameterBag;
        $this->logger = $logger;
        $this->merchantApi = $merchantApi;
        $this->merchantService = $merchantService;
        $this->countryService = $countryService;
        $this->marketplaceService = $marketplaceService;
        $this->urlGenerator = $urlGenerator;
    }

    public function loadUserFromIzberg(string $code, string $marketplaceName): UserInterface
    {
        try {
            $marketplace = $this->marketplaceService->getMarketPlaceByName($marketplaceName);
        } catch (MarketPlaceException $exception) {
            $this->logger->error('error while vendor auth - marketplace not found', 'VENDOR_AUTH', null, [
                'error' => $exception
            ]);

            throw new RuntimeException($exception->getMessage(), $exception->getCode(), $exception);
        }

        $merchant = $this->fetchIzbergVendor(
            $marketplace->getApiConfigurationKey(),
            $this->fetchIzbergVendorAccessToken($code, $marketplace->getName())
        );
        $merchantBdd = $this->merchantService->findMerchantEntityByIzbergId($merchant->getMerchantId());

        if (!$merchantBdd) {
            $merchantBdd = new Merchant();
            $merchantBdd
                ->setIzbergId($merchant->getMerchantId())
                ->setName($merchant->getName())
                ->setSlug($merchant->getSlug())
                ->setEmail($merchant->getEmail())
                ->setFirstname($merchant->getFirstName())
                ->setLastname($merchant->getLastName())
                ->setLanguage($merchant->getPreferredLanguage())
                ->setCurrency($merchant->getCurrency())
                ->setCountry($this->countryService->getCountryByCode($merchant->getCountry()))
                ->setMarketPlace($marketplace)
                ->setStatus('')
                ->setIzbergUsername($merchant->getUserName());

            $this->merchantService->saveMerchant($merchantBdd);
        }

        return $merchantBdd;
    }


    private function fetchIzbergVendorAccessToken(string $code, string $marketplace): string
    {
        // load request params
        $authTokenUrl = (string) $this->parameterBag->get('izberg_sso_auth_token_url');
        $clientId = $this->parameterBag->get(sprintf('izberg_sso_%s_client_id', $marketplace));
        $clientSecret = $this->parameterBag->get(sprintf('izberg_sso_%s_client_secret', $marketplace));
        $redirectUriName = (string) $this->parameterBag->get(sprintf('izberg_sso_%s_redirect_uri', $marketplace));

        try {
            $options = [
                'body' => [
                    'grant_type' => 'authorization_code',
                    'response_type' => 'id_token token refresh_token',
                    'audience' => 'vendor.auth',
                    'client_id' => $clientId,
                    'client_secret' => $clientSecret,
                    'redirect_uri' => $this->urlGenerator->generate($redirectUriName, [], UrlGeneratorInterface::ABSOLUTE_URL),
                    'code' => $code,
                ]
            ];

            $response = $this->httpClient->request('POST', $authTokenUrl, $options)->toArray();
            return $response['access_token'];

        } catch (ClientExceptionInterface|DecodingExceptionInterface|RedirectionExceptionInterface|ServerExceptionInterface|TransportExceptionInterface $e) {
            $this->logger->critical('Error while izberg vendor auth', 'VENDOR_AUTH', null, [
                'error' => $e
            ]);
            throw new RuntimeException($e->getMessage(), $e->getCode(), $e);
        }
    }

    private function fetchIzbergVendor(string $marketplaceApiConfigurationKey, string $accessToken): IzbergVendor
    {
        $keys = $this->getJwtValidKeys();
        $decoded = JWT::decode($accessToken, new Key($keys['n'], $keys['alg']));
        $merchantId = (int)$decoded->merchant_id;

        $this->merchantApi->configureApiConnection($marketplaceApiConfigurationKey);
        $izbergRawMerchant = $this->merchantApi->getMerchant($merchantId);

        if (!$izbergRawMerchant) {
            $msg = sprintf('izberg merchant %s does not exists', $merchantId);
            $this->logger->error($msg, 'VENDOR_AUTH', null, [
                'merchantId' => $merchantId
            ]);
            throw new RuntimeException($msg);
        }

        // base merchant information
        $merchant = ((new IzbergVendor())
            ->setMerchantId($izbergRawMerchant->id)
            ->setSlug($izbergRawMerchant->slug)
            ->setName($izbergRawMerchant->name)
            ->setUserName($izbergRawMerchant->name)
            ->setCurrency($izbergRawMerchant->default_currency)
            ->setPreferredLanguage($izbergRawMerchant->prefered_language)
        );

        if (!empty($izbergRawMerchant->addresses)) {
            $merchantContact = $izbergRawMerchant->addresses[0];
            ($merchant
                ->setEmail($merchantContact->contact_email)
                ->setCompanyName($merchantContact->contact_social_reason)
                ->setFirstName($merchantContact->contact_first_name)
                ->setLastName($merchantContact->contact_last_name)
                ->setCountry(str_replace(' ', '_', strtolower($merchantContact->country->name)))
            );
        }

        return $merchant;
    }

    private function getJwtValidKeys(): array
    {
        $url = (string) $this->parameterBag->get('izberg_sso_valid_jwt_keys_url');
        $response = $this->httpClient->request('GET', $url)->toArray();
        return array_shift($response['keys']);
    }
}
