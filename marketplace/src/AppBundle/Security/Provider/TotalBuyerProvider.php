<?php

namespace AppBundle\Security\Provider;

use AppBundle\Security\Model\TotalBuyerResourceOwner;
use League\OAuth2\Client\Provider\GenericProvider;
use League\OAuth2\Client\Token\AccessToken;

class TotalBuyerProvider extends GenericProvider
{
    public string $urlLogout;

    public function getDefaultScopes(): array
    {
        return ['openid'];
    }


    /**
     * @inheritdoc
     */
    protected function createResourceOwner(array $response, AccessToken $token)
    {
        return new TotalBuyerResourceOwner($response, 'id');
    }

    protected function getRequiredOptions()
    {
        return array_merge(parent::getRequiredOptions(), ['urlLogout']);
    }

    public function logout()
    {
        $params = $this->getAuthorizationParameters(['approval_prompt' => 'none']);
        $query = $this->getAuthorizationQuery($params);

        $url = $this->appendQuery($this->urlLogout, $query);

        return $url;
    }
}
