<?php

namespace AppBundle\Security\Model;

use KnpU\OAuth2ClientBundle\Client\OAuth2Client;
use League\OAuth2\Client\Token\AccessToken;
use AppBundle\Security\Model\TotalBuyerResourceOwner;

class TotalAuthClient extends OAuth2Client
{
    public function fetchUserFromToken(AccessToken $accessToken)
    {
        $accessTokenValues = $accessToken->getValues();

        $idToken = $accessTokenValues['id_token'] ?? null;

        $response = [];
        if ($idToken) {
            $response['id_token'] = $idToken;
        }

        return new TotalBuyerResourceOwner($response, 'id');
    }
}