<?php

namespace AppBundle\Security\Model;

use Firebase\JWT\JWT;
use Firebase\JWT\JWK;
use League\OAuth2\Client\Provider\GenericResourceOwner;

class TotalBuyerResourceOwner extends GenericResourceOwner
{
    private ?array $idTokenClaims = null;
    private string $keysUrl;

    public function setKeysUrl($kesUrl)
    {
        $this->keysUrl = $kesUrl;
    }

    public function getEmail()
    {
        return $this->response['Email'];
    }

    public function getIGG()
    {
        return $this->response['IGG'] ?? $this->getClaim('IGG') ?? $this->response['sub'] ?? null;
    }

    private function getClaim(string $claimKey): ?string
    {
        if ($this->idTokenClaims === null) {
            $this->decodeIdToken();
        }

        return $this->idTokenClaims[$claimKey] ?? null;
    }

    private function decodeIdToken(): void
    {
        $idToken = $this->response['id_token'] ?? null;

        if (!$idToken) {
            $this->idTokenClaims = [];
            return;
        }

        $base64 = explode('.', $idToken);
        $headerJson = json_decode(base64_decode(strtr($base64[0], '-_', '+/')), true);
        $kid = $headerJson['kid'] ?? null;

        if (!$kid) {
            $this->idTokenClaims = [];
            return;
        }

        $keys_url = $this->keysUrl ?? getenv('TOTAL_OPEN_ID_CONNECT_URL_KEYS');
        if (empty($keys_url)) {
            $keys_url = 'https://login.microsoftonline.com/common/discovery/v2.0/keys';
        }
        $jwks = json_decode(file_get_contents($keys_url), true);
        foreach ($jwks['keys'] as &$key) {
            if (!isset($key['alg'])) {
                $key['alg'] = 'RS256';
            }
        }
        $keys = JWK::parseKeySet($jwks);

        if (!isset($keys[$kid])) {
            $this->idTokenClaims = [];
            return;
        }

        $decoded = JWT::decode($idToken, $keys[$kid]);
        $this->idTokenClaims = (array) $decoded;
    }
}
