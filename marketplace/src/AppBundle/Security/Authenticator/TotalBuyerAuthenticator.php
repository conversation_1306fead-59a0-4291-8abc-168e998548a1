<?php

namespace AppBundle\Security\Authenticator;

use AppBundle\Entity\User;
use AppBundle\Security\Model\TotalBuyerResourceOwner;
use Doctrine\ORM\EntityManagerInterface;
use KnpU\OAuth2ClientBundle\Client\ClientRegistry;
use KnpU\OAuth2ClientBundle\Security\Authenticator\SocialAuthenticator;
use Open\LogBundle\Service\LogService;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\User\UserProviderInterface;

class TotalBuyerAuthenticator extends SocialAuthenticator
{
    private $clientRegistry;
    private $em;
    private LogService $logger;

    public function __construct(ClientRegistry $clientRegistry, EntityManagerInterface $em, LogService $logger)
    {
        $this->clientRegistry = $clientRegistry;
        $this->em = $em;
        $this->logger = $logger;
    }

    public function supports(Request $request)
    {
        // continue ONLY if the current ROUTE matches the check ROUTE
         $match = $request->attributes->get('_route') === 'connect_buyer_check';
        $this->logger->info("supports", "AUTHENT_SSO", null,["match"=>$match]);
        return $request->attributes->get('_route') === 'connect_buyer_check';
    }

    public function getCredentials(Request $request)
    {
        if (!$this->supports($request)) {
            return null;
        }
        $token  = $this->fetchAccessToken($this->getTotalBuyerClient());
        $this->logger->info("getCredentials token :", "AUTHENT_SSO",null,["token"=>json_encode($token)]);
        return $token;
    }

    public function getUser($credentials, UserProviderInterface $userProvider)
    {
        $this->logger->info("getUser function", "AUTHENT_SSO");
        /** @var TotalBuyerResourceOwner $totalBuyer */
        $totalBuyer = $this->getTotalBuyerClient()->fetchUserFromToken($credentials);
        $this->logger->info("getUser totalBuyer:", "AUTHENT_SSO",null,["totalBuyer"=>json_encode($totalBuyer)]);
        // 1) have they logged in with Total before? Easy!
        /** @var User $existingUser */
        $existingUser = $this->em->getRepository(User::class)
            ->findOneBy(
                [
                    'username' => $totalBuyer->getIGG(),
                    'enabled' => true,
                ]
            );

        if ($existingUser && !$existingUser->isDeleted()) {
            $this->logger->info("User Exist:", "AUTHENT_SSO");
            return $existingUser;
        }

        return null;
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, $providerKey)
    {
        return new RedirectResponse ($this->getPreviousUrl($request,$providerKey));
    }

    public function onAuthenticationFailure(Request $request, AuthenticationException $exception)
    {
        throw new AccessDeniedHttpException();
    }

    /**
     * Called when authentication is needed, but it's not sent.
     * This redirects to the 'login'.
     */
    public function start(Request $request, AuthenticationException $authException = null)
    {
        return new RedirectResponse(
            '/connect/buyer', // might be the site, where users choose their oauth provider
            Response::HTTP_TEMPORARY_REDIRECT
        );
    }

    private function getTotalBuyerClient()
    {
        return $this->clientRegistry->getClient('total_buyer');
    }
}
