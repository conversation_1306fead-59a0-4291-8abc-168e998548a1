<?php

namespace AppBundle\Security\Authenticator;

use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Http\Authenticator\AbstractLoginFormAuthenticator;
use Symfony\Component\Security\Http\Authenticator\InteractiveAuthenticatorInterface;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\CsrfTokenBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Credentials\PasswordCredentials;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;
use Symfony\Component\Security\Http\Authenticator\Passport\PassportInterface;
use Symfony\Component\Security\Http\EntryPoint\AuthenticationEntryPointInterface;

class BuyerFormAuthenticator extends AbstractLoginFormAuthenticator implements AuthenticationEntryPointInterface, InteractiveAuthenticatorInterface
{
    private const LOGIN_ROUTE_NAME = 'buyer.login';
    private const HOME_ROUTE_NAME = 'homepage';

    private UrlGeneratorInterface $urlGenerator;

    public function __construct(UrlGeneratorInterface $urlGenerator)
    {
        $this->urlGenerator = $urlGenerator;
    }

    protected function getLoginUrl(Request $request): string
    {
        return $this->urlGenerator->generate(self::LOGIN_ROUTE_NAME);
    }

    public function authenticate(Request $request): PassportInterface
    {
        $email = (string) $request->request->get('_username', "");
        $password = (string) $request->request->get('_password', "");
        $csrfToken = (string) $request->request->get('_csrf_token', "");

        return new Passport(new UserBadge($email), new PasswordCredentials($password), [
            new CsrfTokenBadge('authenticate', $csrfToken)
        ]);
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): ?Response
    {
        return new RedirectResponse($this->urlGenerator->generate(self::HOME_ROUTE_NAME));
    }
}
