<?php

namespace AppBundle\Security\Authenticator;

use AppBundle\Entity\User;
use AppBundle\Repository\UserRepository;
use AppBundle\Security\Model\TotalBuyerResourceOwner;
use KnpU\OAuth2ClientBundle\Client\ClientRegistry;
use KnpU\OAuth2ClientBundle\Client\OAuth2ClientInterface;
use Open\LogBundle\Service\LogService;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Http\Authenticator\AbstractAuthenticator;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Credentials\CustomCredentials;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;
use Symfony\Component\Security\Http\EntryPoint\AuthenticationEntryPointInterface;
use Symfony\Component\Security\Http\Util\TargetPathTrait;

/**
 * Compatible DigitalPass et EntraID
 */
class TotalAuthenticator extends AbstractAuthenticator implements AuthenticationEntryPointInterface
{
    use TargetPathTrait;

    private const OAUTH_CLIENT_NAME = 'total_buyer';
    private const LOGIN_CHECK_ROUTE_NAME = 'connect_buyer_check';
    private const LOGIN_START_ROUTE = '/connect/buyer';
    private const HOME_ROUTE = '/home';

    private UrlGeneratorInterface $urlGenerator;
    private UserRepository $userRepository;
    private ClientRegistry $clientRegistry;
    private LogService $logService;

    public function __construct(
        UrlGeneratorInterface $urlGenerator,
        LogService $logService,
        UserRepository $userRepository,
        ClientRegistry $clientRegistry
    ) {
        $this->urlGenerator = $urlGenerator;
        $this->logService = $logService;
        $this->userRepository = $userRepository;
        $this->clientRegistry = $clientRegistry;
    }


    public function start(Request $request, AuthenticationException $authException = null): RedirectResponse
    {
        return new RedirectResponse(self::LOGIN_START_ROUTE);
    }

    public function supports(Request $request): ?bool
    {
        return $request->attributes->get('_route') === self::LOGIN_CHECK_ROUTE_NAME;
    }

    public function authenticate(Request $request): ?Passport
    {
        $token = $this->getTotalClient()->getAccessToken();
        /** @var TotalBuyerResourceOwner $totalBuyer */
        $totalBuyer = $this->getTotalClient()->fetchUserFromToken($token);

        /** @var User $existingUser */
        $existingUser = $this->userRepository->findOneBy([
            'username' => $totalBuyer->getIGG(),
            'enabled' => true,
        ]);


        $email = '';
        if ($existingUser instanceof User) {
            $this->logService->info(
                sprintf('user %s is authenticated successfully', $existingUser->getEmail()),
                'USER AUTHENTICATED',
                $existingUser->getUsername(),
                ['user' => $existingUser]
            );
            $email = $existingUser->getEmail();
        }

        return new Passport(
            new UserBadge($email, function () use ($existingUser) {
                return $existingUser;
            }),
            new CustomCredentials(function () use ($existingUser) {
                return !!$existingUser && !$existingUser->isDeleted();
            }, '')
        );
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): ?Response
    {
        $url = $this->getTargetPath($request->getSession(), $firewallName);
        return new RedirectResponse ($url ?? self::HOME_ROUTE);
    }

    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): ?Response
    {
        throw new AccessDeniedHttpException();
    }

    private function getTotalClient(): OAuth2ClientInterface
    {
        return $this->clientRegistry->getClient(self::OAUTH_CLIENT_NAME);
    }
}
