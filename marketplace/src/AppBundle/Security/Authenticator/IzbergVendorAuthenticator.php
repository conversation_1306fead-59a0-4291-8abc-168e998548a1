<?php

namespace AppBundle\Security\Authenticator;

use AppBundle\Entity\Merchant;
use AppBundle\Security\Provider\IzbergVendorProvider;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Http\Authenticator\AbstractAuthenticator;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Credentials\CustomCredentials;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;
use Symfony\Component\Security\Http\EntryPoint\AuthenticationEntryPointInterface;

class IzbergVendorAuthenticator extends AbstractAuthenticator implements AuthenticationEntryPointInterface
{
    private const LOGIN_START_ROUTE_NAME = 'front_supplier_connect_start';
    private const LOGIN_CHECK_ROUTE_NAME = 'front_supplier_connect_check';
    private const DEFAULT_MARKETPLACE = 'france';

    private UrlGeneratorInterface $urlGenerator;
    private IzbergVendorProvider $vendorProvider;

    public function __construct(UrlGeneratorInterface $urlGenerator, IzbergVendorProvider $vendorProvider)
    {
        $this->urlGenerator = $urlGenerator;
        $this->vendorProvider = $vendorProvider;
    }

    public function start(Request $request, AuthenticationException $authException = null): Response
    {
        return new RedirectResponse($this->urlGenerator->generate(self::LOGIN_START_ROUTE_NAME, [
            'marketplaceName' => $this->getMarketplace($request),
        ]));
    }

    public function supports(Request $request): ?bool
    {
        return (
            $request->attributes->get('_route') === self::LOGIN_CHECK_ROUTE_NAME &&
            $request->query->has('marketplace')
        );
    }

    public function authenticate(Request $request): Passport
    {
        /** @var Merchant $vendor */
        $vendor = $this->vendorProvider->loadUserFromIzberg((string)$request->query->get('code',""), $this->getMarketplace($request));
        return new Passport(
            new UserBadge($vendor->getId(), function () use ($vendor) {
                return $vendor;
            }),
            new CustomCredentials(function () use ($vendor) {
                return !!$vendor;
            }, '')
        );
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): ?Response
    {
        return null;
    }

    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): ?Response
    {
        return null;
    }

    private function getMarketplace(Request $request): string
    {
        return (string)$request->query->get('marketplace', self::DEFAULT_MARKETPLACE);
    }
}
