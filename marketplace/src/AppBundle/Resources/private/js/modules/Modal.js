const Handlebars = require("handlebars/dist/handlebars");

(function ($, h) {
    'use strict';

    let zIndex = 9999;

    let confirmModal = null;

    let _destroyModal = function (id) {
        $('#' + id).remove();
    };

    /**
     * Close modal from its id
     * @param id
     * @param cb
     * @private
     */
    let _closeModal = function (id, cb) {

        zIndex--;

        // Destroy loading modal if launched
        _destroyModal('js-loading-modal');

        // Hide the Modal then destroy it
        $('#' + id).fadeOut(100, function () {

            // Remove DOM elements
            _destroyModal(id);

            // Trigger close callback if needed
            if (typeof cb === 'function') {
                cb();
            }
        });
    };

    /**
     * Show modal
     * @param id
     * @param classes
     * @param $content
     * @returns {{close: close}}
     * @private
     */
    let _show = function (id, classes, $content, canClose, onShow, onClose) {

        const _onClose = onClose;

        // if the modal already exists do nothing
        if (document.getElementById(id) !== null) {
            console.log('Modal already exists : #' + id);
            return {
                show: function (onShow) {
                    $('#' + id).fadeIn(100, function () {
                        if (typeof onShow === 'function') {
                            onShow(
                                {
                                    close: function () {
                                        _closeModal(id, onClose);
                                    }
                                }
                            );
                        }
                    });
                },
                close: function (onClose) {
                    _closeModal(id, onClose);
                }
            };
        }

        if (typeof canClose === 'undefined') {
            canClose = true;
        } else {
            canClose = !!canClose;
        }

        if (canClose === false) {
            onClose = undefined;
        }

        // Compile template
        let $tpl = h.compile($('#js-modal-tpl').html());

        // Build data
        let o = {
            id: id,
            classes: null
        };

        if (typeof classes === 'string' && classes !== '') {
            o.classes = ' ' + classes
        }

        // Append html to dom
        $('body').append($tpl(o));

        // Get the modal container
        let $modal = $('#' + id);


        if (canClose) {
            // Bind click on close button to close the modal
            $modal.find('.js-close-icon').on('click', function () {
                _closeModal(id, onClose);
            });
        } else {
            $modal.find('.js-close-icon').remove();
        }

        $modal.find('.js-modal-overlay').css('zIndex', zIndex);

        // Add content to the modal
        let $contentContainer = $modal.find('.js-modal-content');

        //seem that back bundle use a sub div .modal
        if ($contentContainer.parents('.modal').length) {
            $contentContainer.parents('.modal').css('zIndex', zIndex + 1);
        }
        //for front
        if ($contentContainer.parents('.Modal-wrapper').length) {
            $contentContainer.parents('.Modal-wrapper').css('zIndex', zIndex + 1);
        } else {
            $contentContainer.css('zIndex', zIndex + 1);
        }

        $contentContainer.append($content);
        overrideCSS(id);

        // Show the modal
        $modal.fadeIn(100, function () {
            if (typeof onShow === 'function') {

                onShow(
                    {
                        close: function () {
                            _closeModal(id, _onClose);
                        }
                    }
                );
            }
        });

        zIndex++;

        // Return a close method to close the modal
        return {
            close: function () {
                _closeModal(id, onClose);
            }
        }
    };

    let _showLoading = function () {
        let modalHTML = $('#js-loading-modal-tpl').html();
        return _show('js-loading-modal', 'Modal--loading', $(modalHTML), false);
    };

    let _hideLoading = function () {
        _destroyModal('js-loading-modal');
    };

    let _showLogin = function () {
        let modalHTML = $('#js-login-modal-tpl').html();
        if (modalHTML != null) {
            return _show('js-login-modal', 'Modal--login', $(modalHTML), true);
        } else {
            return null;
        }
    };

    let _showCustomSearchModal = function () {
        let modalHTML = $('#js-custom-search-modal-tpl').html();
        return _show('js-custom-search-modal', 'Modal--custom-search', $(modalHTML), true);
    };

    let _showConfirmModal = function (title, txt, onConfirm, onCancel) {
        const modalHTML = $('#js-confirm-modal-tpl').html();

        // generate the bid confirm modal with the entered value
        const modalContent = h.compile(modalHTML)(
            {
                'title': title,
                'txt': txt
            }
        );

        return _show('js-confirm-modal', 'Modal--confirm', modalContent, false, function (modal) {

                if (typeof onConfirm === 'function') {
                    $('#js-confirm-button').on('click', function () {
                        modal.close();
                        onConfirm();
                    });
                }

                if (typeof onCancel === 'function') {
                    $('.js-cancel-button').on('click', function () {
                        modal.close();
                        onCancel();
                    });
                }

            },
            function () {
                $('#js-confirm-button').off();
                $('#js-cancel-button').off();
            });

    };

    let _showConfirmCountryModal = function (title, txt1, txt2, txt3, onConfirm, onCancel) {
        const modalHTML = $('#js-confirm-modal-country-tpl').html();

        // generate the bid confirm modal with the entered value
        const modalContent = h.compile(modalHTML)(
            {
                'title': title,
                'txt1': txt1,
                'txt2': txt2,
                'txt3': txt3
            }
        );

        return _show('js-confirm-modal-country', 'Modal--confirm', modalContent, false, function (modal) {

                if (typeof onConfirm === 'function') {
                    $('#js-confirm-button').on('click', function () {
                        modal.close();
                        onConfirm();
                    });
                }

                if (typeof onCancel === 'function') {
                    $('.js-cancel-button').on('click', function () {
                        modal.close();
                        onCancel();
                    });
                }

            },
            function () {
                $('#js-confirm-button').off();
                $('#js-cancel-button').off();
            });

    };

    let _showRejectModal = function (title, txt, onConfirm, onCancel) {
        const modalHTML = $('#js-reject-order-modal-tpl').html();
        var loadingModal = null;
        // generate the bid confirm modal with the entered value
        const modalContent = h.compile(modalHTML)(
            {
                'title': title,
                'txt': txt
            }
        );

        return _show('js-reject-modal', 'Modal--reject', modalContent, false, function (modal) {

                if (typeof onConfirm === 'function') {
                    $('#js-confirm-button').on('click', function () {
                        let $reasonVal = $('#reject-reason').val();

                        if(!$reasonVal) {
                            $('#reject-reason').css('border', '1px solid red');
                        } else {
                            loadingModal = window.UI.Modal.showLoading();
                            onConfirm();
                            modal.close();
                        }
                    });
                }

                if (typeof onCancel === 'function') {
                    $('.js-cancel-button').on('click', function () {
                        modal.close();
                        onCancel();
                    });
                }

            },
            function () {
                // ('Confirm modal closed');
                $('#js-confirm-button').off();
                $('#js-cancel-button').off();
            });

    };

    let _showCancelModal = function (title, txt, onConfirm, onCancel) {
        const modalHTML = $('#js-cancel-order-modal-tpl').html();
        var loadingModal = null;
        // generate the bid confirm modal with the entered value
        const modalContent = h.compile(modalHTML)(
            {
                'title': title,
                'txt': txt
            }
        );

        return _show('js-reject-modal', 'Modal--reject', modalContent, false, function (modal) {

                if (typeof onConfirm === 'function') {
                    $('#js-confirm-button').on('click', function () {
                        let $reasonVal = $('#reject-reason').val();

                        if(!$reasonVal) {
                            $('#reject-reason').css('border', '1px solid red');
                        } else {
                            loadingModal = window.UI.Modal.showLoading();
                            onConfirm();
                            modal.close();
                        }
                    });
                }

                if (typeof onCancel === 'function') {
                    $('.js-cancel-button').on('click', function () {
                        modal.close();
                        onCancel();
                    });
                }

            },
            function () {
                // ('Confirm modal closed');
                $('#js-confirm-button').off();
                $('#js-cancel-button').off();
            });

    };

    let _showAlertModal = function (txt, onConfirm) {

        const modalHTML = $('#js-alert-modal-tpl').html();

        // generate the bid confirm modal with the entered value
        const modalContent = h.compile(modalHTML, {noEscape: true})(
            {
                'txt': txt
            }
        );


        return _show('js-alert-modal', 'Modal--alert', modalContent, false, function (modal) {


                $('#js-confirm-button').on('click', function () {
                    modal.close();
                    if (typeof onConfirm === 'function') {
                        onConfirm();
                    }
                });

                $('.js-cancel-button').on('click', function () {
                    modal.close();
                });

            },
            function () {
                // ('Confirm modal closed');
                $('#js-confirm-button').off();
            });

    };

    let _showQuestionModal = function (ref, url, emptyMessage, successMessage, errorMessage) {

        var loadingModal = null;
        var messageModal = "";
        const modalHTML = $('#js-supplier-question-modal-tpl').html();

        // generate the bid confirm modal with the entered value
        const modalContent = h.compile(modalHTML)(
            {
                'ref': ref
            }
        );


        return _show('js-supplier-question-modal', 'Modal--alert Modal--question', modalContent, false, function (modal) {

                $('#js-send-button').on('click', function (event) {
                    event.preventDefault();
                    var $inputMessage = $('#supplier-question-subject');

                    var message = $inputMessage.val();

                    if (!message) {
                        messageModal = window.UI.Modal.alert(emptyMessage);
                    } else {
                        loadingModal = window.UI.Modal.showLoading();
                        $.ajax({
                            type: 'POST',
                            url: url,
                            data: $('#questionForm').serialize(),
                            success: function (response) {
                                loadingModal.close();
                                modal.close();
                                messageModal = window.UI.Modal.alert(successMessage, loadingModal.close());
                            },
                            error: function (response) {
                                loadingModal.close();
                                modal.close();
                                messageModal = window.UI.Modal.alert(errorMessage, loadingModal.close());
                            }
                        });
                    }
                    return false;
                });

                $('.js-cancel-button').on('click', function () {
                    modal.close();
                });

            },
            function () {
                // ('Confirm modal closed');
                $('#js-send-button').off();
            });

    };

    let _showOrderQuestionModal = function (url, emptyMessage, successMessage, errorMessage) {

        var loadingModal = null;
        var messageModal = "";
        const modalHTML = $('#js-supplier-order-question-modal-tpl').html();

        // generate the bid confirm modal with the entered value
        const modalContent = h.compile(modalHTML)();


        return _show('js-supplier-order-question-modal', 'Modal--alert Modal--question', modalContent, false, function (modal) {

                $('#js-question-order-send-button').on('click', function (event) {
                    event.preventDefault();
                    var $inputMessage = $('#supplier-order-question-subject');

                    var message = $inputMessage.val();

                    if (!message) {
                        messageModal = window.UI.Modal.alert(emptyMessage);
                    } else {
                        loadingModal = window.UI.Modal.showLoading();
                        $.ajax({
                            type: 'POST',
                            url: url,
                            data: $('#orderQuestionForm').serialize(),
                            success: function (response) {
                                loadingModal.close();
                                modal.close();
                                messageModal = window.UI.Modal.alert(successMessage, loadingModal.close());
                            },
                            error: function (response) {
                                loadingModal.close();
                                modal.close();
                                messageModal = window.UI.Modal.alert(errorMessage, loadingModal.close());
                            }
                        });
                    }
                    return false;
                });

                $('.js-cancel-button').on('click', function () {
                    modal.close();
                });

            },
            function () {
                // ('Confirm modal closed');
                $('#js-question-order-send-button').off();
            });

    };

    let _showReportModal = function (url, emptyMessage, successMessage, errorMessage) {

        var loadingModal = null;
        var messageModal = "";
        const modalHTML = $('#js-report-content-modal-tpl').html();

        // generate the bid confirm modal with the entered value
        const modalContent = h.compile(modalHTML)(
            {
                'url': url,
            }
        );

        return _show('js-report-content-modal', 'Modal--alert Modal--question', modalContent, false, function (modal) {

                $('#js-report-button').on('click', function (event) {
                    event.preventDefault();
                    var $inputMessage = $('#report-subject');

                    var message = $inputMessage.val();
                    if (!message) {
                        messageModal = window.UI.Modal.alert(emptyMessage);
                    } else {
                        loadingModal = window.UI.Modal.showLoading();
                        $.ajax({
                            type: 'POST',
                            url: url,
                            data: $('#reportContentForm').serialize(),
                            success: function (response) {
                                loadingModal.close();
                                modal.close();
                                messageModal = window.UI.Modal.alert(successMessage, loadingModal.close());
                            },
                            error: function (response) {
                                loadingModal.close();
                                modal.close();
                                messageModal = window.UI.Modal.alert(errorMessage, loadingModal.close());
                            }
                        });
                    }
                    return false;
                });

                $('.js-cancel-button').on('click', function () {
                    modal.close();
                });

            },
            function () {
                $('#js-report-button').off();
            });

    };

    let _showAskForQuotationModal = function (ref, url, emptyMessage, successMessage, errorMessage) {

        var loadingModal = null;
        var messageModal = "";
        const modalHTML = $('#js-ask-for-quotation-modal-tpl').html();

        // generate the bid confirm modal with the entered value
        const modalContent = h.compile(modalHTML)(
            {
                'ref': ref
            }
        );


        return _show('js-ask-for-quotation-modal', 'Modal--alert Modal--question', modalContent, false, function (modal) {

                $('#js-ask-quotation-send-button').on('click', function (event) {
                    event.preventDefault();
                    var $inputMessage = $('#quotation-question');
                    var message = $inputMessage.val();

                    var form = $(event.target).parents('form').get(0);
                    var formData = new FormData(form);

                    if (!message) {
                        messageModal = window.UI.Modal.alert(emptyMessage);
                    } else {
                        loadingModal = window.UI.Modal.showLoading();
                        $.ajax({
                            type: 'POST',
                            url: url,
                            data: formData,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (response) {
                                loadingModal.close();
                                modal.close();
                                messageModal = window.UI.Modal.alert(successMessage, loadingModal.close());
                            },
                            error: function ({responseJSON}) {
                                let message = '';
                                if (Array.isArray(responseJSON) && responseJSON.length > 0) {
                                    responseJSON.map(msg => message += `${msg} <br/>`)
                                } else {
                                    message = errorMessage;
                                }

                                loadingModal.close();
                                modal.close();
                                messageModal = window.UI.Modal.alert(message, loadingModal.close());
                            }
                        });
                    }
                    return false;
                });

                $('.js-cancel-button').on('click', function () {
                    modal.close();
                });

                if(typeof fileChooser.init === "function") {
                    fileChooser.init();
                }
            },
            function () {
                // ('Confirm modal closed');
                $('#js-send-button').off();
            });

    };



    let _askForMerchant = function ( url,emptySubject, emptyMerchant ,emptyMessage, successMessage, errorMessage) {

        var loadingModal = null;
        var messageModal = "";
        const modalHTML = $('#js-ask-for-merchant-modal-tpl').html();

        // generate the bid confirm modal with the entered value
        const modalContent = h.compile(modalHTML)(
            {
                'ref': ""
            }
        );




        return _show('js-ask-for-quotation-modal', 'Modal--alert Modal--question', modalContent, false, function (modal) {

                $('#js-ask-merchant-send-button').on('click', function (event) {
                    event.preventDefault();
                    let $inputMessage = $('#message-to-merchant');
                    let message = $inputMessage.val();

                    let $inputSubject = $('#subject');
                    let subject = $inputSubject.val();

                    let $inputMerchantId = $('#merchant');
                    let merchantId = $inputMerchantId.val();
                    let $inputMerchantName = $('#merchantName');
                    let merchantName = $inputMerchantName.val();
                    console.log(merchantId, message, subject)

                    var form = $(event.target).parents('form').get(0);
                    var formData = new FormData(form);


                    if (!message) {
                        messageModal = window.UI.Modal.alert(emptyMessage);
                    }
                    else if(!subject) {
                        window.UI.Modal.alert(emptySubject);
                    }
                    else if(!merchantId || ! merchantName) {
                        window.UI.Modal.alert(emptyMerchant);
                    }
                    else {
                        loadingModal = window.UI.Modal.showLoading();
                        $.ajax({
                            type: 'POST',
                            url: url,
                            data: formData,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (response) {
                                loadingModal.close();
                                modal.close();
                                messageModal = window.UI.Modal.alert(successMessage, function () {
                                    loadingModal = window.UI.Modal.showLoading();
                                    window.location.reload();
                                });
                            },
                            error: function ({responseJSON}) {
                                let message = '';
                                if (Array.isArray(responseJSON) && responseJSON.length > 0) {
                                    responseJSON.map(msg => message += `${msg} <br/>`)
                                } else {
                                    message = errorMessage;
                                }

                                loadingModal.close();
                                modal.close();
                                messageModal = window.UI.Modal.alert(message, loadingModal.close());
                            }
                        });
                    }
                    return false;
                });

                $('.js-cancel-button').on('click', function () {
                    modal.close();
                });

                if(typeof fileChooser.init === "function") {
                    fileChooser.init();
                }
            },
            function () {
                // ('Confirm modal closed');
                $('#js-send-button').off();
            });

    };




    // override CSS styles for small screen
    let overrideCSS = function (id) {
        var ratio = 0.8;
        var breakpointPx = 850;
        var winH = $(window).height();

        if (winH < breakpointPx) {
            var newModalH = Math.round(ratio * winH) + 'px';
            var smallScreenProps = {
                'max-height': newModalH,
                'overflow-y': id == 'js-loading-modal' ? 'visible' : 'auto'
            };
            // apply css properties
            $('.Modal-content').css(smallScreenProps);
            // remove useless margin/padding for small screens
            $('.Modal-content input').css('margin', '0');
            $('.Modal-content form').css('padding-top', '0');
        } else {
            var otherScreenProps = {
                'max-height': '90%',
                'overflow-y': id == 'js-loading-modal' ? 'visible' : 'auto',
            };
            $('.Modal-content').css(otherScreenProps);
        }h

        // override for login
        $('#js-login-modal').find('.Modal-content').css('max-height', '100%');
        $('#js-login-modal').find('.Modal-content form').css('padding-top', '20px');
    };

    let _onValidate = function (url, metaCartId, title, errorMessage, redirectUrl, token) {
        var loadingModal = null;
        confirmModal = window.UI.Modal.confirm("", title,
            function () {
                loadingModal = window.UI.Modal.showLoading();
                $.ajax({
                    type: 'POST',
                    url: url,
                    data: {
                        cartId: metaCartId,
                        _token: token
                    },
                    success: function () {
                        window.location.href = redirectUrl;
                        loadingModal.close();
                    },
                    error: function () {
                        loadingModal.close();
                        messageModal = window.UI.Modal.alert(errorMessage);
                    }
                });
            }, function() {
                this.confirmModal.close();
            });
    };

    let _onReject = function (url, metaCartId, title, errorMessage, redirectUrl, token) {
        rejectModal = window.UI.Modal.reject(title, "",
            function () {
                let $reasonVal = $('#reject-reason').val();
                $.ajax({
                    type: 'POST',
                    url: url,
                    data: {
                        cartId: metaCartId,
                        _token: token,
                        reason: $reasonVal
                    },
                    success: function () {
                        window.location.href = redirectUrl;
                    },
                    error: function () {
                        messageModal = window.UI.Modal.alert(errorMessage);
                    }
                });

            }, function() {
                rejectModal.close();
            });
    };

    let _onCancel = function (url, metaCartId, orderId, title, footer, errorMessage, redirectUrl, merchantOrderStatus, token) {
        cancelModal = window.UI.Modal.cancel(title, footer,
            function () {
                let $reasonVal = $('#reject-reason').val();
                $.ajax({
                    type: 'POST',
                    url: url,
                    data: {
                        cartId: metaCartId,
                        orderId: orderId,
                        _token: token,
                        reason: $reasonVal,
                        merchantOrderStatus:merchantOrderStatus
                    },
                    success: function () {
                        window.location.href = redirectUrl;
                    },
                    error: function () {
                        messageModal = window.UI.Modal.alert(errorMessage);
                    }
                });

            }, function() {
                this.cancelModal.close();
            });
    };

    let _onRemoveDelegation = function (url, username, title, errorMessage, redirectUrl, token) {
        confirmModal = window.UI.Modal.confirm(title, "",
            function () {
                let messageModal = null;
                $.ajax({
                    type: 'DELETE',
                    url: url,
                    data: {
                        'form[username]': username,
                        'form[_token]': token,
                    },
                    success: function (response) {
                        $('#delegation_'+username).remove();
                        if($('.delegations-table .delegations-items').children().length === 0 ) {
                            $('.delegations-table').css('display', 'none');
                            $('.empty-delegation').append(response);
                        }
                    },
                    error: function () {
                        messageModal = window.UI.Modal.alert(errorMessage);
                    }
                });

            }, function() {
                this.confirmModal.close();
            });
    };

    const _addToCartAlert = function (successMessage, merchantMinOrderMessage) {

        const modalHTML = $('#js-alert-modal-tpl-add-to-cart').html();

        const modalContent = h.compile(modalHTML)({
            successMessage,
            merchantMinOrderMessage
        });

        return _show('js-alert-modal-add-to-cart', 'Modal--alert', modalContent);
    };

    // exports

    module.exports = {
        show: _show,
        showLoading: _showLoading,
        confirm: _showConfirmModal,
        showCustomSearch: _showCustomSearchModal,
        showLogin: _showLogin,
        alert: _showAlertModal,
        hideLoading: _hideLoading,
        reject: _showRejectModal,
        cancel: _showCancelModal,
        supplierQuestion: _showQuestionModal,
        reportContent: _showReportModal,
        supplierOrderQuestion: _showOrderQuestionModal,
        askForQuotation: _showAskForQuotationModal,
        onValidate: _onValidate,
        onReject: _onReject,
        onCancel: _onCancel,
        confirmCountry: _showConfirmCountryModal,
        onRemoveDelegation: _onRemoveDelegation,
        addToCartAlert: _addToCartAlert,
        askForMerchant: _askForMerchant,

    };

    $(window).on('resize', function () {
        overrideCSS();
        //     var modal = $('.Modal-content');
        //     if($(window).height() < 450) {
        //         modal.css('height', $(window).height());
        //         if (!modal.hasClass('tiny-height')) {
        //             modal.addClass('tiny-height');
        //         }
        //     } else {
        //         modal.css('height', '');
        //         if (modal.hasClass('tiny-height')) {
        //             modal.removeClass('tiny-height');
        //         }
        //     }
    });

})(jQuery, Handlebars);
