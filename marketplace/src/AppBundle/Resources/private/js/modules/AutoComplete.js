(function ($) {
    'use strict';

    let createAutoComplete = function (options) {
        let path = options.path;
        let countryEl = $(options.countryId);
        let cityEl = $(options.cityId);
        let zipcodeId = options.zipcodeId;
        let cityField = options.cityField;

        if(countryEl.val()){
            addAutoComplete(path, countryEl, cityEl, zipcodeId, cityField);
        }
        countryEl.on('change', function () {
            if ($(this).val()) {
                addAutoComplete(path, countryEl, cityEl, zipcodeId, cityField);
            } else {
                cityEl.autocomplete("destroy");
                $('.ui-autocomplete-input').attr("autocomplete", "new-password");
                cityEl.removeData('autocomplete');
            }
        });

    };

    let addAutoComplete = function (path, countryEl, cityEl, zipcodeId, cityField) {
        let data = {};
        cityEl.on("keydown", function (event) {
            if (event.keyCode === $.ui.keyCode.TAB &&
                $(this).autocomplete("instance").menu.active) {
                $('.ui-autocomplete-input').attr("autocomplete", "new-password");
                event.preventDefault();
            }
        }).autocomplete({
            source: function (request, response) {
                data.term = request.term;
                data.country = countryEl.children('option:selected').val();
                $.ajax({
                    type: "POST",
                    url: path,
                    data: JSON.stringify(data),
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    success: function (data) {
                        response($.map(data, function (item) {

                            if (cityField) {
                                return {
                                    label: item.label,
                                    value: item.value,
                                    zipcode: item.zipcode
                                };   //  return object
                            } else {
                                return {
                                    label: item.label,
                                    value: item.zipcode,
                                    zipcode: item.value
                                };   //  return object
                            }
                        }))
                    },
                    error: function (msg) {

                    }
                })
            },
            minLength: 3,
            select: function (event, ui) {
                $(zipcodeId).val(ui.item.zipcode);
                $('.ui-autocomplete-input').attr("autocomplete", "new-password");
                // Trigger blur event so that the geoloc is updated
                $(zipcodeId).trigger('blur');
            },
        });
    };

    let updateDateValidity = function (options) {


        let inputToNotValidate = $('#methanation_site_form_weightbridgeDateValidity');

        if($('#methanation_site_form_weightbridge_0:checked').length) {
            inputToNotValidate.show();
            inputToNotValidate.prop('disabled', false);
            $('#dateValidity').toggle(true);
        } else {
            inputToNotValidate.hide();
            inputToNotValidate.prop('disabled', true);
            $('#dateValidity').toggle(false);
        }

    };

    let updatePumpTechnology = function (options) {

        if($('#methanation_site_form_pump_0:checked').length) {
            $('#pumpTechnology').toggle(true);
        }else{
            $('#pumpTechnology').toggle(false);
        }
    };

    module.exports = {
        createAutoComplete: createAutoComplete,
        updateDateValidity: updateDateValidity,
        updatePumpTechnology: updatePumpTechnology
    };



})(jQuery);
