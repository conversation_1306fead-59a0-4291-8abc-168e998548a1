(function ($) {
  'use strict';

  let $countryField;
  let $identField;
  let $identLabel;


  let updateIdentLabel = function () {
    const option = $countryField.find(':selected')[0];
    const label = option.dataset.label;
    const help = option.dataset.help;

    $identLabel.html(label);
    $identLabel.attr('title', help);

//    console.log(option);

    if (option.value !== '') {
      $identField.prop('readonly', false);
    } else {
        $identField.prop('readonly', true);
    }
  };

  // Init method
  let init = function (countrySelector, identSelector) {

    $countryField = $(countrySelector);
    $identField = $(identSelector);
    $identLabel = $identField.parent().find('label');

    $countryField.on('change', updateIdentLabel);

    updateIdentLabel();
  };

    let filterRegion = function(options){
        let countrySelector = $(options.countrySelector);
        countrySelector.change({regionSelector:options.regionSelector, countrySelector: options.countrySelector}, filterSelect);
        countrySelector.trigger("change");

    };

    let filterSelect = function(event){
        let country_code = "";
        let countrySelector = $(event.data.countrySelector);
        let selector = countrySelector.parent().find("div.select-wrapper__box").find('span');
        selector.each(function() {
            if($(this).hasClass('selected')) {
                country_code = $(this).data("country-code");
            }
        });
        let regionSelector = $(event.data.regionSelector);
        if(country_code !== "") {
            regionSelector.parent().find("div.select-wrapper__box").find('span').each(function () {
                if ($(this).data('country-code')) {
                    if ($(this).data('country-code') === country_code) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                } else {
                    $(this).hide();
                }
            });
        }

    };


  module.exports = {
    init: init,
    filterRegion: filterRegion,
  };


})(jQuery);
