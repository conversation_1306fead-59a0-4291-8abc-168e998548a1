{% trans_default_domain 'AppBundle' %}
<!DOCTYPE html>
<html lang="{{ app.request.locale|split('_')[0] }}">



    <head>
        <style>
            .error{
                width: 80%;
                margin: 50px auto;
                color: #6B6F82;
                font-family: 'Helvetica Neue', 'Arial', sans-serif;
            }

            .error-content{
                padding: 10px;
            }

            a {
                color: #DF0C36;
            }

            .home-link {
                margin-top: 50px;
            }

            .intro {
                border-bottom: 1px solid #666;
                display: flex;
                margin-bottom: 50px;
            }

            .intro p{
                font-size: 16px;
                color: #e64d5d;
            }

            .intro section{
                text-transform: uppercase;
                color: #DF0C36;
                font-size: 2.188rem;
                font-family: 'Helvetica Neue', 'Arial', sans-serif;
                line-height: 1em;
                font-weight: 400;
                margin-right: 50px;
            }

        </style>
    </head>


    <body>
    <div class="error">
        <div class="error-content">
            <div class="intro">
                <section>{% block title %}{% endblock %}</section>
                <p>{% block code %}{% endblock %}</p>
            </div>

            <div>
                {% block description %}{% endblock %}
            </div>

            {% block link %}
            <div class="home-link">
                <a href="{{ path("homepage") }}">{{ 'error.generic.home' | trans}}</a>
            </div>
            {% endblock %}

        </div>
    </div>

    </body>

</html>