{% extends 'OpenFrontBundle::base.html.twig' %}

{% block body %}
    <div class="Page-inner">

        {% if error %}
            <ul class="Messages">
                <li class="Messages-item Message--error">
                    {{ error.messageKey|trans(error.messageData, 'security') }}
                </li>
            </ul>
        {% endif %}

        <div class="Form LoginForm">

            <form class="form-signin" action="{{ path('buyer.login') }}" method="post" autocomplete="off">
                <input type="hidden" name="_csrf_token" value="{{ csrf_token('authenticate') }}">

                <div class="Page-header">
                    <h1 class="form-signin-heading">{{ 'operator.security.login.signin'|trans }}</h1>
                </div>

                <div class="form-group">
                    <label for="_username">Email address</label>
                    <input type="email" class="form-control" id="_username" name="_username"
                           value="{{ last_username }}" required="" autofocus=""
                           placeholder="{{ 'operator.security.login.email'|trans }}">
                </div>

                <div class="form-group">
                    <label for="_password">Password</label>
                    <input id="_password" name="_password" class="form-control"
                           placeholder="{{ 'operator.security.login.password'|trans }}" required="" type="password">
                </div>

                <button class="Button button_margin Button-primary" type="submit">{{ 'operator.security.login.submit'|trans }}</button>

                <a href="{{ path('send.user.password.reset') }}"
                   class="red">{{ 'operator.security.login.forgot'|trans }}</a>
            </form>
        </div>
    </div>
{% endblock %}