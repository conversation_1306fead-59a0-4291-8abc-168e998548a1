parameters:
services:
  _defaults:
    autowire: true
    autoconfigure: true
    public: false

  AppBundle\Security\Model\TotalAuthClient:
    autowire: true
    autoconfigure: true
    arguments:
      $httpClient: '@knpu.oauth2.http_client'
      $options: '%knpu.oauth2.client.total_buyer.options%'
      $collaborators: '@knpu.oauth2.client.collaborators'
    tags:
      - { name: knpu.oauth2.client, key: total_buyer }

  AppBundle\Security\Authenticator\DigitalPassAuthenticator:
    alias: AppBundle\Security\Authenticator\TotalAuthenticator

  AppBundle\:
    resource: '../../*'
    exclude: '../../{Entity,Tests}'

  AppBundle\Controller\:
    resource: '../../Controller'
    public: true
    tags: [ 'controller.service_arguments' ]

  Monolog\Formatter\LineFormatter:
    public: true
    arguments:
      - "[%%datetime%%] %%channel%%.%%level_name%%: %%message%% %%context%% %%extra%%\n"
      - 'Y-m-d H:i:s.u'

  Open\RabbitMQ\Queue:
    public: true
    arguments:
      - '%rabbit_host%'
      - '%rabbit_port%'
      - '%rabbit_user%'
      - '%rabbit_password%'
      - '%rabbit_queue%'

  Ddeboer\Vatin\Validator: '@ddeboer_vatin.vatin_validator'

  Illuminate\Encryption\Encrypter:
    arguments:
      $key: '%crypt_key%'

  AppBundle\Command\ImportEmailTemplatesCommand:
    arguments:
      - '%supported_locales%'
      - '%merchant_language%'

  AppBundle\Services\LanguageService:
    arguments:
      $codes: '%supported_locales%'

  AppBundle\Services\CurrencyService:
    arguments:
      $currencies: '%currencies%'

  AppBundle\Services\FacetService:
    arguments:
      $facetsOrder: '%facets_order_list%'
    calls:
      - method: setExcludedCustomAttributes
        arguments:
          - '%izberg_faceting_ignored_attributes%'

  AppBundle\Services\IzbergCustomAttributes:
    arguments:
      $customAttributes: '%izberg_custom_attributes%'
    calls:
      - [ setIgnoredAttributes, [ '%izberg_ignored_attributes%' ] ]

  AppBundle\Services\MerchantOrderService:
    class: AppBundle\Services\MerchantOrderService

  AppBundle\Services\CxmlExportService:
    public: true
    arguments:
      $sftpTimeout: '%sftp_timeout%'
      $cxmlBaseDirectory: '%cxml_basedir%'

  AppBundle\Services\ParameterService:
    public: true
    arguments:
      $locales: '%supported_locales%'
      $displayfirstLevelCategoryOnly: '%display_first_level_category_only%'

  AppBundle\EventSubscriber\ProcessEventSubscriber:
    class: AppBundle\EventSubscriber\ProcessEventSubscriber
    arguments:
      - '@logger.front'
      - '@logger.cron'
    tags:
      - { name: kernel.event_subscriber }

  AppBundle\EventListener\ForceLogoutListener:
    tags:
      - { name: kernel.event_listener, event: kernel.request, method: onKernelRequest }


  AppBundle\Services\MerchantService:
    arguments:
      $supportOperatorEmail: '%support_operator_email%'
      $middleBaseUrl: '%izberg_merchant_base_urls%'
    calls:
      - [ setCommonEmailProviders, [ '%common_email_providers%' ] ]

  AppBundle\Mapper\ElasticSearchMapper:
    public: true
    calls:
      - [ 'setStockMinimum', [ '%stock_minimum%' ] ]

  AppBundle\Services\Reporting\MerchantOfferReporter:
    arguments:
      $minAverageDescriptionLength: '%reporting_min_average_offer_description_length%'

  AppBundle\Services\CurrencyExchangeRateService:
    arguments:
      $supportedCurrencies: '%supported_currencies%'
      $marketPlaceCurrency: '%marketplace_currency%'

  AppBundle\Services\ElasticSearchService:
    arguments:
      $hostname: '%elastic_hostname%'
      $index: '%elastic_index%'
      $ssl: '%elastic_ssl%'
      $certificate_path: '%elastic_certificate_path%'
      $searchableFields: '%elastic_searchable_fields%'
      $facetsFields: '%elastic_facet_fields%'
      $epiFilterCategory: '%epi_filter_category%'

  AppBundle\Services\OfferService:
    arguments:
      $messageCategories: '%message_categories%'

  AppBundle\Services\QuoteService:
    calls:
      - [ 'setLogoPdf', [ '%quote_logo%' ] ]

  AppBundle\Services\MessageQuoteService:
    arguments:
      $izbergBugLinkDomain: '%domain%'
      $izbergBugLinkProtocol: '%bug_izberg_link_protocol%'
      $quoteEventChar: '%quote_event_char%'
      $quoteLinkPattern: '%quote_link_pattern%'

  AppBundle\Services\AddressService:
    arguments:
      $shippingAddressHeader: '%shipping_address_header%'

  AppBundle\Services\MarketPlaceService:
    public: true
    arguments:
      $marketPlaceBackOfficeUrls: '%izberg_merchant_base_urls%'

  AppBundle\Services\WebHelpIbanService:
    public: true
    arguments:
      $accountNameUsd: '%webhelp_iban_account_name_usd%'
      $ibanUsd: '%webhelp_iban_usd%'
      $accountNameEur: '%webhelp_iban_account_name_eur%'
      $ibanEur: '%webhelp_iban_eur%'

  AppBundle\Services\MailService:
    calls:
      - method: 'setBcc'
        arguments: [ '%mail_bcc%' ]
    arguments:
      $parameters: { mailer_from_email: '%mailer_from_email%', mailer_from_name: '%mailer_from_name%', force_locale: false }
      $absoluteUrl: '%domain%'
      $protocol: '%protocol%'
      $environment: '%mail_environment%'

  AppBundle\Services\EmailTemplateService:
    arguments:
      $configFile: '%kernel.project_dir%/config/emails.yaml'

  AppBundle\Provider\ExchangeRates:
    arguments:
      $logDir: '%kernel.logs_dir%'

  # Provide an override of the FOS user registration form
  front.registration.form.type:
    class: AppBundle\Form\Type\RegistrationFormType
    arguments:
      $merchantLanguage: '%merchant_language%'
      $captchaSecret: '%captcha_secret%'
      $captchaEnabled: '%captcha_enabled%'
    tags:
      - { name: form.type , alias: front_user_registration }

  # Provide a new form element (Select) for gender selection
  front.form.type.civ:
    class: AppBundle\Form\Type\CivType
    arguments:
      - '%civs%'
    tags:
      - { name: form.type }


  AppBundle\EventSubscriber\MarketPlaceSubscriber:
    autowire: true
    tags:
      - { name: kernel.event_subscriber }

  app.console_subscriber:
    class: AppBundle\EventSubscriber\ConsoleSubscriber
    tags:
      - { name: kernel.event_subscriber }

  app.doctrine_events_subscriber:
    class: AppBundle\EventListener\DoctrineEventListener
    arguments:
      $typesToBeChecked: '%historized_entities%'
      $entitiesPath: [ "AppBundle\\Entity", "Open\\TicketBundle\\Entity" ]
    tags:
      - { name: doctrine.event_listener, event: onFlush }

  AppBundle\Util\SettingsProvider:
    arguments:
      $sc: '%app_settings%'

  app.buyer_registration_subscriber:
    class: AppBundle\EventSubscriber\BuyerRegistrationSubscriber
    tags:
      - { name: kernel.event_subscriber }


  job_queue.listener:
    class: AppBundle\EventListener\JobQueueEventListener
    tags:
      - { name: kernel.event_listener , event: jms_job_queue.job_state_change , method: onStateChange }

  session.expired.listener:
    class: AppBundle\EventListener\SessionExpiredListener
    arguments:
      $maxIdleTime: '%session_maxidletime%'
    tags:
      - { name: kernel.event_listener, event: kernel.request, method: onKernelRequest }

  AppBundle\Services\Cache\CacheInterface:
    class: AppBundle\Services\Cache\CacheService
    arguments:
      $cacheDirectory: '%cache_directory%'
      $cacheExpiration: '%cache_expiration%'

  AppBundle\Services\CartService:
    class: AppBundle\Services\CartService
    arguments:
      $cartLifetimeDays: '%cart_lifetime_days%'

  AppBundle\Services\CustomsService:
    class: AppBundle\Services\CustomsService
    arguments:
      $riskCategories: '%risk_categories%'

  AppBundle\Services\SFTPService:
    class: AppBundle\Services\SFTPService
    arguments:
      $sftpParams: '%sftp_params%'
      $sftpEpsa: '%$sftp_epsa%'