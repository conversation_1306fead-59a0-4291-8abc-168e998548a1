#use this section for generic label (save, cancel, validate, yes, no...)
generic:
    unauthorized: "You are not authorized to perform this action"
    save: Save
    cancel: Cancel
    delete: Delete
    modify: Modify
    at: 'at'
    day: 'day'
    on: 'on'
offer:
    score: "Score : "
address:
    form:
        address: Address
        address2: 'Additional address details'
        address2_placeholder: 'Additional Information'
        all_country_placeholder: 'All Countries'
        check: Check
        city: City
        country: Country
        country_placeholder: 'Select a country'
        email: Email
        first_lastname: 'Last and First Name'
        phone1: 'Main Phone number'
        phone1_placeholder: 'Mandatory Phone number'
        phone2: 'Phone number'
        region: Region
        region_placeholder: 'Select a Region'
        update:
            error: 'An error occurred during the company update'
            success: 'The company information has been successfully updated'
        zipcode: 'Zip Code'
admin:
    logout: Logout
admin_menu:
    admin_users:
        add_user: 'Add a User'
        admin_list: Administrators
        label: Manage users
        user_list: Users
        user_enabler_list: Entities
        invoice_entity_thresholds: "Entities thresholds"
    companies_menu:
        all_companies: 'All Buyers'
        costs_centers: 'Cost Center'
        label: Buyers
        users: Users
    dashboard:
        label: Dashboard
    merchant_menu:
        label: Suppliers
    message: Messages
    messages_menu:
        add: 'Create a discussion'
        label: Messages
        list: 'Open Requests'
        resolved: 'Solved Requests'
    orders: Orders
    other_menu:
        feeback_form: 'Feedback form'
        label: Other
        notifications: Notifications
        search_historization:
            label: 'Search history'
        serches_list: 'Search list'
        slider: 'Slider object'
        term_payment: 'Term payment requests'
        top_mismatch_catalog_references: 'Top mismatched catalog references'
    redirects:
        label: Redirection
    sys_parameters:
        label: Settings
    web_content:
        add: 'Add web content'
        label: 'Web content'
        list: 'All web content'
        popular_offers: 'Popular deals'
algolia:
    attributes:
        BAB10_MOQ: 'Minimum of quantity'
        BAE10_Incoterm: 'Incoterm'
        DQL10_Friction_coef: 'Coefficient of friction'
        Threshold_2_Prcie: 'Floor price'
    id: 'Marketplace Id'
    merchant:
        name: 'Vendor'
    product:
        description: 'Description'
        manufacturer: 'Manufacturer'
        name: 'Product'
back:
    supplier:
        title: "Quotes | Supplier Backoffice"
    admin:
        filter:
            activeBtn: 'Active Administrators'
            inactiveBtn: 'Inactive Administrators'
    back: 'Back'
    back_to_list: 'Back to the list'
    catalogue_references:
        not_found_mismatch_references: 'Mismatched references not found'
        reference: 'reference'
        top_mismatch_references_title: 'Top mismatched references'
        total: 'total'
    commons:
        actions: Actions
        add: Add
        all: All
        cancel: Cancel
        delete: Delete
        disable: Deactivate
        edit: Edit
        export_csv: 'CSV Export'
        id: ID
        piggy: 'Login As'
        piggy_exit: 'Logout As'
        view: View
    company:
        all_term_payment: All
        all_term_payment_title: 'All companies whith term payments (validated or waiting for validation)'
        companyInfo:
            activation:
                ko: 'An error occurred during company activation'
                ok: 'Company is activated with success'
            address: Address
            addressComplement: 'Additional address details'
            billing:
                service: Service
                title: Billing
            city: City
            contact:
                adv: 'Sell administration contact'
                billing: 'Billing address'
                firstname: 'First Name'
                lastname: 'Last Name'
                logistic: 'Logistic contact'
                mail: Email
                main: 'Main Contact'
                noInformation: 'No Information'
                phone1: 'Main Phone number'
                phone2: 'Phone number'
            country: Country
            deactivate:
                content: 'Are you sure to want to disable this company ?'
                title: 'Company Deactivation'
            deactivation:
                ko: 'An error occurred during company deactivation'
                ok: 'Company is disabled with success'
            document:
                title: Document
            edit: Edit
            info:
                code: 'Company code'
                name: 'Company Name'
                title: 'Company Information'
            invalidate:
                ko: 'An error occurred while refusing the company'
                ok: 'Company is invalidate with success'
            region: Region
            reject:
                ko: 'Error during rejected company'
                ok: 'Company has been rejected with success'
            termpayment:
                active: 'Enable term payment'
                askKo: 'Error during enabled term payment'
                askOk: 'Term payment enabled'
                authorization: 'Term payment authorization'
                deny: 'Reject term payment'
                disabled: disabled
                enabled: enabled
                inactive: 'Disable term payment'
                pending: pending
                reason: 'Deny reason'
                removeKo: 'Term payment disabled'
                removeOk: 'Error during disabled term payment'
                title: 'Term payment Request Date'
            rejectedReason: Rejected reason
            deactivationReason: Deactivation reason
            validate:
                ko: 'An error occurred while validating the company'
                ok: 'Company is validate with success'
            zipCode: 'Zip Code'
        export: 'CSV Export'
        filter_clear: Clear
        filter_title: Filters
        list:
            add:
                site: 'Add a Cost Center'
                user: 'Add a User'
            category: Category
            city: City
            costCenters: 'Cost Centers'
            country: Country
            created: 'Account Creation'
            empty: 'No company waiting for validation'
            filter:
                activeBuyer: 'Active Buyers'
                all: 'All Companies'
                caMax: 'and :'
                caMin: 'CA between :'
                creationMax: 'and :'
                creationMin: 'Creation date between:'
                disabled: Deactivated
                purchaseMax: 'and :'
                purchaseMin: 'Order date between :'
                submit: Filter
                term_payment_date_max: 'and :'
                term_payment_date_min: 'Request/Acceptation between :'
                toConfirm: 'Pending Approval'
                tonMax: 'and :'
                tonMin: 'Tonnage between :'
            identification: Code
            last: 'Last Login'
            name: Company
            purchases: 'Purchases (USD)'
            revenue: 'CA généré'
            status: Status
            status_company:
                acceptable: Pending
                all: All
                disabled: Deactivated
                draft: Draft
                initial: Initial
                pending: 'Pending Validation'
                rejected: Rejected
                valid: Active
            termpayment_moneytransfert_accept: 'Accept term payments'
            termpayment_moneytransfert_date_accepted: Accepted
            termpayment_moneytransfert_date_requested: Rejected
            termpayment_moneytransfert_deny: 'Reject term payments'
            termpayment_moneytransfert_enabled: Active
            termpayment_moneytransfert_pending: Pending
            tonnes: 'Tons on the meter'
            type_:
                1: Buyer
                2: Vendor
                all: All
                buyer: Buyer
            users: Users
        menu:
            general: General
            messages: Messages
            orders: Orders
            sites: 'Cost Center'
            users: Users
        order:
            creation_date: 'Order date'
            number: 'Order number'
            shipping: 'Shipping address'
            price: 'Price'
            export: 'PDF export'
            status:
                title: 'Status'
                past: 'Past Orders'
                running: 'Running Orders'
                cancelled: 'Cancelled Orders'
        pending_term_payment: 'Pending'
        pending_term_payment_title: 'All companies wainting for term payments validation'
        term_payment_empty: 'No company waiting for term payment validation'
        termpayment_moneytransfert_accepted: 'You have accepted the term payments for this company'
        termpayment_moneytransfert_accepted_error: 'Error in the acceptance of term payments process for this company'
        termpayment_moneytransfert_rejected: 'You declined the term payments for this company'
        termpayment_moneytransfert_rejected_error: 'Error refusing term payments for this company'
        users:
            create_ticket: 'Create a ticket'
    invoice_entity:
        threshold:
            header: "Backoffice | Invoice Entities"
            status:
                all: "All"
                enabled: "Enabled"
                disabled: "Disabled"
        list:
            name: 'Invoice entity'
            country: 'Country'
        info:
            title: 'Invoice Entity Information'
            country: 'Country'
            address: 'Adress'
            name: 'Entity Name'
            threshold: "Seuil de validation N+1"
            action: "Mettre à jour le seuil de validation N+1"
        modal:
            title: "N+1 threshold validation update"
            text:
                thresholds: "Are you sure you want to update the N + 1 validation threshold for this entity?"
                validation_on: "Are you sure you want to enable N + 1 validation ?"
                validation_off: "Are you sure you want to disable N + 1 validation"
            actions:
                submit: "Save"
                cancel: "Cancel"
    deactivate:
        ok: Ok
        reason: 'Reason ?'
    index_page:
        companies_term_payment_request_to_validate: 'Companies waiting for term payment acceptance'
        companies_to_validate: 'Companies waiting for validation'
        see_all: 'See all'
        unread_messages: 'Unread messages'
    merchant:
        form:
            update:
                success: 'Vendor has been successfully updated'
                error: Error while updating the vendor
        list:
            name: Name
            identification: Identification
            email: Email
            firstname: First Name
            lastname: Last Name

            status:
                title: Status
                invitation: Invitation envoyée
                pending: Candidature réceptionnée
                accepted: Candidature acceptée
                account_creation: Formulaire de création de compte
                account_validation: Compte et catalogue finalisé
                rejected: Candidature refusée
                all: All
        merchantInfo:
            activities: activities
            buyer_igg: buyer igg
            buyer_firstname: buyer firstname
            buyer_lastname: buyer lastname
            country: Country
            currency:
                title: Currency
                placeholder: Choose a currency
                eur: EUR
                usd: USD
            edit: Edit
            email: Email
            firstname: Firstname
            identification: Identification
            info:
                title: Vendor Information
            lastModifiedBy: Last modified by
            lastModifiedAt: Last modified at
            registrationDate: Registration date
            lastname: Lastname
            name: Name
            password: Password
            phoneNumber: Phone number
            reject:
                ko: 'An error occurred while rejecting the vendor'
                ok: 'Vendor has been rejected with success'
            source: From
            status: Status
            rejectedReason: Rejected reason
            validate:
                ko: 'An error occurred while validating the vendor'
                ok: 'Vendor has been successfully updated'
            language: Language

    notification:
        edit:
            body: Body
            confirm: 'Notification has been updated'
            link: Link
            linkText: 'Text Button'
            slug: Identifier
            title: Title
            test: 'Test email address'
            send_button: 'Update and send test email'
            empty_email_error: 'Please, enter a valid email address'
        list:
            creation: 'Created on'
            header: 'Email Templates'
            lang: 'Available in'
            slug: Identifier
            title: Subject
            update: 'Updated on'
    page:
        add: 'Create a page'
        draft: Draft
        list:
            author: Owner
            creation: Created
            header: 'Static Pages'
            lang: Languages
            modal:
                title: 'Select a Language'
            slug: 'Permalink (Slug)'
            status: Status
            title: Title
            update: Updated
        modal:
            cancel_confirm: 'Are you sure you want to cancel the creation of this content ?'
        published: Published
    redirect:
        add: Redirect
        list:
            confirm_delete: 'Delete the redirection?'
            confirm_delete_title: Confirmation
            creation: Created
            destination: Destination
            header: Redirect
            origin: Origin
            type: Type
            update: Updated
    search_historization:
        date: Date
        datemax: ' to '
        datemin: 'Search date from : '
        filter_label: Filter
        id: id
        is_anonymous: 'Is Anonymous'
        list:
            filter:
                date: 'Filter by search date'
        nb_hits: 'Number of hits'
        searched_term: 'Searched Term'
    shipping_points:
        add: 'Add a new shipping address'
        delete: 'Delete Shipping Address'
        delete_card: Delete
        edit: 'Modify Shipping Address'
        edit_card: Edit
        error_add: 'An error occurred during the creation of the Shipping Address'
        error_edit: 'An error occurred during the modification of the Shipping Address'
        form:
            name: 'Name of Shipping Address'
            save: Save
        shipping_points: 'Shipping Addresses'
        success_add: 'Shipping Address Created'
        success_delete: 'Shipping Address Deleted'
        success_edit: 'Shipping Address Modified'
    site:
        activation:
            ko: 'An error occurred while activating the cost center'
            ok: 'Cost Center activated'
        add: 'Add a Cost Center'
        chargement:
            title: 'Loading Sites'
        deactivate:
            content: 'Deactivate the site?'
            title: 'Site Deactivation'
        deactivation:
            ko: 'Error while deactivating the cost center'
            ok: 'Cost Center deactivated'
        delete: Delete
        filter_clear: 'Clear'
        filter_title: Filters
        form:
            address: Address
            address2: 'Additional address details'
            city: City
            company: Company
            contact:
                email: Email
                firstname: 'First Name'
                function: 'Job Title'
                lastname: 'Last Name'
                main: 'Main Contact'
                other: Contact
                phone1: 'Main Phone number'
                phone2: 'Phone number'
            country: Country
            document:
                legalesDoc: 'Prefectural authorizations'
                title: Documents
            id: ID
            info: 'General Information'
            name: 'Cost Center'
            operatorInfos:
                canPack: 'Ability to pack for export'
                computer: 'Presence of a computer at the pack site'
                crusherType: 'Crusher type'
                device: Device
                sort: 'Sorting process'
                storageCapacity: 'Storage Capacity'
                title: 'Operator informations'
                tools: Tools
            region: Region
            siret: SIRET
            zipCode: 'Zip Code'
        infos: Information
        invalidate:
            ko: 'An error occurred while refusing the cost center'
            ok: 'Cost Center has been ???'
        list:
            add: 'Add a new site'
            address: Address
            city: City
            company: Company
            contactName: Contact
            contactPhone: 'Phone number'
            country: Country
            editName: 'Edit Name'
            filter:
                all: 'All Cost Centers'
                disabled: Deactivated
                submit: Filter
                toConfirmed: 'Pending Approval'
            name: 'Cost Center Name'
            nbShippingPoints: 'Number of Shipping Addresses'
            status: Status
            status_site:
                disabled: Deactivated
                enabled: Enabled
            zipCode: 'Zip Code'
        livraison:
            title: 'Cost Centers'
        modification:
            edit_name: 'Edit Cost Center Name'
            ko: 'An error occurred while modifying the cost center'
            ok: 'Cost Center has been updated'
        suppression:
            ok: 'Cost Center deleted'
        validate:
            error: 'Please validate the company before validating the cost center'
            ko: 'An error occurred while validating the cost center'
            ok: 'Cost Center added'
    slider:
        commons:
            add: Add
            addTitle: 'Add a new object to the slider'
            backgroundImage: 'Background Image'
            create: Create
            editTitle: 'Modify object on the slider'
            link: Link
            lock: Locked
            published: Published
            status: Status
            title: Title
            update: Edit
            order: Order
        create:
            confirm: 'Slider object added'
            error: 'An error occurred during object creation'
        delete:
            confirm: 'Slider object deleted'
            error: 'An error occurred during object deletion'
        edit:
            actualImage: 'Current Image'
            imageP: 'Select a new image to modify the existing one'
        list:
            actions: Actions
            createdAt: Created
            header: 'Slider object list'
            lang: Languages
            updatedAt: Modified
        modal:
            cancel_confirm: 'Are you sure you want to cancel the creation of this object ?'
        status:
            draft: Draft
            published: Published
        update:
            confirm: 'Slider object modified'
            error: 'An error occurred during object modification'
    specificity:
        subtitle: 'Jours de chargement possibles (laissez la ligne vide si la journée est indisponible)'
        title: 'Spécificités du site de chargement'
    ticket:
        filter:
            all: Tous
            closed: Closed
            company: Company
            creationMax: and
            creationMin: 'Created between'
            export_csv: 'Export en CSV'
            main_contact: 'Ticket creator'
            modificationMax: and
            modificationMin: 'Updated between'
            object: Subject
            opened: Open
            submit: Filter
            title: 'Thread list'
    user:
        connection:
            browser: Browser
            date: Date
            hour: Hour
            ip: IP
            os: OS
            title: 'Login History'
            type: Type
            version: Version
        deactivate:
            content: 'Deactivate the user?'
            title: 'User Deactivation'
        filter:
            activeBtn: 'Show all users'
            all: 'All Users'
            connectionMax: 'and :'
            connectionMin: 'Last Login between:'
            creationMax: 'and :'
            creationMin: 'Creation date between:'
            disabled: 'Deactivated Users'
            enabled: 'Active Users'
            filter_clear: 'Clear (or Delete)'
            filter_title: Filter
            inactiveBtn: 'Show Inactive users'
            toConfirmed: 'Confirm User'
        form:
            account: Account
            activate: Activate
            auto_activated: Activate
            activate_all: Activate selected
            activation:
                ko: 'An error occurred during user activation'
                ok: 'User has been activated'
            company: Company
            confirmPassword: 'Confirm new password'
            creation:
                ko: 'An error occurred during the user creation'
                mailKo: 'This email is already being used'
                mailKoDisabled: 'This email is already being used on an inactive account. You can reactivate that account.'
                ok: 'User has been created'
            deactivate: Deactivate
            desactivate_all: Deactivate selected
            deactivation:
                ko: 'An error occurred during user deactivation'
                ok: 'User has been deactivated'
            edit: Edit
            email: Email
            firstname: 'First Name'
            id: ID
            invalidate: Invalidate
            language: Language
            lastname: 'Last Name'
            modification:
                ko: 'An error occurred during user modification'
                ok: 'User has been updated'
            password: 'New password'
            phone1: 'Main Phone number'
            phone2: 'Phone number'
            reject: Reject
            resetPassword: 'Reset Password'
            resetingPassword:
                ko: 'An error occurred during reset password'
                ok: 'Reset password has been sent'
            role: Role
            sites: 'Cost Center'
            sitesUserKo: 'A user must be added to the Cost Center'
            status: Status
            validate: Validate
            cancel: Cancel
            superior: Superior
            delegates: Delegates
        function: 'Job Title'
        history:
            class:
                AppBundle\Entity\Address: Address
                AppBundle\Entity\Company: Company
                AppBundle\Entity\ComplementaryInformation: 'Complementary information'
                AppBundle\Entity\Contact: 'Contact information'
                AppBundle\Entity\Country: Country
                AppBundle\Entity\Document: Document
                AppBundle\Entity\NodeContent\mail: 'Content Template'
                AppBundle\Entity\NodeContent\page: 'Content Page'
                AppBundle\Entity\Node\mail: 'Email Template'
                AppBundle\Entity\Node\page: 'Static Page'
                AppBundle\Entity\Redirect: 'Set redirection rules'
                AppBundle\Entity\Region: Region
                AppBundle\Entity\Setting: Configuration
                AppBundle\Entity\Site: Site
                AppBundle\Entity\User: User
                AppBundle\Entity\ZipCode: 'Zip Code'
                Open\TicketBundle\Entity\Ticket: Ticket
                Open\TicketBundle\Entity\TicketMessage: 'Ticket Message'
            date: Date
            hour: Hour
            id: Identification
            modifications: Modifications
            new: New
            objet: 'Modified Subject'
            old: Old
            operation: Operation
            title: History
            type:
                create: Created
                delete: Delete
                update: Updated
            role: Role
        list:
            activeStatus: Active
            autoActiveStatus: Active (Automaticly)
            allStatus: All
            company: Company
            creation: 'Account Creation'
            disabled: Deactivation
            email: Email
            firstname: 'First Name'
            inactiveStatus: Inactive
            lastLogin: 'Last Login'
            lastname: 'Last Name'
            phone: 'Phone number'
            role: Role
            sites: 'Cost Center'
            status: Status
            enable: Enable
            disable: Disable
            igg: 'IGG'
            iggManager: 'Manager IGG'
            entity: 'Entity'
            service: 'Service'
            ActivationStatus: 'Activating status'
            delegate_to: 'Delegate to'
            delegate_usertype : 'Delegate type'
            delegate_source: 'Delegate origin'
            delegate_placeholder: 'Select a delegate'
            delegate:
                modal:
                    title: Delete delegation
                    text: 'Are you sure you want to delete this delegate ?'
                    actions:
                        submit: "Yes"
                        cancel: "Cancel"
                delegate_type:
                    all: All
                    admin: ADMIN
                    user: USER
        role:
            all: All
            main:
                ROLE_BUYER: Buyer
                ROLE_OPERATOR: Operator
                ROLE_SUPER_ADMIN: Administrator
            secondary:
                ROLE_BUYER: Buyer
                ROLE_OPERATOR: Operator
                ROLE_SUPER_ADMIN: Administrator
        change_status:
            confirm_title: 'User status confirmation'
            status_to: 'Change status of " + buyerString + " to:'
            empty_selection: 'Your selection are empty'
    user_enabler:
        form:
            button:
                activate_selected: Activate selected
                deactivate_selected: Disable selected
                activate_all: Activate all pages
                deactivate_all: Disable all pages
            disable:
                success:
                    Deactivation success
                error:
                    Error during the deactivation
            enable:
                success:
                    Activation success
                error:
                    Error during the activation
        list:
            entity_name: Entities
            site_name: Site
            threshold: "Threshold"
            threshold_status: "N+1 validation"
            status:
                header: Status
                all: All
                enabled: Active
                disabled: Inactive
            actions:
                header: "Actions"
            user_type:
                header: Type
                all: All
                j: Internal
                l: External
            filter:
                title: Filter
        change_status:
            confirm_title: 'Status confirmation'
            status_to: 'Change status to:'
            empty_selection: 'Your selection are empty'
        confirm:
            activate-all: 'Change confirmation'
            deactivate-all: 'Change confirmation'
            info: 'Change will be applied to all below pages'

buyerInvitation:
    onboarding_step: 'supplier account submitted'
    validated_by_operator_step: 'supplier account accepted by operator'
    rejected_by_operator_step: 'supplier account rejected by operator'
    activated_by_operator_step: 'supplier account activated'

buyerMenu:
    complete_profile: 'Complete your profile information'
    complete_profile_why: 'Complete the information below before purchasing products.'
cart:
    accept:
        error: 'An error occurred during the validation of the assignment.'
        title: 'Accept'
    add:
        error: 'An error occured while the product was added to your cart'
        success: 'Product successfully added to your cart'
        successUpdate: 'Product successfully updated on your cart'
    article: article
    articles: articles
    assign:
        assign_to: USER
        assign_to_myself: 'Assign cart to myself'
        comment: COMMENT
        error: 'An error occured during assignation.'
        modal_btn_cancel: Cancel
        modal_btn_confirm: Assign
        modal_title: 'Assign my cart'
        noUser: 'No user to assign cart'
        success: 'Cart correctly assigned.'
    buttons:
        assign: Assign
        checkout: 'Proceed to checkout'
        checkout_tooltip: "Merci de compléter les informations obligatoires avant de pouvoir valider le panier (champs obligatoires marqués d'un astérisque rouge)"
        clear_cart: 'Clear cart'
        continue_shopping: 'Continue shopping'
        save_in_wishlist: 'Save for later'
        see_assignment: 'See assignment history'
        validate: 'Validate'
    my_cart: my cart
    delivery:
        title: 'Delivery'
        standard_type: 'Standard delivery'
        fast_type: 'Fast delivery'
        none: 'The supplier "%merchant%" has not provided any delivery method, please remove the product(s) from your shopping cart to be able to order and inform the supplier.'
    checkout:
        add_new_address: 'Add a new address'
        add_new_cost_center: 'Add a new cost center'
        address: Address
        cost_center: 'Cost center'
        notReady: 'This feature is not available yet'
        payment_mode: 'Payment mode'
        validation_number: 'Buyer reference id'
        title: 'Proceed to checkout'
        select:
            cost_center_placeholder: 'Choose a cost center'
            address_placeholder: 'Choose an address'
            payment_mode_placeholder: 'Choose a payment mode'
            error: 'Please select a cost center, a delivery address and a payment mode'
    cost_center:
        error: 'An error occured while refreshing Cost Center.'
        noSiteId: 'Please select a cost center and an address'
    days: 'days'
    detail: 'Cart detail'
    details:
        back: 'Back to your shopping cart'
    empty:
        back: 'Back to Homepage'
        cart: 'Empty my cart'
        no_user_title: 'Cost center without user'
        text: 'Your Shopping Cart lives to serve. Give it purpose.</br>Continue shopping on the search page.'
        title: 'Your Shopping Cart is empty'
        user_list: 'No user avaible for this cost center.'
    fca_warning: 'For FCA products, see the vendor delivery address.'
    historic:
        date_assign: Assignment date
        user_who: User who assigned
        user_assigned: User assigned
        comment: Comment
    lostItem:
        single_removed: 'one offer is no longer available and has been removed from your cart'
        multiple_removed: '%number% offers are no longer available and have been removed from your cart'
    merchant_default_comment_placeholder: 'Please write a comment'
    offer_not_available: 'Unavailable'
    pending:
        ability_to_assign: 'ability to assign'
        ability_to_pay: 'ability to pay'
        amount_excl_taxes: 'Amount excl. taxes'
        cart_reference: 'Cart reference'
        creation_date: 'Creation date'
        last_comment: 'Last comment'
        me: Me
        now_assigned_to: 'Now assigned to'
        number_of_products: 'Number of products'
        number_of_sellers: 'Number of vendors'
        previous_assignments: 'Previous assignments'
        rejected: rejected
    reject:
        comment: 'Reasons for refusal'
        error: 'Error while rejecting cart'
        success: 'Cart rejected with success'
        title: Reject
    remove:
        error: 'An error occured while removing your product from your cart'
    select:
        placeholder: 'Please choose a cost center'
    shipping:
        address: 'shipping address'
        empty_list: 'No delivery address'
        sentence_empty_list: 'By default, your delivery address is your site address. If you wish to select another TOTAL address, this will be possible directly in your cart. However, if your need doesn’t concern a TOTAL address, we invite you to create your external address directly above and then select it in your cart.'
        recipient_name: 'Recipient''s name'
        recipient_tel: 'phone'
        recipient_comment: 'COMMENTS'
        shipping_date : 'Desired delivery date'
        modify: 'Modify'
        modifyTooltip: 'Modify this address'
        removeTooltip: 'Delete this address'
        cancel: 'Cancel'
        select: 'Select another shipping address'
        add_new_address: 'Add new address'
        add: 'Add'
    shipping_dpa: 'Delivery address for DPA products :'
    table_label:
        delivery: 'Delivery (day)'
        expected_date: 'Expected delivery date'
        delivery_time: 'Delivery time'
        no_vat: 'No VAT for this transaction'
        product_detail: 'Product Details'
        product_name: 'Product Name'
        quantity: Quantity
        subtotal: 'Subtotal (excl tax)'
        subtotal_vat: Sub-Total
        sub_total_without_vat: Subtotal (ET)
        supplier: 'Supplier'
        taxes: Taxes
        total: 'Total (ET)'
        price: 'Total price'
        total_vat: Total
        unit_price: 'Unit Price'
        validate: 'validate'
    total_is_over: 'Your cart cannot be over %amount% %currency% (ET)'
    treatment:
        cost: 'Cart treatment cost : '
        tooltip_text: 'Unbilled cost in pilot phase'
    update_quantity:
        error: 'An error occurred while changing the quantity.'
    validation:
        title: 'Confirmation'
        n1_text: 'Your N + 1 <span>%n1</span> or delegate received a notification to validate your order.'
        warned: 'You will be informed as soon as your order is validated.'
        direct_validation_text_1: '%n1, votre commande a bien été prise en compte.'
        direct_validation_text_2: 'Vous serez informé(e) dès que votre commande sera validée par le(s) fournisseur(s).'
    warning: 'Warning'
    fca_info: 'This product will be delivered by vendor at this address : '
    assignment_history: 'Assignment history'
    no_manager_active:
        line_1: 'You can not place an order on Click & Buy at the moment because the account of your manager is not activated.'
        line_2: 'Your account only allows you to validate the orders of your teams.'
        line_3: 'For more information, don''t hesitate to contact Click and Buy team.'
    recipient_mandatory: 'Please fill in the required fields'
category:
    all_categories_placeholder: 'All categories'
    office_supplies: 'Office supplies'
    hardware: 'IT Accessories'
    catering: 'Catering'
    event: 'Corporate events'
    intellectual_benefits: 'Intellectual services'
    communication_marketing: 'Communication & Marketing'
    gifts: 'Gifts'
    epi: 'Equipement de Protection Individuelle'
    lab_consumable: 'Laboratoire'
    industrial_supply: 'Industrial Supply'
    other: 'Others'
    visa: 'Visa'
cgu:
    cgu_accepted: 'cgu_accepted'
    read_cgu: Please read and accept the <a href="/en/cg" target="_blank"><span> Terms of Use, the Terms and Conditions and the Personal Data and Cookie Policy</span></a> to complete this first step.
    accept: 'Accept the terms and conditions of use'
    error: 'Error during the acceptance of general terms of use'
    errorMessage: 'Please validate the GTU before submitting your company'
    read: 'Read and accept the terms and conditions of use'
    validated: 'You have accepted <a href="%link%" target="_blank">GTU</a>.'
company:
    form:
        address: Address
        back: Back
        finish: Finish
        billingService: 'Department Name'
        billing_address:
            address: 'Billing Address'
            title: 'Billing Address'
            use_main: 'Use the company headquarter address'
        businessRegistration: 'Company identification'
        cgu: 'Terms of service'
        check: 'Billing address differs from the main address?'
        company_info: 'Company information'
        contact:
            add: 'Add a Contact'
            adv: 'Sales Administration Contact'
            billing: 'Billing Contract'
            check:
                adv: 'Check the box to enter a different ADV address'
                billing: 'Check the box to enter a different billing address'
                logistic: 'Check the box to enter a different logistic address'
            logistic: 'Logistics contact'
            logistic_subtitle: 'Add contacts for each site'
            main: 'Main Contact'
        contract: 'Send the signed contract'
        document:
            title: 'Add documents'
            titleReadOnly: Documents
        edit-site: 'Modify the Cost Center'
        ident_number:
            invalid: Invalid
        identification: 'Identification Number'
        info:
            cgu_not_accepted: 'You must accept termes of service on click <a href=%link% target=_blank>HERE</a>'
            incomplete: 'Company Information Incomplete'
        legal_documents: 'Legal Documents'
        main_contact:
            title: Address
        name: 'Legal Company Name'
        next: Next
        password:
            change: 'Change my password'
        profile: 'My Profile'
        save: Save
        service: Service
        siren: SIREN
        siren_placeholder: '(9 numbers)'
        social: Company
        submit: Submit
        submit_infos: 'Register your company'
        tax_rate: 'VAT rates'
        title: 'Administrative Company Information'
        type: Type
        update:
            error: 'An error occurred during the modification of the Shipping Address'
            success: 'Company Information updated'
company_catalog:
    import:
        total_matching_references: 'Number of imported matching references:'
        total_mismatching_references: 'Number of imported mismatching references:'
    import_in_progress: 'catalogue import is in progress'
    imported_references: 'imported references'
    instruction_title: 'Instructions :'
    instructions: 'You can upload your own product references onto the Marketplace. By doing so, it will give you possibility to see, search products under the given reference in your orders (only users from your company can see these references).</br></br>2 options to add your own references :</br>- On a product details page, press "My Own Reference" to add your own reference number/name to the selected product</br>- Import all your products references in the "My catalog" menu, using the .csv file template available <a href="%link%" target="_blank">here</a>.</br>Enter the manufacturer reference in the first column, and your own reference in the second column. Once done, upload your catalogue by clicking the upload button.</br></br>Once your file is uploaded, products can be searched under your own reference.</br></br>The catalogue is available for download at any time by clicking the "Export my own catalog" button.</br></br>The "Export missmatch references" button helps you to check the references added to "my catalog" which are not available on StationOne Marketplace.'
    add_in_catalog: 'Add my own reference'
    buyer_reference: 'Reference in my catalog'
    cancel: Cancel
    custom_search:
        title: 'Search in my catalog'
    delete: 'Delete my catalog'
    delete_confirm: 'Are you sure you want to delete your catalog?'
    delete_confirmed: 'Catalog deleted'
    delete_reference: Delete
    export: 'Export my catalog'
    export_mismatching: 'Export mismatching references'
    overwrite: 'Warning, upload a new catalog will overwrite the last one. Do you really want to continue ?'
    save: Save
    title: 'My Catalog'
    upload: 'Upload my catalog'
    upload_success: '%count% reference(s) added.'
    wrong_file_format: 'Wrong file format.'
comparaisonSheet:
    add:
        error: 'An error occured while the product was added to the comparator'
        itemAlreadyexist: 'The product is already in the comparator'
        maxItemError: 'You cant add product in the comparator. Max : %maxItem%'
        success: 'Product added to the comparator'
    page:
        author: Author
        company_name: 'Company name'
        date: 'Comparator sheet date'
        delivery: Delivery
        discount: Discount
        no_discount: 'no discount'
        price_excl_tax: 'Price excl. tax'
        product: Product
        title: 'Price Comparison Sheet'
        unit_price: 'Unit price'
        unit_price_vat: 'Unit price converted'
        comment: 'Comment'
    sticky: Comparator
    no_article: 'Your comparison sheet is empty'
    back_to_home: 'Back to homepage'
    export: 'Export'
    comment: 'Add a comment you want to see on the comparator sheet pdf : '
    information: 'This comparison is only valid at the time of generation of this pdf. StationOne does not commit to holding these price over time.'
contact:
    adv: 'Sales Administration Contact'
    form:
        email: Email
        firstname: 'First Name'
        function: 'Job Title'
        lastname: 'Last Name'
        phone1: 'Main Phone number'
        phone1_placeholder: 'Mandatory Phone number'
        phone2: 'Phone number'
    logistic: 'Logistics contact'
contactMerchant:
    form:
        error: 'An error occurend whlie sending message'
        message: Message
        object: Object
        save: Send
        success: 'Your message has been sent'
cost_center:
    name:
        first: 'Main Cost Center'
country:
    australia: Australia
    austria: Austria
    belgium: Belgium
    brasil: Brazil
    bulgaria: Bulgaria
    canada: Canada
    china: China
    cyprus: Cyprus
    czech_republic: 'Czech Republic'
    denmark: Denmark
    estonia: Estonia
    finland: Finland
    france: France
    germany: Germany
    greece: Greece
    hongkong: 'Hong Kong'
    hungary: Hungary
    ident:
        australia: 'A.C.N. - Australian Company Number'
        austria: 'intra-community VAT number'
        belgium: 'intra-community VAT number'
        brasil: 'CNPJ Number (N° de TVA)'
        canada: DUNS
        china: 'RNCN (reg number China)'
        cyprus: 'intra-community VAT number'
        czech_republic: 'intra-community VAT number'
        denmark: 'intra-community VAT number'
        estonia: 'intra-community VAT number'
        finland: 'intra-community VAT number'
        france: 'intra-community VAT number'
        germany: 'intra-community VAT number'
        greece: 'intra-community VAT number'
        hongkong: 'RNHK (reg number Hong Kong)'
        hungary: 'intra-community VAT number'
        india: 'CRO Number for EBSG or VAT number India'
        ireland: 'intra-community VAT number'
        italy: 'intra-community VAT number'
        latvia: 'intra-community VAT number'
        lithuania: 'intra-community VAT number'
        luxembourg: 'intra-community VAT number'
        mexico: 'RFC (Mexico)'
        morocco: 'RNMA (reg number Morocco)'
        netherlands: 'intra-community VAT number'
        norway: 'intra-community VAT number'
        poland: 'intra-community VAT number'
        portugal: 'intra-community VAT number'
        russia: 'OGRN (ident RU)'
        singapore: 'CRO number or CRO Number for EBSG'
        spain: 'intra-community VAT number'
        sweden: 'intra-community VAT number'
        switzerland: 'VAT Identification Number'
        taiwan: 'RNTW (company registration number- reg number Taiwan)'
        thailand: 'CRO or ROC number'
        tunisia: 'RNTN (RN Tunisia)'
        turkey: 'TAXTR (tax identifier for Turkey)'
        united_kingdom: 'intra-community VAT number'
        united_states: 'DUNS Number'
        bulgaria: 'intra-community VAT number'
        malta: 'intra-community VAT number'
        slovak_republic: 'intra-community VAT number'
        slovenia: 'intra-community VAT number'
        croatia: 'National identification number'
        israel: 'National identification number'
        indonesia: 'National identification number'
        chile: 'National identification number'
        romania: 'National identification number'
        south_africa: 'National identification number'
        ukraine: 'National identification number'
    ident_helper:
        australia: '9 numbers'
        austria: 'Use intra-community VAT number format'
        belgium: 'Use intra-community VAT number format'
        brasil: '14 numbers'
        canada: '9 numbers'
        china: '18 numbers / letters maximum'
        cyprus: 'Use intra-community VAT number format'
        czech_republic: 'Use intra-community VAT number format'
        denmark: 'Use intra-community VAT number format'
        estonia: 'Use intra-community VAT number format'
        finland: 'Use intra-community VAT number format'
        france: 'Use intra-community VAT number format'
        germany: 'Use intra-community VAT number format'
        greece: 'Use intra-community VAT number format'
        hongkong: '4 - 8 numbers'
        hungary: 'Use intra-community VAT number format'
        india: '2 numbers & 13 characters'
        ireland: 'Use intra-community VAT number format'
        italy: 'Use intra-community VAT number format'
        latvia: 'Use intra-community VAT number format'
        lithuania: 'Use intra-community VAT number format'
        luxembourg: 'Use intra-community VAT number format'
        mexico: '12 - 13 characters'
        morocco: '35 characters maximum'
        netherlands: 'Use intra-community VAT number format'
        norway: 'Use intra-community VAT number format'
        poland: 'Use intra-community VAT number format'
        portugal: 'Use intra-community VAT number format'
        russia: '13 - 15 numbers'
        singapore: '9 numbers maximum & 1 letter'
        spain: 'Use intra-community VAT number format'
        sweden: 'Use intra-community VAT number format'
        switzerland: 'CHE-XXX.XXX.XXX'
        taiwan: '8 numbers'
        thailand: '13 numbers'
        tunisia: '10 - 11 characters'
        turkey: 'Use intra-community VAT number format'
        united_kingdom: 'Use intra-community VAT number format'
        united_states: '9 numbers'
        bulgaria: 'Use intra-community VAT number format'
        malta: 'Use intra-community VAT number format'
        slovak_republic: 'Use intra-community VAT number format'
        slovenia: 'Use intra-community VAT number format'
        croatia: 'National identification number'
        israel: 'National identification number'
        indonesia: 'National identification number'
        chile: 'National identification number'
        romania: 'National identification number'
        south_africa: 'National identification number'
        ukraine: 'National identification number'
    india: India
    ireland: Ireland
    italy: Italy
    latvia: Latvia
    lithuania: Lithuania
    luxembourg: Luxembourg
    malta: Malta
    mexico: Mexico
    morocco: Morocco
    netherlands: Netherlands
    norway: Norway
    poland: Poland
    portugal: Portugal
    region:
        CA-AB: Alberta
        CA-BC: 'British Columbia'
        CA-MB: Manitoba
        CA-NB: 'New Brunswick'
        CA-NL: 'Newfoundland and Labrador'
        CA-NS: 'Nova Scotia'
        CA-NT: 'Northwest Territories'
        CA-NU: Nunavut
        CA-ON: Ontario
        CA-PE: 'Prince Edward Island'
        CA-QC: Quebec
        CA-SK: Saskatchewan
        CA-YT: Yukon
        FR-ARA: Auvergne-Rhône-Alpes
        FR-BFC: Bourgogne-Franche-Comté
        FR-BRE: Brittany
        FR-COR: Corsica
        FR-CVL: 'Centre-Val de Loire'
        FR-GES: 'Grand Est'
        FR-GF: 'French Guiana'
        FR-GP: Guadeloupe
        FR-HDF: Hauts-de-France
        FR-IDF: 'Paris Region'
        FR-MQ: Martinique
        FR-NAQ: Nouvelle-Aquitaine
        FR-NOR: Normandy
        FR-OCC: Occitanie
        FR-PAC: 'Provence-Alpes-Côte dAzur'
        FR-PDL: 'Pays de la Loire'
        FR-RE: Réunion
        FR-YT: Mayotte
    russia: Russia
    singapore: Singapore
    switzerland: Switzerland
    slovak_republic: 'Slovak republic'
    slovenia: Slovenia
    spain: Spain
    sweden: Sweden
    taiwan: Taiwan
    thailand: Thailand
    tunisia: Tunisia
    turkey: Turkey
    united_kingdom: 'United Kingdom'
    united_states: 'Estados-Unidos'
    croatia: Croatia
    israel: Israel
    indonesia: Indonesia
    chile: Chile
    romania: Romania
    south_africa: 'South Africa'
    ukraine: ukraine
customs:
    info:
        domestic: Domestic
        export_EU: 'Export EU'
        export_non_EU: Export
default:
    placeholder: Undetermined
dispute:
    create:
        ko: 'An error occured during the sending of your dispute'
        ok: 'Your dispute has been sent'
    form:
        message: Comments
        new: 'New dispute'
        placeholder: 'Explain here issues faced with products selected'
        products: 'Select products with issues'
        see: 'See all disputes'
        subject: Subject
        table:
            all: All
            expected_date: 'Expected date'
            product_name: 'Product name'
            quantity: Quantity
            reference: Reference
            total_price: 'Total price'
            unit_price: 'Unit price'
        title: Dispute
    list:
        creation_date: 'Creation date'
        id: Id
        messages: Messages
        read: Read
        receiver: Receiver
        subject: Subject
        unread: Unread
document:
    contract:
        download: 'Download here pre-filled contract'
    noDoc: 'No file -- Add here an official document proving the existence of your company'
    upload:
        delete: 'Would you like to delete this file?'
        deleteError: 'An error occurred while deleting the file'
        error: 'Please upload at least 1 document'
        ignored: '1 or more files have been ignored??'
        ko: 'An error occurred during upload'
        mime: 'The document type should include: %contrainte%'
        ok: 'Document uploaded'
        size: 'The document should not exceed %contrainte% Mo'
        title: Select
        type: 'Invalid file'
        typeOrSizeError: 'An error occurred while updating the file. Please verify the document.'
        working: 'In progress'
error:
    forbidden:
        code: '(Error 403)'
        line1: "Votre session a peut-être expirée. Dans ce cas, cliquez sur le lien ci-dessous pour vous reconnecter :"
        description: "Vous n'avez peut-être pas encore accès à Click & Buy. Click & Buy est en cours de déploiement sur la France (2020)."
        description_extra: "En revanche, si votre entité d'appartenance a été déployée, merci de contacter WeCare."
        title: '403 : UNAUTHORIZED ACCESS'
    generic:
        help: 'The following links may help:'
        home: 'Back to Home Page'
        support: 'Contact the Support Team'
    internal:
        code: '(Error 500)'
        description: 'An error occurred during the progress of the request.'
        title: 'An error occurred'
    notfound:
        code: '(Error 404)'
        description: 'Page requested does not exist.'
        title: 'Page not found'
        merchant_order_not_found: "This command is not available in your country"
    zipCode: 'Zip Code should contain 5 numbers'
filter:
    all: All
    no: 'No'
    yes: 'Yes'
flag: '#icon-flag-en'
header:
    back: 'back'
    categories: 'categories'
    purchase_order: 'Purchase order'
    company: 'Company'
    tva_identification_number: 'TVA number'
    address: 'Address'
    site: 'Cost center'
    buyer_id: 'Buyer ID'
    vendor_id: 'Vendor ID'
    vendor_ref: 'Vendor''s ref'
    delivery_delay: 'Estimated delivery date'
    incoterm: 'Incoterm'
    label_en: 'English'
    label_fr: 'French'
    label_es: 'Spanish'
    label_de: 'Deutsch'
    label_it: 'Italian'
    label_france: 'France'
    label_belgium: 'Belgium'
    label_united_states: 'United State'
    delivery_country:
        title: 'WARNING'
        description: 'If you switch country of delivery, your cart will be emptied because the catalog is depending on the country. Do you want to continue?'
footer:
    cgu: 'terms and conditions'
    connexion: 'Connection'
    cgu_connected: 'Terms of Service'
    all_right_reserved: 'All rights reserved'
    invite_suppplier: 'Invite a supplier'
    report_content: 'Report a content'
    about_us:
        code_of_conduct: 'Code of conduct'
        company: 'The company'
        join_us: 'Join us'
        data_privacy_chart: 'Data Privacy Charter'
        cookies: 'Cookies'
        legal_notice: 'Legal notice'
        our_mission: 'Our mission'
        title: 'About us'
    additional:
        purchase_conditions: 'Purchase conditions according to General Terms of Use of Station One and then to the sales conditions of each vendor.'
        copyright: '© TotalEnergies  - 2019'
    buy:
        benefits_for_buyers: 'Benefits for buyers'
        create_an_account: 'Create an account'
        general_terms_and_conditions: 'General Terms of Use'
        title: 'Buy on StationOne'
    contact: 'contacter we care'
    faq: 'Frequently Asked Questions'
    follow_us:
        title: 'Follow us'
    help:
        contact_us: 'Contact'
        illegal_content: 'Illegal content alert'
        questions: 'Q&A'
        title: 'Need help ?'
    mentions: 'Legal notice'
    press:
        news: News
        press_releases: 'Press releases'
        title: Press
    sell:
        benefits_for_sellers: 'Benefits for vendors'
        create_an_account: 'Create an account'
        general_terms_and_conditions: 'General Terms of Use'
    total: 'total'
    visit:
        address_1: '69-73 Boulevard Victor Hugo'
        address_2: '93400 Saint-Ouen'
        address_3: 'FRANCE'
        title: 'Visit us'
    yammer: 'follow us on yammer'
form:
    invoice:
        search: 'Search'
    order:
        search: 'Search'
    user:
        add: 'Add a User'
        delete:
            content: 'Would you like to delete this user ?'
            title: 'Delete a User'
        email: Email
        error_update: 'An error occurred while updating the user. Please, check the entered data'
        firstname: 'First Name'
        function: 'Job Title'
        invalid_role: 'Invalid Role'
        lastname: 'Last Name'
        myself: Myself
        role:
            admin: Account manager
            buy: Requestor
            pay: 'Buyer'
            placeholder: 'Select a Role'
            sell: Order
            view: View
        roles: Profile
        save_edit: Update
        save_new: Create
        sites:
            label: 'Cost Center:'
            mandatory: 'Select at least one Cost Center'
        success_new: 'User has been created'
        success_update: 'User has been updated'
        title: 'List of Users'
        title_common: User
        title_edit: Edit
        title_new: Add
        definition:
            requestor: 'Requestor'
            requestor_definition: 'Select products, prepare orders and send them for approval to the buyer'
            buyer: 'Buyer'
            buyer_definition: 'Requestor rights + Orders approval for his cost centers'
            account_manager: 'Account Manager'
            account_manager_definition: 'Buyer rights + Global account administration'
home:
    main_categories_title:  'Main categories'
    introduction: 'The new TOTAL platform dedicated to TOTAL group purchases'
    dashboard_admin: 'Administrator Dashboard'
    merchant_prompting: 'Become a supplier on Total Market'
    description: 'Cum Homerici cum Roma nobilium'
    disclaimer:
        market:
            line1: 'More opportunities'
            line2: 'Plus de gisements'
            line3: 'Market Price'
            title: 'Market access'
        process:
            line1: 'Time saving'
            line2: 'Reporting and dashboard'
            line3: 'Lettre de voiture digitale'
            title: 'An efficient process'
        transactions:
            line1: 'Transparent (photos & characterization)'
            line2: 'Audit of vendors & buyers'
            line3: 'Inviting sales contract'
            title: 'Secured transaction'
        video: 'Discover on video'
    grande_distribution: 'Large retailers'
    insert:
        all_news: 'All news'
        contact: Contact
        savoir: 'See more'
        sustainability_description: 'Meeting todays needs without compromising tomorrow'
        title: FAQ
        title_duree: Sustainability
        title_news: 'Latest news'
    last_orders_title: 'last ordered products'
    login: 'Login buyer/vendor'
    login_admin: 'Administrator Login / Operator'
    logout: Logout
    register: 'I create my account'
    register_text: 'Whether you are a local or international player, easily join the referenced supplier community at Total and offer your products and services on an innovative platform !'
    register_buyer: Subscription
    register_merchant: 'Vendor registration'
    register_merchant_confirmation_message: 'You have just taken your first steps towards Click & Buy.'
    register_merchant_confirmation_subtitle: ''
    register_merchant_confirmation_title: Congratulations
    register_merchant_confirmation_text_1: 'Your supplier account has been taken into account. Our teams study the first information transmitted within a maximum of 48 hours.'
    register_merchant_confirmation_text_2: 'You will receive an email indicating the procedure to follow.'
    register_merchant_confirmation_url: 'Your registration request has been successfully submitted and will be reviewed'
    register_merchant_error: 'An error occurred during your subscription. Please contact the support team.'
    register_merchant_success: 'Your registration was successful.'
    register_merchant_tva_not_checked: "The identification number has not been checked for this vendor"
    slider:
        subtitle: 'Fruticeta prona valido inmanium fortiter'
        title: 'Cum Homerici cum Roma nobilium'
    start_buy: 'Start purchasing (or buying)?'
    start_sale: 'Start selling'
    ticket_admin: 'Ticket List (administrators)'
    ticket_anonymous: 'Create a ticket anonymously'
    ticket_buyer: 'List of Tickets'
    title: Click & Buy - Home
    video_src: MECmgIz36nU
    why_sell:
        visibility: Visibility
        visibility_text: 'Your products are visible to all TOTAL employees'
        efficiency: Efficiency
        efficiency_text: 'Your orders directly accessible on supplier interface'
        simple: Simplicity
        simple_text: 'A unique and simplified referencing. Your catalogs loaded in a few clicks'
        title: 'Why register on Total Market ?'
    group_presentation:
        text: Presents in more than <span class='bold red'>130 countries</span>, our <span class='bold red'>100,000</span> employees are committed to better, safer, more affordable, cleaner and more accessible energy for all
        hydrocarbures: Productions d'hydrocarbures
        collaborateurs: Collaborateurs
        stations_service: Stations-service
        sites_raffinage: Sites de Raffinage Pétrochimie

illegal_content:
    form:
        ko: 'An error occurred while sending your illegal content'
        ok: 'Your request has been sent successfully'
        save: Send
        title: 'Describe the illegal content that you detected'
import:
    csv:
        invalid_separator: 'list separator not recognized and/or wrong column count'
        no_error: 'Import OK'
        not_all_lines_were_imported: 'Not all the lines were imported'
        not_enough_lines: 'Not enough line in this file'
invitation:
    flash:
        ok: Action exécutée.
    source:
        operator: "Opérateur C&B"
    status:
        invitation: 1 - Invitation envoyée
        pending: 2 - Candidature réceptionnée
        accepted: 3 - Candidature acceptée
        account_creation: Formulaire de création de compte
        account_validation: 4 - Compte et catalogue finalisé
        rejected: Candidature refusée
    list:
        my_invitation:
            title: Mes invitations en cours
        all_invitation:
            title: Invitations click & buy et mes invitations acceptées
        title: Invitations fournisseurs
        block:
            supplier: Fournisseur
            email: Email
            date_title: Date de l'invitation
            status: Status
            src: Source
        resend:
            link:
                tooltip: Send the invitation again
                label: Send again
        empty: 'Cette liste est vide'
        search_table: Search for a supplier of the marketplace
invoice:
    detail:
        already_invoiced: 'Already invoiced :'
        credit_note: 'Credit note'
        go_back: 'Back to order ID : '
        including_taxes: 'incl. taxes'
        invoice: Invoice
        invoice_amount: Amount
        invoice_date: Date
        invoice_due_date: 'Due Date'
        invoice_payment: Payment
        not_paid: 'Not paid'
        not_yet_invoiced: 'Not yet invoiced :'
        order_amount: 'Total :'
        order_date: 'Date :'
        order_id: 'Sub-order Id :'
        order_payment_status: 'Payment status :'
        order_seller: 'Vendor :'
        order_status: 'Status sub-order :'
        paid: 'Paid'
        payment: Payment
        refund: Refund
        remaining_to_pay: 'Remaining amount to pay'
        title: Invoices
        total: Total
        total_invoiced: 'Total invoiced and credit notes :'
        total_remaining_to_pay: 'Total remaining amount to pay :'
        total_paid: 'Total paid :'
    list:
        amount: Amount
        date: 'Date'
        due_date: 'Due date'
        due_date_total_eur: 'To pay this month (€):'
        due_date_total_usd: 'To pay this month ($):'
        invoice: Invoice / Credit note
        late_payment_eur: 'Late payment (€):'
        late_payment_usd: 'Late payment ($):'
        order: 'Order'
        paid_date: 'Paid date'
        remain: Remain
        seller: Vendor
        tab:
            empty: 'You have no invoices in this section.'
            not_paid: 'Not paid invoices'
            paid: 'Paid invoices'
        title: Invoices
        total_to_pay_eur: 'Total invoice(s) received (€):'
        total_to_pay_usd: 'Total invoice(s) received ($):'
    seeInvoices: 'Invoices and payments'
    reminder: 'Invoice reminder'
label_next: Next
label_previous: Previous
login:
    companyError: 'Company has been deactivated'
    userCreatedButCompanyError: 'Your account submission has been sent successfully. It will be active when approved by the StationOne team'
    izbergBuyerError: 'Your account authentication fail'
logout:
    text1: 'You are disconnected from Click & Buy but you are still connected with Digital P@ss.'
    text2: 'If you re-open Click <span>&</ span> Buy, you will be automatically reconnected. To reconnect click here: '
    link_home: 'Re-connect'
    link: 'Logout'
    text3: 'If you are not on your own computer, click here to log out of Digital P@ss: '
main_menu:
    account: 'My Account'
    buyer:
        mybids: 'My bids'
        myorders: 'My orders'
    categories: Categories
    products: 'Products'
    concept: Concept
    contact: Contact
    dashboard: Dashboard
    eur_cart: 'EUR CART'
    explore_categories: 'Search Categories'
    faq: FAQ
    help: Help
    contact_us: 'Contact Us'
    languages: Languages
    locked: 'Please complete the previous step to proceed.'
    merchant:
        myoffers: 'My deals'
        mysales: 'My sales'
        offers:
            active: Active
            draft: Drafts
            nosale: 'Without translation'
            pending: 'Pending Validation'
    messages: Messages
    offers: 'See all deals'
    join_us: 'Join us'
    signin: Login
    terms: 'Terms & Conditions'
    usd_cart: 'USD CART'
menu:
    desktop:
        my_account: 'My account'
        command: 'Orders'
        orders_to_validate: 'Orders to validate'
        news: 'News'
        company: Company
        document: Documents
        invoices: Invoices
        messages: 'Messages'
        my_catalog: 'My catalog'
        orders: Orders
        payment_modes: 'Payment modes'
        pending_carts: 'Pending carts'
        profile: Profile
        quotes: 'My quote requests'
        sites: 'Cost Centers'
        users: Users
        wishlist: 'Wish List'
        shipping_address: 'My shipping addresses'
        stats: Statistics
        my_favorits: 'My favourites'
merchant:
    adapted_company: 'Company of the adapted sector'
    name: Vendor
    invite:
        success_message: 'Your invitation has been sent '
message:
    operator: 'Marketplace Operator'
    empty_list: You have no message
    add:
        success: Your message has been sent, you can find it in the 'Messages' menu of your profile
        error: 'Problem sending your message'
    attachment_limit: "La taille d'un document ne doit pas excéder 1,8MO et l'ensemble de toutes vos pièces jointes ne doit pas dépasser 5MO et %limit% fichiers."
    authorized_types: '(Fichiers autorisés : pdf, jpeg, gif, png, tiff, zip, doc, docx, xls, xlsx).'
    tips: "Astuce : n'oubliez pas de zipper vos documents et n'hésitez pas à les couper dans le cas de documents de taille importante."
    file_too_big: 'The selected files must not exceed %size-limit%'
    file_type_incorrect: "Only files with these extensions are authorized : pdf, jpeg, gif, png, tiff, zip, doc, docx, xls, xlsx"
    go_back: 'Back to the list of discussions'
    labels:
        id: 'Identifier'
        creation_date: 'Creation date'
        last_thread: 'Last message'
        supplier_name: 'Supplier'
        subject: 'Subject'
        threads_count: 'Messages'
        date: 'Date'
        sender: 'Sender'
        content: 'Content'
        attached_file: 'Attached files'
        new: 'new message'
        send: 'send'
        placeholder: 'Your message here'
        add_file: 'Add a file'
        thread_title: 'Thread'
    list:
        title: 'Messages'
    status:
        status_1: 'open'
        status_2: 'closed'
    empty:
        open: 'You have no open message'
        closed: 'You have no closed message'
    modal:
        empty: 'Your message is empty'
    object:
        order: 'Request about order '
        offer: 'Request about your product/service '
        quotation: 'Quotation request for your product/service '
        illicitproduct: ' for offer %product% from supplier %supplier%'
        illicitpage: ' for page %page%'
    body:
        illicit: 'Report type: %body%'
claim:
    order_item_list:
        title: 'Order claim %reference% (amount %amount% %currency%, date %date%)'
        info: 'Products concerned'
        all: 'all'
        reference: 'Reference'
        name: 'Name'
        unit_price: 'Unit price HT'
        quantity: 'Quantity'
        total_price: 'Total price HT'
    choices:
        product_never_received: ' Products never received '
        product_broken: ' Products received broken '
        general: ' General dissatisfaction '
        question: ' Question '
        other: ' Other '
    attachment: 'attach file'
    validate: 'Validate'
    thread:
        claim: 'Claim about order '
        content_title: 'Products concerned by claim :'
        button: 'Validate'
        subject: 'Subject'
        comment: 'Comment'
modal:
    add: add
    validate: Validate
    cancel: cancel
    confirm: Ok
    'no': 'No'
    'yes': 'Yes'
    send: send
    placeholder: Your question...
    internal_reference: 'Internal Reference'
    placeholder_product_identifiant: "Question about the product with the Click & Buy identifier : "
    placeholder_sku_interne: ' and the internal SKU : '
    report_content:
        title: Report content
        option1: Incorrect product sheet
        option2: Illegal content
        option3: Incorrect category of products / services
        option4: Wrong price
        option5: Other reports
        placeholder: Your report...
        label_url: Url
        label_product: Identifiant du produit
month: Month
'no': 'No'
node:
    form:
        author:
            label: Owner
        body:
            label: Message
        content: Content
        delete:
            content: 'Delete static page?'
            error: 'An error occurred during the deletion'
            success: 'Static Page deleted'
            title: 'Delete static page'
        header:
            edit: 'Content Update (%type) :'
            new: 'New Content'
        lang:
            button: Add
            de: German
            en: English
            es: Spanish
            fr: French
            it: Italian
            select: 'Select a Language'
        sections:
            content: Content
            main: General
        slug:
            help: Slug
            label: Permalink
        status:
            label: Status
        submit:
            create: Create
            error:
                create: 'An error occurred during the creation'
                update: 'An error occurred during the update'
            success:
                create: 'The page has been created'
                delete: 'The page has been deleted'
                update: 'Static Page updated'
            update: Update
        template:
            choices:
                default: Default
                default_with_faq: 'Default FAQ'
                default_with_products: 'Default Product List'
                default_with_products_and_faq: 'Default Product List & FAQ'
                fullwidth: 'Full screen'
                fullwidth_with_faq: 'Full screen FAQ'
                fullwidth_with_products: 'Full screen Product List'
                fullwidth_with_products_and_faq: 'Full screen Product List & FAQ'
            label: Canevas
        title:
            label: Title
        test: 'Test emails have been successfully sent to your email address: %email%'
        error:
            template_validation: 'An error occurred while validating content for "%locale%" language: %message%'
offer_detail:
    add_error: 'You already have this product in your cart.'
    add_success: 'Your product is successfully added to your cart.'
    add_to_cart: 'Add to cart'
    add_to_wishlist: 'Add to wish list'
    add_wishlist_success: 'Your product has been successfully added to your wishlist'
    sign_in_to_buy: 'Sign in to buy'
    anonym_contact_merchant: 'Please login for contacting the vendor'
    anonymous:
        add_to_cart: 'You must be authenticated to add product to your cart'
        not_authorized: 'Not authorized'
    ask_question: 'Ask question to vendor'
    back_to_search: 'Back to search results'
    buy_more_for_discount: 'Buy more and get discount'
    company_not_valid: 'Please complete your company informations before checking out your cart'
    contact_seller:
        modal:
            send: Send
            title: 'Contact vendor'
    continue_shopping: 'Continue shopping'
    description: 'See More'
    in_stock: 'On stock'
    login: Login
    on_demand: 'on demand'
    on_stock: 'on stock'
    out_of_stock: 'Out of stock'
    price_quantity: 'For %quantity% %unit%'
    quantity: Quantity
    related_products: 'Related products'
    see_cart: 'See my cart'
    see_less: 'See less'
    see_more: 'See more'
    see_wishlist: 'See my wish list'
    seller:
        label: 'Vendor presentation'
        link_product: 'See all products'
    similar_products: 'Similar Products'
    title: 'Product Details'
    too_much_quantity: 'The selected quantity exceed the stock (maximum: %max%).'
    too_small_quantity: 'The quantity selected is less than the minimum order (minimum: %min%).'
    not_batch_size_multiple: 'The quantity must be a multiple of %batchSize%.'
    total_price: 'Total price'
    wrong_quantity: 'Please enter a valid amount of quantity.'
    any_stock: 'There is not enough stock, you can contact the supplier directly for more information.'
offers:
    bidbutton_msg: 'Bid on your favorite offers soon'
    description: 'All deals'
    title: 'All deals'
orders:
    reorder:
        unavailable: "Nous sommes désolés, les articles précédemment achetés ne sont plus disponibles. Vous ne pouvez pas recommander ce panier."
        partial: "Certains des articles précédemment achetés, ne sont plus disponible. Souhaitez-vous poursuivre et mettre les articles restant dans votre panier ?"
    createCart:
        errorMoq: 'The item %reference% could not be added to the cart because the minimum order quantity is less than the quantity requested'
        errorPrice: 'The item %reference% has been added to the cart, however the price has changed.'
        errorStatus: 'The item %reference% could not be added to the cart because the item and / or the vendor are disabled.'
        errorStock: 'The item %reference% could not be added to cart because the remaining stock on this product is less than the quantity requested'
    detail:
        go_back: 'Go back to orders list'
        title: Orders
        mobile_title: 'Order'
        page_title: 'order_detail'
        order_detail: 'Order details'
        order_id: 'Order ID'
        internal_id: 'Internal ID'
        supplier_id: 'Supplier order ID'
        shipping_address: 'Shipping address'
        order_date: 'Order date'
        notify_manager_again: 'notify the manager again'
        cancel_order: 'cancel order'
        claim: 'claim'
        order_again: 'order again'
        cart_id: 'Cart Ref.'
        tracking_number: 'Tracking number'
        supplier_comment: 'Commentaires à destination du fournisseur'
        sub_total_without_vat: 'Total (ET) '
        total_without_vat: 'TOTAL'
        validate: 'validate'
        recipient: 'Bénéficiaire'
        issuer: 'Issuer'
        tel: 'N° of Tel.'
        comments: 'Comments'
        status: 'Order status'
        shipping_delay: 'Shipping delay'
        shipping_date: 'Shipping date'
        reject: 'reject'
        reason: 'Reason for refusal by N +1'
        reason_cancel: "Reason for cancellation"
        recommendation: recommendation
        feedback_evaluation: 'Evaluation'
        feedback_comments: 'Comment'
        feedback_update_notification: 'You can''t modify review, only the comment'
        feedback_comments_error: "Please fill in your comment"
        feedback_rating_error: "Please select a note"
        cancel_reason: "Cancel Reason"
    modal:
        confirm_validation: 'Are you sure you want to validate this order ?'
        confirm_error: 'An error occured during the validation of your order'
        reject_error: 'An error occured during the reject of your order'
        confirm_reject: 'Are you sure you want to reject this command ?'
        confirm_cancel: 'Are you sure you want to cancel this command ?'
        cancel_placeholder: "Cancel reason..."
        field:
            reminder: "Fill this field please"
        question:
            id_label: 'Order ID'
        confim_notify_again: 'A notification has been sent to your manager'
    empty:
        text: 'You have no orders in progress'
        validation: You have no orders pending validation
        validated: You have no validated order
        cancelled: You have no canceled orders
    export:
        address: 'Shipping address'
        amount: Amount
        date: 'Order date'
        id: Id
        status: Status
        validationNumber: Buyer reference id
    list:
        address: 'Delivery address'
        back: 'back'
        block:
            address_title: 'Delivery address'
            identifier: 'Id'
            validation_number_title: 'Buyer reference id'
            date_title: 'date of demand'
            order_date: 'Date of order'
            validation_date: 'Supplier validation'
            cancelled_date: 'Canceled on'
            cancel_by: 'Cancel by'
            n1_validation_date: 'Validation N + 1'
            cart_number: 'Cart ref.'
            order_title: 'Order N°'
            recipient: 'Recipient'
            status: 'Status'
            total_title: 'Amount'
            supplier: 'Supplier'
        date: 'Date of order'
        detail: Detail
        export: Export
        download_pdf: 'Download.PDF'
        id: 'Order id'
        link:
            cancel: 'Cancel'
            buy_again: 'Buy again'
            details: Details
            export: 'PDF export'
            refund: Refund
            request_validation: 'Requests a validation to the manager'
            validate: 'Validate the order'
            reject: 'Reject the order'
        merchant_products: 'items'
        merchant_product: 'item'
        rejected_by: 'Order rejected by '
        sub_order_id: 'Sub order ID'
        me: me
        supplier: "Supplier"
        tab:
            accepted: 'Accepted'
            cancelled: 'Cancelled'
            empty: 'You have no orders in this section.'
            past: 'Past Orders'
            running: 'To validate'
        total: Total
        to_validate:
            title: 'Orders to validate'
            empty: 'Your list is empty'
        validated_by: 'Order validated by  '
    show_more: 'show more'
    header:
        pending_creation: 'Pending creation'
        pending-supplier-validation: 'to be validated by the supplier'
        pending-manager-validation: 'to be validated by my manager'
        confirmed-by-supplier: 'validated by the supplier'
        ASSIGNED: 'assigned'
        ORDERED: 'ordered'
        REJECTED: 'rejected'
        cancelled: 'cancelled'
    status:
        pending_creation: 'Pending creation'
        pending-supplier-validation: 'to be validated by the supplier'
        pending-manager-validation: 'to be validated by my manager'
        confirmed-by-supplier: 'validated by the supplier'
        ASSIGNED: 'assigned'
        ORDERED: 'ordered'
        REJECTED: 'rejected'
        cancelled: 'cancelled'
        buyer_cancelled: 'buyer cancelled'
        status_0: 'Pending payment'
        status_110: 'Issue solved'
        status_3000: 'Deleted'
        status_60: 'Pending vendor confirmation'
        status_80: 'Confirmed'
        status_85: 'Shipment & Invoice'
        status_1: 'to be validated by my manager'
        status_2: 'Cancelled'
        status_3: 'to be validated by the supplier'
        status_4: 'validated by the supplier'
        status_5: 'To be validated'
        status_6: 'Accepted'
        status_7: 'Rejected'
    review:
        error: 'An error occured  while submitting the feedback. Please contact the support if the problem remains'
        show_reviews: 'See reviews'
payment:
    error: 'An unexpected error occurred during payment process. Your order has been cancelled. If the error persists, please, contact the support'
    form:
        not_authorized: 'Your are not authorized to request term payment mode. Please contact your manager.'
        submit: 'Request authorization for term-payment'
    pre_cc_mode:
        title: 'Payment verification'
        waiting_status: 'Please wait, waiting for the payment authorization for your order %id% ...'
        title_ok: 'Payment confirmed'
        ok: 'Your payment has been successfully performed. Your order %id% is now authorized'
        title_ko: 'Payment failure'
        ko: 'An error occurred during the payment: Your order %id% has been cancelled'
        title_error: 'Technical error'
        error: 'A technical error occurred while retrieving your order information %id%. If the problem persists, please contact support'
        success:
            title: 'Congratulations!'
            text: 'Your order %id% has been confirmed in pre-payment by credit card.'
            text_2: 'You already can check your purchase in the order page.'
            text_3: 'The vendor(s) will soon confirm your purchase.'
    pre_virement_mode:
        pending:
            text: 'Your order has been registered in pre-payment by bank transfer.'
            text_2: 'You will receive an email with all details to proceed to the payment.'
            text_3: 'Once your payment is done, vendor(s) will see your order and will be able to confirm it.'
            text_link1: 'Go back to search page'
            text_link2: 'See my orders'
            title: 'Congratulations!'
        success:
            payment_detail_number: 'Transaction number: %number%'
            payment_details: 'Here is the needed information to perform your bank transfer:'
            payment_details_iban: 'IBAN Number: %iban%'
            payment_details_iban_account_name: 'IBAN account name: %iban_account_name%'
            payment_details_key: 'Reconciliation key: %key%'
            text: 'Your order %numOrder% has been successfully confirmed. It will be processed as soon as your bank transfer is received'
            text_link1: 'Go back to search page'
            text_link2: 'See my orders'
            title: 'Order confirmed'
    select_mode:
        error:
            cc: 'Error while creation transaction. Please contact the support'
        preCreditCard: 'Pre-payment by credit card'
        preTransferWire: 'Pre-payment by bank transfer'
        select: Select
        termTransferWire: 'Term payment (45 days end of month, after issuing of invoice)'
        title: 'Select your payment mode'
    time_mode:
        error:
            text: 'An error occured occured while confirming your order. Please contact the support if the problem remains'
            text_link1: 'See my cart'
            text_link2: 'Go back to search page'
            text_link3: 'Contact support'
            title: 'Error while confirming your order'
        succes:
            text: 'Your order %numOrder% has been successfully confirmed'
            text_2: 'You will receive an email with payment details (IBAN and reconciliation key) when the invoice is emitted.'
            text_link1: 'Go back to search page'
            text_link2: 'See my orders'
            title: 'Order confirmed'
        pending:
            text: 'Your order has been registered in payment at 45 days end of month after invoicing.'
            text_2: 'You will receive a confirmation from your vendor(s) directly on your order page.'
            text_3: 'Once you receive the invoice, you will be able to manage your payment.'
            text_link1: 'Go back to search page'
            text_link2: 'See my orders'
            title: 'Congratulations!'
payment_mode:
    ask_for_term_error: 'Error processing the request for a Term Payment Authorization'
    click_button: 'Click the button below to use the term payment.'
    enabled: 'Your account is also authorized to use bank transfer term-payment method.'
    info: 'The prepayment methods authorized by default are the bank card and the bank transfer.'
    pending: 'Payment authorization is under analysis'
    Prepayment_creditcard: 'Pre-payment by credit card'
    Prepayment_moneytransfert: 'Pre-payment by bank transfer'
    save_error: 'An error occurred while updating the payment'
    saved: 'Payment option has been updated'
    Termpayment_moneytransfert: '45 days end of month, after issuing of invoice'
    title: 'Payment modes'
product:
    about_seller: 'About vendor'
    application_categories: Categories
    buy: 'Purchase / Buy?'
    buyer_reference: Buyer's ref
    comparison: 'Comparison sheet'
    id: 'Product ID'
    info_converted_price: 'For information purposes only.'
    converted_price: 'Based on the daily exchange rate'
    delivery_time: 'Delivery time'
    description: Description
    info_button_buy: 'Products will be available soon. Create your account to be informed on the latest news.'
    logistics_informations: 'Logistical Information'
    made_in: 'Manufacturing country'
    manufacturer: Manufacturer
    manufacturer_reference: Manufacturer's ref
    modal:
        question_title: 'Ask your question...'
        askforquotation_title: 'Ask for a quote'
        askforquotation_message: 'Message'
    private: Private
    seller: Vendor
    reference: Reference
    specifications: Technical specifications
    technical:
        bearing_type: 'Bearing type'
        code_command_rs: 'Code command RS'
        conditioning: Conditioning
        dtr: DTR
        flange_outer_diameter: 'Flange outer diameter'
        inner_diameter: 'Inner diameter'
        manufacturer_ref: 'Manufacturer References'
        marketplace_id: 'Marketplace ID'
        material: Material
        min_order_quantity: 'Minimum quantity'
        nb_row: 'Number of rows'
        outer_diameter: 'Outer diameter'
        rated_static_load: 'Rated static load'
        ring_width: 'Ring width'
        seller_name: 'Vendor name'
        seller_ref: 'Vendor reference'
        termination_type: 'Termination type'
        trendmark: Brand
    technical_detail: 'Technical details'
    technical_details: 'Technical details'
    technical_characteristics: 'Technical characteristics'
products:
    all_offers: 'See all deals'
    home_list_header: 'All deals'
    home_product_list_header: 'Popular products'
profile:
    form:
        email: Email
        firstname: 'First Name'
        lastname: 'Last Name'
        main_phone_number: 'Phone Number'
        optional_phone_number: 'Optional Phone Number'
        submit: Save
        update:
            error: 'An error occurred while updating your personal information'
            success: 'Your contact information has been successfully updated'
    password:
        submit: Apply
        title: 'Change your password'
quote:
    add:
        success: "Your message has been sent, you can find it in the 'My quote requests' menu of your profile"
        not_found: "Your message can not be sent, because it is not available"
        max_price_error: "Impossible d'ajouter au panier car ce devis est supérieur à 2000€. Sous résevre d’accord avec le fournisseur, nous vous conseillons de télécharger ce devis et de passer une commande en dehors de Click&Buy"
    columns:
        referance: 'Référence'
        description: 'Référence et désignation'
        quantity: 'Quantité'
        unit_price: 'Prix unitaire HT'
        total_price: 'Prix total HT'
    signature:
        body: 'Bon pour accord'
        footer: "(Date, signature et cachet de l'entreprise)"
    versions:
        title: "Date de réception"
    detail:
        initial_offer: "Détail de l'offre initiale"
        page_title: 'Mes demandes de devis'
        go_back: 'Retour à la liste des devis'
        status: 'Statut du devis'
        vendor_name: 'Fournisseur'
        title: 'Titre du devis'
        title_small: Titre
        number: 'Numéro du devis'
        subject: 'Sujet'
        receipt_date: 'Date de réception'
        creation_date: "Date de la demande"
        message_subject: 'Demande de devis #offerName#'
        download_versions: 'téléchargez les versions précédentes du devis'
        button:
            add_to_cart: 'Ajouter au panier'
            negociate: 'Rediscuter le devis'
            cancel: 'Annuler ma demande'
        ongoing_message_1: "Votre demande de devis est en cours de traitement par le fournisseur. Vous recevrez un mail de notification lorsqu'un devis vous sera soumis pour approbation."
        ongoing_message_2: "N'hésitez pas à ajouter davantage d'informations à votre besoin grâce à la fenêtre de discussion à droite de l'écran."

    status:
        buyer_draft: En cours
        draft: 'Draft'
        validated: 'Validated'
        buyer_cancelled: Annulé
        cancelled: 'Cancelled'
        redraft: 'Redraft'
        new: 'New'
        send: 'Send'
        refused: 'Refused'
    list:
        title: 'Quotes'
        block:
            supplier: Vendeur
            creation_date: Date de la demande
            quote_subject: Sujet
            quote_number: Numéro du devis
            quote_number_small: N° devis
            quote_status: Status
            quote_date: Date du devis
            cancelled_date: Date d'annulation
            validation_date: Date de validation
            application_date: 'Date de la demande'
        empty:
            Vous n'avez pas de devis dans cette catégorie
    modal:
        confirm:
            add_to_cart: 'Après l’ajout de ce devis au panier, ce dernier ne pourra être à nouveau modifié. Etes-vous sûr de vouloir ajouter ce devis au panier ?'
            negociate: 'Etes-vous sûr(e) de vouloir rediscuter ce devis ? Si oui, merci d’en préciser la/les raison(s). '
            cancel: 'Etes-vous sûr(e) de vouloir annuler cette demande de devis ? Si oui, merci d’en préciser la/les raison(s).'
    item:
        detail:
            reference: Référence
            description: Description
            quantity: Quantité
            unit_price: Prix unitaire HT
            total_price: Prix total HT
            total_price_vat: Prix total TTC
            without_vat: HT
    pdf:
        address: Adresse
        vat: Taux de TVA
        total_vat: Total TTC
        title: "Quote N° %num% - V%version%"
    generate:
        offer:
            title: Devis n° %quoteNumber% - %quoteTitle%
            title_part: Devis n° %quoteNumber% - %quoteTitle% - part %index%
            default_keyword: Devis
    event:
        prefix:
            new: "%message%"
            send: "Bonjour,\r\nSuite à votre demande, je vous prie de trouver ci-contre notre devis.\r\nRestant à votre disposition,\r\nCordialement"
            redraft: "Bonjour,\r\nJe vous remercie pour le devis soumis. En revanche, pourriez-vous s’il vous plait nous proposer une nouvelle version pour la/les raison(s) suivante(s) : %message%."
            cancelled: "Bonjour,\r\nJe suis malheureusement dans l’obligation d’annuler ma demande de devis pour la/les raison(s) suivante(s) : %message%.\r\nEn vous remerciant pour votre compréhension,\r\nCordialement"
            refused: "Bonjour,\r\nJe vous remercie pour votre sollicitation. En revanche, nous ne serons malheureusement pas en mesure de répondre à votre besoin pour la/les raison(s) suivante(s) :  %message%.\r\nEn vous remerciant pour votre compréhension,\r\nCordialement"
            validated: "Bonjour,\r\nJe vous remercie pour votre devis. Je viens de procéder à son ajout au panier dans l’optique de vous passer commande.\r\nVous recevrez une notification mail vous en informant.\r\nEn vous remerciant par avance,\r\nCordialement"
    link_intro: "to see the quote click on this link : "
redirect:
    form:
        delete:
            error: 'An error occurred during deletion'
            success: 'Redirection has been deleted'
        destination:
            help: 'Verify url'
            label: Destination
        header:
            edit: 'Redirection modification:'
            new: 'New redirection'
        origin:
            help: Slug
            label: Origin
        submit:
            error:
                create: 'An error occurred during the creation'
                update: 'An error occurred during the update'
            success:
                create: 'Redirection has been created'
                update: 'Redirection has been updated'
        type:
            help: '301 = permanent / 302 = temporary'
            label: 'Redirection type'
registration:
    header:
        title: '<span>TotalEnergies launches its platform</span><span>Click & Buy</span>'
        subtitle: 'to enable all employees, wherever they are, to buy the low-value goods and services they need in their professional environment.'
        more_info: 'download the presentation support'
    introduction: '<span>Click & Buy</span>, the new platform dedicated to small purchases of <span>TOTAL</span> group'
    title: 'submit your profile'
    buyer: 'Buyer'
    vendor: 'Vendor'
    selectType: 'Select your type'
    label: 'I want to join as'
    error:
        identification_already_used: 'Identification has already been used'
        identification_already_used_alert: Your company have already an account on StationOne. Owner of the account can give you access. If you don't know the owner of the account, please send us a request with the contact form. Do you want go to the contact form ?
        technical: 'An error occurred during the creation of your account. Please contact the support team.'
resetting:
    check_email: 'An email has been sent. It contains a link to reset your password.</br></br> If you do not receive an email, check your spam folder or try resetting your password again.'
    newpwd: 'Reset password'
    request:
        submit: 'Reset password'
    reset:
        submit: Edit
search:
    facet:
        categories: "CATEGORIES"
        common: "COMMONS"
        specific: "FILTER"
    filters: Filters
    advanced_search:
        submit: Search
        title: 'Advanced search'
    compatible_products_with: 'Compatible products with'
    departments:
        all_departments: 'All departments'
        bearing: Bearing
        brake_disc: 'Brake disc'
        camera: Camera
        filter: Filter
        glazing: Glazing
        screen: Screen
    help: Help
    supplier_invitation_text: 'However, if you know of a supplier that can meet your needs, do not hesitate to invite him to join Click & Buy by clicking on the button below.'
    supplier_invitation_btn: invitation
    operator_contact_text0: 'The supplier will receive a link to a dedicated page containing an explicative video and presentation media. The supplier will candidate to Click & Buy knowing these informations'
    operator_contact_text1: 'His profile will be studied by the Click & Buy team within 48 hours.'
    operator_contact_text2: 'Your supplier will then have to complete the information related to his profile and create his catalog in order to have access to his products and / or services.'
    operator_contact_btn: 'contact the operator'
    in_compatible_products: 'Search in compatible products'
    no_custom_ref_found: 'No reference found in your catalog.'
    no_offer: 'No deals for the moment'
    no_offer_in_catalog: 'Sorry, no results found for your search'
    pagination:
        next: Next
        'on': 'on'
        previous: Previous
    products: products
    result_label: 'Search Results (%total% results)'
    results: results
    results_for: for
    searchbar:
        advanced_search: 'Advanced search'
        custom_search: 'My catalog search'
        in_catalog: 'My catalog'
        in_marketplace: 'Marketplace'
        in_product_compatibility: 'Compatible products'
        mobile_placeholder: 'Product or supplier...'
        placeholder: 'Type the name of the product, service, or supplier you are looking for...'
    show: Show
    show_more: 'More products'
    sidebar:
        category: category
        commons: Commons
        departments: Categories
        refine_by: 'Refine by'
        search: 'Search term'
        see_more: 'See more'
        see_less: 'See less'
        specific: Specifics
        up: '< Up'
        filterer:
            categories:
                label: 'CATEGORIES'
            weights:
                label: 'POIDS'
            merchants:
                label: 'FOURNISSEURS'
            colors:
                label: 'COLOR'
                white: 'WHITE'
                blue: 'BLUE'
                red: 'RED'
                green: 'GREEN'
    sort_by: Sort
    sort_form:
        delivery_time: 'By delivery time (lowest first)'
        newest: 'By offer date (newest first)'
        price_max: 'By price (highest first)'
        price_min: 'By price (lowest first)'
        relevance: relevance
        price_asc: 'Sort by ascending price'
        price_desc: 'Sort by price descending'
    title: Search
    facet_info:
        message_communication_marketing: 'Te recordamos que los elementos de Comunicación (materiales impresos, etc.) deben seguir imperativamente las pautas gráficas del Grupo (<a href="https://www.brandcenter.total.com/en"> https: // www. brandcenter.total.com/en </a>).'
        #message_office_supplies: 'placeholder message_office_supplies'
        #message_intellectual_services: 'placeholder message_intellectual_services'
        #message_computer_accessories: 'placeholder message_computer_accessories'
        message_catering: '<b> ATENCIÓN </b>: es obligatorio seleccionar en los "FILTROS" a la izquierda de su pantalla el "<b> SITIO DE ENTREGA </b>" correspondiente a su pedido. De hecho, no todos los proveedores de catering están autorizados por TOTAL FACILITIES MANAGEMENT SERVICES (TFMS) para realizar entregas en todos los sitios del Grupo.'
        message_bouquets_plants_amenities: 'placeholder message_bouquets_plants_amenities'
        message_laboratory: 'Le recordamos que pueden aplicarse reglas locales con respecto a este tipo de compra. Es su responsabilidad asegurarse de que su compra cumpla con estas reglas. Para cualquier duda adicional, le invitamos a que se ponga en contacto con el departamento de compras de su sitio.'
        message_personal_protection_equipment: 'Le recordamos que pueden aplicarse reglas locales con respecto a este tipo de compra. Es su responsabilidad asegurarse de que su compra cumpla con estas reglas. Para cualquier duda adicional, le invitamos a que se ponga en contacto con el departamento de compras de su sitio.'
        message_industrial_supplies: 'Le recordamos que pueden aplicarse reglas locales con respecto a este tipo de compra. Es su responsabilidad asegurarse de que su compra cumpla con estas reglas. Para cualquier duda adicional, le invitamos a que se ponga en contacto con el departamento de compras de su sitio.'
        message_services_translation: 'Le recordamos que algunos proveedores tienen un contrato marco para la prestación de servicios de traducción. Lo invitamos a consultar la lista de estos proveedores haciendo clic aquí'
        message_goodies: 'Recordatorio: Todos los beneficios de la carta TotalEnergies están disponibles en el catálogo CECOP (https://www.clickandbuy.total/fr/search?query=CECOP%20SA), proveedor titular del Contrato Marco.'

security:
    login:
        create_account: 'Create an account'
        footer_text: 'Not a member yet?'
        login: 'Log in'
        title: 'Already member, please login'
        fail: 'Invalid credentials.'
shipping_point:
    form:
        address:
            label: Address
        comment:
            label: Comment
        contact:
            label: Contact
        first: 'Main Shipping Address'
        name: 'Name of Shipping Address'
        save: Save
        title_common: 'Shipping Address'
        title_edit: Edit
        title_new: New
        country: 'Country'
        address2: 'Address 2'
        zip_code: 'Ziop code'
        city: 'City'
        region: 'Region'
        recipient: 'Recipient name'
        phone: 'Phone'
    list:
        title: 'My external delivery addresses'
        intro1: 'By default, your delivery address is your site address. If you wish to select another TOTAL address, this will be possible directly in your cart. However, if your need doesn’t concern a TOTAL address, we invite you to create your external address directly above and then select it in your cart.'
        add_new_address: 'Add new address'
    detail:
        name: 'Name'
        address: 'Address'
        zip_code: 'CP'
        city: 'City'
        country: 'Country'
        contact: 'Contact'
        phone: 'Phone'
        comments: 'Comments'
    modal:
        remove_text: 'Are you sure you want to delete this delivery address ?'
site:
    form:
        add: 'Add a Cost Center'
        add_contact: 'Add a Contact'
        add_modal: 'New Cost Center'
        add_shipping_address: 'Add a Shipping Address'
        adresse: Address
        afternoon:
            end: 'Closing Time (afternoon)'
            start: 'Opening Hours (afternoon)'
        authorization: authorization
        cancel: cancel
        comment: Comment
        complement: 'Additional Information'
        copy: Copy
        corporate_name: 'Company Legal Name'
        cpZipCode: 'Zip Code'
        create: Create
        created: 'Create your Cost Center'
        days: Days
        delete:
            content: 'Delete the Cost Center?'
            shipping_point: 'Delete the Shipping Address'
        delete_shipping_address: Delete
        friday: Friday
        identification: SIRET
        legales_doc: 'Legal Documents'
        main_contact: 'Main Contact'
        modify_shipping_address: Modify
        monday: Monday
        morning:
            end: 'Morning close'
            start: 'Morning open'
        name: 'Cost Center Name'
        opening_time: 'Opening time'
        other_contacts: 'Other Contact'
        save: Save
        submit: Submit
        submitError: 'Register your company before enrolling your site'
        thursday: Thursday
        title: 'Cost Center'
        tuesday: Tuesday
        wednesday: Wednesday
    header:
        edit: 'Modify Cost Center:'
        list: 'Cost Center List'
    list:
        deactivation:
            ko: 'An error has occurred while deleting the Cost Center'
            ok: 'The Cost Center has been deleted'
            users_exist: 'Users are attached to this cost center, so you cannot delete this cost center'
        no_user: 'No user affiliated'
        title: 'Affiliated users to this cost center'
        users:
            action: Action
            firstname: Firstname
            function: Function
            id: '#'
            lastname: Lastname
            role: Role
status:
    draft: 'To be completed'
    pending: 'Pending Operators validation'
    valid: 'Validated by the Operator'
system:
    setting:
        form:
            submit: Edit
    settings:
        homepage:
            disclaimer:
                title: 'Video ID'
                video_src: Video
            slider:
                title: 'Slider settings'
            title: 'Homepage Settings'
            video_1_src: 'YouTube video ID from Slide 2'
            video_2_src: 'YouTube video ID from Slide 3'
            video_en_src: 'Video in English'
            video_fr_src: 'Video in French'
        notifications:
            command:
                title: Orders
            completion_recall_number_of_day: 'Number of days before completion notification'
            title: Notifications
        offers:
            offer_1: 'Izberg identifier 1st popular offer'
            offer_2: 'Izberg identifier 2nd popular offer'
            offer_3: 'Izberg identifier 3rd popular offer'
            offer_4: 'Izberg identifier 4th popular offer'
            offer_5: 'Izberg identifier 5th popular offer'
            offer_6: 'Izberg identifier 6th popular offer'
            offer_7: 'Izberg identifier 7th popular offer'
            offer_8: 'Izberg identifier 8th popular offer'
            popular:
                title: 'Popular deals'
            title: 'Deal Settings'
        security:
            login:
                title: 'Login Security Settings'
            login_attempt_max: 'Maximum login attempts'
            login_banned_user_unlock_timeout: 'Unblock users banned after {x} minutes'
            login_administrator_token: 'Brute force protection administrator token'
            title: 'Security Settings'
        testimonials:
            fader_speed: 'Testimonial rotation speed (in milliseconds)'
            max_age: 'Maximum age in months (testimonials greater than that date will not be visible)'
            max_items: 'Maximum number of visible testimonials'
            parameters:
                title: 'Testimonial Settings'
            title: Testimonials
        update_error: 'An error occurred while updating the settings'
        update_success: 'Settings updated'
tab_infos_seller:
    presentation: 'Vendor presentation'
    see_all_products: 'See all products'
    cgv: 'General sales condition'
technical_support:
    text1: 'If you have IT issues with your current set up/devices/account. For example: password resets, performance issues, Microsoft Office troubleshooting, printer issues, broken IT services or equipment... Dial *321 from any internal phone worldwide or +33 1 47 44 33 21 from an external site.'
    text2: 'If you have any issue relative to an order or a Supplier, please contact directly the supplier from your “order” page.'
ticket:
    common:
        administrator_user: '%fullname% (operator)'
        file_reset: Reset
        message: Message
        nofiles: 'No file selected'
        number: 'Ticket number'
        subject: Subject
        submit: 'Send (Submit)'
        update: 'Send (Submit)'
        add_file: 'Add a file'
    create:
        company: Company
        email: Email
        firstname: 'First Name'
        function: 'Job Title'
        lastname: 'Last Name'
        message_text: Message
        phone: 'Phone number'
        recipient: 'Select a Company'
        title: 'Contact StationOne'
    edit:
        add: 'New Response'
        attachment: Attachment
        attachment_button: Select
        author: Owner
        close: 'Discussion closed'
        close_thread: 'This thread was closed by %firstname% %lastname% on %date%'
        close_thread_anonymous: 'This thread was closed by an anonymous user on %date%'
        closed: Solved
        company: Company
        date: 'Ticket created'
        id: ID
        link: 'Go back to threads list'
        message_label: Messages
        message_placeholder: 'Send a message'
        message_text: Response
        new_message: 'NEW MESSAGE :'
        operator: Operator
        recipient: Recipient
        reopen: Reopen
        save_message: SAVE
        subject: Subject
        timeFormat: 'à $1h$2mn$3s'
        title: Ticket
    error:
        create: 'An error occurred during the ticket creation'
        update: 'An error occurred during the ticket modification'
    list:
        actions: Actions
        add: 'Send a message'
        all: All
        author: 'Created by'
        closed: Solved
        company: Company
        createdAt: Created
        empty: 'You have no messages'
        export_csv: 'CSV Export'
        knp_next: Next
        knp_previous: Previous
        lastAt: 'Modified'
        main_contact: 'Ticket created by'
        me: You
        nb_messages: 'Number of messages'
        next: 'Aging Tickets'
        number: ID
        opened: Open
        previous: 'Recent Tickets'
        status: Status
        sujet: Subject
        sent_to: 'Sent to'
        title:
            resolved: Resolved
            standard: Messages
    status:
        STATUS_CLOSED: Solved
        STATUS_INFORMATION_REQUESTED: 'Request Information'
        STATUS_INVALID: Invalid
        STATUS_IN_PROGRESS: 'In Progress'
        STATUS_NEW: New
        STATUS_ON_HOLD: Pending
        STATUS_OPEN: Open
        STATUS_OPERATOR_RESPONDED: Open
        STATUS_RESOLVED: Solved
        STATUS_USER_RESPONDED: Open
    success:
        create: 'Your request has been created'
        update: 'Your request has been modified'
user:
    roles:
        popup:
            title: "Mise à jour des roles de l'ultilisateur"
            label: "Choisir les rôles de l'utilisateur"
            cancel: "Annuler"
            save: "Enregistrer"
            placeholder: "Choisir les rôles"
        values:
            ROLE_BUYER: "Buyer"
            ROLE_MANAGER: "Manager"
            ROLE_ENTITY_REPORTING: "Entity Reporter"
            ROLE_MARKETPLACE_REPORTING: "Marketplace Reporter"
        choices:
            buyer: 'Buyer'
            manager: 'Manager'
            entityReporting: 'Entity Reporter'
            marketplaceReporting: 'Marketplace Reporter'
    profile:
        title: 'My account'
        infos_title: 'Personal informations'
        infos_name: 'name'
        infos_id: 'igg'
        entity_title: 'Organisational information'
        legal_entity: 'BILLING ENTITY'
        manager: 'manager'
        delegates: 'La validation des commandes a été déléguée à'
        site: 'site'
        information_text: '<span>Information : </span> This data is directly extracted from the Total directory (AIG database) and can not be modified from this interface. Please contact WeCare if necessary (Contact procedure available in the "Need help" link at the bottom of the page)'
    form:
        activities: activities
        ROLE_BUYER: Buyer
        ROLE_OPERATOR: Operator
        ROLE_SUPER_ADMIN: Administrator
        add: 'Create a user'
        company: Company
        email: Email
        firstname: 'First Name'
        function: 'Job Title'
        lastname: 'Last Name'
        phone1: 'Main Phone number'
        phone2: 'Phone number'
        role: Role
        site: 'Cost Centers'
    registration:
        email: Email
    resetting:
        title: 'Reset password'
    delegation:
        menu_title: 'Delegation'
        title: 'Absence manager'
        subtitle: 'Active delegations'
        no_delegation: 'Currently you haven''t active delegation, to add one, use this field'
        ajouter_delegation: 'Add delegate by IGG or name:'
        tablefield_igg: 'IGG'
        tablefield_firstname: 'Firstname'
        tablefield_lastname: 'Lastname'
        tablefield_email: 'Email'
        add_action: 'Add a delegation'
        remove_action: 'Delete this delegation'
        remove_action_confirm: 'Are you sur you want to remove this delegation?'
        remove_action_problem: 'A problem has occured during delegation removing.'
    reports:
        menu_title: "Reporting"
yes: Yes

stats:
    cost_center:
        all: All
        title: 'Cost center'
    accrued:
        title: 'Cumulated amount'
        yes: Yes
        no: No
    orderAmount: 'Orders amount'
    year:
        title: Year


validator:
    required: "This field is required."
    remote: "Please fix this field."
    email: "Please enter a valid email address."
    url: "Please enter a valid URL."
    date: "Please enter a valid date."
    number: "Please enter a valid number."

wishlist:
    add_new: 'Add a new wishlist'
    new_name: 'Name of the new list'
    save: 'Save'
    save_error: 'An error occurred when saving the wishlist'
    save_in: 'Add in wishlist'
    save_success: 'Wishlist successfully saved'
    title: 'Wishlist'
    none: 'You have no wishlist'
    go_back: 'Go back to the list'
    add_to_cart: 'Add to cart'
    delete_confirm: 'Are you sure you want to delete this wish list?'
    delete:
        success: 'Your wish list has been deleted'
        error: 'An error occured during the deleting of your wish list'
    item:
        delete_confirm: 'Are you sure you want to delete this offer?'
        delete:
            success: 'Your offer has been deleted'
            error: 'An error occured during the deleting of your offer'
        update:
            success: 'The quantity has been updated'
            error: 'An error occured during the updating of the quantity'
        noItem: 'You have not offer in your list'
    charge:
        confirm: 'This action will delete the contents of your cart %currency% before adding the offers from this list'
        success: 'The offers from your list have been added to the cart'
        error: 'An error occurred while adding offers to your cart'
    table:
        days: 'days'
        delivery_time_item: 'Leadtime before Delivery'
        delivery_time_wishlist: 'Leadtime of Wish List'

cookie:
    message: "By continuing your visit to this site, you accept the terms and conditions of use, including the use of cookies to perform statistics of audiences."
    accept: 'Got it!'


buyer:
    supplier_invitation:
        title: "Invite a supplier to join Click & Buy"
        form:
            first_name: "NAME"
            last_name: "FIRST NAME"
            email: "EMAIL"
            phone_number: "PHONE NUMBER"
            company_name: "Company name"
            message: "Message"
            message_body: |
                Bonjour,

                Je souhaite vous inviter à soumettre votre profil sur notre Marketplace Click & Buy.
                En effet, TotalEnergies lance sa plateforme Click & Buy pour permettre à tous ses collaborateurs, où qu'ils soient, de gérer leurs achats professionnels courants de biens et services.
                Intégrer cette plateforme, vous permettra de mettre en avant vos produits et services auprès de tous les collaborateurs du groupe.

                Pour se faire, il vous suffit de cliquer sur le lien suivant et de compléter le formulaire.
                Une fois votre profil validé, vous accèderez à votre espace fournisseur où vous aurez la possibilité de mettre en ligne vos offres et ploter votre activité.

                En vous remerciant,
                A bientôt sur Click & Buy
            matching_message: 'A member of this company has already been invited, do you want to proceed to the invitation anyway?'
            invite_button:
                label: "INVITATION"
            continue_invite_button:
                label: "CONTINUE INVITATION"
offer_result_details:
    unit_of_buy: 'Sold in batches of'
    supplier: 'Supplier'
    show_more: 'SHOW MORE'
    show_less: 'SHOW LESS'
    delivery_restriction: 'Delivery restrictions'
    ask_question: 'CONTACT SUPPLIER'
    description: 'DESCRIPTION'
    no_description: 'No description'
    technical_details: 'TECHNICAL DETAILS'
    no_technical_details: 'No technical details'
    about_supplier: 'ABOUT SUPPLIER'
    discount_quantity: 'Discount by quantity'
    quantity: 'Quantity'
    quantity_info: 'We can only add %stock% quantity'
    add_to_cart: 'ADD TO CART'
    ask_quotation: 'REQUEST FOR QUOTATION'
    supplier_presentation: 'Supplier presentation'
    no_supplier_presentation: 'No information about the supplier'
    inactive: 'unavailable'
    in_stock: 'in stock'
    out_of_stock: 'out of stock'
    low_stock: 'low stock'
    limited_stock: 'limited stock'
    stock_on_command: 'on order'
    sku_label: 'placeholder_sku : '

reporting:
    best_offer:
        id_col: 'Offer Identifier'
        gtin_col: 'GTIN'
        sku_col: 'SKU'
        name_col: 'Offer Name'
        price: 'Your Price'
        best_price: 'Best Offer'

table:
    perPage: 'Show %num% per page'

nplus:
    comment: 'Your reasons...'

review:
    text: 'reviews'
    see_all: 'See all'
    show_more: 'See more reviews'
    title: 'All reviews'
    createdAt: 'Created at'
    helper_text: 'Share your experience following your Click & Buy order by giving your feedback on the supplier. You can change your opinion at any time'
    popup_text: 'Your rating has been taken into account.Thank you for your contribution'
    view: 'Views'

btn:
    accept: 'Validate'
    reject: 'Refuse'
    detail: 'Detail'
    cancel: 'Cancel'
    editcomment: 'Edit review'
    savecomment: 'Save'
tabs:
    cancelled: 'CANCELLED'
    validatebyseller: 'VALIDATED BY THE SUPPLIER'
offers_orders:
    relevance: "Relevance"
    price:
        asc: "Ascending price"
        desc: "Decreasing price"
report:
    page_title: "Reporting"
    list:
        block:
            order_number: "N° de commande"
            cart_reference: "Ref du panier"
            order_date: "Date de la commande"
            beneficiary_name: "Nom"
            beneficiary_firstname: "Prénom"
            beneficiaries: "Nom Prénom"
            invoice_entity: "Entité de facturation"
            organisational_entity: "Entité organisationnelle"
            categories: "Catégories"
            designation: "Désignation"
            supplier: "Fournisseur"
            vat_num: "N° de TVA Intracom fournisseur"
            unit_price: "Prix unitaire HT"
            quantity: "Quantité"
            amount: "Prix total HT"
            currency: "Devise"
            address: "Adresse de livraison "
            cost_center: "Centre de coût"
            site_name: "nom du site"
            gift: "gift"
            yes: "yes"
            no: "no"
            no_info: "information non disponible"
        empty: ""
    filters:
        date:
            title: "Filtrer par date"
            divider: "au"
        vue:
            title: "Filtrer par vue"
            super_manager: "Super manager"
            entity_manager: "Manager entité"
            manager: "My N-X"
            n-1: "My N-1"
        export:
            orders: "Export commandes"
            products: "Export products"
            columns:
                product:
                    reference: "Référence"
                    title: "Titre de l’offre"
                    unit_price: "Prix unitaire HT"
                    quantity: "Quantité"
                    total_price: "Prix HT"
                    currency: "Currency"
    stats:
        orders_count: "Nombre de commandes :"
        total_amount: "Dépense total :"
        average_cart_amount: "Panier moyen :"
favourite:
    title: "Mes favoris"
    list:
        empty: "Vous n'avez pas de favoris"
        products: "Produits"
        price: "Prix"
        vendors: "Fournisseurs"
        select_all: "Tout sélectionner"
        remove_selected: "Supprimer la sélection"
        add_cart: "Ajouter au panier"
        offers: article | articles
        intro: ""
        offer_detail:
            add_to_favourite: "Ajouter à mes favoris"
