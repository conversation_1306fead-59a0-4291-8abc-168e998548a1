<?php
namespace AppBundle\Validator\Constraints;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;

class SlugFormatValidator extends ConstraintValidator
{
    /**
     * Validate the slug format and existance
     */
    public function validate($value, Constraint $constraint)
    {

        // Check for invalid characters
        if (preg_match('%^[^/][a-z0-9A-Z/_-]*$%i', $value, $matches) === 0) {
            $this
                ->context
                ->buildViolation($constraint->message)
                ->setParameter('%string%', $value)
                ->addViolation();
        }

    }
}