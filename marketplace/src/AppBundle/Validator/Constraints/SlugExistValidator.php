<?php
namespace AppBundle\Validator\Constraints;

use AppBundle\Doctrine\NodeStatusFilter;
use AppBundle\Entity\Node;
use AppBundle\Repository\NodeRepository;
use AppBundle\Repository\RedirectRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;

class SlugExistValidator extends ConstraintValidator
{
    protected RouterInterface $router;
    protected NodeRepository $node_repository;
    protected RedirectRepository $redirect_repository;
    protected EntityManagerInterface $entity_manager;
    protected RequestStack $request_stack;

    public function __construct(
        RouterInterface $router,
        EntityManagerInterface $em,
        NodeRepository $nr,
        RedirectRepository $rr,
        RequestStack $rs
    ) {
        $this->router = $router;
        $this->entity_manager = $em;
        $this->node_repository = $nr;
        $this->redirect_repository = $rr;
        $this->request_stack = $rs;
    }

    /**
     * Validate the slug format and existance
     * @param $value
     * @param Constraint $constraint
     */
    public function validate($value, Constraint $constraint)
    {
        $valid = true;

		// Ignore empty value because another rule take care of that
		if (!empty($value)) {

            $route = $this->router->match('/fr/' . $value);

            // The route exist somewhere
            if ($route['_route'] !== 'catch_all') {

                $valid = false;

                // If the route does not exist then our catch all route will reply to it
                // It means we have to look in the db for matching content
            } else {

                /** @var NodeStatusFilter $filter */
                // Filter by published nodes
                $this->entity_manager->getFilters()->enable('published_node');

                /** @var Node $node */
                $node = $this->node_repository->findBySlug($value);

                //This make assumption that the slug validation is done on a url that have the id
                $node_id = intval($this->request_stack->getCurrentRequest()->request->get('id'));

                // If the node found is not the same as the one we are editing then it is an error
                if ($node && $node->getId() !== $node_id) {
                    $valid = false;
                } else {

					//This make assumption that the slug validation is done on a url that have the id
					$redirect_id = intval($this->request_stack->getCurrentRequest()->request->get('id'));


                    // Find a redirect starting from this slug
                    $redirect = $this->redirect_repository->findByOrigin($value);

                    // If there is a redirection starting from this slug
                    // the request might end up looping or lead to a 404
                    // We cannot allow this
                    if ($redirect && $redirect->getId() !== $redirect_id) {
                        $valid = false;
                    }
                }

            }
		}

        // Raise validation error if needed
        if (!$valid) {
            $this->context->buildViolation($constraint->message)
                ->setParameter('%string%', $value)
                ->addViolation();
        }
    }
}