<?php

namespace AppBundle\Validator\Constraints;

use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Contracts\Translation\TranslatorInterface;
use Unirest;

class ReCaptchaValidator extends ConstraintValidator
{
	private LogService $logger;
	private TranslatorInterface $translator;

	public function __construct(LogService $logger, TranslatorInterface $translator) {
		$this->logger = $logger;
		$this->translator = $translator;
	}

	/**
	 * Validate the slug format and existance
	 */
	public function validate($value, Constraint|ReCaptcha $constraint) {
		/* @var ReCaptcha $constraint */
		$data = array(
			'secret' => $constraint->getCaptchaSecret(),
			'response' => $value
		);

		$response = Unirest\Request::post(
			'https://www.google.com/recaptcha/api/siteverify',
			[],
			$data
		);

		$this->logger->debug('ReCaptcha verification Request', EventNameEnum::CAPTCHA_VERIFY_REQUEST, "anonymous", $data);
		$this->logger->debug('ReCaptcha verification Response', EventNameEnum::CAPTCHA_VERIFY_RESPONSE, "anonymous", (array)$response->body);

		if ($response->code != 200 || ($response->code == 200 && !$response->body->success)) {

			// Raise validation error if needed
			$this
				->context
				->buildViolation($this->translator->trans($constraint->message, [], 'validators'))
				->addViolation();
		}
	}
}