<?php
namespace AppBundle\Validator\Constraints;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;

class RedirectDestinationFormatValidator extends ConstraintValidator
{
    /**
     * Validate the slug format and existance
     */
    public function validate($value, Constraint $constraint)
    {
		if (preg_match('%^(http|https)://%', $value, $matches)) {

			if (preg_match('%^(?:(?:https?|ftp)://)(?:\S+(?::\S*)?@|\d{1,3}(?:\.\d{1,3}){3}|(?:(?:[a-z\d\x{00a1}-\x{ffff}]+-?)*[a-z\d\x{00a1}-\x{ffff}]+)(?:\.(?:[a-z\d\x{00a1}-\x{ffff}]+-?)*[a-z\d\x{00a1}-\x{ffff}]+)*(?:\.[a-z\x{00a1}-\x{ffff}]{2,6}))(?::\d+)?(?:[^\s]*)?$%iu', $value, $matches) === 0) {
				$this
					->context
					->buildViolation($constraint->message)
					->setParameter('%string%', $value)
					->addViolation();

			}

		} else {

			if (preg_match('%^[^/][a-z0-9A-Z/_-]*$%i', $value, $matches) === 0) {
				$this
					->context
					->buildViolation($constraint->message)
					->setParameter('%string%', $value)
					->addViolation();
			}

        }

    }
}