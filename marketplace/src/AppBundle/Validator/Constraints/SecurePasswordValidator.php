<?php

namespace AppBundle\Validator\Constraints;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;

/**
 * @Annotation
 */
class SecurePasswordValidator extends ConstraintValidator
{
    public function validate($value, Constraint $constraint)
    {
        // Look for a lower case letter
        if (!preg_match('/[a-z]/', $value)) {
            $this
                ->context
                ->buildViolation('user.password.lower')
                ->addViolation();
        }

        // Look for an upper case letter
        if (!preg_match('/[A-Z]/', $value)) {
            $this
                ->context
                ->buildViolation('user.password.upper')
                ->addViolation();
        }

        // Look for a numeric character
        if (preg_match("/[0-9]+/", $value) == 0) {
            $this
                ->context
                ->buildViolation('user.password.num')
                ->addViolation();
        }

        // Look for a special char
        if (preg_match("/[!@#$%^*_-]+/", $value) == 0) {
            $this
                ->context
                ->buildViolation('user.password.special')
                ->addViolation();
        }

    }
}