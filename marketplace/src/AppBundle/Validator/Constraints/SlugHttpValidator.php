<?php
namespace AppBundle\Validator\Constraints;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;

class SlugHttpValidator extends ConstraintValidator
{

    /**
     * Validate the slug format and existance
     */
    public function validate($value, Constraint $constraint)
    {

        // Check format first
        if (preg_match('/^(http|https):\/\//', $value, $matches)) {
            // Raise validation error if needed
            $this
                ->context
                ->buildViolation($constraint->message)
                ->setParameter('%string%', $value)
                ->addViolation();
        }
    }
}