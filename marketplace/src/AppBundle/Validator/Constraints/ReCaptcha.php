<?php
namespace AppBundle\Validator\Constraints;

use AppBundle\Exception\MissingOptionException;
use Symfony\Component\Validator\Constraint;

/**
 * @Annotation
 */
class ReCaptcha extends Constraint
{
    public $message = 'captcha.error';
		protected $captchaSecret;

	/**
	 * @throws MissingOptionException
	 */
	public function __construct($options = null)
	{
		if (is_array($options) && isset($options['captcha_secret'])) {
			$this->captchaSecret = $options['captcha_secret'];
			unset($options['captcha_secret']);
		} elseif ($options instanceof \ArrayAccess && $options->offsetExists('captcha_secret')) {
			$this->captchaSecret = $options['captcha_secret'];
			$options->offsetUnset('captcha_secret');
		}

		parent::__construct($options);

		if (!isset($this->captchaSecret)) {
			throw new \InvalidArgumentException('Captcha API Key is missing');
		}
	}

	public function getCaptchaSecret()
	{
		return $this->captchaSecret;
	}
}
