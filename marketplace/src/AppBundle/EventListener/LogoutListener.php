<?php

namespace AppBundle\EventListener;

use App<PERSON><PERSON>le\Entity\User;
use AppBundle\Services\UserBddService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Security\Http\Logout\LogoutHandlerInterface;

class LogoutListener implements LogoutHandlerInterface {

    private UserBddService $userBddService;

    public function __construct(UserBddService $userBddService)
    {
        $this->userBddService = $userBddService;
    }

    public function logout(Request $request, Response $response, TokenInterface $token): void
    {
        $user = $token->getUser();
        if (!$user instanceof UserInterface) {
            return;
        }

        // else
        $username = $user->getUsername();

        /** @var User $userBdd */
        $userBdd = $this->userBddService->findByUsername($username);
        $userBdd->setForceLogout (false);
        $this->userBddService->saveUser($userBdd);

    }
}
