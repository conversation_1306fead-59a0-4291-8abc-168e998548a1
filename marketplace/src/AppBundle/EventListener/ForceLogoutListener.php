<?php

namespace AppBundle\EventListener;

use AppBundle\Entity\User;
use AppBundle\Services\SecurityService;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\GetResponseEvent;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Security\Http\Logout\LogoutHandlerInterface;


class ForceLogoutListener implements LogoutHandlerInterface
{
    private TokenStorageInterface $tokenStorage;
    private AuthorizationCheckerInterface $authorizationChecker;
    private RouterInterface $router;

    public function __construct(
        TokenStorageInterface         $tokenStorage,
        RouterInterface               $router,
        AuthorizationCheckerInterface $authorizationChecker
    )
    {
        $this->tokenStorage = $tokenStorage;
        $this->router = $router;
        $this->authorizationChecker = $authorizationChecker;
    }

    public function onKernelRequest(RequestEvent $event)
    {

        if (HttpKernelInterface::MAIN_REQUEST != $event->getRequestType()) {
            return;
        }
        $token = $this->tokenStorage->getToken();

        if ($token === null) { // somehow
            return;
        }
        $user = $token->getUser();
        if (!$user instanceof User) {
            return;
        }

        if ($user->isForceLogout() === true) {
            $logoutRouteName = 'buyer.logout';
            if ($this->authorizationChecker->isGranted([SecurityService::ROLE_SUPER_ADMIN, SecurityService::ROLE_OPERATOR])) {
                $logoutRouteName = 'admin.logout';
            }

            //Redirect URL to logout
            $event->setResponse(new RedirectResponse($this->router->generate($logoutRouteName)));
        }


    }

    public function logout(Request $request, Response $response, TokenInterface $token)
    {
        // TODO: Implement logout() method.
    }
}
