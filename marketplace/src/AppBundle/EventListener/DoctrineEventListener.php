<?php

namespace AppBundle\EventListener;

use AppBundle\Entity\ActionHistorization;

use AppBundle\Entity\Node;
use AppBundle\Entity\NodeContent;
use AppBundle\Entity\User;
use Doctrine\ORM\Event\OnFlushEventArgs;
use Doctrine\ORM\ORMException;
use Doctrine\ORM\UnitOfWork;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class DoctrineEventListener
{
    const ACTION_CREATE = "create";
    const ACTION_UPDATE = "update";
    const ACTION_DELETE = "delete";

    private LogService $logService;
    private TokenStorageInterface $securityTokenStorage;
    private array $typesToBeChecked;
    private array $entitiesPath;


    public function __construct(LogService $logService, TokenStorageInterface $securityTokenStorage, array $typesToBeChecked, array $entitiesPath)
    {
        $this->logService = $logService;
        $this->securityTokenStorage = $securityTokenStorage;
        $this->typesToBeChecked = $typesToBeChecked;
        $this->entitiesPath = $entitiesPath;
    }

    public function onFlush(OnFlushEventArgs $args)
    {

        $em = $args->getEntityManager();
        $unitOfWork = $em->getUnitOfWork();

        //list CREATE operations
        foreach ($unitOfWork->getScheduledEntityInsertions() as $keyEntity => $entity) {
            if ($this->checkIfHistorizedEntity($entity)) {
                $this->generateHistory($entity, self::ACTION_CREATE, $keyEntity, $unitOfWork, $em);
            }
        }


        //list UPDATE operations
        foreach ($unitOfWork->getScheduledEntityUpdates() as $keyEntity => $entity) {

            if ($this->checkIfHistorizedEntity($entity)) {
                $this->generateHistory($entity, self::ACTION_UPDATE, $keyEntity, $unitOfWork, $em);
            }
        }

        //list DELETE operations
        foreach ($unitOfWork->getScheduledEntityDeletions() as $keyEntity => $entity) {

            if ($this->checkIfHistorizedEntity($entity)) {
                $this->generateHistory($entity, self::ACTION_DELETE, $keyEntity, $unitOfWork, $em);
            }
        }
    }

    /**
     * cheched if the actions on this entity must be historized
     * @param mixed $entity
     * @return bool true if the actions on this entity must be historized. false otherwise.
     */
    private function checkIfHistorizedEntity($entity)
    {
        foreach ($this->typesToBeChecked as $typeTobeCheck) {
            if ($entity instanceof $typeTobeCheck) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param $entity
     * @param $type
     * @param $unitOfWork
     * @param $em
     * @param $hash
     */
    private function generateHistory($entity, $type, $hash, UnitOfWork $unitOfWork, $em)
    {
        $history = new ActionHistorization();
        $history->setRefEntityId($entity->getTechnicalId());
        $history->setClassName($this->normalizeEntityClassName(get_class($entity)));

        if ($entity instanceof Node) {
            $history->setClassName($history->getClassName() . '\\' . $entity->getType());
        } else if ($entity instanceof NodeContent && $entity->getNode()) {
            $history->setClassName($history->getClassName() . '\\' . $entity->getNode()->getType());
        }
        $history->setType($type);
        $changes = null;
        //we only want to get change set on update modification type
        if ($type === self::ACTION_UPDATE) {
            try {
                $changes = $unitOfWork->getEntityChangeSet($entity);
                if ($changes != null && is_array($changes)) {

                    $reflectionObject = new \ReflectionClass(get_class($entity));
                    $object = $reflectionObject->newInstance();


                    // exclude login change. it's stored another way
                    if(count($changes)==1 && array_key_exists("lastLogin", $changes)) {
                        return;
                    }
                    if(array_key_exists("delegateDate", $changes)){
                        unset($changes["delegateDate"]);
                    }
                    if(array_key_exists("delegateUserName", $changes)){
                        unset($changes["delegateUserName"]);
                    }
                    if(array_key_exists("delegateUserType", $changes)){
                        unset($changes["delegateUserType"]);
                    }
                    if(count($changes)==0){
                        return;
                    }


                    $history->setChangeSet(json_encode($changes));
                }
            } catch (\Exception $e) {
                //we generate empty history
                $history->setChangeSet(json_encode(new \stdClass()));
            }
        } else if ($type === self::ACTION_CREATE) {
            $history->setChangeSet(json_encode($entity));
        }

        $history->setCreatedAt(new \DateTime());

        $user = $this->getCurrentUser();

        if ($user !== null && $user instanceof User) {
            $history->addUser($user);
        }


        try {
            //write legal log. If action is create, the log is written later with the id
            if ($type !== self::ACTION_CREATE) {
                $this->writeLog($user, $history, $changes);
            }

            $em->persist($history);
            // Instead of $em->flush() because we are already in flush process


            $unitOfWork->computeChangeSet($em->getClassMetadata(get_class($history)), $history);
        } catch (ORMException $e) {
            $this->logService->error("error while persisting history: " . $e->getMessage(), EventNameEnum::TECHNICAL_ERROR, null, ["exception" => $e->getTraceAsString()]);
        }
    }

    /**
     * write a log
     * @param User $user the user (or null)
     * @param ActionHistorization $history the action history to log
     */
    private function writeLog($user, $history, $changes)
    {

        if ($user !== null && $user instanceof User) {
            $this->logService->info("the following operation occurred on an entity: " . $history->getType(), EventNameEnum::ACTION_HISTORY, $user->getUsername(), [
                "entity_id" => $history->getRefEntityId(),
                "class_name" => $history->getClassName(),
                "operation_type" => $history->getType(),
                "change_set" => json_encode($changes)
            ]);
        } else {
            $this->logService->info("the following operation occurred on an entity: " . $history->getType(), EventNameEnum::ACTION_HISTORY, 'anonymous', [
                "entity_id" => $history->getRefEntityId(),
                "class_name" => $history->getClassName(),
                "operation_type" => $history->getType(),
                "change_set" => json_encode($changes)
            ]);
        }
    }

    /**
     * @return mixed the current user of null
     */
    private function getCurrentUser()
    {
        $user = null;
        if ($this->securityTokenStorage->getToken() !== null) {
            $user = $this->securityTokenStorage->getToken()->getUser();
        }
        return $user;
    }


    /**
     * Used to avoid to log proxified class name
     * @param string $className name of the class (can be a proxified entity)
     * @return string
     */
    private function normalizeEntityClassName($className)
    {

        foreach ($this->entitiesPath as $path) {
            $pos = strpos($className, $path);
            if ($pos !== false) {
                return substr($className, $pos, strlen($className));
            }
        }
        return $className;
    }
}
