<?php

namespace AppBundle\EventListener;

use AppBundle\Services\SecurityService;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class SessionExpiredListener
{
    private int $maxIdleTime;
    private SessionInterface $session;
    private TokenStorageInterface $tokenStorage;
    private TranslatorInterface $translator;
    private AuthorizationCheckerInterface $authorizationChecker;
    private RouterInterface $router;

    public function __construct(
        SessionInterface $session,
        AuthorizationCheckerInterface $authorizationChecker,
        TokenStorageInterface $tokenStorage,
        RouterInterface $router,
        TranslatorInterface $translator,
        int $maxIdleTime = 0
    )
    {
        $this->session = $session;
        $this->authorizationChecker = $authorizationChecker;
        $this->tokenStorage = $tokenStorage;
        $this->translator = $translator;
        $this->router = $router;
        $this->maxIdleTime = $maxIdleTime;
    }

    public function onKernelRequest(RequestEvent $event)
    {
        if (HttpKernelInterface::MAIN_REQUEST != $event->getRequestType()) {
            return;
        }

        if ($this->maxIdleTime > 0) {

            $this->session->start();
            $lapse = time() - $this->session->getMetadataBag()->getLastUsed();

            if ($lapse > $this->maxIdleTime) {

                $logoutRouteName = 'buyer.logout';
                if($this->authorizationChecker->isGranted([SecurityService::ROLE_SUPER_ADMIN, SecurityService::ROLE_OPERATOR])){
                    $logoutRouteName = 'admin.logout';
                }
                $this->tokenStorage->setToken(null);

                //Redirect URL to logout
                $event->setResponse(new RedirectResponse($this->router->generate($logoutRouteName)));
            }
        }
    }
}
