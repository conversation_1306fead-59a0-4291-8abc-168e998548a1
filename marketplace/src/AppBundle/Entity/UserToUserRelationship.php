<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

/**
 * UserToUserRelationship
 *
 * @ORM\Table(name="user_to_user_relationship")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\UserToUserRelationshipRepository")
 */
class UserToUserRelationship
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var User $parentUser
     *
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\User", inversedBy="parentUsers",cascade={"persist"})
     * @ORM\JoinColumn(name="parent_user_id", referencedColumnName="id", nullable=true)
     */
    private $parentUser;

    /**
     * @var User $childUser
     *
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\User", inversedBy="childUsers",cascade={"persist"})
     * @ORM\JoinColumn(name="child_user_id", referencedColumnName="id", nullable=true)
     */
    private $childUser;

    /**
     * @var bool
     *
     * @ORM\Column(name="isDelegate", type="boolean")
     */
    private $isDelegate;


    public function getId(): int
    {
        return $this->id;
    }

    public function setParentUser(User $parentUser): self
    {
        $this->parentUser = $parentUser;

        return $this;
    }

    public function getParentUser(): User
    {
        return $this->parentUser;
    }

    public function setChildUser(User $childUser): self
    {
        $this->childUser = $childUser;

        return $this;
    }

    public function getChildUser(): User
    {
        return $this->childUser;
    }

    public function setIsDelegate(bool $isDelegate): self
    {
        $this->isDelegate = $isDelegate;

        return $this;
    }

    public function getIsDelegate(): bool
    {
        return $this->isDelegate;
    }
}
