<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 30/01/2018
 * Time: 11:58
 */

namespace AppBundle\Entity;
use Doctrine\ORM\Mapping as ORM;


/**
 * @ORM\Table(name="search_historization")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\SearchHistorizationRepository")
 */
class SearchHistorization implements \JsonSerializable
{
    use ExportableEntityTrait;
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * created Time/Date
     *
     * @var \DateTime
     *
     * @ORM\Column(name="created_at", type="datetime", nullable=true)
     */
    private $createdAt;

    /**
     * @ORM\Column(name="is_anonymous", type="boolean", nullable=true, options={"default":true})
     */
    private $is_anonymous;

    /**
     * @ORM\Column(name="search_term", type="string", length=1024, nullable=true)
     */
    private $search_term;

    /**
     * @ORM\Column(name="filter", type="string", length=1024, nullable=true)
     */
    private $filter;

    /**
     * @ORM\Column(name="nb_hits", type="integer", nullable=true)
     */
    private $nb_hits;

  /**
   * SearchHistorization constructor.
   *
   * @param $is_anonymous
   * @param $search_term
   * @param $filter
   * @param $nb_hits
   */
  public function __construct(
    $is_anonymous,
    $search_term,
    $filter,
    $nb_hits
  )
  {
    $this->setCreatedAt();
    $this->is_anonymous = $is_anonymous;
    $this->search_term = $search_term;
    $this->filter = $filter;
    $this->nb_hits = $nb_hits;
  }

  /**
   * @return int
   */
  public function getId(): int
  {
    return $this->id;
  }

  /**
   * @param int $id
   */
  public function setId(int $id): void
  {
    $this->id = $id;
  }

  /**
   * @return \DateTime
   */
  public function getCreatedAt(): \DateTime
  {
    return $this->createdAt;
  }

  /**
   * @param \DateTime $createdAt
   */
  public function setCreatedAt(\DateTime $createdAt = null): void
  {
    if (empty($createdAt)){
      $createdAt = new \DateTime();
    }
    $this->createdAt = $createdAt;
  }

  /**
   * @return mixed
   */
  public function getIsAnonymous(): bool
  {
    return $this->is_anonymous;
  }

  /**
   * @param mixed $is_anonymous
   */
  public function setIsAnonymous(bool $is_anonymous): void
  {
    $this->is_anonymous = $is_anonymous;
  }

  /**
   * @return mixed
   */
  public function getSearchTerm()
  {
    return $this->search_term;
  }

  /**
   * @param mixed $search_term
   */
  public function setSearchTerm($search_term): void
  {
    $this->search_term = $search_term;
  }

  /**
   * @return mixed
   */
  public function getFilter()
  {
    return $this->filter;
  }

  /**
   * @param mixed $filter
   */
  public function setFilter($filter): void
  {
    $this->filter = $filter;
  }

  /**
   * @return mixed
   */
  public function getNbHits()
  {
    return $this->nb_hits;
  }

  /**
   * @param mixed $nb_hits
   */
  public function setNbHits($nb_hits): void
  {
    $this->nb_hits = $nb_hits;
  }
   public function jsonSerialize()
   {
        return [
            'id' => $this->id,
            'created_at' => $this->createdAt,
            'is_anonymous' => $this->is_anonymous,
            'search_term' => $this->search_term,
            'filter' => $this->filter,
            'nb_hits' => $this->nb_hits,
        ];
    }

}