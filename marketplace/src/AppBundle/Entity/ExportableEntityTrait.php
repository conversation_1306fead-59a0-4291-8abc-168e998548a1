<?php

namespace AppBundle\Entity;

use Exception;
use <PERSON><PERSON>\Serializer\Annotation\Exclude;

trait ExportableEntityTrait
{
    /**
     * @Exclude
     */
    private array $customProperties = [];

    public function getCustomProperties(): array
    {
        return $this->customProperties;
    }

    public function setCustomProperties(array $customProperties)
    {
        $this->customProperties = $customProperties;
    }

    public function addProperty(string $name, $value = "")
    {
        $this->customProperties[$name] = $value;
    }

    public function getPropertyValue(string $name)
    {
        try {
            return $this->customProperties[$name];
        } catch (Exception $e) {
            return "";
        }
    }
}