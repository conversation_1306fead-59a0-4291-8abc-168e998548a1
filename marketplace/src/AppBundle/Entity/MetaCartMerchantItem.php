<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * MetaCartMerchantItem
 *
 * @ORM\Table(name="meta_cart_merchant_item")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\MetaCartMerchantItemRepository")
 */
class MetaCartMerchantItem
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var int
     *
     * @ORM\Column(type="integer")
     */
    private $cartItemId;

    /**
     * @var MetaCartMerchant
     *
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\MetaCartMerchant")
     * @ORM\JoinColumn(name="meta_cart_merchant_id", referencedColumnName="id", onDelete="CASCADE")
     */
    private $metaCartMerchant;

    public function getId(): int
    {
        return $this->id;
    }

    public function getCartItemId(): int
    {
        return $this->cartItemId;
    }

    public function setCartItemId(int $cartItemId): self
    {
        $this->cartItemId = $cartItemId;
        return $this;
    }

    public function getMetaCartMerchant(): MetaCartMerchant
    {
        return $this->metaCartMerchant;
    }

    public function setMetaCartMerchant(MetaCartMerchant $metaCartMerchant): self
    {
        $this->metaCartMerchant = $metaCartMerchant;
        return $this;
    }
}
