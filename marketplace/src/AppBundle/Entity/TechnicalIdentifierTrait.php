<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;

trait TechnicalIdentifierTrait
{
    /**
     * @ORM\Column(name="technical_id", type="string", length=50, nullable=true)
     */
    private ?string $technicalId = null;

    /**
     * @ORM\PrePersist
     */
    public function initTechnicalId()
    {
        if ($this->technicalId === null) {
            $this->technicalId = md5(uniqid('', true));
        }
    }

    public function getTechnicalId(): ?string
    {
        return $this->technicalId;
    }

    public function setTechnicalId(?string $technicalId)
    {
        $this->technicalId = $technicalId;
    }
}