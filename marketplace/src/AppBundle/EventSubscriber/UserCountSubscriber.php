<?php

namespace AppBundle\EventSubscriber;

use App<PERSON><PERSON>le\Entity\User;
use AppBundle\Services\MessageService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\User\UserInterface;

class UserCountSubscriber implements EventSubscriberInterface
{
    private SessionInterface $session;
    private TokenStorageInterface $tokenStorage;
    private MessageService $messageService;

    public function __construct(
        SessionInterface      $session,
        TokenStorageInterface $tokenStorage,
        MessageService        $messageService
    )
    {
        $this->session = $session;
        $this->tokenStorage = $tokenStorage;
        $this->messageService = $messageService;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::REQUEST => 'updateUserCount',
        ];
    }

    public function updateUserCount(RequestEvent $event)
    {
        if (!$event->isMainRequest() || $event->getRequest()->isXmlHttpRequest()) {
            return;
        }

        $user = $this->getCurrentUser();

        if ($user) {
            $unreadMessage = $this->messageService->countBuyerUnreadThreadNumber($user);
            $this->session->set('unreadMessage', $unreadMessage);
        }
    }

    private function getCurrentUser(): null|User|UserInterface
    {
        if ($this->tokenStorage->getToken() !== null &&
            $this->tokenStorage->getToken()->getUser() !== null &&
            $this->tokenStorage->getToken()->getUser() instanceof User) {
            return $this->tokenStorage->getToken()->getUser();
        }

        return null;
    }
}
