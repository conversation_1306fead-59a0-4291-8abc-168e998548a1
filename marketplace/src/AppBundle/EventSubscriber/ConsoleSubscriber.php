<?php

namespace AppBundle\EventSubscriber;

use AppBundle\Services\MarketPlaceService;
use Open\IzbergBundle\Api\ApiClientManager;
use Open\IzbergBundle\Api\ApiConfigurator;
use Open\LogBundle\Service\LoggerConfigurator;
use Open\LogBundle\Service\LogService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\ConsoleEvents;
use Symfony\Component\Console\Event\ConsoleCommandEvent;
use Symfony\Component\Console\Exception\InvalidArgumentException;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class ConsoleSubscriber implements EventSubscriberInterface
{
    private ApiConfigurator $apiConfigurator;
    private ApiClientManager $apiClientManager;
    private LoggerConfigurator $loggerConfigurator;
    private MarketPlaceService $marketPlaceService;
    private LogService $logService;

    public function __construct(
        ApiConfigurator    $apiConfigurator,
        ApiClientManager   $apiClientManager,
        LoggerConfigurator $loggerConfigurator,
        LogService         $logService,
        MarketPlaceService $marketPlaceService
    )
    {
        $this->apiClientManager = $apiClientManager;
        $this->apiConfigurator = $apiConfigurator;
        $this->loggerConfigurator = $loggerConfigurator;
        $this->logService = $logService;
        $this->marketPlaceService = $marketPlaceService;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            ConsoleEvents::COMMAND => 'consoleCommand',
        ];
    }

    public function consoleCommand(ConsoleCommandEvent $event)
    {
        $command = $event->getCommand();
        $marketplace = null;
        try {
            $marketplace = $event->getInput()->getOption('marketplace');
        } catch (InvalidArgumentException $exception) {
            //every script don't need marketplace
        }

        if ($marketplace != null) {
            $this->marketPlaceService->configureIzbergApiForOperatorByName($marketplace);
        }
        $this->configureLoggerChannel();
    }


    private function configureLoggerChannel()
    {
        $this->logService->useConsoleChannel();
        $this->loggerConfigurator->configure($this->logService);
    }
}
