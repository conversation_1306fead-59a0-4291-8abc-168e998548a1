<?php

namespace AppBundle\EventSubscriber;

use App<PERSON><PERSON>le\Event\ProcessBeginForEvent;
use AppBundle\Event\ProcessEndForEvent;
use AppBundle\Model\ProcessId;
use Open\LogBundle\Service\Logger;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class ProcessEventSubscriber implements EventSubscriberInterface
{
    private array $loggers;

    public function __construct(Logger ...$loggers)
    {
        $this->loggers = $loggers;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            ProcessBeginForEvent::NAME => 'onProcessBegin',
            ProcessEndForEvent::NAME => 'onProcessEnd',
        ];
    }

    public function onProcessBegin(ProcessBeginForEvent $processBeginForEvent)
    {
        foreach ($this->loggers as $logger) {
            $logger->setDefaultContext(
                call_user_func(
                    function (ProcessId $processId) {
                        return [
                            'company_email' => $processId->getCompanyEmail(),
                            'order_id' => $processId->getOrderId(),
                            'order_number' => $processId->getOrderNumber(),
                            'merchant_order_id' => $processId->getMerchantOrderId(),
                            'transaction_id' => $processId->getTransactionId(),
                            'refund_id' => $processId->getRefundId(),
                        ];
                    },
                    $processBeginForEvent->getProcessId()
                )
            );
        }
    }

    public function onProcessEnd()
    {
        foreach ($this->loggers as $logger) {
            $logger->flushDefaultContext();
        }
    }
}
