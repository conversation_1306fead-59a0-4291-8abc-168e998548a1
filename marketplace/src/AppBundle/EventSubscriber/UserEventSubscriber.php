<?php

namespace AppBundle\EventSubscriber;

use AppBundle\Entity\User;
use AppBundle\Event\UserEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class UserEventSubscriber implements EventSubscriberInterface
{

    public static function getSubscribedEvents(): array
    {
        return [
          UserEvent::class => 'onUserEvent'
        ];
    }

    public function onUserEvent(UserEvent $event)
    {
        switch ($event->eventName) {
            case UserEvent::USER_CREATED:
                $this->userCreated($event->user);
                break;
            case UserEvent::USER_ACTIVATED:
                 $this->userActivated($event->user);
                break;
            case UserEvent::USER_DEACTIVATED:
                 $this->userDeactivated($event->user);
                break;
            case UserEvent::USER_PASSWORD_CHANGED:
                 $this->userPasswordChanged($event->user);
                break;
            case UserEvent::USER_PROMOTED:
                 $this->userPromoted($event->user);
                break;
            case UserEvent::USER_DEMOTED:
                 $this->userDemoted($event->user);
                break;
            default:
        }
    }

    public function userCreated(User $user)
    {
        // todo
    }

    public function userActivated(User $user)
    {
        // todo
    }

    public function userDeactivated(User $user)
    {
        // todo
    }

    public function userPasswordChanged(User $user)
    {
        // todo
    }

    public function userPromoted(User $user)
    {
        // todo
    }

    public function userDemoted(User $user)
    {
        // todo
    }
}
