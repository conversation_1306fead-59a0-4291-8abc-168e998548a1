<?php

namespace AppBundle\EventSubscriber;

use A<PERSON><PERSON><PERSON><PERSON>\Controller\MkoController;
use App<PERSON><PERSON>le\Entity\User;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpKernel\Event\GetResponseEvent;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\User\UserInterface;

class BuyerRegistrationSubscriber implements EventSubscriberInterface
{
    private RouterInterface $router;
    private TokenStorageInterface $tokenStorage;


    public function __construct(
        RouterInterface $router,
        TokenStorageInterface   $tokenStorage
    )
    {
        $this->router = $router;
        $this->tokenStorage = $tokenStorage;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::REQUEST => ['redirectToCompanyRegistrationForm', 0],
        ];
    }

    public function redirectToCompanyRegistrationForm(RequestEvent $event)
    {
        $request = $event->getRequest();

        if (!$event->isMainRequest() || preg_match('#/company/#', $request->getRequestUri())) {
            return;
        }

        $user = $this->getCurrentUser();

        if ($user && $user->getCompany() && $user->getCompany()->getStep() < 2) {
            $request->getSession()->set(MkoController::SESSION_ACCOUNT_CREATION, true);
            if (!strpos($request->getRequestUri(), "/company/info")) {
                $event->setResponse(new RedirectResponse($this->router->generate('front.company.info')));
            }
        }
    }

    private function getCurrentUser(): null|User|UserInterface
    {
        if ($this->tokenStorage->getToken() !== null &&
            $this->tokenStorage->getToken()->getUser() !== null &&
            $this->tokenStorage->getToken()->getUser() instanceof User) {
            return $this->tokenStorage->getToken()->getUser();
        }

        return null;
    }
}
