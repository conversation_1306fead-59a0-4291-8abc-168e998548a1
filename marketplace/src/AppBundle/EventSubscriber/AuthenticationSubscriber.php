<?php

namespace AppBundle\EventSubscriber;

use AppBundle\Entity\Connection;
use AppBundle\Entity\User;
use AppBundle\Services\BuyerService;
use AppBundle\Services\CartService;
use AppBundle\Services\SecurityService;
use AppBundle\Services\UserBddService;
use AppBundle\Util\SettingsProvider;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\ORMException;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\AuthenticationEvents;
use Symfony\Component\Security\Core\Exception\CustomUserMessageAuthenticationException;
use Symfony\Component\Security\Http\Event\LoginSuccessEvent;
use Symfony\Contracts\Translation\TranslatorInterface;

class AuthenticationSubscriber implements EventSubscriberInterface
{
    private EntityManagerInterface $em;
    private SettingsProvider $settings;
    private LogService $logService;
    private TranslatorInterface $translator;
    private RequestStack $requestStack;
    private RouterInterface $router;
    private SecurityService $securityService;
    private SessionInterface $session;
    private string $administratorToken;
    private BuyerService $buyerService;

    private UserBddService $userBddService;
    private CartService $cartService;

    public function __construct(
        EntityManagerInterface $em,
        SettingsProvider       $sp,
        LogService             $logService,
        TranslatorInterface    $translator,
        RequestStack           $requestStack,
        RouterInterface        $router,
        SessionInterface       $session,
        SecurityService        $securityService,
        BuyerService           $buyerService,
        UserBddService         $userBddService,
        CartService            $cartService
    )
    {
        $this->em = $em;
        $this->settings = $sp;
        $this->logService = $logService;
        $this->translator = $translator;
        $this->requestStack = $requestStack;
        $this->router = $router;
        $this->session = $session;
        $this->securityService = $securityService;
        $this->buyerService = $buyerService;
        $this->userBddService = $userBddService;
        $this->cartService = $cartService;
    }

    public static function getSubscribedEvents(): array
    {
        return array(
            AuthenticationEvents::AUTHENTICATION_FAILURE => 'onLoginFailure',
            LoginSuccessEvent::class => ['onInteractiveLogin', 10],
            KernelEvents::REQUEST => ['beforeFirewall', 10]
        );
    }


    public function beforeFirewall(RequestEvent $event)
    {
        $request = $event->getRequest();
        if ($request->isMethod(Request::METHOD_POST)) {
            if ($request->attributes->get('_route') === 'login_check') {

                /** @var User $user */
                $user = $this->userBddService->findUserByUsernameOrEmail(strval($request->request->get('_username')));

                if ($user &&
                    $this->securityService->isAdmin($user) &&
                    $user->getLoginAttempt() >= $this->settings->get('security.login_attempt_max')) {

                    //from here, all non meeting requirements must redirect user to authentication failure
                    $adminToken = $request->request->get('_admin_token');

                    if (!$adminToken || $adminToken !== $this->settings->get('security.login_administrator_token')) {
                        $this->logService->error("admin user has exceed the max login attempt and has entered a bad token",
                            EventNameEnum::SECURITY_EVENT,
                            null,
                            [
                                "username" => $user->getUsername(),
                                "login_attempt" => $user->getLoginAttempt(),
                                "expectedToken" => $this->administratorToken,
                                "actualToken" => $adminToken
                            ]);
                        $event->setResponse(new RedirectResponse($this->router->generate('front.login.failure')));
                        $this->session->set("SESSION_USE_TOKEN", true);
                    }
                }
            }
        }
    }

    public function onLoginFailure()
    {
        /** @var Request $request */
        $request = $this->requestStack->getCurrentRequest();

        // Get the username or email used in the form
        $username_email = strval($request->request->get('_username'));


        /** @var User $user */
        $user = $this->userBddService->findUserByUsernameOrEmail($username_email);

        // If the user exists
        if ($user) {
            $max_login_attempt = $this->settings->get('security.login_attempt_max');

            // Get number of failed attempt
            $login_count = $user->getLoginAttempt();

            // Increase
            $login_count++;
            $user->setLoginAttempt($login_count);
            $user->setLastFailedLogin(new \DateTime("now"));

            // Lock account if more than authorized
            if ($login_count >= $max_login_attempt) {
                if ($this->securityService->isAdmin($user)) {
                    $this->session->set("SESSION_USE_TOKEN", true);
                } else {
                    $user->setEnabled(false);
                    $user->setDisabledAt(new \DateTime("now"));
                }
            }
            //update user data
            $this->userBddService->saveUser($user);

            //in all case if user is not admin, we want to clear the session
            if (!$this->securityService->isAdmin($user) || $user->getLoginAttempt() < $max_login_attempt) {
                $this->session->remove("SESSION_USE_TOKEN");
            }

        } else {
            $this->session->remove("SESSION_USE_TOKEN");
        }
    }

    public function onInteractiveLogin(LoginSuccessEvent $event): void
    {
        // Retrieve current user
        $user = $event->getUser();

        if (!$user instanceof User) {
            return;
        }

        // Test if user is a buyer and have or can have an IzbergId
        if (!$this->buyerService->isUnRegistereIzbergBuyer($user)) {
            throw new CustomUserMessageAuthenticationException(
                $this->translator->trans('login.izbergBuyerError', array(), 'AppBundle')
            );
        }

        $this->buyerService->setDefaultBuyerCountryOfDelivery($user);
        $user->setForceLogout(false);
        $this->userBddService->saveUser($user);
        $this->updateLastUserConnection($user);
        $this->addConnectionHistory($user);
        $this->cartService->cleanOldCart($user);
    }

    /**
     * @deprecated
     */
    public function onImplicitLogin($event): void
    {
        // Retrieve current user
        $user = $event->getUser();
        if (!$user instanceof User) {
            return;
        }

        // Test if user is a buyer and have or can have an IzbergId
        if (!$this->buyerService->isUnRegistereIzbergBuyer($user)) {
            throw new CustomUserMessageAuthenticationException(
                $this->translator->trans('login.izbergBuyerError', array(), 'AppBundle')
            );
        }

        $this->logService->info("Successful user authentication", EventNameEnum::USER_AUTHENTICATED, $user->getUsername());

        $this->buyerService->setDefaultBuyerCountryOfDelivery($user);
        $user->setForceLogout(false);
        $this->userBddService->saveUser($user);
        $this->updateLastUserConnection($user);
        $this->addConnectionHistory($user);
        $this->cartService->cleanOldCart($user);
    }

    /**
     * @param User $user
     */
    private function addConnectionHistory($user)
    {
        $request = $this->requestStack->getCurrentRequest();

        $connection = new Connection();
        try {
            $cap = get_browser($request->headers->get('User-Agent'));
            $connection->setBrowser($cap->browser);
            $connection->setBrowserVersion($cap->version);
            $connection->setDeviceType($cap->device_type);
            $connection->setPlatform($cap->platform);

            $userAgent = $request->headers->get('User-Agent');
            if (!is_null($userAgent) && strlen($userAgent) > 199) {
                $userAgent = substr($userAgent, 0, 199);
            }
            $connection->setUserAgent($userAgent);

        } catch (\Exception $e) {
            $this->logService->error("Unable to get browser cap: " . $e->getMessage(), EventNameEnum::TECHNICAL_ERROR, $user->getUsername(), ["exception" => $e]);
        }


        $connection->setConnectedAt(new \DateTime());
        $connection->setUser($user);

        //getClientIp use X-Forwarded-For header only for trusted proxy
        $connection->setIp($request->getClientIp());

        $user->addConnection($connection);


        try {
            $this->em->persist($connection);
            $this->em->flush();
        } catch (ORMException $e) {
            $this->logService->error("Unable to add connection history to the user", EventNameEnum::TECHNICAL_ERROR, $user->getUsername(), ["exception" => $e]);
        }
    }

    private function updateLastUserConnection(User $user)
    {
        //update user last connection
        $user->setLastLogin(new \DateTime());

        $user->setLoginAttempt(0);
        $this->session->remove("SESSION_USE_TOKEN");

        //update company last connection
        if ($user->getCompany() != null) {
            $user->getCompany()->setLastConnexion(new \DateTime());
        }

        try {
            $this->em->merge($user);
            $this->em->flush();
        } catch (ORMException $e) {
            $this->logService->error("Unable update last connexionDate for user", EventNameEnum::TECHNICAL_ERROR, $user->getUsername());
        }
    }

    public function getRequestStack(): RequestStack
    {
        return $this->requestStack;
    }

    public function setRequestStack(RequestStack $requestStack): void
    {
        $this->requestStack = $requestStack;
    }

    public function getAdministratorToken(): string
    {
        return $this->administratorToken;
    }

    public function setAdministratorToken(string $administratorToken): void
    {
        $this->administratorToken = $administratorToken;
    }
}
