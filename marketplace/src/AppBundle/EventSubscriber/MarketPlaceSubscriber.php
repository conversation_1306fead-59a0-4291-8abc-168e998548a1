<?php

namespace AppBundle\EventSubscriber;

use A<PERSON><PERSON><PERSON>le\Entity\Merchant;
use AppBundle\Entity\User;
use Open\IzbergBundle\Api\ApiClientManager;
use Open\IzbergBundle\Api\ApiConfigurator;
use Open\LogBundle\Service\LogService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\ControllerArgumentsEvent;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class MarketPlaceSubscriber implements EventSubscriberInterface
{
    private TokenStorageInterface $tokenStorage;
    private ApiConfigurator $apiConfigurator;
    private ApiClientManager $apiClientManager;
    private LogService $logger;

    public function __construct(
        ApiClientManager      $apiClientManager,
        ApiConfigurator       $apiConfigurator,
        TokenStorageInterface $tokenStorage,
        LogService            $logger
    )
    {
        $this->apiClientManager = $apiClientManager;
        $this->apiConfigurator = $apiConfigurator;
        $this->tokenStorage = $tokenStorage;
        $this->logger = $logger;
    }

    public static function getSubscribedEvents(): array
    {

        return array(
            // must be registered before (i.e. with a higher priority than) the default Locale listener
            KernelEvents::REQUEST => ['onRouteCall', 1],
        );
    }

    public function onRouteCall(RequestEvent $event)
    {
        $token = $this->tokenStorage->getToken();
        $this->logger->info("configure izberg api conf", "API_CONF", null, []);
        if ($token != null) {
            $user = $token->getUser();

            if ($user instanceof Merchant) {
                $this->configureApiConnection($user->getMarketPlace()->getApiConfigurationKey());
                $this->logger->info("Conf izerg api, merchant:" . $user->getId(), "API_CONF", null, ["marketplace" => $user->getMarketPlace()->getName()]);
            } else {
                if ($user instanceof User && !is_null($user->getMarketPlaceConfigurationKey())) {
                    $this->logger->info("Conf izerg api, user:" . $user->getId(), "API_CONF", null, ["marketplace" => $user->getMarketPlace()->getName()]);
                    $this->configureApiConnection($user->getMarketPlaceConfigurationKey());
                }
            }
        }

    }

    private function configureApiConnection(string $connectionName = 'default')
    {
        $this->apiClientManager->useConnection($connectionName);
        $this->apiConfigurator->configure($this->apiClientManager);
    }
}
