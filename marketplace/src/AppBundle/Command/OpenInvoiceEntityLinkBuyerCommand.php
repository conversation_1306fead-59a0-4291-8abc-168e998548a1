<?php

namespace AppBundle\Command;

use AppBundle\Entity\User;
use AppBundle\Services\InvoiceEntityService;
use AppBundle\Services\UserBddService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\QuestionHelper;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\Question;

class OpenInvoiceEntityLinkBuyerCommand extends Command
{
    private UserBddService $userManager;
    private InvoiceEntityService $invoiceEntityService;

    public function __construct(InvoiceEntityService $invoiceEntityService, UserBddService $userManager)
    {
        $this->invoiceEntityService = $invoiceEntityService;
        $this->userManager = $userManager;
        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setName('open:invoice-entity:link-buyer')
            ->setDescription(
                'Link an existing buyer identified by email to an existing invoice Entity identified by name'
            )
            ->setDefinition([
                new InputArgument('buyer-email', InputArgument::REQUIRED, 'Buyer email'),
                new InputArgument('invoice-entity-name', InputArgument::REQUIRED, 'Invoice entity name'),
            ]);
    }

    protected function interact(InputInterface $input, OutputInterface $output)
    {
        $questionHelper = $this->getHelperSet()->get('question') ?? new QuestionHelper();
        $output->writeln('Welcome to the open buyer creator!');

        /* -- BUYER EMAIL -- */
        $buyerEmail = $input->getArgument('buyer-email');
        $buyerEmail = $questionHelper->ask($input, $output, new Question('Buyer email', $buyerEmail));
        $input->setArgument('buyer-email', $buyerEmail);

        /* -- INVOICE ENTITY NAME -- */
        $invoiceEntityName = $input->getArgument('invoice-entity-name');
        $invoiceEntityName = $questionHelper->ask(
            $input,
            $output,
            new Question('Invoice entity name', $invoiceEntityName)
        );
        $input->setArgument('invoice-entity-name', $invoiceEntityName);
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $buyerEmail = $input->getArgument('buyer-email');
        $invoiceEntityName = $input->getArgument('invoice-entity-name');

        /**
         * @var User $user
         */
        $user = $this->userManager->findUserByEmail($buyerEmail);

        $invoiceEntity = $this->invoiceEntityService->findInvoiceEntityByName($invoiceEntityName);

        if (!$user) {
            $output->writeln(
                sprintf(
                    'Buyer <comment>%s</comment> unknow',
                    $buyerEmail
                )
            );
        }
        if (!$invoiceEntity) {
            $output->writeln(
                sprintf(
                    'Invoice entity <comment>%s</comment> unknow',
                    $invoiceEntityName
                )
            );
        }

        if ($user && $invoiceEntity) {
            $user = $this->invoiceEntityService->attachBuyerToInvoiceEntity($user, $invoiceEntity);

            $output->writeln(
                sprintf(
                    'Trying to link <comment>%s</comment> buyer to <comment>%s</comment> invoice entity',
                    $user->getUsername(),
                    $user->getInvoiceEntity() !== null ? $user->getInvoiceEntity()->getName() : 'none'
                )
            );
        }
        return 0;
    }
}
