<?php

namespace AppBundle\Command;

use AppBundle\Services\MarketPlaceService;
use AppBundle\Services\MerchantService;
use DateTime;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

final class SyncMerchantCommand extends Command
{
    private MerchantService $merchantService;
    private MarketPlaceService $marketPlaceService;
    private LogService $logger;

    public function __construct(
        MarketPlaceService $marketPlaceService,
        MerchantService $merchantService,
        LogService $logger
    ) {
        $this->merchantService  = $merchantService;
        $this->logger = $logger;
        $this->marketPlaceService = $marketPlaceService;
        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setName('open:merchant:sync')
            ->addOption('marketplace', null, InputOption::VALUE_REQUIRED, 'the market place name')
            ->addOption('init', null, InputOption::VALUE_NONE, 'request all orderItem')
            ->addOption('force', null, InputOption::VALUE_OPTIONAL, 'force update for a merchant')
            ->setDescription('sync all merchant order(s) const center in database');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $startDateAll = new DateTime("now");
        $init = $input->getOption('init');
        $forceMerchant = (int)$input->getOption('force');
        $this->writeLog($output, "sync merchant start");
        $mkpName = $input->getOption('marketplace');
        $marketPlace = $this->marketPlaceService->getMarketPlaceByName($mkpName);
        $this->merchantService->syncMerchant($init, $marketPlace, $forceMerchant);

        $this->writeLog($output, "sync merchant  end");

        $time_interval_all = $startDateAll->diff(new Datetime("now"));
        $time_interval_all_string = $time_interval_all->format('%h hour %i min %s,%F sec');

        $output->writeln(sprintf(
            'Total script duration "%s"',
            $time_interval_all_string
        ));
        return 0;
    }

    private function writeLog(OutputInterface $output, $message, $data = [])
    {
        $output->writeln($message);
        $this->logger->info($message, EventNameEnum::COMMAND_SYNC_MERCHANT, null, $data);
    }
}
