<?php

namespace AppBundle\Command;

use AppBundle\Services\MarketPlaceService;
use AppBundle\Services\MerchantService;
use DateTime;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class UpdatedAttributeCommand extends Command
{
    private const DATE_FORMAT = "d-m-Y G:i:s.u";
    private const LAST_SYNC_DATE_FORMAT = 'Y-m-d H:i:s';

    private MerchantService $merchantService;
    private OutputInterface $output;
    private LogService $logger;
    private array $categories;
    private ParameterBagInterface $paramaterBag;

    /**
     * UpdateWithUpdatedAttributeCommand constructor.
     * @param MerchantService $merchantService
     * @param LogService $logger
     * @param ParameterBagInterface $parameterBag
     */
    public function __construct(MerchantService $merchantService, LogService $logger, ParameterBagInterface $parameterBag)
    {
        parent::__construct();
        $this->merchantService = $merchantService;
        $this->logger = $logger;
        $this->paramaterBag = $parameterBag;
    }

    protected function configure()
    {
        $this
            ->setName('open:merchant:updated_attribute')
            ->setDescription('Update merchant offer for merchant with updated attribute in the last day')
            ->addOption('marketplace', null, InputOption::VALUE_REQUIRED, 'the market place name')
            ->setHelp("This command tell to update merchant offers with latest info for the matching merchants. This command only notify with an event, the update process is unchanged");
    }


    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->output = $output;
        // Showing when the script is launched
        $now = new DateTime();
        $this->logInfo('Start : ' . $now->format(self::DATE_FORMAT) . ' ---');
        $mkpName = $input->getOption('marketplace');
        $this->categories[] = $this->paramaterBag->get('izberg_attribute_adapted');
        $this->categories[] = $this->paramaterBag->get('izberg_security_attribute');
        $this->categories[] = $this->paramaterBag->get('izberg_attribute_branch');
        $merchantId = [];

        // Get the lastSync file path
        $lastSyncFilePath = $this->paramaterBag->get('rabbit_queue_last_sync_file_path');
        $lastSyncFilePath = $lastSyncFilePath . '_' . $mkpName;

        if (file_exists($lastSyncFilePath)) {
            $lastSyncStr = (
            new DateTime(file_get_contents($lastSyncFilePath))
            )->format(self::LAST_SYNC_DATE_FORMAT);
        } else {
            $lastSyncStr = (new DateTime())->format(self::LAST_SYNC_DATE_FORMAT);
            file_put_contents($lastSyncFilePath, $lastSyncStr);
        }

        foreach ($this->categories as $category) {
            $merchantId = array_merge(
                $merchantId,
                $this->merchantService->findUpdatedMerchantByCategory($category, $lastSyncStr)
            );
        }

        /// Update last synchronization date
        $lastSync = (new DateTime())->format(self::LAST_SYNC_DATE_FORMAT);
        file_put_contents($lastSyncFilePath, $lastSync);

        // Update the merchant queue
        $this->merchantService->notifyUpdatedMerchants($merchantId);
        $now = new DateTime();
        $this->logInfo('End : ' . $now->format(self::DATE_FORMAT) . ' ---');
        return 0;
    }


    private function logInfo($message)
    {
        $this->output->writeln("<info>" . $message . "</info>");
        $this->logger->info($this->getName() . ": " . $message, EventNameEnum::INDEX_INFO);
    }
}
