<?php

namespace AppBundle\Command;

use DomainBundle\QueueConsumer\UpdateMerchantCatalog;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class UpdateMerchantCatalogCommand extends Command
{
    const DATE_FORMAT = "d-m-Y G:i:s.u";

    private OutputInterface $output;
    private LogService $logger;
    private UpdateMerchantCatalog $updateMerchantCatalogConsumer;

    /**
     * UpdateOfferPricesCommand constructor.
     *
     * @param UpdateMerchantCatalog $updateMerchantCatalogConsumer
     * @param LogService $logger
     */
    public function __construct(UpdateMerchantCatalog $updateMerchantCatalogConsumer, LogService $logger)
    {
        parent::__construct();
        $this->updateMerchantCatalogConsumer = $updateMerchantCatalogConsumer;
        $this->logger = $logger;
    }

    protected function configure()
    {
        $this
            ->setName('open:merchant:update_offers')
            ->setDescription('Update merchant offer with latest info from merchant')
            ->setHelp("This command updates merchant offers with latest info for the given merchant. This command will check rabbit mq");
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->output = $output;
        // Showing when the script is launched
        $now = new \DateTime();
        $this->logInfo('Start : ' . $now->format(self::DATE_FORMAT) . ' ---');

        $this->rabbitMQExecute();

        $now = new \DateTime();
        $this->logInfo('End : ' . $now->format(self::DATE_FORMAT) . ' ---');
        return 0;
    }

    private function rabbitMQExecute()
    {
        $this->updateMerchantCatalogConsumer->run();
    }

    private function logInfo($message)
    {
        $this->output->writeln("<info>" . $message . "</info>");
        $this->logger->info($this->getName() . ": " . $message, EventNameEnum::INDEX_INFO);
    }
}
