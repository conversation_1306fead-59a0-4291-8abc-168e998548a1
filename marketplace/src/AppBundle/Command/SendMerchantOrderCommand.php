<?php

namespace AppBundle\Command;

use AppBundle\Services\MerchantOrderService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

final class SendMerchantOrderCommand extends Command
{
    private MerchantOrderService $merchantOrderService;

    public function __construct(MerchantOrderService $merchantOrderService, $name = null)
    {
        $this->merchantOrderService = $merchantOrderService;
        parent::__construct($name);
    }

    protected function configure()
    {
        $this
            ->setName('open:order:send')
            ->setDescription('send merchant order through FTP to merchant');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->merchantOrderService->sendMerchantOrders();

        return 0;
    }
}
