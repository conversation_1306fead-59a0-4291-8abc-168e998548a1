<?php

namespace AppBundle\Command;

use AppBundle\Entity\BuyerAddress;
use AppBundle\Entity\Country;
use AppBundle\Entity\MetaCart;
use AppBundle\Entity\User;
use AppBundle\Repository\BuyerAddressRepository;
use AppBundle\Repository\CountryRepository;
use AppBundle\Repository\MetaCartRepository;
use AppBundle\Repository\UserRepository;
use Exception;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ImportBuyerAddressCommand extends Command
{
    private MetaCartRepository $metaCartRepository;
    private BuyerAddressRepository $buyerAddressRepository;
    private UserRepository $userRepository;
    private CountryRepository $countryRepository;

    public function __construct(MetaCartRepository $metaCartRepository, BuyerAddressRepository $buyerAddressRepository, UserRepository $userRepository, CountryRepository $countryRepository)
    {
        $this->metaCartRepository = $metaCartRepository;
        $this->buyerAddressRepository = $buyerAddressRepository;
        $this->userRepository = $userRepository;
        $this->countryRepository = $countryRepository;
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setName('open:address:import')
            ->setDescription('Import all addresses from v1')
            ->addArgument(
                'metafile',
                InputArgument::REQUIRED,
                'The .csv file where import metacart address from '
            )
            ->addArgument(
                'persofile',
                InputArgument::REQUIRED,
                'The .csv file where import personal address from'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $inputMetaFile = $input->getArgument('metafile');

        $output->writeln('Démarrage de l\'import des adresses de metacart');
        try {
            $fp = fopen($inputMetaFile, 'rb');

            $headers = fgetcsv($fp, 0, ';');

            if ($headers) {
                // Hazndle fichier valide

                while (($metaRow = fgetcsv($fp, 0, ';')) !== false) {
                    list(
                        $metacartId,
                        $userId,
                        $shippingName,
                        $shippingAddress,
                        $shippingAddress2,
                        $shippingZipCode,
                        $shippingCity,
                        $shippingCountryCode,
                        $shippingIzbId,
                        $personalAddress,
                        $shippingRecipientContact,
                        $shippingRecipientPhone,
                        $shippingRecipientComment,
                        $billingAddress,
                        $billingAddress2,
                        $billingZipCode,
                        $billingCity,
                        $billingCountryCode,
                        $billingIzbId
                        ) = $metaRow;

                    // Somes field will be necessary
                    if (empty($shippingAddress) || empty($shippingZipCode) || empty($shippingCity) || empty($billingAddress) || empty($billingZipCode) || empty($billingCity)) {
                        // Next row

                        continue;
                    }


                    /** @var MetaCart $metacart */
                    $metacart = $this->metaCartRepository->find((int) $metacartId);

                    if ($metacart && $metacart->getBuyer()->getId() === (int) $userId) {
                        $user = $metacart->getBuyer();

                        /** @var Country|null $shippingCountry */
                        $shippingCountry = $this->countryRepository->findByCode($shippingCountryCode);

                        $buyerShippingAddress = new BuyerAddress();

                        $buyerShippingAddress->setUser($user);
                        $buyerShippingAddress->setName($shippingName);
                        $buyerShippingAddress->setAddress($shippingAddress);
                        $buyerShippingAddress->setAddress2($shippingAddress2);
                        $buyerShippingAddress->setZipCode($shippingZipCode);
                        $buyerShippingAddress->setCity($shippingCity);
                        $buyerShippingAddress->setCountry($shippingCountry);

                        $buyerShippingAddress->setIzbergAddressId((int) $shippingIzbId);

                        $type = (int)$personalAddress > 0 ? BuyerAddress::
                        TYPE_PERSONAL : BuyerAddress::TYPE_TOTAL;

                        $buyerShippingAddress->setType($type);

                        $buyerShippingAddress->setContact($shippingRecipientContact);
                        $buyerShippingAddress->setPhone($shippingRecipientPhone);
                        $buyerShippingAddress->setComment($shippingRecipientComment);

                        $oldAddress = $this->getSameBuyerAddress($buyerShippingAddress);
                        foreach ($oldAddress as $address) {
                            if ($address instanceof BuyerAddress && $address->isIsActive()) {
                                $address->setIsActive(false);

                                $this->buyerAddressRepository->save($address);
                            }
                        }

                        $buyerShippingAddress->setIsActive(true);
                        $buyerShippingAddress = $this->buyerAddressRepository->save($buyerShippingAddress);

                        /** @var Country|null $billingCountry */
                        $billingCountry = $this->countryRepository->findByCode($billingCountryCode);

                        $buyerBillingAddress = new BuyerAddress();

                        $buyerBillingAddress->setIsActive(false);
                        $buyerBillingAddress->setType(BuyerAddress::TYPE_BILLING);

                        $buyerBillingAddress->setUser($user);
                        $buyerBillingAddress->setAddress($billingAddress);
                        $buyerBillingAddress->setAddress2($billingAddress2);
                        $buyerBillingAddress->setZipCode($billingZipCode);
                        $buyerBillingAddress->setCity($billingCity);
                        $buyerBillingAddress->setCountry($billingCountry);

                        $buyerBillingAddress->setIzbergAddressId((int) $billingIzbId);

                        $sameBuyerBillingAddress = $this->getSameBuyerAddress($buyerBillingAddress);
                        if (count($sameBuyerBillingAddress) > 0) {
                            $buyerBillingAddress = array_shift($sameBuyerBillingAddress);
                        } else {
                            $buyerBillingAddress = $this->buyerAddressRepository->save($buyerBillingAddress);
                        }

                        $metacart->setBuyerShippingAddress($buyerShippingAddress);
                        $metacart->setBuyerBillingAddress($buyerBillingAddress);

                        $this->metaCartRepository->save($metacart);
                    }
                }
            }
        } catch (Exception $e) {
            $output->writeln($e->getMessage());
            return 1;
        } finally {
            $output->writeln('Fin de l\'import des addresses de metacart');
        }


        $inputPersoFile = $input->getArgument('persofile');

        $output->writeln('Démarrage de l\'import des adresses perso restante');
        try {
            $fp = fopen($inputPersoFile, 'rb');

            $headers = fgetcsv($fp, 0, ';');

            if ($headers) {
                while (($persoRow = fgetcsv($fp, 0, ';')) !== false) {
                    list(
                        $userId,
                        $persoName,
                        $persoAddress,
                        $persoAddress2,
                        $persoZipCode,
                        $persoCity,
                        $persoCountryCode,
                        $persoContact,
                        $persoPhone,
                        $persoComment
                        ) = $persoRow;

                    // Somes field will be necessary
                    if (empty($persoAddress) || empty($persoZipCode) || empty($persoCity)) {
                        // next row
                        continue;
                    }

                    /** @var User $user */
                    $user = $this->userRepository->find((int) $userId);

                    if ($user) {
                        /** @var Country|null $personalCountry */
                        $personalCountry = $this->countryRepository->findByCode($persoCountryCode);

                        $personalBuyerAddress = new BuyerAddress();
                        $personalBuyerAddress->setUser($user);
                        $personalBuyerAddress->setType(BuyerAddress::TYPE_PERSONAL);
                        $personalBuyerAddress->setIsActive(true);

                        $personalBuyerAddress->setName($persoName);

                        $personalBuyerAddress->setAddress($persoAddress);
                        $personalBuyerAddress->setAddress2($persoAddress2);
                        $personalBuyerAddress->setZipCode($persoZipCode);
                        $personalBuyerAddress->setCity($persoCity);
                        $personalBuyerAddress->setCountry($personalCountry);

                        $personalBuyerAddress->setContact($persoContact);
                        $personalBuyerAddress->setPhone($persoPhone);
                        $personalBuyerAddress->setComment($persoComment);

                        $this->buyerAddressRepository->save($personalBuyerAddress);
                    }
                }
            }
        } catch (Exception $e) {
            $output->writeln($e->getMessage());
            return 1;
        } finally {
            $output->writeln('Fin de l\'import des addresses perso restante');
        }
        return 0;
    }

    private function getSameBuyerAddress(BuyerAddress $buyerAddress): array
    {
        return $this->buyerAddressRepository->findBy([
                'user' => $buyerAddress->getUser(),
                'name' => $buyerAddress->getName(),

                'address' => $buyerAddress->getAddress(),
                'address2' => $buyerAddress->getAddress2(),
                'zipCode' => $buyerAddress->getZipCode(),
                'city' => $buyerAddress->getCity(),
                'country' => $buyerAddress->getCountry(),

                'izbergAddressId' => $buyerAddress->getIzbergAddressId(),
            ]);
    }
}
