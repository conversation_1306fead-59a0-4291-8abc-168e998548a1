<?php

namespace AppBundle\Command;

use AppBundle\Exception\CreateUserCommandException;
use AppBundle\Util\UserManipulator;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\Question;

class CreateUserCommand extends Command
{
    private const EMAIL = 'email';
    private const FIRST_NAME = 'first_name';
    private const LAST_NAME = 'last_name';
    private const MDP = 'password';
    private const SADMIN = 'super-admin';
    private const FUNCTION = 'function';

    private UserManipulator $userManipulator;

    /**
     * CreateUserCommand constructor.
     * @param UserManipulator $userManipulator
     */
    public function __construct(UserManipulator $userManipulator)
    {
        parent::__construct();
        $this->userManipulator = $userManipulator;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this
            ->setName('open:user:create')
            ->setDescription('Create an operator/superadmin user.')
            ->setDefinition(array(
                new InputArgument(self::EMAIL, InputArgument::REQUIRED, 'Email'),
                new InputArgument(self::FIRST_NAME, InputArgument::REQUIRED, 'First Name'),
                new InputArgument(self::LAST_NAME, InputArgument::REQUIRED, 'Last Name'),
                new InputArgument(self::MDP, InputArgument::REQUIRED, 'Password'),
                new InputArgument(self::FUNCTION, InputArgument::REQUIRED, 'User Function'),
                new InputOption(self::SADMIN, null, InputOption::VALUE_NONE, 'Set the user as super admin')
            ));
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $username = uniqid('user_');
        $email = $input->getArgument(self::EMAIL);
        $first_name = $input->getArgument(self::FIRST_NAME);
        $last_name = $input->getArgument(self::LAST_NAME);
        $password = $input->getArgument(self::MDP);
        $superadmin = $input->getOption(self::SADMIN);

        $this->userManipulator->create($username, $password, $email, $first_name, $last_name, $superadmin, 'default');

        $output->writeln(sprintf('Created user <comment>%s</comment>', $username));
        return 0;
    }

    /**
     * {@inheritdoc}
     */
    protected function interact(InputInterface $input, OutputInterface $output)
    {
        $questions = array();

        $this->buildQuestion($questions, $input, self::EMAIL);
        $this->buildQuestion($questions, $input, self::FIRST_NAME);
        $this->buildQuestion($questions, $input, self::LAST_NAME);
        $this->buildQuestion($questions, $input, self::MDP);
        $this->buildQuestion($questions, $input, self::FUNCTION);

        foreach ($questions as $name => $question) {
            $answer = $this->getHelper('question')->ask($input, $output, $question);
            $input->setArgument($name, $answer);
        }
    }

    private function buildQuestion(&$questions, InputInterface $input, $fieldName)
    {
        if (!$input->getArgument($fieldName)) {
            $question = new Question('Please choose a ' . $fieldName . ':');
            $question->setValidator(function ($field) {
                if (empty($field)) {
                    throw new CreateUserCommandException('field can not be empty');
                }
                return $field;
            });

            $question->setHidden(true);
            $questions[$fieldName] = $question;
        }
    }
}
