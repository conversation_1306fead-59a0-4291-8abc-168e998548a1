<?php

namespace AppBundle\Command;

use AppBundle\Entity\Setting;
use AppBundle\Repository\SettingRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class SettingsInitializeCommand extends Command
{
    private SettingRepository $settingRepository;
    private EntityManagerInterface $em;
    private ParameterBagInterface $parameterBag;

    /**
     * SettingsInitializeCommand constructor.
     * @param SettingRepository $settingRepository
     * @param EntityManagerInterface $em
     */
    public function __construct(SettingRepository $settingRepository, EntityManagerInterface $em, ParameterBagInterface $parameterBag)
    {
        parent::__construct();
        $this->settingRepository = $settingRepository;
        $this->em = $em;
        $this->parameterBag = $parameterBag;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this
            ->setName('open:settings:initialize')
            ->setDescription('Initialize System Settings.');
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $settings_config = array();

        // Get all settings grouped by domain
        foreach ($this->parameterBag->get('app.settings') as $domain => $groups) {
            $settings_config[$domain] = array();

            foreach ($groups as $keys) {
                foreach ($keys as $key_name => $setting) {
                    $settings_config[$domain][$key_name] = $setting;
                }
            }
        }

        // Find all settings in the DB (shouldn't be that many anyway)
        $flat_keys = $this->settingRepository->findAll();

        $domain_keys = array();

        // Group the settings by domain so we can compare with $settings_config
        /** @var Setting $setting */


        foreach ($flat_keys as $setting) {
            if (!isset($domain_keys[$setting->getDomain()])) {
                $domain_keys[$setting->getDomain()] = array();
            }

            $domain_keys[$setting->getDomain()][$setting->getName()] = $setting;
        }

        $need_flush = false;


        // For all defined settings in config.yml
        foreach ($settings_config as $domain => $keys) {
            foreach ($keys as $name => $setting) {
                // If the setting is not found in the DB then persist it
                if (!isset($domain_keys[$domain][$name])) {
                    $output->writeln(sprintf('<comment>Setting %s.%s will be created</comment>', $domain, $name));

                    $new_setting = new Setting();

                    $new_setting->setDomain($domain);
                    $new_setting->setName($name);
                    $new_setting->setValueType($setting['value_type']);
                    $new_setting->setValue($setting['default_value']);
                    $new_setting->setRequired($setting['required']);
                    $new_setting->setTags($setting['tags']);

                    $this->em->persist($new_setting);

                    $need_flush = true;
                }
            }
        }

        // Do we need to flush ?
        if ($need_flush) {
            $this->em->flush();
            $output->writeln(sprintf('<info>Settings table has been updated</info>'));
        } else {
            $output->writeln(sprintf('<info>Settings table is already up to date</info>'));
        }
        return 0;
    }
}
