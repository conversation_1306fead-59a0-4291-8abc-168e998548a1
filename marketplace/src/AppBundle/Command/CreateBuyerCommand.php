<?php

namespace AppBundle\Command;

use AppBundle\Services\BuyerService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\QuestionHelper;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\Question;

class CreateBuyerCommand extends Command
{
    private BuyerService $buyerService;

    public function __construct(BuyerService $buyerService)
    {
        $this->buyerService = $buyerService;
        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setName('open:buyer:create')
            ->setDescription('Create a buyer user.')
            ->setDefinition([
                new InputArgument('email', InputArgument::REQUIRED, 'Email'),
                new InputArgument('first-name', InputArgument::REQUIRED, 'First Name'),
                new InputArgument('last-name', InputArgument::REQUIRED, 'Last Name'),
                new InputArgument('password', InputArgument::REQUIRED, 'Password'),
            ]);
    }

    protected function interact(InputInterface $input, OutputInterface $output): int
    {
        $questionHelper = $this->getHelperSet()->get('question') ?? new QuestionHelper();
        $output->writeln('Welcome to the open buyer creator!');

        /* -- EMAIL -- */
        $email = $input->getArgument('email');
        $email = $questionHelper->ask($input, $output, new Question('Email', $email));
        $input->setArgument('email', $email);

        /* -- FIRST NAME -- */
        $firstName = $input->getArgument('first-name');
        $firstName = $questionHelper->ask($input, $output, new Question('First Name', $firstName));
        $input->setArgument('first-name', $firstName);

        /* -- LAST NAME -- */
        $lastName = $input->getArgument('last-name');
        $lastName = $questionHelper->ask($input, $output, new Question('Last Name', $lastName));
        $input->setArgument('last-name', $lastName);

        /* -- PASSWORD -- */
        $password = $input->getArgument('password');
        $password = $questionHelper->ask($input, $output, new Question('Password', $password));
        $input->setArgument('password', $password);
        return 0;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $email = $input->getArgument('email');
        $firstName = $input->getArgument('first-name');
        $lastName = $input->getArgument('last-name');
        $password = $input->getArgument('password');

        $buyer = $this->buyerService->createBuyer($email, $password, $firstName, $lastName);

        $output->writeln(sprintf('Created buyer <comment>%s</comment>', $buyer->getUsername()));
        return 0;
    }
}
