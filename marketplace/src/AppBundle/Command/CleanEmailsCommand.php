<?php

namespace AppBundle\Command;

use AppBundle\Entity\Node;
use AppBundle\Repository\NodeRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class CleanEmailsCommand extends Command
{
    public const DATE_FORMAT = "d-m-Y G:i:s";

    private EntityManagerInterface $em;
    private NodeRepository $nodeRepository;

    /**
     * CleanEmailsCommand constructor.
     * @param EntityManagerInterface $em
     * @param NodeRepository $nodeRepository
     */
    public function __construct(EntityManagerInterface $em, NodeRepository $nodeRepository)
    {
        parent::__construct();
        $this->em = $em;
        $this->nodeRepository = $nodeRepository;
    }

    protected function configure()
    {
        $this
            ->setName("open:clean:emails")
            ->setDescription('clean all emails templates from DB')
            ->setHelp('clean all emails templates from DB');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $now = new \DateTime();
        $output->writeln('<comment>Start : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        $nodes = $this->nodeRepository->findBy(['type' => Node::TYPE_EMAIL]);

        if (!empty($nodes)) {
            $progress = new ProgressBar($output, count($nodes));
            $progress->start();

            /** @var Node $node */
            foreach ($nodes as $node) {
                $this->em->remove($node);
            }

            $this->em->flush();
            $this->em->clear();

            $progress->finish();
        }

        $now = new \DateTime();
        $output->writeln('<comment>End : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');
        return 0;
    }
}
