<?php

namespace AppBundle\Command\Trigger;

use AppBundle\Exception\MarketPlaceException;
use AppBundle\Services\MarketPlaceService;
use Open\IzbergBundle\Api\ApiException;
use Open\IzbergBundle\Api\WebHookApi;
use Open\LogBundle\Service\LogService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\HttpFoundation\File\File;

class TriggerWebHookCommand extends Command
{
    private OutputInterface $output;
    private LogService $logger;
    private MarketPlaceService $marketPlaceService;
    private WebHookApi $webHookApi;

    public function __construct(
        LogService         $logger,
        MarketPlaceService $marketPlaceService,
        WebHookApi         $webHookApi
    )
    {
        parent::__construct();
        $this->logger = $logger;
        $this->marketPlaceService = $marketPlaceService;
        $this->webHookApi = $webHookApi;
    }

    protected function configure(): void
    {
        $this->setName('open:webhook:trigger')
            ->setDescription('issue retrigger call on izberg api')
            ->addOption('filepath', null, InputOption::VALUE_REQUIRED, 'file with comma-separated webhook id')
            ->addOption('marketplace', null, InputOption::VALUE_REQUIRED, 'the market place name');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->output = $output;
        $path = $input->getOption('filepath');

        $mkpName = $input->getOption('marketplace');

        try {
            $marketPlace = $this->marketPlaceService->getMarketPlaceByName($mkpName);
        } catch (MarketPlaceException $ex) {
            $this->output->writeln("<error>unknown marketplace $mkpName</error>");
        }

        if (!file_exists($path)) {
            $output->writeln("<error>file not found</error>");
        }
        $file = new File($path);

        $this->webHookApi->configureApiConnection($marketPlace->getApiConfigurationKey());

        $content = $file->getContent();
        $webhookIds = preg_split('/,|;/', $content, -1, PREG_SPLIT_NO_EMPTY);

        foreach ($webhookIds as $webhookId) {
            $this->trigger($webhookId);
            sleep(1);
        }

        return 0;
    }

    private function trigger(string $webhookId): void
    {
        try {
            $ckeck = $this->webHookApi->retrigger($webhookId);

            if($ckeck){
                $this->writeLog(
                    '[WEBHOOK] retrigger success:' . $webhookId,
                    [
                        'id' => $webhookId,
                        'response' => $ckeck
                    ]
                );
            }else{
                $this->writeLog(
                    "[WEBHOOK] retrigger failed:" . $webhookId,
                    [
                        'id' => $webhookId,
                        'response' => $ckeck
                    ],
                    true
                );
            }


        } catch (ApiException $ex) {
            $this->writeLog(
                "[WEBHOOK] retrigger failed:" . $webhookId,
                [
                    "message" => $ex->getMessage(),
                    "trace" => $ex->getTraceAsString()
                ],
                true
            );
        }
    }

    private function writeLog(string $message, array $context, bool $error = false): void
    {

        if ($error) {
            $prefixMessage = "<error>%s</error>";
            $this->output->writeln(sprintf($prefixMessage, $message));
            $this->logger->error($message, $context);
        } else {
            $prefixMessage = "<info>%s</info>";
            $this->output->writeln(sprintf($prefixMessage, $message));
            $this->logger->info($message, $context);
        }
    }
}
