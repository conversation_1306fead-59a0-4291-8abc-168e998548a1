<?php

namespace AppBundle\Command;

use AppBundle\Model\Offer;
use AppBundle\Services\MarketPlaceService;
use AppBundle\Services\OfferService;
use AppBundle\Util\MemoryUtil;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class UpdateOfferPricesCommand extends Command
{
    const DATE_FORMAT = "d-m-Y G:i:s.u";

    private OfferService $offerService;
    private MarketPlaceService $marketPlaceService;
    private OutputInterface $output;
    private LogService $logger;

    /**
     * UpdateOfferPricesCommand constructor.
     * @param OfferService $offerService
     * @param MarketPlaceService $marketPlaceService
     * @param LogService $logger
     */
    public function __construct(OfferService $offerService, MarketPlaceService $marketPlaceService, LogService $logger)
    {
        parent::__construct();
        $this->offerService = $offerService;
        $this->marketPlaceService = $marketPlaceService;
        $this->logger = $logger;
    }


    protected function configure()
    {
        $this
            ->setName('open:offer:update_prices')
            ->setDescription('Update offer prices in the elastic search on the basis of the current change rates')
            ->setHelp("This command updates offer prices in the elastic search on the basis of the current change rates")
            ->addOption('marketplace', null, InputOption::VALUE_REQUIRED, 'the market place name');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->output = $output;
        // Showing when the script is launched
        $now = new \DateTime();
        $this->logInfo('Start : ' . $now->format(self::DATE_FORMAT) . ' ---');

        $mkpName = $input->getOption('marketplace')??'france';
        $marketPlace = $this->marketPlaceService->getMarketPlaceByName($mkpName);

        // Get the offers from offer service
        $page = 1;
        $pageSize = 30;
        //init the cursor
        $result = $this->offerService->scan($pageSize, "30s", null, "fr", $marketPlace);

        while (count($result->getOffers()) !== 0) {
            /** @var Offer $offer */
            foreach ($result->getOffers() as $offer) {
                //only continue if offer price is set
                if ($offer->getPrice() !== null) {
                    $this->offerService->addOrUpdateDetailedPricesForOffer($offer, $marketPlace);
                }
            }
            $this->logInfo("page:" . $page . ", nb offer:" . $page * $pageSize . ", Inner loop memory usage: " . MemoryUtil::getMemoryUsageAsString());
            // Increment page number
            $page++;
            //update cursor
            $result = $this->offerService->scroll($result->getScrollId(), "30s", "fr");
        }

        $now = new \DateTime();
        $this->logInfo('End : ' . $now->format(self::DATE_FORMAT) . ' ---');
        return 0;
    }

    private function logInfo($message)
    {
        $this->output->writeln("<info>" . $message . "</info>");
        $this->logger->info($this->getName() . ": " . $message, EventNameEnum::INDEX_INFO);
    }
}
