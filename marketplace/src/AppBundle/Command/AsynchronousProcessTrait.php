<?php

namespace AppBundle\Command;

use AppBundle\Entity\User;
use AppBundle\Services\ProcessService;
use Open\IzbergBundle\Api\ApiClientManager;
use Open\IzbergBundle\Api\ApiConfigurator;
use Open\IzbergBundle\Api\AuthenticationApi;

trait AsynchronousProcessTrait
{
    /** @var ProcessService */
    protected $processService;

    /** @var ApiClientManager */
    protected $apiClientManager;

    /** @var ApiConfigurator */
    protected $apiConfigurator;

    /** @var AuthenticationApi */
    protected $authenticationApi;

    public function isAsynchronousProcess(): bool
    {
        return true;
    }

    public function getApiClientManager(): ApiClientManager
    {
        return $this->apiClientManager;
    }

    public function setApiClientManager(ApiClientManager $apiClientManager): void
    {
        $this->apiClientManager = $apiClientManager;
    }

    public function getApiConfigurator(): ApiConfigurator
    {
        return $this->apiConfigurator;
    }

    public function setApiConfigurator(ApiConfigurator $apiConfigurator): void
    {
        $this->apiConfigurator = $apiConfigurator;
    }

    public function getAuthenticationApi(): AuthenticationApi
    {
        return $this->authenticationApi;
    }

    public function setAuthenticationApi(AuthenticationApi $authenticationApi): void
    {
        $this->authenticationApi = $authenticationApi;
    }

    public function useUserConnection(User $user)
    {
        $this->apiConfigurator->generateAsyncUserConfiguration($user, $this->getAuthenticationApi());
        $this->apiClientManager->useConnection(ApiConfigurator::CONNECTION_ASYNC_USER_ACTION);
        $this->apiConfigurator->configure($this->apiClientManager);
    }
}
