<?php

namespace AppBundle\Command;

use AppBundle\Entity\User;
use AppBundle\Services\BuyerService;
use AppBundle\Services\UserBddService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\QuestionHelper;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\Question;

class OpenBuyerDelegateCommand extends Command
{
    private BuyerService $buyerService;
    private UserBddService $userManager;

    public function __construct(BuyerService $buyerService, UserBddService $userManager, $name = null)
    {
        $this->buyerService = $buyerService;
        $this->userManager = $userManager;
        parent::__construct($name);
    }

    protected function configure(): void
    {
        $this
            ->setName('open:buyer:delegate')
            ->setDescription('Create a command to link delegate User to an existing Buyer')
            ->setDefinition([
                new InputArgument('buyer-email', InputArgument::REQUIRED, 'Buyer email'),
                new InputArgument('delegate-email', InputArgument::REQUIRED, 'Delegate email'),
            ]);
    }

    protected function interact(InputInterface $input, OutputInterface $output): void
    {
        $questionHelper = $this->getHelperSet()->get('question') ?? new QuestionHelper();
        $output->writeln('Welcome to the open buyer creator!');

        /* -- CHILD BUYER EMAIL -- */
        $buyerEmail = $input->getArgument('buyer-email');
        $buyerEmail = $questionHelper->ask($input, $output, new Question('Buyer user email', $buyerEmail));
        $input->setArgument('buyer-email', $buyerEmail);

        /* -- PARENT BUYER EMAIL -- */
        $delegateEmail = $input->getArgument('delegate-email');
        $delegateEmail = $questionHelper->ask($input, $output, new Question('Delegate user email', $delegateEmail));
        $input->setArgument('delegate-email', $delegateEmail);
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $buyerEmail = $input->getArgument('buyer-email');
        $delegateEmail = $input->getArgument('delegate-email');

        if ($buyerEmail === $delegateEmail) {
            // User can't be directly self managed

            $output->writeln(
                sprintf('A buyer can\'t be delegated by himself')
            );
        } else {

            /**
             * @var User $buyer
             */
            $buyer = $this->userManager->findUserByEmail($buyerEmail);

            /**
             * @var User $delegate
             */
            $delegate = $this->userManager->findUserByEmail($delegateEmail);

            if (!$buyer) {
                $output->writeln(
                    sprintf(
                        'Parent buyer <comment>%s</comment> unknow',
                        $buyerEmail
                    )
                );
            }
            if (!$delegate) {
                $output->writeln(
                    sprintf(
                        'Child buyer <comment>%s</comment> unknow',
                        $delegateEmail
                    )
                );
            }

            if ($delegate && $buyer) {
                // lineManager is !isDelegate (manager is opposite of delegate)

                $this->buyerService->attachBuyerToDelegate($buyer, $delegate, null, true);

                $output->writeln(
                    sprintf(
                        'Create a link from <comment>%s</comment> buyer to the delegate <comment>%s</comment>',
                        $delegate->getUsername(),
                        $buyer->getUsername()
                    )
                );
            }
        }
        return 0;
    }
}
