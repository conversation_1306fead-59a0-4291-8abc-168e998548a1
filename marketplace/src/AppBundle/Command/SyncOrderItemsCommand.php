<?php

namespace AppBundle\Command;

use AppBundle\Entity\MarketPlace;
use AppBundle\Exception\MarketPlaceException;
use AppBundle\Services\MarketPlaceService;
use AppBundle\Services\OrderService;
use Open\LogBundle\Service\LogService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\HttpFoundation\File\File;

final class SyncOrderItemsCommand extends Command
{
    private OrderService $orderService;
    private MarketPlaceService $marketPlaceService;
    private LogService  $logService;

    public function __construct(MarketPlaceService $marketPlaceService, OrderService $orderService, LogService  $logService)
    {
        $this->orderService = $orderService;
        $this->marketPlaceService = $marketPlaceService;
        $this->logService = $logService;
        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setName('open:order-item:sync')
            ->addOption('marketplace', null, InputOption::VALUE_REQUIRED, 'the market place name')
            ->addArgument('merchantOrder', InputArgument::OPTIONAL, 'merchant order filter')
            ->addOption('init', null, InputOption::VALUE_NONE, 'request all orderItem')
            ->addOption('since', null, InputOption::VALUE_REQUIRED, 'get last x days')
            ->addOption('filepath', null, InputOption::VALUE_OPTIONAL, 'filepath stored merchant order id')
            ->setDescription('sync all order items from izberg merchant orders in database');
    }


    /**
     * @throws MarketPlaceException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $mkpName = $input->getOption('marketplace');
        $marketPlace = $this->marketPlaceService->getMarketPlaceByName($mkpName);

        $merchantOrder = $input->getArgument('merchantOrder');

        if ($this->myIsInt($merchantOrder)) {
            $merchantOrder = intval($merchantOrder);
        }else{
            $merchantOrder = null;
        }


        $init = $input->getOption('init');
        $since = $input->getOption('since');
        $sinceNum = null;
        if ($since && !$this->myIsInt($since)) {
            $output->writeln("<error>since parameter must be an integer.</error>");
            return 1;
        } else {
            $sinceNum = intval($since);
        }
        $path = $input->getOption('filepath');

        if ($path !== null) {
            $output->writeln("<info>filepath mode</info>");
            $this->logService->info("filepath mode", "SYNC_ORDER_ITEMS");
            return $this->syncOrderWithFile($output, false, $marketPlace, $path, $sinceNum);
        }

        $output->writeln("<info>normal mode</info>");
        $this->logService->info("normal mode", "SYNC_ORDER_ITEMS");
        $this->orderService->syncOrderItems($init, $marketPlace, $sinceNum, $merchantOrder);
        return 0;
    }

    private function syncOrderWithFile(OutputInterface $output, bool $init, MarketPlace $marketPlace, string $path, ?int $sinceNum = null): int
    {

        if (!file_exists($path)) {
            $output->writeln("<error>file not found</error>");
            $this->logService->error("file not found:".$path, "SYNC_ORDER_ITEMS");
            return -1;
        } else {
            $file = new File($path);
            $content = $file->getContent();
            $merchantOrderIds = preg_split('/,/', $content, -1, PREG_SPLIT_NO_EMPTY);
            if (!empty($merchantOrderIds)) {
                $output->writeln("<info>merchantOrderIds founds !</info>");
                $this->logService->info("merchantOrderIds founds !", "SYNC_ORDER_ITEMS");
                foreach ($merchantOrderIds as $merchantOrderId) {
                    $id = trim($merchantOrderId);
                    $output->writeln("<info>will sync item for :".$id."</info>");
                    $this->logService->info("will sync item for :".$id, "SYNC_ORDER_ITEMS");
                    if( $this->myIsInt($id)) {
                        $this->orderService->syncOrderItems($init, $marketPlace, $sinceNum, intval($id));
                    }else{
                        $output->writeln("<error>bad merchantOrderId, not a number:".$id."</error>");
                        $this->logService->error("bad merchantOrderId, not a number:".$id, "SYNC_ORDER_ITEMS");
                    }
                    sleep(1);
                }
            }else{
                $this->logService->error("merchantOrderIds not founds !", "SYNC_ORDER_ITEMS");
                $output->writeln("<error>merchantOrderIds not founds !</error>");
            }
        }
        return 0;
    }

    private function myIsInt($s): bool
    {
        return (is_numeric($s) ? intval($s) == $s : false);
    }


}
