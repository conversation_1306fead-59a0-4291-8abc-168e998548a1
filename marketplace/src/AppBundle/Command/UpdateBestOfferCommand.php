<?php

namespace AppBundle\Command;

use AppBundle\Entity\MarketPlace;
use AppBundle\Model\BestOffer;
use AppBundle\Model\Offer;
use AppBundle\Services\MarketPlaceService;
use AppBundle\Services\OfferService;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class UpdateBestOfferCommand extends Command
{
    private const DATE_FORMAT = "d-m-Y G:i:s.u";

    private OfferService $offerService;
    private MarketPlaceService $marketPlaceService;
    private LogService $logger;
    private OutputInterface $output;

    /**
     * UpdateBestOfferCommand constructor.
     * @param OfferService $offerService
     * @param MarketPlaceService $marketPlaceService
     * @param LogService $logger logger of the application
     */
    public function __construct(OfferService $offerService, MarketPlaceService $marketPlaceService, LogService $logger)
    {
        parent::__construct();
        $this->offerService = $offerService;
        $this->marketPlaceService = $marketPlaceService;
        $this->logger = $logger;
    }

    protected function configure()
    {
        $this
            ->setName('open:offer:update_best_offer')
            ->addOption('marketplace', null, InputOption::VALUE_REQUIRED, 'the market place name')
            ->setDescription('Update best offer attribute in the elastic search for each offer')
            ->setHelp("Update best offer attribute in the elastic search for each offer");
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->output = $output;
        // Showing when the script is launched
        $now = new \DateTime();
        $this->logInfo('Start : ' . $now->format(self::DATE_FORMAT) . ' ---');

        $mkpName = $input->getOption('marketplace');
        $marketPlace = $this->marketPlaceService->getMarketPlaceByName($mkpName);
        // First, find all gtin values and iterate through them
        foreach ($this->offerService->findAllDistinctGTIN($marketPlace) as $gtin) {
            //now get all country of delivery for this gtin
            foreach ($this->offerService->findCountryOfDeliveryValuesForGTIN($gtin, $marketPlace) as $country) {
                $this->manageBestOfferForGtinAndCountry($gtin, $country, $marketPlace);
            }
        }
        $now = new \DateTime();
        $this->logInfo('End : ' . $now->format(self::DATE_FORMAT) . ' ---');
        return 0;
    }

    private function manageBestOfferForGtinAndCountry(string $gtin, string $country, MarketPlace $marketPlace)
    {
        $this->logInfo("Updating best offer info for [country=" . $country . ", gtin=" . $gtin . "]");
        $page = 1;
        $hitsPerPage = 30;
        $bestOffer = new BestOffer();

        do {
            $result = $this->offerService->findOffersByCountryOfDeliveryAndGTINOrderedByPriceASC($country, $gtin, $hitsPerPage, $page, $marketPlace);

            //first page, means, that the first offer is the best offer
            //also note that we want the price converting for the country of delivery => price attribute can be for different currencies
            //for the same country of delivery
            if ($page === 1 && !empty($result)) {
                $bestOffer->setPrice($result[0]->getCountryPrice());
                $bestOffer->setOfferId($result[0]->getId());
                $bestOffer->setCurrency($result[0]->getCountryCurrency());
                $bestOffer->setCreatedAt(new \DateTime());
            }

            //we only update index if our best offer has a valid price
            //it may not valid if the update price command has never been run
            if ($bestOffer->getPrice() !== null) {
                 /** @var Offer $offer */
                foreach ($result as $offer) {
                    $this->offerService->addOrUpdateBestOfferForOffer($offer->getId(), $bestOffer, $marketPlace);
                }
            } else {
                $this->logError("Unable to compute best offers for [country=" . $country . ", gtin=" . $gtin . "]. Its seems that the update_prices command has never been run");
            }

            // Increment page number
            $page++;
        } while (count($result) !== 0);
    }

    private function logInfo($message)
    {
        $this->output->writeln("<info>" . $message . "</info>");
        $this->logger->info($this->getName() . ": " . $message, EventNameEnum::INDEX_INFO);
    }

    private function logError($message)
    {
        $this->output->writeln("<error>" . $message . "</error>");
        $this->logger->info($this->getName() . ": " . $message, EventNameEnum::INDEX_ERROR);
    }
}
