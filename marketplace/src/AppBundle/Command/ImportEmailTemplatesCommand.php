<?php

namespace AppBundle\Command;

use AppBundle\Entity\Node;
use AppBundle\Entity\NodeContent;
use AppBundle\Model\EmailTemplate;
use AppBundle\Repository\NodeRepository;
use AppBundle\Services\EmailTemplateService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class ImportEmailTemplatesCommand extends Command
{
    private const DATE_FORMAT = "d-m-Y G:i:s";

    private array $locales;
    private EntityManagerInterface $em;
    private NodeRepository $nodeRepository;
    private EmailTemplateService $emailTemplateService;

    public function __construct(array $buyerLocales, array $merchantLocales, EntityManagerInterface $em, NodeRepository $nodeRepository, EmailTemplateService $emailTemplateService)
    {
        parent::__construct();
        $array1 = array_map(function ($locales) {
            return strtolower($locales);
        }, $merchantLocales);
        $array2 = array_map(function ($locales) {
            return strtolower($locales);
        }, $buyerLocales);
        $this->locales = array_unique(array_merge($array1, $array2));
        $this->em = $em;
        $this->nodeRepository = $nodeRepository;
        $this->emailTemplateService = $emailTemplateService;
    }

    protected function configure()
    {
        // Name and description for app/console command
        $this
            ->setName('open:import:emails')
            ->setDescription('Create default email templates')
            // the full command description shown when running the command with
            // the "--help" option
            ->addOption('force', 'f', InputOption::VALUE_NONE, 'Recreate all the email templates. !!BE AWARE!!: Will erase the existing email templates')
            ->setHelp("This command allows you to create default email templates");
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $force = true === $input->getOption('force');

      // Showing when the script is launched
        $now = new \DateTime();
        $output->writeln('<comment>Start : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        foreach ($this->emailTemplateService->fetchAllTemplateNames() as $templateName) {
            /** @var EmailTemplate $template */
            $template = $this->emailTemplateService->getTemplate($templateName);
            $output->writeln('<comment>Create notification with ID ' . $templateName . '...</comment>');

            $node = $this->nodeRepository->findOneBy(array("slug" => $templateName));

            if ($force && $node) {
                $this->em->remove($node);
                $this->em->flush();
                $node = null;
            }

            /*
             * if notification don't exist, create if
             */
            if (is_null($node)) {
                $node = new Node();
                $node->setSlug($templateName);
                $node->setType("email");
            }

            foreach ($this->locales as $locale) {
                $nodeContentExist = $this->nodeRepository->findEmailBySlugAndLanguage($templateName, $locale);
                $title = $templateName . "_OBJECT_" . strtoupper($locale);
                if (!$nodeContentExist) {
                    //create default 'en' content
                    $nodeContent = new NodeContent();
                    $nodeContent->setBody('<p>' . $template->getContent() . '</p>');
                    $nodeContent->setTitle($title);
                    $nodeContent->setLang($locale);
                    $nodeContent->setNode($node);
                    $node->addContent($nodeContent);
                } else {
                    $output->writeln('<comment>notification already exist with title ' . $title . '...</comment>');
                }

                try {
                    $this->em->persist($node);
                    $this->em->flush();
                    $output->writeln('<comment>   - Done</comment>');
                } catch (\Exception $e) {
                    $output->writeln('<error> - Error: enable to persist email with id ' . $templateName . ': ' . $e->getMessage() . '</error>');
                    return 1;
                }
            }
        }

        $now = new \DateTime();
        $output->writeln('<comment>End : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');
        return 0;
    }
}
