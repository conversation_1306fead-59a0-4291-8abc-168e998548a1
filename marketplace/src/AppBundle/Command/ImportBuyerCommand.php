<?php

namespace AppBundle\Command;

use AppBundle\Services\BuyerImportationService;
use AppBundle\Services\SFTPService;
use DateTime;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Exception;

class ImportBuyerCommand extends Command
{
    private BuyerImportationService $buyerImportationService;
    private LogService $logger;

    public function __construct(BuyerImportationService $buyerImportationService, LogService $logger)
    {
        $this->buyerImportationService = $buyerImportationService;
        $this->logger = $logger;
        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setName('open:buyer:import')
            ->setDescription('Import buyer from csv source');

    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {

        $startDateAll = new DateTime("now");
        $output->writeln('import buyer start');
        $this->logger->info("import buyer start", EventNameEnum::COMMAND_IMPORT_BUYER);


        $this->buyerImportationService->disableSqlLog();
        $nbLine = $this->buyerImportationService->importFromDistantUSer();

        $output->writeln('import buyer end');
        $this->logger->info("import buyer end", EventNameEnum::COMMAND_IMPORT_BUYER, null, ["nb_line" => $nbLine]);

        $time_interval_all = $startDateAll->diff(new Datetime("now"));
        $time_interval_all_string = $time_interval_all->format('%h hour %i min %s,%F sec');

        $this->logger->info(
            "import buyer total duration",
            EventNameEnum::COMMAND_IMPORT_BUYER,
            null,
            [
                "all_time" => $time_interval_all_string
            ]
        );
        $output->writeln(sprintf(
            'Total script duration "%s"',
            $time_interval_all_string
        ));

        return 0;
    }
}
