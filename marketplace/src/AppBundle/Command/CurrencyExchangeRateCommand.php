<?php

namespace AppBundle\Command;

use AppBundle\Provider\ExchangeRates;
use DateTime;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class CurrencyExchangeRateCommand extends Command
{
    private const DATE_FORMAT = "d-m-Y G:i:s";

    private ExchangeRates $exchangeRatesProvider;

    /**
     * CurrencyExchangeRateCommand constructor.
     * @param ExchangeRates $exchangeRates
     */
    public function __construct(ExchangeRates $exchangeRates)
    {
        $this->exchangeRatesProvider = $exchangeRates;
        parent::__construct();
    }

    protected function configure()
    {
        // Name and description for app/console command
        $this
            ->setName('open:import:currency_exchange_rate')
            ->setDescription('Import Currency Exchange Rate from api.exchangeratesapi.io')
            ->setHelp("The default exchange reference is EUR")
            ->addArgument('currency', InputArgument::OPTIONAL, 'The Currency exchange reference.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $currency = $input->getArgument('currency') ?? ExchangeRates::CURRENCY_EURO;
        /** @var ExchangeRates $exchangeRatesProvider */

        $now = new DateTime();
        $output->writeln('<comment>Start : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        $this->exchangeRatesProvider->getExchangeRate($currency, false);

        $now = new DateTime();
        $output->writeln('<comment>End : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');
        return 0;
    }
}
