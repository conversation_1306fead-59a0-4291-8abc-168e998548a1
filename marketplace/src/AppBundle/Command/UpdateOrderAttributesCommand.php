<?php

namespace AppBundle\Command;

use Open\IzbergBundle\Service\AttributeService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class UpdateOrderAttributesCommand extends Command
{
    const DATE_FORMAT = "d-m-Y G:i:s";

    private AttributeService $attributeService;
    private ParameterBagInterface $parameterBag;

    /**
     * UpdateOrderAttributesCommand constructor.
     * @param AttributeService $attributeService
     * @param ParameterBagInterface $parameterBag
     */
    public function __construct(AttributeService $attributeService, ParameterBagInterface $parameterBag)
    {
        parent::__construct();
        $this->attributeService = $attributeService;
        $this->parameterBag = $parameterBag;
    }

    protected function configure()
    {
        $this
            ->setName('open:update:orderAttribute')
            ->setDescription('Update order attribute ids in redis cache')
            ->setHelp("This command update order attribute ids in redis cache");
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        // Showing when the script is launched
        $now = new \DateTime();
        $output->writeln('<comment>Start : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        $orderCustomAttributes = $this->parameterBag->get('izberg_order_attributes');
        $merchantCustomAttributes = $this->parameterBag->get("izberg_merchant_attributes");

        $this->attributeService->getCachedAttributes(true);

        foreach ($orderCustomAttributes as $attribute) {
            $this->attributeService->getOrderAttributeId($attribute, true);
        }

        foreach ($merchantCustomAttributes as $attribute) {
            $this->attributeService->getMerchantAttributeId($attribute, true);
        }

        $now = new \DateTime();
        $output->writeln('<comment>End : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');
        return 0;
    }
}
