<?php

namespace AppBundle\Command;

use AppBundle\Services\OrderService;
use Open\LogBundle\Service\LogService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class InitMerchantOrderInvoiceEntityCommand extends Command
{
    private const COMMAND_NAME = "open:merchant-order-invoice-entity:init";

    private OrderService $orderService;
    private LogService $logger;

    public function __construct(OrderService $orderService, LogService $logger)
    {
        $this->orderService = $orderService;
        $this->logger = $logger;
        parent::__construct(self::COMMAND_NAME);
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->logger->info("init invoice entity start", "INIT_INVOICE_COMMAND");
        $output->writeln('init invoice entity start');
        $this->orderService->initMerchantOrdersInvoiceEntity();
        $output->writeln('init invoice entity stop');
        $this->logger->info("init invoice entity stop", "INIT_INVOICE_COMMAND");
        return 0;
    }
}
