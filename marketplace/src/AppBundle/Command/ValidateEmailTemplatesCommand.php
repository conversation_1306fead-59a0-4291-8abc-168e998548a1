<?php

namespace AppBundle\Command;

use AppBundle\Entity\Node;
use AppBundle\Entity\NodeContent;
use AppBundle\Exception\MailException;
use AppBundle\Exception\MailValidationException;
use AppBundle\Repository\NodeRepository;
use AppBundle\Services\EmailTemplateService;
use AppBundle\Services\MailService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ValidateEmailTemplatesCommand extends Command
{
    private const DATE_FORMAT = "d-m-Y G:i:s";

    private NodeRepository $nodeRepository;
    private MailService $emailService;
    private EmailTemplateService $emailTemplateService;

    /**
     * ValidateEmailTemplatesCommand constructor.
     * @param NodeRepository $nodeRepository
     * @param MailService $mailService
     * @param EmailTemplateService $emailTemplateService
     */
    public function __construct(
        NodeRepository $nodeRepository,
        MailService $mailService,
        EmailTemplateService $emailTemplateService
    ) {
        parent::__construct();
        $this->nodeRepository = $nodeRepository;
        $this->emailService = $mailService;
        $this->emailTemplateService = $emailTemplateService;
    }

    protected function configure()
    {
        // Name and description for app/console command
        $this
            ->setName('open:check:emails')
            ->setDescription('Checks all the email templates')
            ->setHelp("This command allows you to check the validity of email templates");
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        // Showing when the script is launched
        $now = new \DateTime();
        $output->writeln('<comment>Start : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        $errors = false;
        $warning = false;

        //iterate over all email templates
        /** @var Node $node */
        foreach ($this->nodeRepository->findByType("email") as $node) {
            $output->writeln('<comment>testing email template with slug ' . $node->getSlug() . '</comment>');

            //get base template
            $baseTemplate = $this->emailTemplateService->getTemplate($node->getSlug());
            if ($baseTemplate === null) {
                $output->writeln(
                    "<error>email template with slug " . $node->getSlug() . " doesn't exist in template list (see emails.yml) </error>"
                );
                $errors = true;
            } else {
                //testing each content of the node
                /** @var NodeContent $content */
                foreach ($node->getContent() as $content) {
                    try {
                        $currentLanguage = $content->getLang();
                        $baseTemplate->setContent($content->getBody());
                        $baseTemplate->setSubject($content->getTitle());
                        $this->emailService->validateEmailTemplate($baseTemplate);
                    } catch (MailException $e) {
                        $output->writeln("<error>template [" . $baseTemplate->getTemplateName() . ", " . $currentLanguage . "] is invalid: " . preg_replace("/\"__string_template__.*\"/", "template", $e->getMessage()) . " </error>");
                        $errors = true;
                    } catch (MailValidationException $e) {
                        $output->writeln("<question>template [" . $baseTemplate->getTemplateName() . ", " . $currentLanguage . "] has incorrect formatted variables: " . $e->getMessage() . " </question>");
                        $warning = true;
                    }
                }
            }
        }

        $now = new \DateTime();
        $output->writeln('<comment>End : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        //Reminder: On POSIX systems the standard exit code is 0 for success and any number from 1 to 255 for anything else
        if ($errors) {
            return 2;
        }

        if ($warning) {
            return 1;
        }

        return 0;
    }
}
