<?php

namespace AppBundle\Command;

use AppBundle\Entity\Country;
use AppBundle\Entity\ZipCode;
use AppBundle\Repository\CountryRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ImportZipCodeCommand extends Command
{
    const DATE_FORMAT = "d-m-Y G:i:s";
    private CountryRepository $countryRepository;
    private EntityManagerInterface $em;
    private CsvToArrayService $csvToArrayService;

    /**
     * ImportZipCodeCommand constructor.
     * @param CountryRepository $countryRepository
     * @param EntityManagerInterface $em
     * @param CsvToArrayService $csvToArrayService
     */
    public function __construct(CountryRepository $countryRepository, EntityManagerInterface $em, CsvToArrayService $csvToArrayService)
    {
        $this->countryRepository = $countryRepository;
        $this->em = $em;
        $this->csvToArrayService = $csvToArrayService;
        parent::__construct();
    }

    protected function configure()
    {
        // Name and description for app/console command
        $this
            ->setName('open:import:zipcode')
            ->setDescription('Import zipCode from CSV file')
            // the full command description shown when running the command with
            // the "--help" option
            ->setHelp("This command allows you to import zipcode from csv file")
            ->addArgument('filename', InputArgument::REQUIRED, 'The filename.')
            ->addArgument('country', InputArgument::REQUIRED, 'The country.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        // Showing when the script is launched
        $now = new \DateTime();
        $output->writeln('<comment>Start : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');
        // Importing CSV on DB via Doctrine ORM
        $this->import($input, $output);
        // Showing when the script is over
        $now = new \DateTime();
        $output->writeln('<comment>End : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');
        return 0;
    }

    protected function import(InputInterface $input, OutputInterface $output)
    {
        // Getting php array of data from CSV
        $data = $this->get($input);
        $country_code = $input->getArgument('country');
        // Turning off doctrine default logs queries for saving memory
        $this->em->getConnection()->getConfiguration()->setSQLLogger(null);
        /** @var Country $country */
        $country = $this->countryRepository->findOneBy(array("code" => $country_code));
        // Define the size of record, the frequency for persisting the data and the current index of records
        $size = count($data);
        if ($size > 0) {
            $output->writeln("Deleting zipCode...");
            $country->setZipcode(new ArrayCollection());
            $this->em->persist($country);
            $this->em->flush();
            $output->writeln("ZipCode deleted");
        }
        $batchSize = 20;
        $i = 1;
        // Starting progress
        $progress = new ProgressBar($output, $size);
        $progress->start();
        // Processing on each row of data
        foreach ($data as $row) {
            //insee_code city zipcode gps
            $zipcode = new ZipCode();
            if (array_key_exists('insee_code', $row)) {
                $zipcode->setInseeCode($row['insee_code']);
            }
            $zipcode->setCity($row['city']);
            $zipcode->setZipCode($row['zipcode']);
            if (array_key_exists('gps', $row)) {
                $zipcode->setGps($row['gps']);
            }
            $zipcode->setLabel($row['city'] . " (" . $row['zipcode'] . ")");
            $country = $this->countryRepository->find($country->getId());
            $zipcode->setCountry($country);
            $this->em->persist($zipcode);
            // Each 20 users persisted we flush everything
            if (($i % $batchSize) === 0) {
                $this->em->flush();
                $this->em->clear();
                // Advancing for progress display on console
                $progress->advance($batchSize);
                $now = new \DateTime();
                $output->writeln(' of zipCode imported ... | ' . $now->format(self::DATE_FORMAT));
            }
            $i++;
        }
        // Flushing and clear data on queue
        $this->em->flush();
        $this->em->clear();
        // Ending the progress bar process
        $progress->finish();
    }

    protected function get(InputInterface $input)
    {
        return $this->csvToArrayService->convert($input->getArgument('filename'), ';');
    }
}
