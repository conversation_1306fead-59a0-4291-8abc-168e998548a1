<?php

namespace AppBundle\Command;

use AppBundle\Entity\User;
use AppBundle\Services\BuyerService;
use AppBundle\Services\UserBddService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\QuestionHelper;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\Question;

class OpenBuyerSuperiorCommand extends Command
{
    private BuyerService $buyerService;
    private UserBddService $userManager;

    public function __construct(BuyerService $buyerService, UserBddService $userManager, $name = null)
    {
        $this->buyerService = $buyerService;
        $this->userManager = $userManager;

        parent::__construct($name);
    }

    protected function configure(): void
    {
        $this
            ->setName('open:buyer:superior')
            ->setDescription('Create a command to link Superior User to an existing Buyer')
            ->setDefinition([
                new InputArgument('buyer-email', InputArgument::REQUIRED, 'Buyer email'),
                new InputArgument('superior-email', InputArgument::REQUIRED, 'Superior email'),
            ]);
    }

    protected function interact(InputInterface $input, OutputInterface $output): void
    {
        $questionHelper = $this->getHelperSet()->get('question') ?? new QuestionHelper();
        $output->writeln('Welcome to the open buyer creator!');

        /* -- CHILD BUYER EMAIL -- */
        $BuyerEmail = $input->getArgument('buyer-email');
        $BuyerEmail = $questionHelper->ask($input, $output, new Question('Buyer user email', $BuyerEmail));
        $input->setArgument('buyer-email', $BuyerEmail);

        /* -- PARENT BUYER EMAIL -- */
        $superiorEmail = $input->getArgument('superior-email');
        $superiorEmail = $questionHelper->ask($input, $output, new Question('Superior user email', $superiorEmail));
        $input->setArgument('superior-email', $superiorEmail);
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $buyerEmail = $input->getArgument('buyer-email');
        $superiorEmail = $input->getArgument('superior-email');

        if ($buyerEmail === $superiorEmail) {
            // User can't be directly self managed
            $output->writeln(
                sprintf('A buyer can\'t be managed by himself')
            );
        } else {

            /**
             * @var User $buyer
             */
            $buyer = $this->userManager->findUserByEmail($buyerEmail);

            /**
             * @var User $superior
             */
            $superior = $this->userManager->findUserByEmail($superiorEmail);

            if (!$buyer) {
                $output->writeln(
                    sprintf(
                        'Parent buyer <comment>%s</comment> unknown',
                        $buyerEmail
                    )
                );
            }
            if (!$superior) {
                $output->writeln(
                    sprintf(
                        'Child buyer <comment>%s</comment> unknown',
                        $superiorEmail
                    )
                );
            }

            if ($superior && $buyer) {
                $this->buyerService->attachBuyerToSuperior($buyer, $superior);

                $output->writeln(
                    sprintf(
                        'Create a link from <comment>%s</comment> buyer to the superior <comment>%s</comment>',
                        $superior->getUsername(),
                        $buyer->getUsername()
                    )
                );
            }
        }
        return 0;
    }
}
