<?php

namespace AppBundle\Command;

use AppBundle\Entity\MerchantOrder;
use AppBundle\Repository\MerchantOrderRepository;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Open\IzbergBundle\Api\OrderApi;
use Open\LogBundle\Service\LogService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class FixMerchantOrderShippingCommand extends Command
{
    const DATE_FORMAT = "d-m-Y G:i:s";

    private LogService $logger;
    private OrderApi $orderApi;
    private EntityManagerInterface $em;
    private MerchantOrderRepository $merchantOrderRepository;

    public function __construct(LogService $logger, OrderApi $orderApi, EntityManagerInterface $em, MerchantOrderRepository $merchantOrderRepository)
    {
        $this->logger = $logger;
        $this->orderApi = $orderApi;
        $this->em = $em;
        $this->merchantOrderRepository = $merchantOrderRepository;
        parent::__construct();
    }

    protected function configure()
    {
        // Name and description for app/console command
        $this
            ->setName('open:fix:merchant_order_shipping')
            ->setDescription('fix cart merchantOrder shipping')
            // the full command description shown when running the command with
            // the "--help" option
            ->addOption('do', 'd', InputOption::VALUE_NONE, 'do modification')
            ->setHelp("This command fix merchantOrder shipping  TOTALMP-1640");
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $now = new DateTime();
        $output->writeln('<comment>Start : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        $batchSize = 20;
        $i = 1;
        $iterableResult = $this->merchantOrderRepository->getNotShippedCommands();
        foreach ($iterableResult as $row) {
            /** @var MerchantOrder $merchantOrder */
            $merchantOrder = $row[0];
            $output->writeln(printf('iteration : %s, merchantOrder id :%s', $i, $merchantOrder->getId()));
            $izbergMerchantOrder = $this->orderApi->fetchMerchantOrderById($merchantOrder->getId());
            $merchantOrder->setShipping($izbergMerchantOrder->getShipping());
            if (($i % $batchSize) === 0) {
                $this->em->flush(); // Executes all updates.
                $this->em->clear(); // Detaches all objects from Doctrine!
                $output->writeln('saved !');
            }
            ++$i;
        }
        $this->em->flush();
        $output->writeln('saved !');

        $now = new DateTime();
        $output->writeln('<comment>End : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');
        return 0;
    }
}
