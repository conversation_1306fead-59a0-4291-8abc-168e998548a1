<?php

namespace AppBundle\Command;

use AppBundle\Services\MarketPlaceService;
use AppBundle\Services\Reporting\MerchantOfferReporter;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class ReportingMerchantOfferPriceCommand extends Command
{
    private MerchantOfferReporter $merchantOfferReporter;
    private MarketPlaceService $marketPlaceService;
    private $output;
    private $logger;

    /**
     * ReportingMerchantOfferPriceCommand constructor.
     * @param MerchantOfferReporter $merchantOfferReporter
     * @param LogService $logger
     */
    public function __construct(MerchantOfferReporter $merchantOfferReporter, MarketPlaceService $marketPlaceService, LogService $logger)
    {
        parent::__construct();
        $this->merchantOfferReporter = $merchantOfferReporter;
        $this->marketPlaceService = $marketPlaceService;
        $this->logger = $logger;
    }

    protected function configure()
    {
        $this
            ->setName("open:reporting:merchant_price")
            ->addOption('marketplace', null, InputOption::VALUE_REQUIRED, 'the market place name')
            ->setDescription('Command to send report to merchants about their offers prices positioning')
            ->setHelp('Command to send report to merchants about their offers prices positioning');
    }


    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->output = $output;
        $start = new \DateTime();
        $this->logInfo("start of " . $this->getName() . " command at " . $start->format("D M d, Y G:i:s.v\""));
        $mkpName = $input->getOption('marketplace');
        $marketPlace = $this->marketPlaceService->getMarketPlaceByName($mkpName);
        $this->logInfo("list of notified merchants: ");
        foreach ($this->merchantOfferReporter->report($marketPlace) as $userId) {
            $this->logInfo($userId);
        }

        $end = new \DateTime();
        $this->logInfo("end of " . $this->getName() . " command at " . $end->format("D M d, Y G:i:s.v"));
        return 0;
    }

    private function logInfo($message)
    {
        $this->output->writeln("<info>" . $message . "</info>");
        $this->logger->info($this->getName() . ": " . $message, EventNameEnum::INDEX_INFO);
    }
}
