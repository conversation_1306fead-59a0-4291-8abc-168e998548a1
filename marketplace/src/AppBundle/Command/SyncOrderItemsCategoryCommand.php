<?php

namespace AppBundle\Command;

use AppBundle\Services\MarketPlaceService;
use AppBundle\Services\OrderService;
use Doctrine\ORM\OptimisticLockException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

final class SyncOrderItemsCategoryCommand extends Command
{
    private const DEFAULT_FILTER_DATE = "2000-01-01";
    private OrderService $orderService;
    private MarketPlaceService $marketPlaceService;

    public function __construct(OrderService $orderService, MarketPlaceService $marketPlaceService)
    {
        $this->orderService = $orderService;
        $this->marketPlaceService = $marketPlaceService;
        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setName('open:order-item-category:sync')
            ->addOption('marketplace', null, InputOption::VALUE_REQUIRED, 'the market place name')
            ->addArgument('orderDate', InputArgument::OPTIONAL, 'merchant order filter')
            ->setDescription('sync all order items site name from izberg merchant orders in database');
    }

    /**
     * @throws OptimisticLockException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $mkpName = $input->getOption('marketplace');
        $marketPlace = $this->marketPlaceService->getMarketPlaceByName($mkpName);

        $orderDate = $input->getArgument('orderDate') ?? self::DEFAULT_FILTER_DATE;
        $this->orderService->syncOrderItemsCategory($orderDate, $marketPlace);
        return 0;
    }
}
