<?php

namespace AppBundle\Command;

use AppBundle\Services\Import\DistantUserService;
use DateTime;
use Open\IdealBundle\Model\Test;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;


class RetrieveBuyerCommand extends Command
{
    private LogService $logger;
    private DistantUserService $distantUserService;


    public function __construct(LogService $logger, DistantUserService $distantUserService, string $name = null)
    {
        $this->logger = $logger;
        $this->distantUserService = $distantUserService;
        parent::__construct($name);
    }

    protected function configure()
    {
        $this
            ->setName('open:buyer:retrieve')
            ->setDescription('retrieve buyer from ideal api source');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {

        $startDateAll = new DateTime("now");
        $output->writeln('retrieve buyer start');
        $this->logger->info("retrieve buyer start", EventNameEnum::COMMAND_RETRIEVE_BUYER);

        $nbUsers = $this->distantUserService->retrieveUsers($output);

        $output->writeln('retrieve buyer end');
        $this->logger->info("retrieve buyer end", EventNameEnum::COMMAND_RETRIEVE_BUYER, null, ["nb_user" => $nbUsers]);

        $time_interval_all = $startDateAll->diff(new Datetime("now"));
        $time_interval_all_string = $time_interval_all->format('%h hour %i min %s,%F sec');

        $this->logger->info(
            "retrieve buyer total duration",
            EventNameEnum::COMMAND_RETRIEVE_BUYER,
            null,
            [
                "nb_user" => $nbUsers,
                "all_time" => $time_interval_all_string
            ]
        );
        $output->writeln(sprintf(
            'Total script duration "%s"',
            $time_interval_all_string
        ));
        return 0;

    }


}