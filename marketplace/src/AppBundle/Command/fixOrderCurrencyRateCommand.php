<?php

namespace AppBundle\Command;

use AppBundle\Repository\MerchantOrderRepository;
use DateTime;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class fixOrderCurrencyRateCommand extends Command
{
    private const DATE_FORMAT = "d-m-Y G:i:s";

    private MerchantOrderRepository $merchantOrderRepository;

    /**
     * fixOrderCurrencyRateCommand constructor.
     * @param MerchantOrderRepository $merchantOrderRepository
     */
    public function __construct(MerchantOrderRepository $merchantOrderRepository)
    {
        $this->merchantOrderRepository = $merchantOrderRepository;
        parent::__construct();
    }

    protected function configure()
    {
        // Name and description for app/console command
        $this
            ->setName('open:fix:merchant_order_exchange_rate')
            ->setDescription('fix merchant order exchange rate')
            // the full command description shown when running the command with
            // the "--help" option
            ->setHelp("This command fix merchant order exchange rate on already existing order (before rate was stored)");
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $now = new DateTime();
        $output->writeln('<comment>Start : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');
        $this->merchantOrderRepository->fixMerchantOrderCurrencyRate();

        $now = new DateTime();
        $output->writeln('<comment>End : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');
        return 0;
    }
}
