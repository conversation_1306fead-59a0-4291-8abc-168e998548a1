<?php

namespace AppBundle\Command;

use AppBundle\Entity\Country;
use AppBundle\Entity\Region;
use AppBundle\Repository\CountryRepository;
use AppBundle\Repository\RegionRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Helper\ProgressBar;

class ImportRegionCommand extends Command
{
    private const DATE_FORMAT = "d-m-Y G:i:s";

    private EntityManagerInterface $em;
    private RegionRepository $regionRepository;
    private CountryRepository $countryRepository;
    private CsvToArrayService $csvToArrayService;

    /**
     * ImportRegionCommand constructor.
     * @param EntityManagerInterface $em
     * @param RegionRepository $regionRepository
     * @param CountryRepository $countryRepository
     * @param CsvToArrayService $csvToArrayService
     */
    public function __construct(EntityManagerInterface $em, RegionRepository $regionRepository, CountryRepository $countryRepository, CsvToArrayService $csvToArrayService)
    {
        $this->em = $em;
        $this->regionRepository = $regionRepository;
        $this->countryRepository = $countryRepository;
        $this->csvToArrayService = $csvToArrayService;
        parent::__construct();
    }

    protected function configure()
    {
        // Name and description for app/console command
        $this
            ->setName('open:import:regions')
            ->setDescription('Import regions from a CSV file')
            // the full command description shown when running the command with
            // the "--help" option
            ->setHelp("This command allows you to import regions  from a csv file")
            ->addArgument('filename', InputArgument::REQUIRED, 'The filename.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        // Showing when the script is launched
        $now = new \DateTime();
        $output->writeln('<comment>Start : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        // Importing CSV on DB via Doctrine ORM
        $this->import($input, $output);

        // Showing when the script is over
        $now = new \DateTime();
        $output->writeln('<comment>End : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');
        return 0;
    }


    protected function import(InputInterface $input, OutputInterface $output)
    {
        // Getting php array of data from CSV
        $data = $this->getData($input, $output);

        // Turning off doctrine default logs queries for saving memory
        $this->em->getConnection()->getConfiguration()->setSQLLogger(null);

        // Define the size of record, the frequency for persisting the data and the current index of records
        $size = count($data);

        $batchSize = 1;
        $i = 1;

        // Starting progress
        $progress = new ProgressBar($output, $size);
        $progress->start();

        $regions = $this->regionRepository->findAll();

        $ids = [];
        $regions_map = [];

        /** @var Region $region */
        foreach ($regions as $region) {
            $ids[] = $region->getId();
            $regions_map[$region->getId()] = $region;
        }

        // Processing on each row of data
        foreach ($data as $row) {
            /** @var Country $country */
            $country = $this->countryRepository->findOneBy(array("code" => $row['country']));

            if (is_null($country)) {
                $output->writeln('<error> Country code is unknown : ' . $row['country'] . '</error>');
                $output->writeln("<error> import didn't finish</error>");
                die();
            }
            $id = intval($row['id']);

            if (!in_array($id, $ids)) {
                //new
                $region = new Region();
                $region->setId(intval($row['id']));
                $region->setCode($row['code']);
                $region->setCountry($country);
                $country->getRegions()->add($region);
            } else {
                // update
                $region = $regions_map[$id];
                $region->setCode($row['code']);
            }

            $this->em->persist($country);

            // Each 20 users persisted we flush everything
            if (($i % $batchSize) === 0) {
                $this->em->flush();
                $this->em->clear();
            }

            $i++;
        }

        // Flushing and clear data on queue
        $this->em->flush();
        $this->em->clear();

        // Ending the progress bar process
        $progress->finish();
    }


    protected function getData(InputInterface $input, OutputInterface $output)
    {
        $fileName = $input->getArgument('filename');

        // Getting the CSV from filesystem
        $fileName = 'app/import/' . $fileName;

        // Using service for converting CSV to PHP Array
        return $this->csvToArrayService->convert($fileName, ';');
    }
}
