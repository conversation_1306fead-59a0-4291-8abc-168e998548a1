<?php

namespace AppBundle\Command;

use AppBundle\Entity\Country;
use AppBundle\Repository\CountryRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Helper\ProgressBar;

class ImportCountriesCommand extends Command
{
    const DATE_FORMAT = "d-m-Y G:i:s";

    private CsvToArrayService $csvToArrayService;
    private EntityManagerInterface $em;
    private CountryRepository $countryRepository;

    /**
     * ImportCountriesCommand constructor.
     * @param CsvToArrayService $csvToArrayService
     * @param EntityManagerInterface $em
     * @param CountryRepository $countryRepository
     */
    public function __construct(CsvToArrayService $csvToArrayService, EntityManagerInterface $em, CountryRepository $countryRepository)
    {
        $this->csvToArrayService = $csvToArrayService;
        $this->em = $em;
        $this->countryRepository = $countryRepository;
        parent::__construct();
    }

    protected function configure()
    {
        // Name and description for app/console command
        $this
            ->setName('open:import:countries')
            ->setDescription('Import countries from a CSV file')
            // the full command description shown when running the command with
            // the "--help" option
            ->setHelp("This command allows you to import countries  from a csv file")
            ->addArgument('filename', InputArgument::REQUIRED, 'The filename.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        // Showing when the script is launched
        $now = new \DateTime();
        $output->writeln('<comment>Start : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        // Importing CSV on DB via Doctrine ORM
        $this->import($input, $output);

        // Showing when the script is over
        $now = new \DateTime();
        $output->writeln('<comment>End : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');
        return 0;
    }


    protected function import(InputInterface $input, OutputInterface $output)
    {
        // Getting php array of data from CSV
        $data = $this->getData($input);

        // Turning off doctrine default logs queries for saving memory
        $this->em->getConnection()->getConfiguration()->setSQLLogger(null);

        // Define the size of record, the frequency for persisting the data and the current index of records
        $size = count($data);

        $batchSize = 1;
        $i = 1;

        // Starting progress
        $progress = new ProgressBar($output, $size);
        $progress->start();

        $countries = $this->countryRepository->findAll();

        $ids = [];
        $country_map = [];

        /** @var Country $country */
        foreach ($countries as $country) {
            $ids[] = $country->getId();
            $country_map[$country->getId()] = $country;
        }


        // Processing on each row of data
        foreach ($data as $row) {
            $progress->advance();

            if (!in_array(intval($row['id']), $ids)) {
                $country = new Country();
                $country->setEnabled(true);
                $country->setId(intval($row['id']));
                $country->setCode($row['code']);
                $country->setBuyer(intval($row['buyer']) == 1);
                $country->setVendor(intval($row['vendor']) == 1);
                $country->setLocale($row['locale']);
                $country->setCompanyIdentRegex($row['regex']);
                $country->setIzbergCode($row['izberg-code']);
                $country->setIzbergId($row['izberg-id']);
                $country->setInEU($row['EU'] == 1);

                $country->setLegalNoticeProductExportEU($row['legal-product-EU-export']);
                $country->setLegalNoticeProductExportNonEU($row['legal-product-NON-EU-export']);
                $country->setLegalNoticeServiceExportEU($row['legal-service-EU-export']);
                $country->setLegalNoticeServiceExportNonEU($row['legal-service-NON-EU-export']);
                $country->setCurrency($row['currency']);

                $this->em->persist($country);

                // Each batchSize companies persisted we flush everything
                if (($i % $batchSize) === 0) {
                    $this->em->flush();
                    $this->em->clear();
                }
            } else {
                // update
                $id = intval($row['id']);
                $country = $country_map[$id];
                $country->setCode($row['code']);
                $country->setBuyer(intval($row['buyer']) == 1);
                $country->setVendor(intval($row['vendor']) == 1);
                $country->setLocale($row['locale']);
                $country->setCompanyIdentRegex($row['regex']);
                $country->setIzbergCode($row['izberg-code']);
                $country->setIzbergId($row['izberg-id']);
                $country->setInEU($row['EU'] == 1);

                $country->setLegalNoticeProductExportEU($row['legal-product-EU-export']);
                $country->setLegalNoticeProductExportNonEU($row['legal-product-NON-EU-export']);
                $country->setLegalNoticeServiceExportEU($row['legal-service-EU-export']);
                $country->setLegalNoticeServiceExportNonEU($row['legal-service-NON-EU-export']);
                $country->setCurrency($row['currency']);

                $this->em->persist($country);
            }
            $i++;
        }

        // Flushing and clear data on queue
        $this->em->flush();
        $this->em->clear();

        // Ending the progress bar process
        $progress->finish();
    }

    protected function getData(InputInterface $input)
    {
        return $this->csvToArrayService->convert($input->getArgument('filename'), ';');
    }
}
