<?php

namespace AppBundle\Command;

use AppBundle\Services\MerchantOrderService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

final class SyncMerchantOrderSiteCommand extends Command
{
    private MerchantOrderService $merchantOrderService;

    public function __construct(MerchantOrderService $merchantOrderService)
    {
        $this->merchantOrderService = $merchantOrderService;
        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setName('open:merchant-order-site:sync')
            ->addArgument('merchantOrder', InputArgument::OPTIONAL, 'request specific merchant order')
            ->setDescription('sync specific/all merchant order(s) site in database');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $merchantOrder = intval($input->getArgument('merchantOrder'));
        $this->merchantOrderService->merchantOrderSiteSync($merchantOrder);
        return 0;
    }
}
