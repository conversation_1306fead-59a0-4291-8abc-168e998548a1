<?php

namespace AppBundle\Command;

use AppBundle\Services\OrderService;
use AppBundle\Services\SerializerService;
use Open\IzbergBundle\Model\Order;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class MerchantOrdersExtraInfoCreationCommand extends Command
{
    use AsynchronousProcessTrait;
    use QueueableCommandTrait;

    private OrderService $orderService;
    private SerializerService $serializerService;

    /**
     * MerchantOrdersExtraInfoCreationCommand constructor.
     * @param OrderService $orderService
     * @param SerializerService $serializerService
     */
    public function __construct(OrderService $orderService, SerializerService $serializerService)
    {
        $this->orderService = $orderService;
        $this->serializerService = $serializerService;
        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setName('open:merchant-orders:extra-info')
            ->addArgument('marketplace', InputArgument::REQUIRED, 'The marketplace name')
            ->addArgument('merchantOrdersId', InputArgument::REQUIRED, 'The merchant order serialized')
            ->addArgument('commonAttributes', InputArgument::REQUIRED, 'The common attributes serialized')
            ->addArgument('specificAttributes', InputArgument::REQUIRED, 'The specific attributes serialized')
            ->addArgument('order', InputArgument::REQUIRED, 'The parent order serialized')
            ->setDescription('create order attributes for the given merchant orders');
    }

    /**
     * @throws \Exception
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->startCommand($output);
        $mkpName = $input->getArgument('marketplace');
        $merchantOrdersId = $this->serializerService->deserialize(
            $input->getArgument('merchantOrdersId'),
            'array'
        );

        $commonAttributes = $this->serializerService->deserialize(
            $input->getArgument('commonAttributes'),
            'array'
        );

        $specificAttributes = $this->serializerService->deserialize(
            $input->getArgument('specificAttributes'),
            'array'
        );

        $order = $this->serializerService->deserialize(
            $input->getArgument('order'),
            Order::class
        );

        $processId = $this->processService->processBeginFor($order);
        /** @psalm-suppress UndefinedClass */
        $this->orderService->updateMerchantOrdersExtraInfos($mkpName, $merchantOrdersId, $commonAttributes, $specificAttributes, $order);
        $this->processService->processEndFor($processId);

        $this->endCommand($output);
        return 0;
    }
}
