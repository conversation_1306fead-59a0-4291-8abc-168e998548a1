<?php

namespace AppBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class GenerateMissingIDCommand extends Command
{
    private EntityManagerInterface $em;
    private ParameterBagInterface $parameterBag;

    /**
     * GenerateMissingIDCommand constructor.
     * @param EntityManagerInterface $em
     * @param ParameterBagInterface $parameterBag
     */
    public function __construct(EntityManagerInterface $em, ParameterBagInterface $parameterBag)
    {
        $this->em = $em;
        $this->parameterBag = $parameterBag;
        parent::__construct();
    }

    protected function configure()
    {
        // Name and description for app/console command
        $this
            ->setName('open:ids:generate')
            ->setDescription('Generate missing IDS ')
            // the full command description shown when running the command with
            // the "--help" option
            ->setHelp("This command allows you to generate missing technical IDs on entities");
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $entityTypes = $this->parameterBag->get('historized_entities');
        $count = 0;
        foreach ($entityTypes as $entityType) {
            $entities = $this->em->createQueryBuilder()->select("e")->from($entityType, "e")->getQuery()->getResult();
            foreach ($entities as $entity) {
                if ($entity->getTechnicalId() === null) {
                    $output->writeln('<comment>Generate ID for entityType' . $entityType . ' with id ' . $entity->getId() . '</comment>');
                    $entity->setTechnicalId(md5(uniqid()));
                    $count++;
                    if ($count % 1000 === 0) {
                        $this->em->flush();
                    }
                }
            }
        }
        $this->em->flush();
        return 0;
    }
}
