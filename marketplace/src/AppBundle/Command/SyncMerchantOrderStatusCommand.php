<?php

namespace AppBundle\Command;

use AppBundle\Services\MerchantOrderService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

final class SyncMerchantOrderStatusCommand extends Command
{
    private MerchantOrderService $merchantOrderService;

    public function __construct(MerchantOrderService $merchantOrderService)
    {
        $this->merchantOrderService = $merchantOrderService;
        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setName('open:merchant-order-status:sync')
            ->addArgument('merchantOrder', InputArgument::OPTIONAL, 'request specific merchant order')
            ->setDescription('sync specific/all merchant order(s) status in database');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $merchantOrder = intval($input->getArgument('merchantOrder'));
        $this->merchantOrderService->merchantOrderStatusSync($merchantOrder);
        return 0;
    }
}
