<?php

Class RetrievePasswordCommand extends Command
{
    private LogService $logger;
    private DistantUserService $distantUserService;


    public function __construct(LogService $logger, DistantUserService $distantUserService, string $name = null)
    {
        $this->logger = $logger;
        $this->distantUserService = $distantUserService;
        parent::__construct($name);
    }

    protected function configure()
    {
        $this
            ->setName('retrieve-password')
            ->setDescription('retrieve merchant password');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $email = $input->getArgument('email');

        // use the method $this->encryptor->decrypt($merchant->getPassword());
        // to decrypt the password
        $merchant = $this->merchantRepository->findOneBy(['email' => $email]);
        $output->writeln($this->encryptor->decrypt($merchant->getPassword()));

    }
}
