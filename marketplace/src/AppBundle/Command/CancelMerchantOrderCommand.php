<?php

namespace AppBundle\Command;

use AppBundle\Services\MerchantOrderService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class CancelMerchantOrderCommand extends Command
{
    const DATE_FORMAT = "d-m-Y G:i:s";

    private MerchantOrderService $merchantOrderService;
    private CsvToArrayService $csvToArrayService;

    public function __construct(MerchantOrderService $merchantOrderService, CsvToArrayService $csvToArrayService)
    {
        parent::__construct();
        $this->merchantOrderService = $merchantOrderService;
        $this->csvToArrayService = $csvToArrayService;
    }

    protected function configure()
    {
        $this
            ->setName('open:merchant-order:cancel')
            ->setDescription('Cancel merchant order from csv source')
            ->addArgument('filename', InputArgument::OPTIONAL, 'The filename.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Import file csv IDs for cancel the merchant order:');

        $fileName = $input->getArgument('filename');
        $data = $this->csvToArrayService->convert($fileName);

        foreach ($data as $row) {
            $this->merchantOrderService->cancelMerchantOrder($row['merchant_order_id']);
        }

        $io->success('Command cancel merchant order!');
        return 0;
    }
}
