<?php

namespace AppBundle\Command;

use AppBundle\Services\MerchantOrderService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

final class SyncMerchantOrderItemsCategoriesCommand extends Command
{
    private MerchantOrderService $merchantOrderService;

    public function __construct(MerchantOrderService $merchantOrderService)
    {
        $this->merchantOrderService = $merchantOrderService;
        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setName('open:merchant-order-items-categories:sync')
            ->addOption('marketplace', null, InputOption::VALUE_REQUIRED, 'the market place name', 'france')
            ->addOption('merchantOrder', null, InputOption::VALUE_REQUIRED, 'request specific merchant order', null)
            ->setDescription('sync specific/all merchant order(s) items categories in database');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $marketplace = $input->getOption('marketplace');
        $merchantOrder = intval($input->getOption('merchantOrder'));
        $this->merchantOrderService->merchantOrderItemsCategoriesSync($marketplace, $merchantOrder);
        return 0;
    }
}
