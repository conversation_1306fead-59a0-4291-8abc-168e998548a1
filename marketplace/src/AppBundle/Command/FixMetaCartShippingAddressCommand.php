<?php

namespace AppBundle\Command;

use AppBundle\Repository\MetaCartRepository;
use DateTime;
use Doctrine\ORM\EntityManager;
use Open\LogBundle\Service\LogService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class FixMetaCartShippingAddressCommand extends Command
{
    private const DATE_FORMAT = "d-m-Y G:i:s";
    private LogService $logger;
    private MetaCartRepository $metaCartRepository;

    public function __construct(LogService $logger, MetaCartRepository $metaCartRepository)
    {
        $this->logger = $logger;
        $this->metaCartRepository = $metaCartRepository;
        parent::__construct();
    }

    protected function configure()
    {
        // Name and description for app/console command
        $this
            ->setName('open:fix:cart_shipping')
            ->setDescription('fix cart shipping address')
            // the full command description shown when running the command with
            // the "--help" option
            ->addOption('do', 'd', InputOption::VALUE_NONE, 'do modification')
            ->setHelp("This command fix meta cart shipping address TOTALTMA_120");
    }


    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $do  = true === $input->getOption('do');
        $now = new DateTime();
        $output->writeln('<comment>Start : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');
        /** @var EntityManager $em */
        $data = $this->metaCartRepository->showResumeFix();
        foreach ($data as $row) {
            $this->logger->info("info to changed", "FIX_CART_SHIPPING", null, $row);
            $output->writeln(sprintf(
                "info to  changed :  meta_cart.id:%s, current_shipping_id:%s, default_shipping_id:%s",
                $row["meta_id"],
                $row["current_ship_id"],
                $row["default_ship_id"]
            ));
        }

        if ($do) {
            $this->metaCartRepository->fixShippingAddress();
        }
        $now = new DateTime();
        $output->writeln('<comment>End : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');
        return 0;
    }
}
