<?php

namespace AppBundle\Command;

use AppBundle\Services\MarketPlaceService;
use AppBundle\Services\ProductUpdateService;
use Open\FrontBundle\Exception\TotalException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class UpdateIzbergQuoteProductCommand extends Command
{
    private const DATE_FORMAT = "d-m-Y G:i:s";

    private ProductUpdateService $productUpdateService;
    private MarketPlaceService $marketPlaceService;

    /**
     * UpdateIzbergQuoteProductCommand constructor.
     * @param ProductUpdateService $productUpdateService
     * @param MarketPlaceService $marketPlaceService
     */
    public function __construct(ProductUpdateService $productUpdateService, MarketPlaceService $marketPlaceService)
    {
        $this->productUpdateService = $productUpdateService;
        $this->marketPlaceService = $marketPlaceService;
        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setName('open:quote_izberg:update')
            ->setDescription('update product category for quote offer with custom attribute "Devis"');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        // Showing when the script is launched
        $now = new \DateTime();
        $output->writeln('<comment>Start : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        $marketPlaces = $this->marketPlaceService->getAllMarketPlace();

        try {
            foreach ($marketPlaces as $marketPlace) {
                $output->writeln('<comment>--->MarketPlace : ' . $marketPlace->getName() . ' ---</comment>');
                $this->productUpdateService->updateIzbergQuoteProduct($marketPlace, $output);
            }
        } catch (TotalException $exception) {
            $output->writeln(sprintf('<error>%s</error>', $exception->getMessage()));
        }
        // Showing when the script is over
        $now = new \DateTime();
        $output->writeln('<comment>End : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');
        return 0;
    }
}
