<?php

namespace AppBundle\Command;

use Symfony\Component\Console\Output\OutputInterface;

trait QueueableCommandTrait
{
    private function startCommand(OutputInterface $output)
    {
        $output->writeln(sprintf('<comment>Start : %s ---</comment>', (new \DateTimeImmutable())->format(\DATE_ISO8601)));
    }

    private function endCommand(OutputInterface $output)
    {
        $output->writeln(sprintf('<comment>End : %s ---</comment>', (new \DateTimeImmutable())->format(\DATE_ISO8601)));
    }
}
