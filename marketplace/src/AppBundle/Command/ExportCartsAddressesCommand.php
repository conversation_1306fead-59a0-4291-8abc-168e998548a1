<?php

namespace AppBundle\Command;

use AppBundle\Entity\BuyerAddress;
use AppBundle\Entity\Country;
use AppBundle\Entity\MetaCart;
use AppBundle\Repository\MetaCartRepository;
use Exception;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * @deprecated
 * not used
 */
class ExportCartsAddressesCommand extends Command
{
    private MetaCartRepository $metaCartRepository;

    public function __construct(MetaCartRepository $metaCartRepository)
    {
        $this->metaCartRepository = $metaCartRepository;
        parent::__construct();
    }


    protected function configure()
    {
        $this
            ->setName('open:address:export')
            ->setDescription('Export all addresses from v1')
            ->addArgument(
                'metafile',
                InputArgument::REQUIRED,
                'The .csv file where export Addresses'
            );
    }

    /**
     * @psalm-suppress RedundantCondition
     * @psalm-suppress TypeDoesNotContainType
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $outputMetaFile = $input->getArgument('metafile');

        $metacart = $this->metaCartRepository->findAll();

        $output->writeln('Démarrage de l\'export des adresses de metacart');
        try {
            $fp = fopen($outputMetaFile, 'wb+');

            $csvHeaders = [
                'id metacart',
                'id user control',

                'shipping name',
                'shipping address',
                'shipping address 2',
                'shipping zip code',
                'shipping city',
                'shipping country code',
                'shipping izberg id',

                'type personal address',

                'recipient contact',
                'recipient phone',
                'recipient comment',

                'billing address',
                'billing address 2',
                'billing zip code',
                'billing city',
                'billing country code',
                'billing izberg id',
            ];
            fputcsv($fp, $csvHeaders, ';');

            foreach ($metacart as $cart) {
                if ($cart instanceof MetaCart) {

                    /** @var BuyerAddress $shippingAddress */
                    $shippingAddress = $cart->getBuyerShippingAddress();

                    /** @var Country $shippingCountry */
                    $shippingCountry = $shippingAddress ? $shippingAddress->getCountry() : null;


                    /** @var BuyerAddress $buyerBillingAddress */
                    $buyerBillingAddress = $cart->getBuyerBillingAddress();

                    /** @var Country $billingCountry */
                    $billingCountry = $buyerBillingAddress ? $buyerBillingAddress->getCountry() : null;

                    $sippingAndBillingAddress = [
                        'metacartId' => $cart->getId(),
                        'userId' => $cart->getBuyer()->getId(),

                        'shippingName' => $shippingAddress ? $shippingAddress->getName() : null,

                        'shippingAddress' => $shippingAddress ? $shippingAddress->getAddress() : null,
                        'shippingAddress2' => $shippingAddress ? $shippingAddress->getAddress2() : null,
                        'shippingZipCode' => $shippingAddress ? $shippingAddress->getZipCode() : null,
                        'shippingCity' => $shippingAddress ? $shippingAddress->getCity() : null,

                        'shippingCountryCode' => $shippingCountry ? $shippingCountry->getCode() : null,
                        'shippingIzbId' => $shippingAddress ? $shippingAddress->getIzbergAddressId() : null,

                        'shippingRecipientContact' => $shippingAddress ? $shippingAddress->getContact() : null,
                        'shippingRecipientPhone' => $shippingAddress ? $shippingAddress->getPhone() : null,
                        'shippingRecipientComment' => $shippingAddress ? $shippingAddress->getComment() : null,

                        'billingAddress' => $buyerBillingAddress ? $buyerBillingAddress->getAddress() : null,
                        'billingAddress2' => $buyerBillingAddress ? $buyerBillingAddress->getAddress2() : null,
                        'billingZipCode' => $buyerBillingAddress ? $buyerBillingAddress->getZipCode() : null,
                        'billingCity' => $buyerBillingAddress ? $buyerBillingAddress->getCity() : null,

                        'billingCountryCode' => $billingCountry ? $billingCountry->getCode() : null,
                        'billingIzbId' => $buyerBillingAddress ? $buyerBillingAddress->getIzbergAddressId() : null,
                    ];

                    fputcsv($fp, $sippingAndBillingAddress, ';');
                }
            }
        } catch (Exception $e) {
            $output->writeln($e->getMessage());
            return 1;
        } finally {
            $output->writeln('Fin de l\'export des addresses de metacart');
        }
        return 0;
    }
}
