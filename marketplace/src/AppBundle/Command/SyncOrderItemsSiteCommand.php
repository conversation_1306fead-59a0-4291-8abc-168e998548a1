<?php

namespace AppBundle\Command;

use AppBundle\Services\OrderService;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Psr\Cache\InvalidArgumentException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

final class SyncOrderItemsSiteCommand extends Command
{
    private OrderService $orderService;

    public function __construct(OrderService $orderService)
    {
        $this->orderService = $orderService;
        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setName('open:order-item-site:sync')
            ->addOption('init', null, InputOption::VALUE_NONE, 'request all orderItem')
            ->setDescription('sync all order items site name from izberg merchant orders in database');
    }


    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $init = $input->getOption('init');
        $this->orderService->syncOrderItemsSite($init);
        return 0;
    }
}
