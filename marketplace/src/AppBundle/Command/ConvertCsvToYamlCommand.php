<?php

namespace AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Helper\ProgressBar;

class ConvertCsvToYamlCommand extends Command
{
    const DATE_FORMAT = "d-m-Y G:i:s";

    private CsvToArrayService $csvToArrayService;

    /**
     * ConvertCsvToYamlCommand constructor.
     * @param CsvToArrayService $csvToArrayService
     */
    public function __construct(CsvToArrayService $csvToArrayService)
    {
        parent::__construct();
        $this->csvToArrayService = $csvToArrayService;
    }

    protected function configure()
    {
        // Name and description for app/console command
        $this
            ->setName('open:convert:cvs_to_yaml')
            ->setDescription('Convert label from a CSV file')
            // the full command description shown when running the command with
            // the "--help" option
            ->setHelp("This command allows you to convert label from a csv file (enter full path) to a yaml file in /tmp/output.yml")
            ->addArgument('filename', InputArgument::REQUIRED, 'The filename.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        // Showing when the script is launched
        $now = new \DateTime();
        $output->writeln('<comment>Start : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');

        // Importing CSV on DB via Doctrine ORM
        $this->convert($input, $output);

        // Showing when the script is over
        $now = new \DateTime();
        $output->writeln('<comment>End : ' . $now->format(self::DATE_FORMAT) . ' ---</comment>');
        return 0;
    }

    protected function convert(InputInterface $input, OutputInterface $output)
    {
        // Getting php array of data from CSV
        $data = $this->getData($input, $output);

        // Define the size of record, the frequency for persisting the data and the current index of records
        $size = count($data);
        /*if ($size > 0) {
            $output->writeln("Deleting categories...");
            $nb = $this->deleteCategories();
            $output->writeln("Categories deleted: " . $nb);
        }*/
        $batchSize = 1;
        $i = 1;

        // Starting progress
        $progress = new ProgressBar($output, $size);
        $progress->start();

        $treeData = array();
        // Processing on each row of data
        foreach ($data as $row) {
            $treeData[$row["key"]] = $row["label"];
        }

        $tree = $this->explodeTree($treeData, ".");

        $fp = fopen('/tmp/output.yml', 'w');
        $this->writeTreeNode($tree, 0, $fp);
        fclose($fp);
        // Ending the progress bar process
        $progress->finish();
    }

    private function writeTreeNode($tree, $depth, $file)
    {
        $tab = str_repeat(" ", $depth * 2);
        foreach ($tree as $key => $val) {
            $line = $tab;
            if (is_array($val)) {
                $line = $line . $key . ":\r\n";
                fwrite($file, $line);
                $this->writeTreeNode($val, $depth + 1, $file);
            } else {
                $line = $line . $key . ": " . $val . "\r\n";
                fwrite($file, $line);
            }
        }
    }

    function explodeTree($array, $delimiter = '/', $baseval = false)
    {
        if (!is_array($array)) {
            return false;
        }
        $splitRE   = '/' . preg_quote($delimiter, '/') . '/';
        $returnArr = array();
        foreach ($array as $key => $val) {
            // Get parent parts and the current leaf
            $parts  = preg_split($splitRE, $key, -1, PREG_SPLIT_NO_EMPTY);
            $leafPart = array_pop($parts);

            // Build parent structure
            // Might be slow for really deep and large structures
            $parentArr = &$returnArr;
            foreach ($parts as $part) {
                if (!isset($parentArr[$part])) {
                    $parentArr[$part] = array();
                } elseif (!is_array($parentArr[$part])) {
                    if ($baseval) {
                        $parentArr[$part] = array('__base_val' => $parentArr[$part]);
                    } else {
                        $parentArr[$part] = array();
                    }
                }
                $parentArr = &$parentArr[$part];
            }

            // Add the final part to the structure
            if (empty($parentArr[$leafPart])) {
                $parentArr[$leafPart] = $val;
            } elseif ($baseval && is_array($parentArr[$leafPart])) {
                $parentArr[$leafPart]['__base_val'] = $val;
            }
        }
        return $returnArr;
    }


    protected function getData(InputInterface $input, OutputInterface $output)
    {
        $fileName = $input->getArgument('filename');

        // Using service for converting CSV to PHP Array
        return $this->csvToArrayService->convert($fileName, ';');
    }
}
