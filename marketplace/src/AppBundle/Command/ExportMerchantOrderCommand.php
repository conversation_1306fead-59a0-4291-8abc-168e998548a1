<?php

namespace AppBundle\Command;

use AppBundle\Exception\MarketPlaceException;
use AppBundle\Services\MarketPlaceService;
use AppBundle\Services\MerchantOrderService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

final class ExportMerchantOrderCommand extends Command
{
    private MerchantOrderService $merchantOrderService;
    private MarketPlaceService $marketPlaceService;

    public function __construct(
        MarketPlaceService $marketPlaceService,
        MerchantOrderService $merchantOrderService
    ) {
        parent::__construct();
        $this->merchantOrderService = $merchantOrderService;
        $this->marketPlaceService = $marketPlaceService;
    }

    protected function configure()
    {
        $this
            ->setName('open:order:export')
            ->addOption('marketplace', null, InputOption::VALUE_REQUIRED, 'the market place name')
            ->addArgument('merchantOrderId', InputArgument::REQUIRED, 'merchant order filter')
            ->setDescription('export merchant order into xml file');
    }

    /**
     * @throws MarketPlaceException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $marketPlace = $this->marketPlaceService->getMarketPlaceByName(
            (string)$input->getOption('marketplace')
        );

        $merchantOrderId = (int)$input->getArgument('merchantOrderId');
        $this->merchantOrderService->exportOrder($marketPlace, $merchantOrderId);
        return 0;
    }
}
