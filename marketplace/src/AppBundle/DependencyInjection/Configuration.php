<?php

namespace AppBundle\DependencyInjection;

use Symfony\Component\Config\Definition\Builder\TreeBuilder;
use Symfony\Component\Config\Definition\ConfigurationInterface;

class Configuration implements ConfigurationInterface
{
    public function getConfigTreeBuilder()
    {
        $treeBuilder = new TreeBuilder('app');
		$rootNode = $treeBuilder->getRootNode();

		$rootNode
			->children()
			->integerNode('default_redirect_type')
			->defaultValue(301)
			->end()
			->variableNode('settings')

			->end();


        return $treeBuilder;
    }
}