<?php

namespace AppBundle\DependencyInjection;

use S<PERSON>fony\Component\Config\FileLocator;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Loader;
use Symfony\Component\HttpKernel\DependencyInjection\Extension;

class AppExtension extends Extension
{
    public function load(array $configs, ContainerBuilder $container)
    {
        $configuration = new Configuration();

        $this->processConfiguration($configuration, $configs);

        $loader = new Loader\YamlFileLoader($container, new FileLocator(__DIR__.'/../Resources/config'));

        $loader->load('services.yaml');


		$config = $this->processConfiguration($configuration, $configs);

		// pass config to the services
		// Not sure if it is the correct way to do it
		foreach($config as $key=>$value)
		{
			$container->setParameter('app.' . $key, $value);
		}
    }
}
