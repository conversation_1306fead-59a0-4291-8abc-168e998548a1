<?php

namespace AppBundle\Factory;

use AppBundle\Entity\MarketPlace;
use AppBundle\Entity\MetaCart;
use AppBundle\Entity\Cart as CurrencyCart;
use AppBundle\Entity\User;
use AppBundle\Model\Cart\Cart;
use AppBundle\Model\Cart\CartItem;
use AppBundle\Model\Cart\CartItemShippingGroup;
use AppBundle\Model\Cart\CartMerchant;
use AppBundle\Model\Offer;
use AppBundle\Repository\MetaCartRepository;
use AppBundle\Services\CountryService;
use AppBundle\Services\CurrencyExchangeRateService;
use AppBundle\Services\CustomsService;
use AppBundle\Services\IzbergCustomAttributes;
use AppBundle\Services\MerchantService;
use AppBundle\Services\MetaCartMerchantService;
use AppBundle\Services\OfferService;
use Open\IzbergBundle\Api\CartApi;
use Open\IzbergBundle\Api\MerchantApi;
use Open\IzbergBundle\Model\CartShippingOption;
use Open\IzbergBundle\Model\CartShippingOptionChoice;
use Symfony\Component\HttpFoundation\RequestStack;
use function PHPUnit\Framework\isNull;

class CartFactory
{
    private CartApi $cartApi;
    private MerchantApi $merchantApi;
    private MerchantService $merchantService;
    private OfferService $offerService;
    private CustomsService $customsService;
    private MetaCartRepository $metaCartRepository;
    private CurrencyExchangeRateService $currencyExchangeRateService;
    private RequestStack $requestStack;
    private $currencyCartItems;
    private MetaCartMerchantService $metaCartMerchantService;
    private IzbergCustomAttributes $izbergCustomAttributes;
    private CountryService $countryService;

    public function __construct(
        RequestStack $requestStack,
        CartApi $cartApi,
        MerchantApi $merchantApi,
        MerchantService $merchantService,
        OfferService $offerService,
        CustomsService $customsService,
        MetaCartRepository $metaCartRepository,
        CurrencyExchangeRateService $currencyExchangeRateService,
        MetaCartMerchantService $metaCartMerchantService,
        IzbergCustomAttributes $izbergCustomAttributes,
        CountryService $countryService
    ) {
        $this->cartApi = $cartApi;
        $this->merchantApi = $merchantApi;
        $this->merchantService = $merchantService;
        $this->offerService = $offerService;
        $this->customsService = $customsService;
        $this->metaCartRepository = $metaCartRepository;
        $this->requestStack = $requestStack;
        $this->currencyExchangeRateService = $currencyExchangeRateService;
        $this->metaCartMerchantService = $metaCartMerchantService;
        $this->izbergCustomAttributes = $izbergCustomAttributes;
        $this->countryService = $countryService;
    }

    public function refreshCart(Cart $cart): Cart
    {
        $metaCart = $this->metaCartRepository->findMetaCart($cart->getId());

        return $this->buildCartFromMetaCart($metaCart);
    }

    public function buildCartFromMetaCart(MetaCart $metaCart): Cart
    {

        $cart = new Cart();
        $cart->setStatus($metaCart->getStatus());
        $cart->setId($metaCart->getId());
        $cart->setCountryOfDelivery($metaCart->getCountryOfDelivery());
        $cart->setCurrency($metaCart->getCurrency());
        $cart->setSubTotalWithoutVat(0);
        $cart->setSubTotalVat([]);
        $cart->setTotal(0);
        $cart->setCreator($metaCart->getBuyer());
        $cart->setItems([]);
        $cart->setItemsCount($metaCart->getItemsCount());
        $cart->setGift($metaCart->isGift());
        $cart->setRisk($metaCart->isRisk());
        $cart->setBuyerShippingAddress($metaCart->getBuyerShippingAddress());
        $cart->setBuyerBillingAddress($metaCart->getBuyerBillingAddress());

        $cart->setOrderCreatedAt($metaCart->getOrderCreatedAt());
        if($metaCart->getStatus()==MetaCart::STATUS_REJECTED) {
            $cart->setStatusComment($metaCart->getRejectReason());
        }
        if($metaCart->getStatus()==MetaCart::STATUS_CANCELLED) {
            $cart->setStatusComment($metaCart->getCancelReason());
        }

        $currencyCarts = $metaCart->getCarts();
        $cart->setCurrencyCartIds(
            array_map(
                static function (CurrencyCart $currencyCart): int {
                    return $currencyCart->getCartId();
                },
                $currencyCarts->toArray()
            )
        );

        $this->currencyCartItems = [];

        $izbergCartItems = array_reduce(
            $currencyCarts->toArray(),
            function (array $izbergCartItems, CurrencyCart $currencyCart) use ($cart) {
               // $this->cartApi->configureApiConnection($cart->getCreator()->getMarketPlace()->getApiConfigurationKey());
                $izbergCart = $this->cartApi->fetchIzbergCart($currencyCart->getId());

                $this->currencyCartItems[$currencyCart->getId()] = array_map(
                    static function (\Open\IzbergBundle\Model\CartItem $cartItem) {
                        return $cartItem->getId();
                    },
                    $izbergCart->getItems()->toArray()
                );

                return array_merge($izbergCartItems, $izbergCart->getItems()->toArray());
            },
            []
        );

        $offers = $this->retrieveOffers(
            array_map(
                static function (\Open\IzbergBundle\Model\CartItem $izbergCartItem) {
                    return $izbergCartItem->getOffer()->getId();
                },
                $izbergCartItems
            ),
            $metaCart->getBuyer()->getMarketPlace(),
            $metaCart->getBuyer()
        );

        $currency = $metaCart->getCurrency();

        $this->updateCartItems(
            $cart,
            array_map(
                function (\Open\IzbergBundle\Model\CartItem $izbergCartItem) use ($cart, $offers, $currency) {
                    $cartItem = $this->buildCartItem(
                        $izbergCartItem,
                        $offers,
                        $currency
                    );

                    $cart->addSubTotalVat($cartItem->getTaxRate(), $cartItem->getQuantity() * $cartItem->getUnitVat());
                    $cart->addVat($cartItem->getQuantity() * $cartItem->getUnitVat());
                    $cart->addSubTotalWithoutVat($cartItem->getTotalItem());
                    $cart->addTotal($cartItem->getQuantity() * $cartItem->getUnitPrice());

                    return $cartItem;
                },
                $izbergCartItems
            )
        );

        $this->updateMerchantShippingOptions($cart);
        $cart->setTotalWithoutDelivery($cart->getTotal());

        $cart->setTotal(
            array_reduce(
                $cart->getMerchants(),
                static function (float $cartTotal, CartMerchant $cartMerchant) {
                    return $cartTotal + $cartMerchant->getDeliveryPrice();
                },
                $cart->getTotal()
            )
        );

        $metaCart->setItemsCount($cart->getItemsCount()); // todo check this
        $metaCart->setAmount($cart->getTotal());
        $this->metaCartRepository->save($metaCart);

        $cart->setManager($metaCart->getBuyerManager());
        return $cart;
    }

    private function retrieveOffers(array $offerIds, MarketPlace $marketPlace, User $buyer)
    {
        $offers = $this->offerService->findOffers($offerIds, $this->requestStack->getCurrentRequest()->getLocale(), $marketPlace, $buyer);

        return array_combine(
            array_map(
                static function (Offer $offer) {
                    return $offer->getId();
                },
                $offers
            ),
            $offers
        );
    }

    private function buildCartItem(
        \Open\IzbergBundle\Model\CartItem $izbergCartItem,
        array $offers,
        string $toCurrency
    ): CartItem
    {
        $unitPrice = $izbergCartItem->getUnitPrice();

        $fromCurrency = $izbergCartItem->getCurrency();

        if ($fromCurrency !== $toCurrency) {
            $unitPrice = $this->currencyExchangeRateService
                ->computeExchange(
                    $unitPrice,
                    $toCurrency,
                    $fromCurrency
                );
        }

        $cartItem = new CartItem();
        $cartItem->setId($izbergCartItem->getId());
        $cartItem->setName($izbergCartItem->getName());
        $cartItem->setUnitPrice($unitPrice);
        $cartItem->setUnitVat($izbergCartItem->getUnitVat());
        $cartItem->setUnitPriceVatIncluded($izbergCartItem->getUnitPriceVatIncluded());
        $cartItem->setQuantity($izbergCartItem->getQuantity());
        $cartItem->setMoq(0);
        $cartItem->setBatchSize(1);
        $cartItem->setStock(0);
        $cartItem->setOfferId($izbergCartItem->getOffer()->getId());
        $cartItem->setImageUrl($izbergCartItem->getImageUrl());
        $cartItem->setTaxRate(($unitPrice > 0) ? '' . ($izbergCartItem->getUnitVat() * 100) / $unitPrice : '0.0' );
        $cartItem->setTotalItem($izbergCartItem->getQuantity() * $unitPrice);
        $cartItem->setMerchantId($izbergCartItem->getMerchant()->getId());

        $isGift = false;
        $isRisk = false;
        $isFromQuote = false;

        $extraInfo = $izbergCartItem->getExtraInfo();

        if ($extraInfo) {
            if (strtolower($extraInfo->getGift()) == 'yes') {
                $isGift = true;
            }

            if (strtolower($extraInfo->getQuote()) == 'yes') {
                $isFromQuote = true;
            }

            if (strtolower($extraInfo->getRisk()) == 'yes') {
                $isRisk = true;
            }
        }

        $cartItem->setGift($isGift);
        $cartItem->setQuote($isFromQuote);
        $cartItem->setRisk($isRisk);

        $offer = $offers[$cartItem->getOfferId()] ?? null;
        if (!$offer instanceof Offer) {
            $cartItem->lost();
            return $cartItem;
        }

        $this->buildCartItemFromOffer($cartItem, $offer);

        return $cartItem;
    }

    private function buildCartItemFromOffer(CartItem $cartItem, Offer $offer): void
    {
        $cartItem->setMoq($offer->getMoq());
        $cartItem->setBatchSize($offer->getBatchSize());
        $cartItem->setStock($offer->getStock());
        $cartItem->setName($offer->getName());
        $cartItem->setIncoterm(null);
        $cartItem->setCountryOfDelivery(null);
        $cartItem->setSellerRef($offer->getSku());
        $cartItem->setBuyerRef(null);
        $cartItem->setFcaAddress(null);
        $cartItem->setDeliveryTime(null);
        $cartItem->setTrueDeliveryTime(null);
        $cartItem->setCurrency($offer->getCurrency());
    }

    private function buildCartMerchants(Cart $cart): void
    {
        $merchantItems = [];
        $merchantIds = array_unique(
            array_map(
                static function (CartItem $cartItem) use (&$merchantItems) {
                    $merchantId = $cartItem->getMerchantId();

                    if (!isset($merchantItems[$merchantId])) {
                        $merchantItems[$merchantId] = [];
                    }

                    $merchantItems[$merchantId][] = $cartItem;

                    return $merchantId;
                },
                $cart->getItems()
            )
        );

        $merchants = array_map(
            function (int $merchantId) use (&$merchantItems, $cart) {
                return $this->buildCartMerchant($cart->getId(), $merchantId, $merchantItems[$merchantId], $cart->getCountryOfDelivery());
            },
            $merchantIds
        );

        usort($merchants, static function (CartMerchant $merchantA, CartMerchant $merchantB) {
            return $merchantA->getName() <=> $merchantB->getName();
        });

        $cart->setMerchants($merchants);
    }

    private function buildCartMerchant(int $metaCartId, int $izbergMerchantId, array $cartItems, $countryOfDelivery): CartMerchant
    {
        $izbergMerchant = $this->merchantApi->getMerchant($izbergMerchantId);
        $merchantMinimumOrderAmount = $this->merchantApi->getMerchantCustomAttribute($izbergMerchantId, $this->izbergCustomAttributes->getMerchantMinimumOrderAmount());

        $cartMerchant = new CartMerchant();
        $cartMerchant->setId($izbergMerchant->id);
        $cartMerchant->setName($izbergMerchant->name);
        $cartMerchant->setDescription($izbergMerchant->description);
        $cartMerchant->setLongDescription($izbergMerchant->long_description);
        $cartMerchant->setLogoImage($izbergMerchant->logo_image);
        $cartMerchant->setCountry($this->merchantService->getCountryFromIzbergResponse($izbergMerchant));
        $cartMerchant->setConditions('');
        $cartMerchant->setSubTotalVat([]);
        $cartMerchant->setSubTotalWithoutVat(0);
        $cartMerchant->setVat(0);
        $cartMerchant->setTotal(0);
        $cartMerchant->setRating($izbergMerchant->overall_score);
        $cartMerchant->setDeliveryPrice(0);
        $cartMerchant->setMerchantIsActive($izbergMerchant->status==10?true:false);
        $cartMerchant->setMinimumOrderAmount($merchantMinimumOrderAmount);

        // retrieve comment from MetaCartMerchant entity
        $comment = $this->metaCartMerchantService->fetchMerchantComment($metaCartId, $izbergMerchantId);
        $cartMerchant->setComment($comment);

        $commentPlaceHolder = $this->metaCartMerchantService->fetchMerchantCommentPlaceholder($metaCartId, $izbergMerchantId);
        $cartMerchant->setCommentPlaceHolder($commentPlaceHolder);

        $items = array_map(
            static function (CartItem $cartItem) use ($cartMerchant) {
                $cartMerchant->addSubTotalVat($cartItem->getTaxRate(), $cartItem->getQuantity() * $cartItem->getUnitVat());
                $cartMerchant->addVat($cartItem->getQuantity() * $cartItem->getUnitVat());
                $cartMerchant->addSubTotalWithoutVat($cartItem->getTotalItem());
                $cartMerchant->addTotal($cartItem->getQuantity() * $cartItem->getUnitPrice());

                return $cartItem;
            },
            $cartItems
        );

        if (count($items)) {
            $cartMerchant->setCartId($this->retrieveCartIdFromCartItem($items[0]));
        }

        $cartMerchant->setTotal($cartMerchant->getDeliveryPrice() + $cartMerchant->getTotal());

        usort($items, static function (CartItem $cartItemA, CartItem $cartItemB) {
            return $cartItemA->getName() <=> $cartItemB->getName();
        });

        $cartMerchant->setItems($items);
        $cartMerchant->setVatInformation(
            $this->customsService->getInfo(
                $cartMerchant->getCountry(),
                $this->countryService->getCountryByCode($countryOfDelivery)
            )
        );

        return $cartMerchant;
    }

    private function updateCartItems(Cart $cart, array $items): void
    {
        if (count($items) === 0) {
            $cart->setItems([]);
        }

        foreach ($items as $item) {
            $cart->addItem($item);
        }
        $cart->setItemsCount(count($cart->getItems()));
        $this->buildCartMerchants($cart);
    }

    private function updateMerchantShippingOptions(Cart $cart): void
    {
        // currency cart
        // call cart shipping options API
        // retrieve all shipping options available per merchant per item
        [
            'itemId' => [
                'shippingOptionIdA',
                'shippingOptionIdB',
            ]
        ];

        [
            'shippingOptionIdA' => [
                'id' => 'shippingOptionIdA',
                'price' => 'shippingOptionAPrice',
                'currency' => 'shippingOptionACurrency',
                'deliveryTime' => 'shippingOptionADeliveryTime', // delivery_within_hours + collection_within_hours
            ]
        ];

        $itemShippingOptionsIndex = [];
        $itemsSelectedShippingOptionIndex = [];
        $shippingOptions = [];
        $cartItemGroups = [];

        foreach ($cart->getCurrencyCartIds() as $currencyCartId) {
            $shippingOptionsIzbergObjects = $this->cartApi->fetchShippingOptions($currencyCartId);

            /** @var CartShippingOption $shippingOptionsIzbergObject */
            foreach ($shippingOptionsIzbergObjects as $shippingOptionsIzbergObject) {

                if (!isset($cartItemGroups[$shippingOptionsIzbergObject->getMerchant()->getId()])) {
                    $cartItemGroups[$shippingOptionsIzbergObject->getMerchant()->getId()] = [];
                }

                $shippingOptionsAvailableForProcessingCartItems = [];
                $shippingOptionChoices = $shippingOptionsIzbergObject->getShippingChoices();

                /** @var CartShippingOptionChoice $shippingOptionChoice */
                foreach ($shippingOptionChoices as $shippingOptionChoice) {
                    if (!array_key_exists($shippingOptionChoice->getId(), $shippingOptions)) {
                        $shippingOptions[$shippingOptionChoice->getId()] = [
                            'id' => $shippingOptionChoice->getId(),
                            'price' => $shippingOptionChoice->getAmountWithoutVat(),
                            'currency' => $shippingOptionChoice->getCurrency(),
                            'deliveryTime' => $shippingOptionChoice->getDeliveryWithinHours() + $shippingOptionChoice->getCollectionWithinHours(),
                        ];
                    }

                    $shippingOptionsAvailableForProcessingCartItems[] = $shippingOptionChoice->getId();
                }

                $cartItems = $shippingOptionsIzbergObject->getCartItems();

                if (count($shippingOptionsAvailableForProcessingCartItems)) {
                    $cartItemGroups[$shippingOptionsIzbergObject->getMerchant()->getId()][] = array_map(
                        static function (\Open\IzbergBundle\Model\CartItem $cartItem) {
                            return $cartItem->getId();
                        },
                        $cartItems->toArray()
                    );

                    foreach ($cartItems as $cartItem) {
                        $itemShippingOptionsIndex[$cartItem->getId()] = $shippingOptionsAvailableForProcessingCartItems;
                        $selectedChoice = null;
                        if ($shippingOptionsIzbergObject->getSelectedChoice()) {
                            $selectedChoice = $shippingOptionsIzbergObject->getSelectedChoice()->getId();
                        }
                        $itemsSelectedShippingOptionIndex[$cartItem->getId()] = $selectedChoice;
                    }
                }
            }
        }

        $cartItemIds = array_keys($itemShippingOptionsIndex);


        // check all items per merchant
        /** @var CartMerchant $merchant */
        foreach ($cart->getMerchants() as $merchant) {
            $merchantCartItemIds = array_map(
                static function (CartItem $cartItem) {
                    return $cartItem->getId();
                },
                $merchant->getItems()
            );

            $merchant->setItems(
                array_map(
                    static function (CartItem $cartItem) use ($cartItemIds, $cart) {
                        $cartItem->setDeliveryOptionExists(true);
                        if (!in_array($cartItem->getId(), $cartItemIds, false)) {
                            $cartItem->setDeliveryOptionExists(false);
                            $cart->disallowCheckout();
                        }

                        return $cartItem;
                    },
                    $merchant->getItems()
                )
            );

            if (count(array_diff($merchantCartItemIds, $cartItemIds))) {
                continue;
            }

            $merchantGroups = $cartItemGroups[$merchant->getId()] ?? [];

            foreach ($merchantGroups as $index => $merchantGroup) {

                $merchantAvailableShippingOptions = [];

                $merchantShippingOptions = array_map(
                    static function (int $cartItemId) use ($itemShippingOptionsIndex) : array {
                        return $itemShippingOptionsIndex[$cartItemId];
                    },
                    $merchantGroup
                );

                if (count($merchantShippingOptions)) {
                    if (count($merchantShippingOptions) > 1) {
                        $merchantAvailableShippingOptions = array_intersect(...$merchantShippingOptions);
                    } else {
                        $merchantAvailableShippingOptions = $merchantShippingOptions[0];
                    }
                }

                $merchantAvailableShippingOptions = array_map(
                    static function (int $shippingOptionId) use ($shippingOptions) {
                        return $shippingOptions[$shippingOptionId];
                    },
                    $merchantAvailableShippingOptions
                );

                usort(
                    $merchantAvailableShippingOptions,
                    static function ($optionA, $optionB) {
                        $optionAPrice = $optionA['price'];
                        $optionBPrice = $optionB['price'];

                        $optionADeliveryTime = $optionA['deliveryTime'];
                        $optionBDeliveryTime = $optionB['deliveryTime'];

                        if ($optionAPrice < $optionBPrice) {
                            return -1;
                        }

                        if ($optionAPrice === $optionBPrice) {
                            if ($optionADeliveryTime < $optionBDeliveryTime) {
                                return -1;
                            }
                        }

                        return 1;
                    }
                );

                $standardShippingOption = $merchantAvailableShippingOptions[0];
                $standardShippingOptionDeliveryTime = null;
                $standardShippingGroup = null;

                $standardShippingGroup = (new CartItemShippingGroup())
                    ->setShippingId($standardShippingOption['id'])
                    ->setDeliveryTime($standardShippingOption['deliveryTime'])
                    ->setCartItemIds($merchantGroup)
                    ->setDeliveryPrice($standardShippingOption['price'])
                    ->setOrder($index)
                    ->flagStandardMode();

                $standardShippingOptionDeliveryTime = $standardShippingGroup->getDeliveryTime();
                $merchant->addStandardDeliveryShippingGroupItem($standardShippingGroup);

                usort(
                    $merchantAvailableShippingOptions,
                    static function ($optionA, $optionB) {
                        $optionAPrice = $optionA['price'];
                        $optionBPrice = $optionB['price'];

                        $optionADeliveryTime = $optionA['deliveryTime'];
                        $optionBDeliveryTime = $optionB['deliveryTime'];

                        if ($optionADeliveryTime < $optionBDeliveryTime) {
                            return -1;
                        }

                        if (($optionADeliveryTime === $optionBDeliveryTime) && $optionAPrice < $optionBPrice) {
                            return -1;
                        }

                        return 1;
                    }
                );

                $fastShippingOption = $merchantAvailableShippingOptions[0];
                $fastShippingOptionDeliveryTime = null;
                $fastShippingOptionGroup = null;

                $fastShippingOptionGroup = (new CartItemShippingGroup())
                    ->setShippingId($fastShippingOption['id'])
                    ->setDeliveryTime($fastShippingOption['deliveryTime'])
                    ->setCartItemIds($merchantGroup)
                    ->setDeliveryPrice($fastShippingOption['price'])
                    ->setOrder($index)
                    ->flagFastMode();

                $fastShippingOptionDeliveryTime = $fastShippingOptionGroup->getDeliveryTime();
                $differentThanStandard = ($standardShippingOptionDeliveryTime > $fastShippingOptionDeliveryTime);

                $merchant->addFastDeliveryShippingGroupItem($fastShippingOptionGroup, $differentThanStandard);

                $merchant->setItems(array_map(
                    static function (CartItem $cartItem) use ($merchantGroup, $standardShippingGroup, $fastShippingOptionGroup) {
                        if (in_array($cartItem->getId(), $merchantGroup, false)) {
                            $cartItem->setStandardDeliveryOption($standardShippingGroup);
                            $cartItem->setFastDeliveryOption($fastShippingOptionGroup);
                        }

                        return $cartItem;
                    },
                    $merchant->getItems()
                ));

            }

            // recuperate all merchant items
            // recuperate all items selected shipping options

            $merchantSelectedShippingOptionIds = array_unique(array_values(array_filter(
                $itemsSelectedShippingOptionIndex,
                static function (int $itemId) use ($merchantCartItemIds): bool {
                    return in_array($itemId, $merchantCartItemIds, false);
                },
                ARRAY_FILTER_USE_KEY
            )));

            // check if shipping mode is standard
            $standardShippingOptions = array_map(
                static function (CartItemShippingGroup $cartItemShippingGroup) {
                    return $cartItemShippingGroup->getShippingId();
                },
                $merchant->getStandardShippingOptions()
            );

            $isStandard = (count(array_diff($merchantSelectedShippingOptionIds, $standardShippingOptions)) === 0);
            if ($isStandard) {
                $merchant->useStandardDeliveryOption();
            } else {
                // check if shipping mode is fast
                $fastShippingOptions = array_map(
                    static function (CartItemShippingGroup $cartItemShippingGroup) {
                        return $cartItemShippingGroup->getShippingId();
                    },
                    $merchant->getFastShippingOptions()
                );

                $isFast = (count(array_diff($merchantSelectedShippingOptionIds, $fastShippingOptions)) === 0);
                if ($isFast) {
                    $merchant->useFastDeliveryOption();
                }
            }

            // if selected shipping option different than standard or fast
            // then use standard one
            // set up merchant delivery price with given shipping option
        }

        // if one item is not reference in the list
        // then the merchant has no shipping options
        // intersect all shipping options
        // determine the standard shipping and the fast shipping
    }

    private function retrieveCartIdFromCartItem(CartItem $cartItem): ?int
    {
        foreach ($this->currencyCartItems as $cartId => $cartItemIds) {
            if (in_array($cartItem->getId(), $cartItemIds, false)) {
                return $cartId;
            }
        }

        return null;
    }
}
