<?php

namespace AppBundle\Factory;

use AppBundle\Exception\PaymentException;
use AppBundle\Model\Order\Order;
use AppBundle\Model\Payment;
use Open\IzbergBundle\Api\PaymentApi;

class PaymentFactory
{
    private PaymentApi $paymentApi;

    public function __construct(PaymentApi $paymentApi)
    {
        $this->paymentApi = $paymentApi;
    }

    /**
     * @throws PaymentException
     */
    public function buildPaymentFromOrder(Order $order): Payment
    {
        $payment = new Payment();
        /** @var \Open\IzbergBundle\Model\Payment $izbergPayment */
        $izbergPayment = $this->paymentApi->findOneByFilters(['order' => $order->getIzbergId()]);

        if ($izbergPayment) {
            $codeTypeMapper = [
                'cb' => Payment::PAYMENT_PREPAYMENT_BY_CREDIT_CARD,
                'bankwire' => Payment::PAYMENT_PREPAYMENT_BY_BANK_WIRE_TRANSFER,
            ];

            if (!isset($codeTypeMapper[$izbergPayment->getPaymentMethod()->getCode()])) {
                throw new PaymentException((string)$codeTypeMapper[$izbergPayment->getPaymentMethod()->getCode()]);
            }

            $payment->setType($codeTypeMapper[$izbergPayment->getPaymentMethod()->getCode()]);
            $payment->setIzbergId($izbergPayment->getId());

        } else {
            $payment->setType(Payment::PAYMENT_BY_TERM_PAYMENT);
        }

        return $payment;
    }
}
