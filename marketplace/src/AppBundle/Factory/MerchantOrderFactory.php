<?php

namespace AppBundle\Factory;

use AppBundle\Entity\MarketPlace;
use AppBundle\Entity\User;
use AppBundle\Model\Cart\CartItem;
use AppBundle\Model\Cart\CartMerchant;
use AppBundle\Model\MerchantOrder;
use AppBundle\Entity\MerchantOrder as MerchantOrderEntity;
use AppBundle\Model\Offer;
use AppBundle\Services\CustomsService;
use AppBundle\Services\IzbergCustomAttributes;
use AppBundle\Services\MerchantService;
use AppBundle\Services\OfferService;
use AppBundle\Services\SecurityService;
use Open\IzbergBundle\Api\MerchantApi;
use Open\IzbergBundle\Api\OrderApi;
use Open\IzbergBundle\Model\Item as IzbergItem;
use Open\IzbergBundle\Model\MerchantOrder as IzbergMerchantOrder;

class MerchantOrderFactory
{
    private OrderApi $orderApi;
    private MerchantApi $merchantApi;
    private MerchantService $merchantService;
    private OfferService $offerService;
    private CustomsService $customsService;
    private SecurityService $securityService;
    private IzbergCustomAttributes $customAttributes;

    public function __construct(
        OrderApi $orderApi,
        MerchantApi $merchantApi,
        MerchantService $merchantService,
        OfferService $offerService,
        CustomsService $customsService,
        SecurityService $securityService,
        IzbergCustomAttributes $customAttributes
    ) {
        $this->orderApi = $orderApi;
        $this->merchantApi = $merchantApi;
        $this->merchantService = $merchantService;
        $this->offerService = $offerService;
        $this->customsService = $customsService;
        $this->securityService = $securityService;
        $this->customAttributes = $customAttributes;
    }

    public function buildMerchantOrderFromEntity(MerchantOrderEntity $merchantOrderEntity): ?MerchantOrder
    {
        $merchantOrder = new MerchantOrder();
        $merchantOrder->setId($merchantOrderEntity->getId());
        $merchantOrder->setMerchantOrderEntity($merchantOrderEntity);

        $izbergMerchantOrder = $this->orderApi->fetchMerchantOrderById($merchantOrderEntity->getId());
        if (!$izbergMerchantOrder) {
            return null;
        }
        $this->buildMerchant($merchantOrder, $izbergMerchantOrder);

        return $merchantOrder;
    }

    private function buildMerchant(MerchantOrder $merchantOrder, IzbergMerchantOrder $izbergMerchantOrder)
    {
        $izbergOrderItems = $izbergMerchantOrder->getItems()->toArray();

        $offers = $this->retrieveOffers(
            array_map(
                function(IzbergItem $item ) {
                    return $item->getOfferId();
                },
                $izbergOrderItems
            ),
            $merchantOrder->getMerchantOrderEntity()->getBuyer()->getMarketPlace(),
            $merchantOrder->getMerchantOrderEntity()->getBuyer()
        );

        $cartItems = array_map(
            function(IzbergItem $item) use ($offers){
                $cartItem = $this->buildCartItem($item, $offers);
                return $cartItem;
            },
            $izbergOrderItems
        );

        $izbergMerchant = $this->merchantApi->getMerchant($izbergMerchantOrder->getMerchant()->getId());

        $cartMerchant = new CartMerchant();
        $cartMerchant->setId($izbergMerchant->id);
        $cartMerchant->setName($izbergMerchant->name);
        $cartMerchant->setDescription($izbergMerchant->description);
        $cartMerchant->setLongDescription($izbergMerchant->long_description);
        $cartMerchant->setLogoImage($izbergMerchant->logo_image);
        $cartMerchant->setCountry($this->merchantService->getCountryFromIzbergResponse($izbergMerchant));
        $cartMerchant->setConditions('');
        $cartMerchant->setSubTotalVat([]);
        $cartMerchant->setSubTotalWithoutVat(0);
        $cartMerchant->setVat(0);
        $cartMerchant->setTotal(0);
        $cartMerchant->setRating($izbergMerchant->overall_score);

        // set merchant comment from izberg merchant order custom attributes
        $comment = null;
        $attributes = $izbergMerchantOrder->getAttributes();
        if (array_key_exists($this->customAttributes->getMerchantComment(), $attributes)) {
            $commentValue = $attributes[$this->customAttributes->getMerchantComment()];
            if (!empty($commentValue)) {
                $comment = $commentValue;
            }
        }
        $cartMerchant->setComment($comment);

        $cartMerchant->setDeliveryPrice($izbergMerchantOrder->getShipping());

        $items = array_map(
            function(CartItem $cartItem) use ($cartMerchant){
                $cartMerchant->addSubTotalWithoutVat($cartItem->getTotalItem());
                $cartMerchant->addTotal(
                    $cartItem->getStatus() === 2000 ? 0 : $cartItem->getQuantity() * $cartItem->getUnitPrice()
                );

                return $cartItem;
            },
            $cartItems
        );

        $cartMerchant->setTotal($cartMerchant->getDeliveryPrice() + $cartMerchant->getTotal());

        usort($items, function (CartItem $cartItemA, CartItem $cartItemB) {
            return $cartItemA->getName() <=> $cartItemB->getName();
        });

        $cartMerchant->setItems($items);
        $cartMerchant->setVatInformation(
            $this->customsService->getInfo(
                $cartMerchant->getCountry(),
                //$this->securityService->getFiscalCountry()
                $merchantOrder->getMerchantOrderEntity()->getBuyer()->getCountryOfDelivery()
            )
        );

        $merchantOrder->setMerchant($cartMerchant);
    }

    private function retrieveOffers(array $offerIds, MarketPlace $marketPlace, User $buyer)
    {
        $offers = $this->offerService->findOffers($offerIds, 'fr', $marketPlace, $buyer);

        return array_combine(
            array_map(
                function(Offer $offer){
                    return $offer->getId();
                },
                $offers
            ),
            $offers
        );
    }

    private function buildCartItem(IzbergItem $izbergItem, array $offers)
    {
        $izbergOrderItem = $this->orderApi->fetchOrderItemById($izbergItem->getId());
        $itemStatus = (int) $izbergItem->getStatus();

        $cartItem = new CartItem();
        $cartItem->setId($izbergItem->getId());
        $cartItem->setStatus($itemStatus);
        $cartItem->setName($izbergItem->getName());
        $cartItem->setUnitPrice($izbergItem->getPrice());
        $cartItem->setQuantity($izbergItem->getQuantity());
        $cartItem->setMoq(0);
        $cartItem->setBatchSize(1);
        $cartItem->setStock(0);
        $cartItem->setOfferId($izbergItem->getOfferId());
        $cartItem->setImageUrl($izbergItem->getItemImageUrl());
        if ($izbergItem->getPrice() == 0) {
            $cartItem->setTaxRate('0');
        } else {
            $cartItem->setTaxRate('' . ($izbergItem->getVat() * 100) / $izbergItem->getPrice());
        }
        $cartItem->setTotalItem(
            $itemStatus === 2000 ? 0 : $izbergItem->getQuantity() * $izbergItem->getPrice()
        );

        $offer = $offers[$cartItem->getOfferId()] ?? null;
        if (!$offer instanceof Offer) {
            $cartItem->lost();
            return $cartItem;
        }

        $isGift = false;
        $isRisk = false;
        $extraInfo = $izbergOrderItem->getExtraInfo();

        if ($extraInfo) {
            if (strtolower($extraInfo->getGift()) == 'yes') {
                $isGift = true;
            }
            if (strtolower($extraInfo->getRisk()) == 'yes') {
                $isRisk = true;
            }
        }
        $cartItem->setGift($isGift);
        $cartItem->setRisk($isRisk);

        $this->buildCartItemFromOffer($cartItem, $offer);

        return $cartItem;
    }

    private function buildCartItemFromOffer(CartItem $cartItem, Offer $offer)
    {
        $cartItem->setMoq($offer->getMoq());
        $cartItem->setBatchSize($offer->getBatchSize());
        $cartItem->setStock(10000);
        $cartItem->setName($offer->getName());
        $cartItem->setIncoterm(null);
        $cartItem->setCountryOfDelivery(null);
        $cartItem->setSellerRef($offer->getSku());
        $cartItem->setBuyerRef(null);
        $cartItem->setFcaAddress(null);
        $cartItem->setDeliveryTime(null);
        $cartItem->setTrueDeliveryTime(null);
        $cartItem->setIsFavourite($offer->getIsFavorit());
        $cartItem->setOfferStatus($offer->getStatus());
    }
}
