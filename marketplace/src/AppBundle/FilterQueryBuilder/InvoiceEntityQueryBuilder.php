<?php

namespace AppBundle\FilterQueryBuilder;


use Doctrine\ORM\QueryBuilder;

class InvoiceEntityQueryBuilder extends EntityQueryBuilder implements FilterQueryBuilderInterface
{
    public function build(QueryBuilder $qb, $data): self
    {
        $this->addWhereClause($qb, 'id',  'id', $data, '=', 'e', true);
        $this->addWhereClause($qb, 'name',  'name', $data, self::LIKE_OPERATOR, 'e', false);
        $this->addWhereClause($qb, 'validationThreshold',  'validationThreshold', $data, '=', 'e', true);
        $this->addWhereClause($qb, 'managerValidation',  'managerValidation', $data, '=', 'e', true);

        return $this;
    }
}
