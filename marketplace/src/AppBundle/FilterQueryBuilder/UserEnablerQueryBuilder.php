<?php
/**
 * Created by PhpStorm.
 * User: AQU04740
 * Date: 21/01/2020
 * Time: 10:26
 */

namespace AppBundle\FilterQueryBuilder;


use Doctrine\ORM\QueryBuilder;

class UserEnablerQueryBuilder extends EntityQueryBuilder implements FilterQueryBuilderInterface
{
    public function build(QueryBuilder $qb, $data, $subQuery = false, QueryBuilder $subQb=null) {

        $this->addWhereClause($qb, 'name',  'entityName', $data, self::LIKE_OPERATOR, 'e', false);
        $this->addWhereClause($qb, 'name',  'siteName', $data, self::LIKE_OPERATOR, 's', false);
        if (!empty($data['userType']) && $data['userType'] !== 'all') {
            $this->addWhereClause($qb, 'userType',  'userType', $data, self::LIKE_OPERATOR, 'ue', false);
        }
        if (!empty($data['status']) && $data['status'] !== 'all') {
            if($data['status'] === 'enabled'){
                $qb->andWhere('ue.enabled = true');
            }else{
                $qb->andWhere('ue.enabled = false');
            }
        }

    }

}