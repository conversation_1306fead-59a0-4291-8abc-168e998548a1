<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 05/04/2018
 * Time: 17:37
 */

namespace AppBundle\FilterQueryBuilder;


use Doctrine\ORM\QueryBuilder;

class EntityQueryBuilder
{

    protected const STATUS = 'STATUS';

    protected const LIKE_OPERATOR = 'LIKE';

    protected function addWhereClause (QueryBuilder &$qb, $attributeName, $index, $data, $operator, $prefix, $strict = true)
    {

        if (!empty($data[$index]) || (array_key_exists($index, $data) && $data[$index] === 0)) {
            $qb->andWhere($prefix . '.' . $attributeName . ' ' . $operator . ' :' . $index);
            if ($strict) {
                $qb->setParameter(':' . $index, $data[$index]);
            } else {
                $qb->setParameter(':' . $index, '%' . $data[$index] . '%');
            }
        }
    }
       protected function orWhereClause (QueryBuilder &$qb, $attributeName, $index, $data, $operator, $prefix, $strict = true){

       if (!empty($data[$index])) {
           $qb->orwhere($prefix . '.' . $attributeName . ' ' . $operator . ' :' . $index);
           if ($strict) {
               $qb->setParameter(':' . $index, $data[$index]);
           } else {
               $qb->setParameter(':' . $index, '%' . $data[$index] . '%');
           }
       }
    }


}