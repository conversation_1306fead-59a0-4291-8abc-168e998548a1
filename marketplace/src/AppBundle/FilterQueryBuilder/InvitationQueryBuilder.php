<?php
/**
 * Created by PhpStorm.
 * User: AQU04740
 * Date: 03/02/2020
 * Time: 11:57
 */

namespace AppBundle\FilterQueryBuilder;


use Doctrine\ORM\QueryBuilder;

class InvitationQueryBuilder  extends EntityQueryBuilder implements FilterQueryBuilderInterface
{
    public function build(QueryBuilder $qb, $data)
    {
        if(!empty($data["search"])) {
            $data["email"] = $data["search"];
        }
        $this->addWhereClause($qb, 'companyName', 'search', $data, self::LIKE_OPERATOR, 'e', false);
        $this->orWhereClause($qb, 'email', 'email', $data, self::LIKE_OPERATOR, 'e', false);

    }
}