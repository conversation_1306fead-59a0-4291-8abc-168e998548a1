<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 30/11/2017
 * Time: 15:24
 */

namespace AppBundle\FilterQueryBuilder;


use AppBundle\Entity\Quote;
use Doctrine\ORM\QueryBuilder;

class QuoteQueryBuilder extends EntityQueryBuilder implements FilterQueryBuilderInterface
{

    public function build(QueryBuilder $qb, $data)
    {
        $this->addWhereClause($qb, 'id',  'id', $data, '=', 'q', true);
        $this->addWhereClause($qb, 'title',  'title', $data, self::LIKE_OPERATOR, 'q', false);


        if(!empty($data['search'])){
           $qb->andWhere(' ( q.quoteNumber like :search or q.subject like :search or v.name like :search)')
            ->setParameter('search','%' . $data['search'] . '%');
        }

        // build for buyer
        if(!empty($data['buyer'])){
            $this->buildBuyer($qb,$data);
        }

        if(!empty($data['vendor'])){
            $this->buildVendor($qb,$data);
        }

    }

    public function buildBuyer (QueryBuilder $qb, $data){


        if (!empty($data['status']) && $data['status'] !== 'all') {

            switch($data['status']){
                case   Quote::META_STATUS_BUYER_DRAFT:
                    $qb->andWhere('q.status in ( :status)');
                    $qb->setParameter('status', Quote::META_BUYER_STATUS_DEF[Quote::META_STATUS_BUYER_DRAFT]);
                    break;
                case   Quote::META_STATUS_BUYER_CANCELLED:
                    $qb->andWhere('q.status in ( :status)');
                    $qb->setParameter('status', Quote::META_BUYER_STATUS_DEF[Quote::META_STATUS_BUYER_CANCELLED]);
                    break;
                default:
                    $qb->andWhere('q.status = :status');
                    $qb->setParameter('status', $data['status']);
                    break;

            }

        }

        if(!empty($data['buyer'])){
            $qb->andWhere('q.buyer = :buyerId')
                ->setParameter('buyerId',$data['buyer']);
        }
    }

    public function buildVendor (QueryBuilder $qb, $data){


        if(!empty($data['vendor'])){
            $qb->andWhere('v.id = :vendorId')
                ->setParameter('vendorId',$data['vendor']);
        }

        if (!empty($data['status']) && $data['status'] !== 'all') {

            switch($data['status']){
                case   Quote::META_STATUS_VENDOR_DRAFT:
                    $qb->andWhere('q.status in ( :status)');
                    $qb->setParameter('status', Quote::META_VENDOR_STATUS_DEF[Quote::META_STATUS_VENDOR_DRAFT]);
                    break;
                case   Quote::META_STATUS_VENDOR_CANCELLED:
                    $qb->andWhere('q.status in ( :status)');
                    $qb->setParameter('status', Quote::META_VENDOR_STATUS_DEF[Quote::META_STATUS_VENDOR_CANCELLED]);
                    break;
                default:
                    $qb->andWhere('q.status = :status');
                    $qb->setParameter('status', $data['status']);
                    break;

            }

        }

    }




}