<?php
/**
 * Created by PhpStorm.
 * User: AQU04740
 * Date: 03/02/2020
 * Time: 11:57
 */

namespace AppBundle\FilterQueryBuilder;


use Doctrine\ORM\QueryBuilder;

class InvitationQueryAdminBuilder  extends EntityQueryBuilder implements FilterQueryBuilderInterface
{
    public function build(QueryBuilder $qb, $data)
    {
        if(!empty($data["search"])) {
            $data["email"] = $data["search"];
        }
        $this->addWhereClause($qb, 'id', 'id', $data, '=', 'e', false);
        $this->addWhereClause($qb, 'companyName', 'companyName', $data, self::LIKE_OPERATOR, 'e', false);
        $this->addWhereClause($qb, 'email', 'email', $data, self::LIKE_OPERATOR, 'e', false);
        $this->addWhereClause($qb, 'firstName', 'firstname', $data, self::LIKE_OPERATOR, 'e', false);
        $this->addWhereClause($qb, 'lastName', 'lastname', $data, self::LIKE_OPERATOR, 'e', false);
        if (!empty($data['status']) && $data['status'] != 'all') {
            $this->addWhereClause($qb, 'status', 'status', $data, '=', 'e', true);
        }

    }
}