<?php

namespace AppBundle\Provider;

use AppBundle\Exception\ExchangeRatesException;
use BenMajor\ExchangeRatesAPI\ExchangeRatesAPI;
use BenMajor\ExchangeRatesAPI\Response;
use Open\IzbergBundle\Service\RedisService;
use Open\LogBundle\Service\LogService;

class ExchangeRates
{
    public const CURRENCY_EURO = 'EUR';
    public const CURRENCY_EXCHANGE_RATE_KEY = 'currency_exchange_rate_key';
    public const CACHE_FILE_NAME = 'currency_exchange_rate';

    protected LogService $logger;
    protected RedisService $cache;
    protected string $logDir;

    public function __construct(string $logDir, LogService $logger, RedisService $cache)
    {
        $this->logger = $logger;
        $this->cache = $cache;
        $this->logDir = $logDir;
    }

    public function getExchangeRate(string $baseCurrency = self::CURRENCY_EURO, bool $fromCache = true): array
    {
        $cache = $this->getCache();
        $cacheKey = $this->computeRedisKey($baseCurrency);

        if (!$fromCache) {
            $cache->removeItem($cacheKey);
        }

        $exchangeRate = $cache->getItem($cacheKey);

        if ($exchangeRate === null) {
            $exchangeRate = $this->fetchCurrencyExchangeRate($baseCurrency);
            $cache->saveItem($cacheKey, $exchangeRate);
        }

        return $exchangeRate;
    }

    private function getCache(): RedisService
    {
        return $this->cache;
    }

    private function fetchCurrencyExchangeRate(string $baseCurrency = self::CURRENCY_EURO): array
    {
        $lookup = new ExchangeRatesAPI();
        $exchangeRates = [];

        try {
            if ($this->isCurrencyDataFileExpired($baseCurrency)) {
                $exchangeRates = $this->getExchangeRatesFromCurrencyDataFile($baseCurrency);
            } else {
                /** @var Response $response */
                $response = $lookup->setBaseCurrency($baseCurrency)->fetch();
                $exchangeRates = $response->getRates();
                $this->saveExchangeRateAsFileCache(json_encode($exchangeRates), $baseCurrency);
            }
        } catch (\Exception $e) {
            $this->logger->error('unable to fetch CurrencyExchangeRate',null, ["currency"=>$baseCurrency,"error"=>$e->getMessage()]);
            throw new ExchangeRatesException($e->getMessage());
        }
        return $exchangeRates;
    }

    private function isCurrencyDataFileExpired(string $baseCurrency): bool
    {
        $fullPath = $this->computeCurrencyDataFileFullPath($baseCurrency);

        return file_exists($fullPath) && (strtotime('-1 day') < fileatime($fullPath));
    }


    private function computeCurrencyDataFileFullPath(string $currency): string
    {
        return $this->logDir . DIRECTORY_SEPARATOR . self::CACHE_FILE_NAME . '_' . strtolower($currency) . '.txt';
    }

    private function writeCurrencyDataFile(string $fullPath, string $exchangeRates)
    {
        $handle = fopen($fullPath, "w");
        fwrite($handle, $exchangeRates);
        fclose($handle);
    }


    private function computeRedisKey($baseCurrency)
    {
        return self::CURRENCY_EXCHANGE_RATE_KEY . '_' . strtoupper($baseCurrency);
    }


    private function saveExchangeRateAsFileCache(string $exchangeRates, string $baseCurrency)
    {
        $fullPath = $this->computeCurrencyDataFileFullPath($baseCurrency);

        if (file_exists($fullPath)) {
            if ($this->isCurrencyDataFileExpired($baseCurrency)) {
                unlink($fullPath);
                $this->writeCurrencyDataFile($fullPath, $exchangeRates);
            }
        } else {
            $this->writeCurrencyDataFile($fullPath, $exchangeRates);
        }
    }

    private function getExchangeRatesFromCurrencyDataFile(string $baseCurrency): array
    {
        $fullPath = $this->computeCurrencyDataFileFullPath($baseCurrency);
        $handle = fopen($fullPath, "r");
        $contents = fread($handle, filesize($fullPath));

        return json_decode($contents, true);
    }
}