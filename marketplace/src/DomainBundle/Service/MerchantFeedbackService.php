<?php

namespace DomainBundle\Service;

use DomainBundle\Domain\Entity\MerchantReview;
use DomainBundle\Domain\Repository\MerchantReviewRepositoryInterface;
use DomainBundle\Event\MerchantModifiedEvent;
use DomainBundle\EventDispatcher\MerchantEventDispatcherInterface;

final class MerchantFeedbackService
{
    /**
     * @var MerchantReviewRepositoryInterface
     */
    private $merchantReviewRepository;

    /**
     * @var MerchantEventDispatcherInterface
     */
    private $merchantEventDispatcher;

    public function __construct(MerchantReviewRepositoryInterface $merchantReviewRepository, MerchantEventDispatcherInterface $merchantEventDispatcher)
    {
        $this->merchantReviewRepository = $merchantReviewRepository;
        $this->merchantEventDispatcher = $merchantEventDispatcher;
    }

    public function addMerchantReview(int $merchantId, int $merchantOrderId, ?string $comment, float $rating)
    {
        if (is_null($comment)) {
            $comment = '';
        }

        $merchantReview = (new MerchantReview())
            ->setMerchantId($merchantId)
            ->setMerchantOrderId($merchantOrderId)
            ->setComment($comment)
            ->setRating($rating);

        $this->merchantReviewRepository->addMerchantReview($merchantReview);
        $this->notifyUpdatedMerchant($merchantId);
    }

    public function updateMerchantReview(int $merchantId, int $merchantOrderId, string $comment, float $rating, string $reviewId)
    {
        $merchantReview = (new MerchantReview())
            ->setReviewId($reviewId)
            ->setMerchantId($merchantId)
            ->setMerchantOrderId($merchantOrderId)
            ->setComment($comment)
            ->setRating($rating);

        $this->merchantReviewRepository->modifyMerchantReview($merchantReview);
        $this->notifyUpdatedMerchant($merchantId);
    }

    private function notifyUpdatedMerchant(int $merchantId)
    {
        $merchantModifiedEvent = new MerchantModifiedEvent($merchantId);
        $this->merchantEventDispatcher->dispatch($merchantModifiedEvent);
    }
}
