<?php

namespace DomainBundle\Command;

use AppBundle\Entity\Merchant;
use AppBundle\Model\Offer;
use AppBundle\Model\Search\Filter\SearchFilter;
use AppBundle\Model\Search\Filter\SearchFilterQuery;
use AppBundle\Services\ElasticSearchService;
use AppBundle\Services\MerchantService;
use AppBundle\Services\OfferService;

class UpdateMerchantCatalog
{
    /**
     * @var OfferService
     */
    private $offerService;

    /**
     * @var MerchantService
     */
    private $merchantService;

    /**
     * @var int
     */
    private $inputArgumentMerchantId;

    public function __construct(OfferService $offerService, MerchantService $merchantService)
    {
        $this->offerService = $offerService;
        $this->merchantService = $merchantService;
    }

    public function setArgumentMerchantId(int $argumentMerchantId): self
    {
        $this->inputArgumentMerchantId = $argumentMerchantId;
        return $this;
    }

    /**
     * @deprectated moved to sf command UpdateMerchantCatalogCommand for sending heartbeats
     * @throws \AppBundle\Model\InvalidSearchParametersException
     */
    public function execute()
    {
        if (!$this->inputArgumentMerchantId) {
            throw new \InvalidArgumentException();
        }
        $countryCode = "test"; // TODO: add country code
        // delay 15 secs
        sleep(15);
        /** @var \AppBundle\Model\Merchant $merchant */
        $merchant = $this->merchantService->findMerchantById($this->inputArgumentMerchantId, true);

        $merchantEntity = $this->merchantService->findMerchantEntityByIzbergId($this->inputArgumentMerchantId);
        if (!$merchant || !$merchantEntity) {
            return;
        }


        // Get the offers from offer service
        $page = 1;
        $pageSize = 30;

        $filterQuery = (new SearchFilterQuery())
            ->addMust(
                (new SearchFilter('merchant.id', $this->inputArgumentMerchantId))
            );

        do {
            $result = $this->offerService->search(
                null,
                $filterQuery,
                $pageSize,
                $page,
                ElasticSearchService::CUSTOM_ANALYZER,
                null,
                null,
                "",
                $merchantEntity->getMarketplace()
            );

            /** @var Offer $offer */
            foreach ($result->getOffers() as $offer) {
                $this->offerService->updateMerchantForOffer($offer, $merchant, $merchantEntity->getMarketplace());
            }
            // Increment page number
            $page++;
        } while(count($result->getOffers()) !== 0);
    }
}
