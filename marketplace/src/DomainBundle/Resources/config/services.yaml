parameters:

services:
    _defaults:
        autowire: true
        autoconfigure: true
        public: false

    DomainBundle\:
        resource: '../../*'
        exclude: '../../{Entity,EventSubscriber,Tests,Util}'

    DomainBundle\QueueConsumer\UpdateMerchantCatalog:
        public: true
        arguments:
            - '%rabbit_host%'
            - '%rabbit_port%'
            - '%rabbit_user%'
            - '%rabbit_password%'
            - '%rabbit_queue%'
            - '@AppBundle\Services\OfferService'
            - '@AppBundle\Services\MerchantService'
            - '@logger.service'