<?php

namespace DomainBundle\Domain\Repository;

use DomainBundle\Domain\Entity\MerchantReview;
use Open\IzbergBundle\Api\MerchantApi;

final class MerchantReviewRepository implements MerchantReviewRepositoryInterface
{
    /**
     * @var MerchantApi
     */
    private $merchantApi;

    public function __construct(MerchantApi $merchantApi)
    {
        $this->merchantApi = $merchantApi;
    }

    public function addMerchantReview(MerchantReview $merchantReview)
    {
        $this->merchantApi->addOrUpdateMerchantReview(
            $merchantReview->getMerchantId(),
            $merchantReview->getMerchantOrderId(),
            $merchantReview->getComment(),
            $merchantReview->getRating()
        );
    }

    public function modifyMerchantReview(MerchantReview $merchantReview)
    {
        $this->merchantApi->addOrUpdateMerchantReview(
            $merchantReview->getMerchantId(),
            $merchantReview->getMerchantOrderId(),
            $merchantReview->getComment(),
            $merchantReview->getRating(),
            $merchantReview->getReviewId()
        );
    }
}
