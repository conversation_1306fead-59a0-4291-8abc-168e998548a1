<?php

namespace DomainBundle\Domain\Entity;

final class MerchantReview
{
    /**
     * @var string
     */
    private $reviewId;

    /**
     * @var int
     */
    private $merchantId;

    /**
     * @var int
     */
    private $merchantOrderId;

    /**
     * @var string
     */
    private $comment;

    /**
     * @var float
     */
    private $rating;

    public function getReviewId(): string
    {
        return $this->reviewId;
    }

    public function setReviewId(string $reviewId): self
    {
        $this->reviewId = $reviewId;
        return $this;
    }

    public function getMerchantId(): int
    {
        return $this->merchantId;
    }

    public function setMerchantId(int $merchantId): self
    {
        $this->merchantId = $merchantId;
        return $this;
    }

    public function getMerchantOrderId(): int
    {
        return $this->merchantOrderId;
    }

    public function setMerchantOrderId(int $merchantOrderId): self
    {
        $this->merchantOrderId = $merchantOrderId;
        return $this;
    }

    public function getComment(): string
    {
        return $this->comment;
    }

    public function setComment(string $comment): self
    {
        $this->comment = $comment;
        return $this;
    }

    public function getRating(): float
    {
        return $this->rating;
    }

    public function setRating(float $rating): self
    {
        $this->rating = $rating;
        return $this;
    }
}
