<?php

namespace DomainBundle\QueueConsumer;

use AppB<PERSON>le\Model\Offer;
use AppBundle\Model\Search\Filter\SearchFilter;
use AppBundle\Model\Search\Filter\SearchFilterQuery;
use AppBundle\Services\ElasticSearchService;
use AppBundle\Services\MerchantService;
use AppBundle\Services\OfferService;
use AppBundle\Util\MemoryUtil;
use Open\IzbergBundle\Api\MerchantApi;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Open\RabbitMQ\ConsumerInterface;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Connection\AMQPStreamConnection;

class UpdateMerchantCatalog implements ConsumerInterface
{
    /**
     * @var string the name of the queue
     */
    private $queue;

    /**
     * rabbit MQ channel
     * @var AMQPChannel $channel
     */
    private $channel;

    /**
     * rabbitMQ Connection
     * @var AMQPStreamConnection $connection
     */
    private $connection;

    /**
     * @var OfferService
     */
    private $offerService;

    /**
     * @var MerchantService
     */
    private $merchantService;

    /**
     * @var LogService
     */
    private $logger;

    /** @var MerchantApi */
    private $merchantApi;

    /**
     * RabbitMQConsumer constructor.
     * @param string $hostname rabbit hostname
     * @param string $port rabbit port
     * @param string $username rabbit username
     * @param string $password rabbit password
     * @param string $queueName rabbit queue name
     */
    public function __construct(string $hostname, string $port, string $username, string $password, string $queueName, OfferService $offerService, MerchantService $merchantService, LogService $logger, MerchantApi $merchantApi)
    {
        $this->logger = $logger;
        $this->connection = new AMQPStreamConnection($hostname,
            (int) $port,
            $username,
            $password);

        $this->queue = $queueName;

        $this->channel = $this->connection->channel();
        $this->channel->queue_declare($this->queue, false, false, false, false);

        $this->offerService = $offerService;
        $this->merchantService = $merchantService;
        $this->merchantApi = $merchantApi;
    }

    /**
     * @param $msg
     * use to send ack to the queue for this message
     */
    public function ackMessage ($msg): void{
        $msg->delivery_info['channel']->basic_ack($msg->delivery_info['delivery_tag']);
    }

    /**
     * run the consumer
     * @throws \ErrorException
     */
    public function run(): void
    {

        //invoke the callback each time a message is available
        $this->channel->basic_consume($this->queue, '', false, false, false, false, [$this, 'callback']);

        while ($this->channel->is_consuming()) {
            $this->channel->wait();
        }
    }

    /**
     * Implementing heartbeat to send heart beats to RMQ server
     * problem describe in this article  https://blog.mollie.com/keeping-rabbitmq-connections-alive-in-php-b11cb657d5fb
     *
     * @throws \PhpAmqpLib\Exception\AMQPIOException
     */
    public function sendHeartbeat()
    {
        $this->connection->checkHeartBeat();
    }

    public function callback($msg): void
    {

        $merchantId = $msg->body;
        $this->logger->info("update merchant catalog start :".$merchantId,EventNameEnum::UPDATE_MERCHANT_CATALOG,null, []);
        // delay 15 secs because of rating which needs to be updated in izberg first
        sleep(15);
        $this->sendHeartbeat();
        $merchantEntity = $this->merchantService->findMerchantEntityByIzbergId($merchantId);

        if(is_null($merchantEntity)){
            return;
        }
        $this->merchantApi->configureApiConnection($merchantEntity->getMarketplace()->getApiConfigurationKey());
        $merchant = $this->merchantService->findMerchantById($merchantId, true);

        if (!$merchant) {

            return;
        }

        // Get the offers from offer service
        $page = 1;
        $pageSize = 30;
        $nbOffer = 0;

        $elasticQuery =  [ 'match' => ['merchant.id'=>$merchantId]];
        //init the cursor
        foreach ($merchantEntity->getMarketplace()->getLanguages() as $language) {
            $result = $this->offerService->scan(
                $pageSize,
                "30s",
                $elasticQuery,
                $language,
                $merchantEntity->getMarketplace()
            );

            while (count($result->getOffers()) !==0) {
                /** @var Offer $offer */
                foreach ($result->getOffers() as $offer){
                    $this->offerService->updateMerchantForOffer($offer,$merchant, $merchantEntity->getMarketplace());
                    $nbOffer++;
                }
                $this->logger->info( "stat", EventNameEnum::UPDATE_MERCHANT_CATALOG, null, ["merchant_id"=>$merchantId,"page"=>$page, "nb_offer"=>$nbOffer,"memory"=>MemoryUtil::getMemoryUsageAsString()]);
                // Increment page number
                $page++;
                //update cursor
                $result = $this->offerService->scroll($result->getScrollId(),"30s", $language);

            }
        }

        $this->logger->info("update merchant catalog end :".$merchantId,EventNameEnum::UPDATE_MERCHANT_CATALOG,null, []);

        // Get the offers from offer service
//        $page = 1;
//        $pageSize = 30;
//
//        $filterQuery = (new SearchFilterQuery())
//            ->addMust(
//                (new SearchFilter('merchant.id', $merchantId))
//            );
//
//        do {
//            $result = $this->offerService->search(null, $filterQuery, $pageSize, $page, ElasticSearchService::CUSTOM_ANALYZER, null, null, "fr");
//            $this->sendHeartbeat();
//
//            /** @var Offer $offer */
//            foreach ($result->getOffers() as $offer) {
//                $this->offerService->updateMerchantForOffer($offer, $merchant);
//                $this->sendHeartbeat();
//            }
//            // Increment page number
//            $page++;
//        } while(count($result->getOffers()) !== 0);

        $this->ackMessage($msg);
    }
}
