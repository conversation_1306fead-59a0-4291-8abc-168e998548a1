<?php

namespace DomainBundle\EventDispatcher;

use DomainBundle\Event\MerchantModifiedEvent;
use Open\RabbitMQ\Queue;

final class MerchantEventDispatcher implements MerchantEventDispatcherInterface
{
    private Queue $queue;

    public function __construct(Queue $queue)
    {
        $this->queue = $queue;
    }

    public function dispatch(MerchantModifiedEvent $merchantModifiedEvent)
    {
        $this->queue->produce((string)$merchantModifiedEvent->getMerchantId());
    }
}
