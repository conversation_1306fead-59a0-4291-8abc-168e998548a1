# API Authentication Troubleshooting Guide

## Overview
This guide helps diagnose authentication issues with the remote APIs in your Click & Buy marketplace system.

## 1. Environment Variables Check

First, verify all required environment variables are properly set:

```bash
# Check if environment variables are loaded
echo "=== IZBERG API CONFIGURATION ==="
echo "IZBERG_API_DOMAIN: $IZBERG_API_DOMAIN"
echo "IZBERG_API_PROTOCOL: $IZBERG_API_PROTOCOL"
echo "IZBERG_API_VERSION: $IZBERG_API_VERSION"

echo "=== IZBERG FRANCE CONFIGURATION ==="
echo "IZBERG_FRANCE_APPLICATION_ID: $IZBERG_FRANCE_APPLICATION_ID"
echo "IZBERG_FRANCE_ACCESS_TOKEN: ${IZBERG_FRANCE_ACCESS_TOKEN:0:20}..." # Show only first 20 chars
echo "IZBERG_FRANCE_USERNAME: $IZBERG_FRANCE_USERNAME"

echo "=== IDEAL API CONFIGURATION ==="
echo "IDEAL_API_DOMAIN: $IDEAL_API_DOMAIN"
echo "IDEAL_API_CLIENT_ID: $IDEAL_API_CLIENT_ID"
echo "IDEAL_API_CLIENT_SECRET: ${IDEAL_API_CLIENT_SECRET:0:10}..." # Show only first 10 chars

echo "=== TOTAL OPENID CONFIGURATION ==="
echo "TOTAL_OPEN_ID_CONNECT_CLIENT_ID: $TOTAL_OPEN_ID_CONNECT_CLIENT_ID"
echo "TOTAL_OPEN_ID_CONNECT_URL_AUTHORIZE: $TOTAL_OPEN_ID_CONNECT_URL_AUTHORIZE"
```

## 2. Network Connectivity Tests

### Test Basic Connectivity

```bash
# Test Izberg API connectivity
curl -v https://api.sandbox.iceberg.technology/v1/

# Test Ideal API connectivity  
curl -v https://pprd.idealv5.totalenergies.com/

# Test Total OpenID Connect
curl -v http://localhost:8080/auth/realms/total/.well-known/openid_configuration
```

### Test DNS Resolution

```bash
# Check DNS resolution
nslookup api.sandbox.iceberg.technology
nslookup pprd.idealv5.totalenergies.com
nslookup localhost
```

## 3. Izberg API Authentication Tests

### Test Anonymous Authentication

```bash
# Test anonymous user authentication (based on your AuthenticationApi)
curl -X POST "https://api.sandbox.iceberg.technology/v1/user/authenticate/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer open_service-account2884:b2297a6c9543871e10dd057aab98d9863345635e" \
  -d '{
    "email": null,
    "username": null,
    "first_name": null,
    "last_name": null
  }' \
  -v
```

### Test Current User Endpoint

```bash
# Test getting current user (requires valid token)
curl -X GET "https://api.sandbox.iceberg.technology/v1/user/me/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer open_service-account2884:b2297a6c9543871e10dd057aab98d9863345635e" \
  -v
```

### Test Application Info

```bash
# Test getting current application
curl -X GET "https://api.sandbox.iceberg.technology/v1/application/mine/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer open_service-account2884:b2297a6c9543871e10dd057aab98d9863345635e" \
  -H "x-application-id: 2954" \
  -v
```

## 4. Ideal API Authentication Tests

### Test OAuth2 Token Endpoint

```bash
# Test Ideal API OAuth2 authentication
curl -X POST "https://pprd.idealv5.totalenergies.com/auth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "Accept: application/json" \
  -H "Ocp-Apim-Subscription-Key: YOUR_APIM_KEY_HERE" \
  -d "grant_type=client_credentials&client_id=<EMAIL>&client_secret=NoQlcbzyVl8z8zjXQQyoperxaS+NKr8Y&scope=usercube_api" \
  -v
```

### Test with Bearer Token

```bash
# After getting token from above, test API call
ACCESS_TOKEN="your_access_token_here"
curl -X GET "https://pprd.idealv5.totalenergies.com/api/endpoint" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Ocp-Apim-Subscription-Key: YOUR_APIM_KEY_HERE" \
  -v
```

## 5. Total OpenID Connect Tests

### Test Authorization Endpoint

```bash
# Test Total OpenID Connect discovery
curl -X GET "http://localhost:8080/auth/realms/total/.well-known/openid_configuration" \
  -H "Accept: application/json" \
  -v
```

### Test Token Endpoint

```bash
# Test token endpoint (replace with actual authorization code)
curl -X POST "http://localhost:8080/auth/realms/total/protocol/openid-connect/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=authorization_code&client_id=marketplace&client_secret=13f45f14-6193-4ca4-9318-b2c46f02b89f&code=YOUR_AUTH_CODE&redirect_uri=YOUR_REDIRECT_URI" \
  -v
```

## 6. Common Authentication Issues

### Issue 1: 401 Unauthorized
**Symptoms:** HTTP 401 responses
**Possible Causes:**
- Invalid or expired access token
- Wrong client credentials
- Missing required headers

**Debug Steps:**
```bash
# Check token format and expiration
echo "YOUR_TOKEN" | base64 -d | jq .

# Verify client credentials are correct
curl -X POST "https://your-auth-endpoint/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=client_credentials&client_id=YOUR_CLIENT_ID&client_secret=YOUR_CLIENT_SECRET" \
  -v
```

### Issue 2: 403 Forbidden
**Symptoms:** HTTP 403 responses
**Possible Causes:**
- Valid authentication but insufficient permissions
- Wrong application ID or namespace
- API rate limiting

**Debug Steps:**
```bash
# Check application permissions
curl -X GET "https://api.sandbox.iceberg.technology/v1/application/mine/" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "x-application-id: YOUR_APP_ID" \
  -v
```

### Issue 3: Invalid Signature (Izberg HMAC)
**Symptoms:** Authentication failures with HMAC-based auth
**Debug Steps:**
```bash
# Test HMAC signature generation (example for debugging)
EMAIL="<EMAIL>"
FIRST_NAME="total"
LAST_NAME="Open"
TIMESTAMP=$(date +%s)
SECRET_KEY="e9c72225-c0e7-47d7-a432-300a3be257b3"

# Create signature string
AUTH_RAW="${EMAIL};${FIRST_NAME};${LAST_NAME};${TIMESTAMP}"
echo "Auth raw: $AUTH_RAW"

# Generate HMAC-SHA1 signature
SIGNATURE=$(echo -n "$AUTH_RAW" | openssl dgst -sha1 -hmac "$SECRET_KEY" | cut -d' ' -f2)
echo "Signature: $SIGNATURE"
```

## 7. Database Connectivity Tests

### Test Database Connection

```sql
-- Test basic database connectivity
SELECT 1 as connection_test;

-- Check marketplace configurations
SELECT * FROM marketplace LIMIT 5;

-- Check user authentication status
SELECT id, username, email, is_active FROM user WHERE email = '<EMAIL>';

-- Check API configurations (if stored in DB)
SELECT * FROM api_configuration LIMIT 5;
```

### Check User Permissions

```sql
-- Check user roles and permissions
SELECT u.username, u.email, r.name as role_name 
FROM user u 
LEFT JOIN user_role ur ON u.id = ur.user_id 
LEFT JOIN role r ON ur.role_id = r.id 
WHERE u.email = '<EMAIL>';
```

## 8. Application Logs Analysis

### Check Symfony Logs

```bash
# Check recent API errors
tail -f var/log/dev.log | grep -i "api\|auth\|401\|403"

# Check Izberg API specific logs
tail -f var/log/dev.log | grep -i "izberg"

# Check authentication errors
tail -f var/log/dev.log | grep -i "authentication\|login"
```

### Check Application-Specific Logs

```bash
# Check custom log directory
tail -f /tmp/log/*.log | grep -i "error\|auth"

# Check for specific error patterns
grep -r "SECURITY ALERT" /tmp/log/
grep -r "401\|403" /tmp/log/
```

## 9. Environment-Specific Tests

### Development Environment

```bash
# Test local services
curl -v http://localhost:8080/health
curl -v http://maria-db:3306
curl -v http://redis:6379
```

### Production Environment

```bash
# Test production endpoints (replace with actual URLs)
curl -v https://your-production-api.com/health
curl -v https://your-production-auth.com/token
```

## 10. Quick Diagnostic Script

Create this script to run all basic tests:

```bash
#!/bin/bash
echo "=== API Authentication Diagnostic ==="
echo "Timestamp: $(date)"
echo ""

echo "1. Testing Izberg API connectivity..."
curl -s -o /dev/null -w "HTTP Status: %{http_code}, Time: %{time_total}s\n" https://api.sandbox.iceberg.technology/v1/

echo "2. Testing Ideal API connectivity..."
curl -s -o /dev/null -w "HTTP Status: %{http_code}, Time: %{time_total}s\n" https://pprd.idealv5.totalenergies.com/

echo "3. Testing Total OpenID connectivity..."
curl -s -o /dev/null -w "HTTP Status: %{http_code}, Time: %{time_total}s\n" http://localhost:8080/auth/realms/total/.well-known/openid_configuration

echo "4. Testing database connectivity..."
mysql -h maria-db -u clickandbuy -ppassword -e "SELECT 1" clickandbuy 2>/dev/null && echo "Database: OK" || echo "Database: FAILED"

echo ""
echo "=== Environment Variables Check ==="
env | grep -E "(IZBERG|IDEAL|TOTAL)_" | head -10

echo ""
echo "Diagnostic complete. Check the results above for any failures."
```

## Next Steps

1. Run the environment variables check first
2. Test basic connectivity to all APIs
3. Try the authentication tests for each API
4. Check application logs for specific error messages
5. Verify database connectivity and user permissions
6. If issues persist, enable debug logging and capture full request/response cycles

## Advanced Debugging

### Enable Symfony Debug Mode

```bash
# Enable debug mode in .env
echo "APP_DEBUG=1" >> marketplace/.env

# Clear cache
cd marketplace && php bin/console cache:clear --env=dev

# Enable verbose HTTP client logging
export SYMFONY_HTTP_CLIENT_DEBUG=1
```

### Capture Full HTTP Traffic

```bash
# Use mitmproxy to capture all HTTP traffic
pip install mitmproxy
mitmdump -s capture_api_calls.py

# Or use tcpdump for network-level debugging
sudo tcpdump -i any -w api_debug.pcap host api.sandbox.iceberg.technology
```

### Test with Different User Contexts

```bash
# Test with different marketplace configurations
curl -X GET "https://api.sandbox.iceberg.technology/v1/user/me/" \
  -H "Authorization: Bearer open_service-account2884:b2297a6c9543871e10dd057aab98d9863345635e" \
  -H "x-application-id: 2954" \
  -H "x-marketplace: france" \
  -v

# Test Belgium configuration
curl -X GET "https://api.sandbox.iceberg.technology/v1/user/me/" \
  -H "Authorization: Bearer open_service-account2884:3e31fb22e2f7ee3fc66f564b2a7079e2fa4d1260" \
  -H "x-application-id: 1004375" \
  -H "x-marketplace: belgium" \
  -v
```

## Database Diagnostic Queries

### Check API Configuration Status

```sql
-- Check marketplace configurations and their API settings
SELECT
    m.id,
    m.name,
    m.api_configuration_key,
    m.is_active,
    m.created_at
FROM marketplace m
ORDER BY m.created_at DESC;

-- Check if API configurations exist
SELECT
    ac.configuration_name,
    ac.application_id,
    ac.username,
    ac.domain_id,
    ac.is_active
FROM api_configuration ac;

-- Check user sessions and authentication status
SELECT
    s.session_id,
    s.user_id,
    s.created_at,
    s.updated_at,
    u.username,
    u.email
FROM sessions s
JOIN user u ON s.user_id = u.id
WHERE s.updated_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY s.updated_at DESC
LIMIT 10;
```

### Check Authentication Logs

```sql
-- Check recent authentication attempts
SELECT
    l.id,
    l.level,
    l.message,
    l.context,
    l.created_at
FROM log_entries l
WHERE l.message LIKE '%auth%'
   OR l.message LIKE '%401%'
   OR l.message LIKE '%403%'
   OR l.context LIKE '%IZBERG_API%'
ORDER BY l.created_at DESC
LIMIT 20;

-- Check for specific API errors
SELECT
    l.message,
    l.context,
    l.created_at,
    COUNT(*) as occurrence_count
FROM log_entries l
WHERE l.created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
  AND (l.message LIKE '%API ERROR%' OR l.level = 'error')
GROUP BY l.message, l.context
ORDER BY occurrence_count DESC, l.created_at DESC;
```

## Symfony Console Commands for Debugging

### Test API Connections via Console

```bash
# Test Izberg API connection (create custom command if needed)
cd marketplace && php bin/console app:test-izberg-api --marketplace=france

# Check current API configuration
cd marketplace && php bin/console debug:container | grep -i api

# Check environment variables
cd marketplace && php bin/console debug:dotenv

# Test database connection
cd marketplace && php bin/console doctrine:query:sql "SELECT 1"
```

### Custom Debug Commands

Create these custom Symfony commands for testing:

```bash
# Test API authentication
cd marketplace && php bin/console app:debug-api-auth --api=izberg --marketplace=france

# Test all API endpoints
cd marketplace && php bin/console app:test-all-apis

# Validate API configuration
cd marketplace && php bin/console app:validate-api-config
```

## Common Solutions

### Solution 1: Token Refresh Issues
```bash
# Force token refresh for Izberg API
curl -X POST "https://api.sandbox.iceberg.technology/v1/user/authenticate/" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": null,
    "first_name": "total",
    "last_name": "Open"
  }' \
  -v
```

### Solution 2: Environment Configuration
```bash
# Verify all required environment variables are set
required_vars=("IZBERG_API_DOMAIN" "IZBERG_FRANCE_ACCESS_TOKEN" "IDEAL_API_CLIENT_ID" "TOTAL_OPEN_ID_CONNECT_CLIENT_ID")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "ERROR: $var is not set"
    else
        echo "OK: $var is set"
    fi
done
```

### Solution 3: Network and Firewall Issues
```bash
# Test network connectivity with timeout
timeout 10 curl -v https://api.sandbox.iceberg.technology/v1/ || echo "Network timeout or connection refused"

# Test DNS resolution
dig api.sandbox.iceberg.technology
dig pprd.idealv5.totalenergies.com

# Check if running in Docker and test container networking
docker network ls
docker exec -it your_container_name curl -v https://api.sandbox.iceberg.technology/v1/
```

### Solution 4: Configuration Mismatch
```bash
# Compare environment variables with configuration files
echo "=== Environment Variables ==="
env | grep IZBERG | sort

echo "=== Configuration Files ==="
grep -r "izberg" marketplace/config/parameters/ | grep -v ".git"

# Validate JSON configuration
echo $IZBERG_MERCHANT_BASE_URLS | jq .
echo $IZBERG_CATEGORY_ICONS | jq .
```

## Emergency Troubleshooting Checklist

1. **[ ]** Environment variables are loaded correctly
2. **[ ]** Network connectivity to all API endpoints works
3. **[ ]** Database connection is established
4. **[ ]** API credentials are valid and not expired
5. **[ ]** Required headers are included in requests
6. **[ ]** Application logs show no critical errors
7. **[ ]** Symfony cache is cleared
8. **[ ]** Docker containers are running (if applicable)
9. **[ ]** Firewall rules allow outbound connections
10. **[ ]** API rate limits are not exceeded

## Contact Information

If issues persist after following this guide:
1. Collect all test results from this guide
2. Gather recent application logs
3. Document the specific error messages
4. Note the exact steps that reproduce the issue
5. Contact your API provider support with this information
