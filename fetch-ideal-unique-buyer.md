# Fetching a Single User from Ideal API

## Overview

This document explores the possibility of fetching a single user by their IGG (identifier) from the Ideal API, based on analysis of the current system implementation.

## Current System Analysis

### Existing API Endpoint

The current system uses a bulk retrieval endpoint:
```
GET /api/Resource/Directory_User?api-version=1.0
```

**Current Query Structure**:
- Uses complex SQL-like `squery` parameter
- Retrieves all users matching criteria
- No apparent filtering by specific IGG in the API call

### Query Analysis

The current `squery` parameter includes:
```sql
where (MainEmployeeId!%="L9" and MainEmployeeId!%="N" and MainEmployeeId!=null and MainFirstName!=null and MainLastName!=null and s.Identifier="P")
```

This suggests the query language supports:
- Field filtering (`MainEmployeeId!=null`)
- Pattern matching (`MainEmployeeId!%="L9"`)
- Equality conditions (`s.Identifier="P"`)

## Potential Single User Fetching

### Method 1: Modified Query Filter

**Hypothesis**: Add IGG-specific filter to existing endpoint

**Modified Query**:
```sql
join MainRecord u 
join u.Organization o 
join o.OrganizationType ot 
join u.WorkingLocation wl 
join wl.ParentLocation pwl 
join pwl.ParentLocation gpwl 
join PresenceState s 
join u.PersonalTitle pt 
join u.PreferredLanguage pl 
join u.CostCenter cc 
join u.EmployeeType et 
join u.AssignmentCompany ac 
join ac.Location acl 
join u.Manager m 
select MainEmployeeId, MainLastName, MainPhoneticFirstName, u.SecondaryEmailAddress, u.EmailAddress, ot.ShortName_en, o.DisplayEntity, wl.Name_fr, wl.Uid, wl.Street, wl.Street2, wl.Street3, wl.Cedex, wl.PostalCode, wl.Locality, pwl.Name_fr, gpwl.Name_fr, pwl.Uid, gpwl.Uid, s.Identifier, pt.Name_fr, pl.DisplayName, u.ToipPhoneNumber, u.PhoneNumber, cc.Name, u.CostCenterText, et.Name_fr, ac.Uid, ac.Name, acl.Name_fr, acl.Uid, m.MainEmployeeId, u.UserRecordWhenIn, u.UserRecordWhenOut 
where (MainEmployeeId="{IGG_VALUE}" and s.Identifier="P")
```

**API Call Example**:
```http
GET /api/Resource/Directory_User?api-version=1.0
Headers:
  Authorization: Bearer {ACCESS_TOKEN}
  Ocp-Apim-Subscription-Key: {APIM_KEY}
Query Parameters:
  path: /Custom/Resources/Directory_User/View
  squery: [Modified query with specific IGG filter]
  PageSize: 1
```

### Method 2: Alternative Endpoint Discovery

**Potential Endpoints** (to be tested):
```
GET /api/Resource/Directory_User/{IGG}?api-version=1.0
GET /api/User/{IGG}?api-version=1.0
GET /api/Resource/Directory_User/View/{IGG}?api-version=1.0
```

## Implementation Approach

### Step 1: Extend UserApi Class

Add method to existing `UserApi` class:

```php
<?php
// marketplace/src/Open/IdealBundle/Api/UserApi.php

public function retrieveUserByIgg(string $igg): ?Entry
{
    $squery = 'join MainRecord u join u.Organization o join o.OrganizationType ot join u.WorkingLocation wl join wl.ParentLocation pwl join pwl.ParentLocation gpwl join PresenceState s join u.PersonalTitle pt join u.PreferredLanguage pl join u.CostCenter cc join u.EmployeeType et join u.AssignmentCompany ac join ac.Location acl join u.Manager m select MainEmployeeId, MainLastName, MainPhoneticFirstName, u.SecondaryEmailAddress, u.EmailAddress, ot.ShortName_en, o.DisplayEntity, wl.Name_fr, wl.Uid, wl.Street, wl.Street2, wl.Street3, wl.Cedex, wl.PostalCode, wl.Locality, pwl.Name_fr, gpwl.Name_fr, pwl.Uid, gpwl.Uid, s.Identifier, pt.Name_fr, pl.DisplayName, u.ToipPhoneNumber, u.PhoneNumber, cc.Name, u.CostCenterText, et.Name_fr, ac.Uid, ac.Name, acl.Name_fr, acl.Uid, m.MainEmployeeId, u.UserRecordWhenIn, u.UserRecordWhenOut where (MainEmployeeId="' . $igg . '" and s.Identifier="P")';
    
    $path = "/Custom/Resources/Directory_User/View";
    
    $query = [
        "path" => $path,
        "squery" => $squery,
        "PageSize" => 1
    ];
    
    $response = $this->get(
        (new Request("/api/Resource/Directory_User?api-version=1.0"))
            ->setQuery($query)
            ->setMethod(Request::GET)
            ->setConf([Request::TIMEOUT => $this->apiConfiguration->getTimeout()])
    );
    
    $data = json_decode($response, true);
    
    if (isset($data['Result']) && count($data['Result']) > 0) {
        return $this->serializer->deserialize(
            json_encode($data['Result'][0]), 
            Entry::class, 
            'json'
        );
    }
    
    return null;
}
```

### Step 2: Create Service Method

Add to `DistantUserService`:

```php
public function retrieveUserByIgg(string $igg): ?DistantUser
{
    try {
        $entry = $this->idealClient->userApi->retrieveUserByIgg($igg);
        
        if ($entry === null) {
            return null;
        }
        
        return $this->mapUser($entry);
        
    } catch (\Throwable $t) {
        $this->logService->error(
            'Error retrieving user by IGG: ' . $t->getMessage(), 
            EventNameEnum::COMMAND_RETRIEVE_BUYER,
            null,
            ['igg' => $igg]
        );
        return null;
    }
}
```

### Step 3: Create Console Command

```php
<?php
// marketplace/src/AppBundle/Command/RetrieveUserByIggCommand.php

namespace AppBundle\Command;

use AppBundle\Services\Import\DistantUserService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class RetrieveUserByIggCommand extends Command
{
    private DistantUserService $distantUserService;

    public function __construct(DistantUserService $distantUserService)
    {
        $this->distantUserService = $distantUserService;
        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setName('open:buyer:retrieve-by-igg')
            ->setDescription('Retrieve a single buyer by IGG from Ideal API')
            ->addArgument('igg', InputArgument::REQUIRED, 'The IGG identifier');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $igg = $input->getArgument('igg');
        
        $output->writeln("Retrieving user with IGG: {$igg}");
        
        $distantUser = $this->distantUserService->retrieveUserByIgg($igg);
        
        if ($distantUser === null) {
            $output->writeln("<error>User not found or error occurred</error>");
            return 1;
        }
        
        $output->writeln("<info>User found:</info>");
        $output->writeln("IGG: " . $distantUser->getIgg());
        $output->writeln("Name: " . $distantUser->getFirstName() . " " . $distantUser->getLastName());
        $output->writeln("Email: " . $distantUser->getEmail());
        $output->writeln("Organization: " . $distantUser->getOrganization());
        
        return 0;
    }
}
```

## Testing Approach

### Step 1: Test Query Modification

1. **Manual API Test**:
   ```bash
   # Get access token first
   curl -X POST "https://pprd.idealv5.totalenergies.com/connect/token" \
     -H "Accept: application/json" \
     -H "Ocp-Apim-Subscription-Key: {APIM_KEY}" \
     -d "grant_type=client_credentials&client_id={CLIENT_ID}&client_secret={CLIENT_SECRET}&scope=usercube_api"
   
   # Test single user query
   curl -X GET "https://pprd.idealv5.totalenergies.com/api/Resource/Directory_User?api-version=1.0" \
     -H "Authorization: Bearer {ACCESS_TOKEN}" \
     -H "Ocp-Apim-Subscription-Key: {APIM_KEY}" \
     -G \
     --data-urlencode "path=/Custom/Resources/Directory_User/View" \
     --data-urlencode "squery=join MainRecord u ... where (MainEmployeeId=\"{TEST_IGG}\" and s.Identifier=\"P\")" \
     --data-urlencode "PageSize=1"
   ```

2. **Console Command Test**:
   ```bash
   docker exec -it click-and-buy php bin/console open:buyer:retrieve-by-igg {TEST_IGG}
   ```

### Step 2: Validate Response Structure

Ensure the response matches the expected `Entry` structure used in bulk retrieval.

## Limitations and Considerations

### Current Limitations

1. **API Documentation**: No official documentation for single user retrieval
2. **Query Language**: Limited understanding of full query capabilities
3. **Performance**: Unknown if single-user queries are optimized
4. **Rate Limiting**: May have different limits than bulk operations

### Alternative Approaches

1. **Local Database Query**: Query existing `distant_user` table
2. **Filtered Bulk Query**: Use small PageSize with IGG filter
3. **Cache Strategy**: Implement local caching of frequently accessed users

## Recommended Implementation

### Phase 1: Proof of Concept
1. Test query modification approach
2. Validate response structure
3. Measure performance impact

### Phase 2: Production Implementation
1. Add error handling and logging
2. Implement caching strategy
3. Add monitoring and metrics
4. Create comprehensive tests

### Phase 3: Integration
1. Integrate with existing user management
2. Add to admin interface
3. Document for operations team

## Usage Examples

```bash
# Retrieve specific user
docker exec -it click-and-buy php bin/console open:buyer:retrieve-by-igg "12345"

# Retrieve and import specific user
docker exec -it click-and-buy php bin/console open:buyer:retrieve-by-igg "12345"
docker exec -it click-and-buy php bin/console open:buyer:import-single "12345"
```

## Next Steps

1. **Test the modified query approach** with a known IGG
2. **Validate API response structure** matches bulk retrieval
3. **Implement error handling** for edge cases
4. **Add performance monitoring** for single-user queries
5. **Document findings** and update this guide based on test results
