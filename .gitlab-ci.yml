image: gitlab.lateos.net:5050/click_and_buy/click-and-buy/php:8.1

cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
        - marketplace/vendor/
        - izberg-offers-sync/vendor

variables:
    DOCKER_IMAGES_UPDATES_BRANCH: 'docker-images-updates'

stages:
    - build
    - analysis
    - tests
    - security
    - docker

include:
    - local: .gitlab/ci-templates/jobs-template-ci.yml
    - local: .gitlab/ci-templates/docker-publish-template-ci.yml
    - local: .gitlab/ci-jobs/docker-publish-ci.yml
    - local: .gitlab/ci-jobs/marketplace-jobs-ci.yml
    - local: .gitlab/ci-jobs/izberg-offers-sync-jobs-ci.yml
