# Ideal API - Current Cron Job System Documentation

## Overview

The Click & Buy system implements an automated two-phase process to synchronize users from the Ideal API. This process runs via cron jobs and handles the complete lifecycle of user import and hierarchy management.

## System Architecture

### Components

1. **DistantUserService** (`marketplace/src/AppBundle/Services/Import/DistantUserService.php`)
2. **BuyerImportationService** (`marketplace/src/AppBundle/Services/BuyerImportationService.php`)
3. **IdealClient** (`marketplace/src/Open/IdealBundle/IdealClient.php`)
4. **Commands**:
   - `RetrieveBuyerCommand` (`open:buyer:retrieve`)
   - `ImportBuyerCommand` (`open:buyer:import`)

## Two-Phase Process

### Phase 1: Data Retrieval (`open:buyer:retrieve`)

**Command**: `php bin/console open:buyer:retrieve`

**Process Flow**:

1. **Clear Existing Data**: Removes all records from `distant_user` table
2. **API Authentication**: Authenticates with Ideal API using OAuth2 client credentials
3. **Paginated Data Retrieval**: Fetches users in batches using continuation tokens
4. **Data Transformation**: Maps API response to `DistantUser` entities
5. **Database Storage**: Persists users to `distant_user` table in batches of 100

**API Configuration**:
```
Endpoint: https://pprd.idealv5.totalenergies.com/api/Resource/Directory_User
Method: GET
Authentication: OAuth2 Bearer Token
Pagination: ContinuationToken-based
Page Size: 10,000 users per call (configurable)
```

**Query Parameters**:
- `path`: "/Custom/Resources/Directory_User/View"
- `squery`: Complex SQL-like query (see below)
- `PageSize`: Number of users per request
- `ContinuationToken`: For pagination

**SQL Query Used**:
```sql
join MainRecord u 
join u.Organization o 
join o.OrganizationType ot 
join u.WorkingLocation wl 
join wl.ParentLocation pwl 
join pwl.ParentLocation gpwl 
join PresenceState s 
join u.PersonalTitle pt 
join u.PreferredLanguage pl 
join u.CostCenter cc 
join u.EmployeeType et 
join u.AssignmentCompany ac 
join ac.Location acl 
join u.Manager m 
select MainEmployeeId, MainLastName, MainPhoneticFirstName, u.SecondaryEmailAddress, u.EmailAddress, ot.ShortName_en, o.DisplayEntity, wl.Name_fr, wl.Uid, wl.Street, wl.Street2, wl.Street3, wl.Cedex, wl.PostalCode, wl.Locality, pwl.Name_fr, gpwl.Name_fr, pwl.Uid, gpwl.Uid, s.Identifier, pt.Name_fr, pl.DisplayName, u.ToipPhoneNumber, u.PhoneNumber, cc.Name, u.CostCenterText, et.Name_fr, ac.Uid, ac.Name, acl.Name_fr, acl.Uid, m.MainEmployeeId, u.UserRecordWhenIn, u.UserRecordWhenOut 
where (MainEmployeeId!%="L9" and MainEmployeeId!%="N" and MainEmployeeId!=null and MainFirstName!=null and MainLastName!=null and s.Identifier="P") 
order by MainEmployeeId asc
```

**Data Mapping** (Entry → DistantUser):
- `MainEmployeeId` → `igg` (Primary identifier)
- `MainPhoneticFirstName` → `firstName`
- `MainLastName` → `lastName` (UTF-8 encoded)
- `PersonalTitle.NameFr` → `personalTitle`
- `PreferredLanguage.DisplayName` → `preferredLanguage`
- `Email` → `email`
- `Phone` → `telephone`
- `Organization.DisplayEntity` → `organization`
- `Manager.MainEmployeeId` → `managerIgg`
- Site information (name, street, zipcode, locality, country)
- Invoicing entity details
- Contract and date information

### Phase 2: User Import (`open:buyer:import`)

**Command**: `php bin/console open:buyer:import`

**Process Flow**:

1. **Pass 1 - User Creation**:
   - Reads all `DistantUser` records
   - Maps to `BuyerImportationDataObject`
   - Creates/updates `User` entities
   - Handles Izberg API integration
   - Processes in batches of 100

2. **Pass 2 - Hierarchy Management**:
   - Establishes manager-subordinate relationships
   - Creates `UserToUserRelationship` entities
   - Handles delegation permissions

**Business Logic**:
- Users are identified by IGG (MainEmployeeId)
- Email validation and formatting
- Language preference handling (defaults to "EN")
- Address normalization
- Manager hierarchy establishment
- Role assignment (ROLE_BUYER)

## Database Schema

### DistantUser Table
```sql
CREATE TABLE distant_user (
    id INT AUTO_INCREMENT PRIMARY KEY,
    igg VARCHAR(180) NOT NULL,
    first_name VARCHAR(180),
    last_name VARCHAR(180),
    personal_title VARCHAR(10),
    preferred_language VARCHAR(50),
    email VARCHAR(180),
    telephone VARCHAR(180),
    organization VARCHAR(180),
    manager_igg VARCHAR(180),
    cost_center VARCHAR(180),
    site_name VARCHAR(180),
    site_street VARCHAR(180),
    site_street2 VARCHAR(180),
    site_zipcode VARCHAR(180),
    site_locality VARCHAR(180),
    site_country VARCHAR(180),
    site_country_code VARCHAR(10),
    invoicing_entity_name VARCHAR(180),
    invoicing_entity_country VARCHAR(180),
    invoicing_entity_country_code VARCHAR(10),
    start_date VARCHAR(50),
    end_date VARCHAR(50),
    update_date VARCHAR(50),
    contrat VARCHAR(180)
);
```

## API Authentication

**OAuth2 Flow**:
1. **Token Request**:
   ```
   POST /connect/token
   Headers:
     - Accept: application/json
     - Ocp-Apim-Subscription-Key: {APIM_KEY}
   Body:
     - grant_type: client_credentials
     - client_id: {CLIENT_ID}
     - client_secret: {CLIENT_SECRET}
     - scope: usercube_api
   ```

2. **API Requests**:
   ```
   Headers:
     - Authorization: Bearer {ACCESS_TOKEN}
     - Ocp-Apim-Subscription-Key: {APIM_KEY}
   ```

## Configuration

**Environment Variables**:
```
IDEAL_API_SCHEME=https
IDEAL_API_DOMAIN=pprd.idealv5.totalenergies.com
IDEAL_API_APIM_KEY=Ocp-Apim-Subscription-Key
IDEAL_API_CLIENT_ID=<EMAIL>
IDEAL_API_CLIENT_SECRET=NoQlcbzyVl8z8zjXQQyoperxaS+NKr8Y
IDEAL_TIMEOUT=300
IDEAL_NB_USER_BY_CALL=10000
```

## Error Handling

- **Deserialization Errors**: Logged and skipped, process continues
- **API Timeouts**: Configurable timeout (300 seconds)
- **Memory Management**: Garbage collection after each batch
- **SQL Logging**: Disabled during import for performance

## Performance Considerations

- **Batch Processing**: 100 users per database flush
- **Memory Management**: Entity manager clearing and garbage collection
- **Pagination**: Large page sizes (10,000 users) to minimize API calls
- **Streaming**: Uses HTTP streaming for large responses

## Monitoring and Logging

- **Log Events**: 
  - `COMMAND_RETRIEVE_BUYER`
  - `COMMAND_IMPORT_BUYER`
- **Metrics**: User count, processing time, error counts
- **Debug Information**: API request/response details

## Manual Execution

```bash
# Step 1: Retrieve users from Ideal API
docker exec -it click-and-buy php bin/console open:buyer:retrieve

# Step 2: Import users into system
docker exec -it click-and-buy php bin/console open:buyer:import
```

## Dependencies

- **JsonMachine**: For streaming JSON parsing
- **Symfony HttpClient**: For API communication
- **Doctrine ORM**: For database operations
- **Symfony Serializer**: For data transformation
