{"doctrine/cache": {"version": "2.2.0"}, "doctrine/collections": {"version": "1.8.0"}, "doctrine/common": {"version": "3.4.3"}, "doctrine/dbal": {"version": "3.6.1"}, "doctrine/deprecations": {"version": "v1.0.0"}, "doctrine/doctrine-migrations-bundle": {"version": "3.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "1d01ec03c6ecbd67c3375c5478c9a423ae5d6a33"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "doctrine/event-manager": {"version": "1.2.0"}, "doctrine/inflector": {"version": "2.0.6"}, "doctrine/instantiator": {"version": "1.5.0"}, "doctrine/lexer": {"version": "2.1.0"}, "doctrine/migrations": {"version": "3.5.5"}, "doctrine/orm": {"version": "2.14.1"}, "doctrine/persistence": {"version": "3.1.4"}, "doctrine/sql-formatter": {"version": "1.1.3"}, "elasticsearch/elasticsearch": {"version": "v7.17.0"}, "ezimuel/guzzlestreams": {"version": "3.0.1"}, "ezimuel/ringphp": {"version": "1.2.0"}, "friendsofphp/proxy-manager-lts": {"version": "v1.0.14"}, "halaxa/json-machine": {"version": "1.0.1"}, "laminas/laminas-code": {"version": "4.7.1"}, "monolog/monolog": {"version": "2.3.5"}, "predis/predis": {"version": "v1.1.10"}, "psr/cache": {"version": "1.0.1"}, "psr/container": {"version": "1.1.2"}, "psr/event-dispatcher": {"version": "1.0.0"}, "psr/log": {"version": "1.1.4"}, "react/promise": {"version": "v2.9.0"}, "symfony/amqp-messenger": {"version": "v5.4.3"}, "symfony/cache": {"version": "v5.4.3"}, "symfony/cache-contracts": {"version": "v2.5.0"}, "symfony/config": {"version": "v5.4.3"}, "symfony/console": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.3", "ref": "da0c8be8157600ad34f10ff0c9cc91232522e047"}, "files": ["bin/console"]}, "symfony/dependency-injection": {"version": "v5.4.3"}, "symfony/deprecation-contracts": {"version": "v2.5.0"}, "symfony/doctrine-bridge": {"version": "v5.4.21"}, "symfony/doctrine-messenger": {"version": "v5.4.3"}, "symfony/dotenv": {"version": "v5.4.3"}, "symfony/error-handler": {"version": "v5.4.3"}, "symfony/event-dispatcher": {"version": "v5.4.3"}, "symfony/event-dispatcher-contracts": {"version": "v2.5.0"}, "symfony/filesystem": {"version": "v5.4.3"}, "symfony/finder": {"version": "v5.4.3"}, "symfony/flex": {"version": "1.18", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "c0eeb50665f0f77226616b6038a9b06c03752d8e"}, "files": [".env"]}, "symfony/framework-bundle": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.4", "ref": "3cd216a4d007b78d8554d44a5b1c0a446dab24fb"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/http-client": {"version": "v5.4.3"}, "symfony/http-client-contracts": {"version": "v2.5.0"}, "symfony/http-foundation": {"version": "v5.4.3"}, "symfony/http-kernel": {"version": "v5.4.4"}, "symfony/messenger": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.3", "ref": "25e3c964d3aee480b3acc3114ffb7940c89edfed"}, "files": ["config/packages/messenger.yaml"]}, "symfony/monolog-bridge": {"version": "v5.4.3"}, "symfony/monolog-bundle": {"version": "3.7", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.7", "ref": "a7bace7dbc5a7ed5608dbe2165e0774c87175fe6"}, "files": ["config/packages/dev/monolog.yaml", "config/packages/prod/deprecations.yaml", "config/packages/prod/monolog.yaml", "config/packages/test/monolog.yaml"]}, "symfony/password-hasher": {"version": "v5.4.19"}, "symfony/polyfill-intl-grapheme": {"version": "v1.24.0"}, "symfony/polyfill-intl-normalizer": {"version": "v1.24.0"}, "symfony/polyfill-mbstring": {"version": "v1.24.0"}, "symfony/polyfill-php73": {"version": "v1.24.0"}, "symfony/polyfill-php80": {"version": "v1.24.0"}, "symfony/polyfill-php81": {"version": "v1.24.0"}, "symfony/property-access": {"version": "v5.4.19"}, "symfony/property-info": {"version": "v5.4.19"}, "symfony/redis-messenger": {"version": "v5.4.3"}, "symfony/routing": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.3", "ref": "44633353926a0382d7dfb0530922c5c0b30fae11"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/runtime": {"version": "v5.4.3"}, "symfony/security-bundle": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "98f1f2b0d635908c2b40f3675da2d23b1a069d30"}, "files": ["config/packages/security.yaml"]}, "symfony/security-core": {"version": "v5.4.19"}, "symfony/security-csrf": {"version": "v5.4.19"}, "symfony/security-guard": {"version": "v5.4.19"}, "symfony/security-http": {"version": "v5.4.20"}, "symfony/service-contracts": {"version": "v2.5.0"}, "symfony/stopwatch": {"version": "v5.4.21"}, "symfony/string": {"version": "v5.4.3"}, "symfony/var-dumper": {"version": "v5.4.3"}, "symfony/var-exporter": {"version": "v5.4.3"}, "symfony/yaml": {"version": "v5.4.3"}}