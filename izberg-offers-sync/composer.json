{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=7.2.5", "ext-ctype": "*", "ext-iconv": "*", "doctrine/annotations": "^1.0", "doctrine/doctrine-bundle": "^2.4", "doctrine/doctrine-migrations-bundle": "^3.1", "doctrine/orm": "^2.9", "elasticsearch/elasticsearch": "^7.17", "halaxa/json-machine": "^1.0", "predis/predis": "^1.1", "symfony/cache": "5.4.*", "symfony/console": "5.4.*", "symfony/dotenv": "5.4.*", "symfony/flex": "^1.17|^2", "symfony/framework-bundle": "5.4.*", "symfony/http-client": "5.4.*", "symfony/http-kernel": "^v5.4", "symfony/messenger": "5.4.*", "symfony/monolog-bundle": "^3.7", "symfony/runtime": "5.4.*", "symfony/security-bundle": "^5.4", "symfony/security-core": "^5.4", "symfony/security-http": "^5.4", "symfony/yaml": "5.4.*"}, "config": {"allow-plugins": {"composer/package-versions-deprecated": true, "symfony/flex": true, "symfony/runtime": true}, "optimize-autoloader": true, "preferred-install": {"*": "dist"}, "sort-packages": true, "platform": {"php": "7.4"}}, "autoload": {"psr-4": {"Izberg\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "5.4.*"}}}