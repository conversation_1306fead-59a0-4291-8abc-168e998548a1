<?php

namespace <PERSON><PERSON><PERSON>\Helper;

use <PERSON><PERSON><PERSON>\Exception\InvalidParameterException;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

trait ParameterBagTrait
{
    use ExceptionTrait;

    private ParameterBagInterface $parameterBag;

    /**
     * @required
     */
    public function setParameterBag(ParameterBagInterface $parameterBag)
    {
        $this->parameterBag = $parameterBag;
    }

    public function getParameter(string $key)
    {
        if (!$this->parameterBag->has($key)) {
            $this->throwException(new InvalidParameterException($key));
        }

        return $this->parameterBag->get($key);
    }
}
