<?php

namespace <PERSON><PERSON><PERSON>\Helper;

use Exception;
use <PERSON><PERSON><PERSON>\Event\ExceptionEvent;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;

trait ExceptionTrait
{
    protected EventDispatcherInterface $eventDispatcher;

    /**
     * @required
     */
    public function setEventDispatcher(EventDispatcherInterface $eventDispatcher)
    {
        $this->eventDispatcher = $eventDispatcher;
    }

    public function throwException(Exception $exception)
    {
        $this->eventDispatcher->dispatch(new ExceptionEvent($exception));
    }
}
