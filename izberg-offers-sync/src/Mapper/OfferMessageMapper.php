<?php

namespace I<PERSON><PERSON>\Mapper;

use <PERSON><PERSON><PERSON>\Event\OffersListSyncEvent;
use <PERSON><PERSON><PERSON>\Model\Elastic\Document;
use <PERSON><PERSON>berg\Model\Elastic\DocumentsList;
use <PERSON><PERSON><PERSON>\Model\Messenger\OfferMessage;

class OfferMessageMapper
{
    public function mapFromOffersListSyncEvent(OffersListSyncEvent $event): OfferMessage
    {
        $offersDocumentsList = new DocumentsList();

        foreach ($event->getOffersList() as $offer) {
            $offersDocumentsList->addDocument((new Document())
                ->setDocumentIndexName($event->getIndexName())
                ->setDocumentId($offer->id)
                ->setDocumentContent(json_decode(json_encode($offer), true))
            );
        }

        return new OfferMessage($event->getIndexName(), $offersDocumentsList);
    }
}
