<?php

namespace <PERSON><PERSON><PERSON>\Mapper;

use <PERSON><PERSON><PERSON>\Model\Api\ApplicationConfiguration;

class ApplicationConfigurationMapper
{
    public function map(array $applicationConfig): ApplicationConfiguration
    {
        return ((new ApplicationConfiguration())
            ->setApplicationId($applicationConfig['application_id'])
            ->setAccessToken($applicationConfig['access_token'])
            ->setChannels($applicationConfig['channels'])
        );
    }
}
