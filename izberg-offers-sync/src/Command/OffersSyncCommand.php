<?php

namespace I<PERSON><PERSON>\Command;

use <PERSON><PERSON><PERSON>\Service\SyncOffersService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class OffersSyncCommand extends Command
{
    private SyncOffersService $syncOffersService;

    public function __construct(SyncOffersService $syncOffersService)
    {
        parent::__construct('izberg:sync:offers');
        $this->syncOffersService = $syncOffersService;
    }

    protected function configure()
    {
        $this
            ->setDescription('Command that synchronize izberg offers with local index')
            ->setHelp('[php bin/console | symfony] izberg:sync:offers --marketplace=[france]')
            ->addArgument('marketplace', InputArgument::OPTIONAL, 'the marketplace name to synchronize', 'france');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->syncOffersService->syncOffers($input->getArgument('marketplace'));
        return 0;
    }
}
