<?php

namespace <PERSON><PERSON><PERSON>\Api;

use Generator;
use <PERSON><PERSON><PERSON>\Contract\CacheInterface;
use <PERSON><PERSON><PERSON>\Exception\MarketplaceConfigurationException;
use <PERSON><PERSON><PERSON>\Exception\ResponseStreamChunksException;
use <PERSON><PERSON><PERSON>\Helper\ExceptionTrait;
use <PERSON><PERSON><PERSON>\Helper\ParameterBagTrait;
use <PERSON><PERSON><PERSON>\Mapper\ApplicationConfigurationMapper;
use <PERSON><PERSON><PERSON>\Model\Api\ApplicationConfiguration;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseStreamInterface;

abstract class AbstractApi
{
    use ExceptionTrait;
    use ParameterBagTrait;

    #############################
    ### PRIVATE SERVICES
    #############################
    private ParameterBagInterface $parameterBag;
    private ApplicationConfigurationMapper $applicationConfigurationMapper;

    #############################
    ### SHARED SERVICES
    #############################
    protected EventDispatcherInterface $eventDispatcher;
    protected CacheInterface $cache;

    #############################
    ### SHARED PROPERTIES
    #############################
    protected int $cacheExpiration;
    protected int $httpTimeout;

    protected string $baseUrl;
    protected string $channelBaseUrl;

    protected HttpClientInterface $httpClient;
    protected ?ApplicationConfiguration $applicationConfiguration = null;

    public function __construct()
    {
        $this->httpClient = HttpClient::create();
    }

    #############################
    ### GETTERS ANS SETTERS
    #############################
    public function setParameterBag(ParameterBagInterface $parameterBag): self
    {
        $this->parameterBag = $parameterBag;
        return $this;
    }

    public function setApplicationConfigurationMapper(ApplicationConfigurationMapper $applicationConfigurationMapper): self
    {
        $this->applicationConfigurationMapper = $applicationConfigurationMapper;
        return $this;
    }

    public function getEventDispatcher(): EventDispatcherInterface
    {
        return $this->eventDispatcher;
    }

    public function setEventDispatcher(EventDispatcherInterface $eventDispatcher): self
    {
        $this->eventDispatcher = $eventDispatcher;
        return $this;
    }

    public function getCache(): CacheInterface
    {
        return $this->cache;
    }

    public function setCache(CacheInterface $cache): self
    {
        $this->cache = $cache;
        return $this;
    }

    public function getCacheExpiration(): int
    {
        return $this->cacheExpiration;
    }

    public function setCacheExpiration(int $cacheExpiration): self
    {
        $this->cacheExpiration = $cacheExpiration;
        return $this;
    }

    public function getHttpTimeout(): int
    {
        return $this->httpTimeout;
    }

    public function setHttpTimeout(int $httpTimeout): self
    {
        $this->httpTimeout = $httpTimeout;
        return $this;
    }

    public function getBaseUrl(): string
    {
        return $this->baseUrl;
    }

    public function setBaseUrl(string $baseUrl): self
    {
        $this->baseUrl = $baseUrl;
        return $this;
    }

    public function getChannelBaseUrl(): string
    {
        return $this->channelBaseUrl;
    }

    public function setChannelBaseUrl(string $channelBaseUrl): self
    {
        $this->channelBaseUrl = $channelBaseUrl;
        return $this;
    }

    public function getApplicationConfiguration(): ?ApplicationConfiguration
    {
        return $this->applicationConfiguration;
    }

    public function setApplicationConfiguration(string $marketplace): self
    {
        $configParameters = "$marketplace.client.config";

        if (!$this->parameterBag->has($configParameters)) {
            $this->throwException(new MarketplaceConfigurationException($configParameters));
        }

        $this->applicationConfiguration = $this->applicationConfigurationMapper->map(
            $this->parameterBag->get($configParameters)
        );
        return $this;
    }

    #############################
    ### UTILITY METHODS
    #############################
    public function getBaseRequestHeaders(): array
    {
        return [
            'timeout' => $this->getHttpTimeout(),
            'headers' => [
                'Authorization' => $this->getApplicationConfiguration()->getAccessToken(),
                'x-application-id' => $this->getApplicationConfiguration()->getApplicationId(),
            ]
        ];
    }

    public function httpClientChunks(ResponseStreamInterface $responseStream): Generator
    {
        try {
            foreach ($responseStream as $chunk) {
                yield $chunk->getContent();
            }
        } catch (TransportExceptionInterface $exception) {
            $this->throwException((new ResponseStreamChunksException($exception)));
        }
    }
}
