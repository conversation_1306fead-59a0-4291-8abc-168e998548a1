<?php

namespace <PERSON><PERSON><PERSON>\Api;

use Generator;
use <PERSON><PERSON><PERSON>\Exception\FetchChannelsChangesException;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

class OfferApi extends AbstractApi
{
    private const CHANNELS_URL_API_PARAM = 'api_channel_base_url';

    /**
     * Methods that return channels changes as an array of : channelId => Generator
     */
    public function fetchChannelChanges(int $channelId,array $filters = []): Generator
    {

            $url = sprintf('%s%s/viewer/?%s',
                $this->getParameter(self::CHANNELS_URL_API_PARAM),
                $channelId,
                http_build_query($filters)
            );

            try {
                $response = $this->httpClient->request('GET', $url, $this->getBaseRequestHeaders());
            } catch (TransportExceptionInterface $exception) {
                $this->throwException((new FetchChannelsChangesException($channelId, $exception)));
            }

            return $this->httpClientChunks($this->httpClient->stream($response));


    }
}
