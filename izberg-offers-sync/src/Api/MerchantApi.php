<?php

namespace <PERSON><PERSON><PERSON>\Api;

use <PERSON><PERSON><PERSON>\Exception\FetchMerchantAttributeException;
use <PERSON><PERSON><PERSON>\Exception\FetchMerchantException;
use <PERSON><PERSON><PERSON>\Helper\CustomAttributes;
use Throwable;

class Merchant<PERSON>pi extends AbstractApi
{
    private const MERCHANT_CACHE_PREFIX = 'IZBERG_MERCHANT';
    private const MERCHANT_URL_API_PARAM = 'api_merchant_base_url';

    public function fetchMerchant(string $merchantId): ?array
    {
        $cacheKey = sprintf('%s_%s', self::MERCHANT_CACHE_PREFIX, $merchantId);
        if ($this->cache->keyExists($cacheKey)) {
            return $this->cache->get($cacheKey);
        }

        $requestUrl = sprintf('%s%s', $this->getParameter(self::MERCHANT_URL_API_PARAM), $merchantId);
        $merchant = null;

        try {
            // fetch merchant
            $response = $this->httpClient->request('GET', $requestUrl, $this->getBaseRequestHeaders());
            $merchant = $response->toArray();

            // put on cache merchant
            $this->cache->save($cacheKey, $merchant);
        } catch (Throwable $e) {
            $this->throwException(new FetchMerchantException($merchantId, $e));
        }

        return $merchant;
    }

    public function fetchMerchantAttribute(string $merchantId, string $attributeKey): ?string
    {
        $cacheKey = sprintf('%s_%s_ATTRS', self::MERCHANT_CACHE_PREFIX, $merchantId);

        if ($this->cache->keyExists($cacheKey)) {
            $merchantAttributes = $this->cache->get($cacheKey);
            return key_exists($attributeKey, $merchantAttributes) ? $merchantAttributes[$attributeKey] : null;
        }

        $requestUrl = sprintf('%s%s/attributes', $this->getParameter(self::MERCHANT_URL_API_PARAM), $merchantId);
        $attributeValue = null;

        try {
            $response = $this->httpClient->request('GET', $requestUrl, $this->getBaseRequestHeaders());
            $responseData = $response->toArray();

            $merchantAttributes = [];
            foreach ($responseData['objects'] as $attribute) {
                // save wanted attribute value
                if ($attribute['key'] == $attributeKey) {
                    $attributeValue = $attribute['value'];
                }
                // save all merchant attributes
                $merchantAttributes[$attribute['key']] = $attribute['value'];
            }
            // put on cache merchant attributes
            $this->cache->save($cacheKey, $merchantAttributes);
        } catch (Throwable $e) {
            $this->throwException(new FetchMerchantAttributeException($attributeKey, $e));
        }

        return $attributeValue;
    }

    public function fetchMerchantRating(string $merchantId): float
    {
        $merchant = $this->fetchMerchant($merchantId);
        return (float)($merchant['overall_score'] ?? 0.0);
    }

    public function fetchMerchantAdaptedCompany(string $merchantId): ?string
    {
        return $this->fetchMerchantAttribute(
            $merchantId,
            CustomAttributes::IZBERG_MERCHANT_ATTR_ADAPTED_COMPANY
        );
    }

    public function fetchMerchantContractCadre(string $merchantId): ?string
    {
        return $this->fetchMerchantAttribute(
            $merchantId,
            CustomAttributes::IZBERG_MERCHANT_ATTR_CONTRACT_CADRE
        );
    }

    public function fetchMerchantBranches(string $merchantId)
    {
        $branchesAttribute = $this->fetchMerchantAttribute(
            $merchantId,
            CustomAttributes::IZBERG_MERCHANT_ATTR_BRANCH
        );

        if (!empty($branchesAttribute)) {
            $branchesAttribute = explode(';', $branchesAttribute);
            $branchesAttribute = array_unique(array_filter($branchesAttribute));
        }

        return $branchesAttribute;
    }
}
