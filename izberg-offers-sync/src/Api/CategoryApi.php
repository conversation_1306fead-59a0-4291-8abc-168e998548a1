<?php

namespace <PERSON><PERSON><PERSON>\Api;

use <PERSON><PERSON><PERSON>\Exception\FetchCategoryException;
use Throwable;

class CategoryApi extends AbstractApi
{
    private const CATEGORY_CACHE_PREFIX = 'IZBERG_CATEGORY';
    private const CATEGORY_URL_API_PARAM = 'api_category_base_url';

    public function fetchCategory(int $categoryId): ?array
    {
        $cacheKey = sprintf('%s_%s', self::CATEGORY_CACHE_PREFIX, $categoryId);
        if ($this->cache->keyExists($cacheKey)) {
            return $this->cache->get($cacheKey);
        }

        $requestUrl = sprintf('%s%s', $this->getParameter(self::CATEGORY_URL_API_PARAM), $categoryId);
        $category = null;

        try {
            // fetch category
            $response = $this->httpClient->request('GET', $requestUrl, $this->getBaseRequestHeaders());
            $category = $response->toArray();

            // put on cache category
            $this->cache->save($cacheKey, $category);
        } catch (Throwable $e) {
            $this->throwException(new FetchCategoryException($categoryId, $e));
        }

        return $category;
    }

    public function fetchCategoryKeywords(int $categoryId): array
    {
        $category = $this->fetchCategory($categoryId);
        return ($category && $category['keywords']) ? explode(';', $category['keywords']) : [];
    }
}
