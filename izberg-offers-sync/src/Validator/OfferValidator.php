<?php

namespace <PERSON><PERSON><PERSON>\Validator;

use stdClass;

class OfferValidator
{
    private iterable $validators;

    public function __construct(iterable $validators)
    {
        $this->validators = $validators;
    }

    public function isValid(stdClass $offer): bool
    {
        foreach ($this->validators as $validator) {
            if (!call_user_func($validator, $offer)) {
                return false;
            }
        }

        return true;
    }
}
