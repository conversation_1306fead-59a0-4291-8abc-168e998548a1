<?php

namespace <PERSON><PERSON><PERSON>\Validator\Offer;

use <PERSON><PERSON><PERSON>\Exception\OfferValidationException;
use <PERSON><PERSON><PERSON>\Helper\ExceptionTrait;
use <PERSON><PERSON><PERSON>\Helper\ParameterBagTrait;

class OfferSecurityCategoriesValidator
{
    use ExceptionTrait;
    use ParameterBagTrait;

    private const PARAM_SECURITY_CATEGORIES = 'izberg_security_categories';

    public function __invoke($offer): bool
    {
        $offerCategories = [];
        $securityCategories = $this->getParameter(self::PARAM_SECURITY_CATEGORIES);

        if ($offer->product && $offer->product->application_categories) {
            $offerCategories = $offer->product->application_categories;
        }

        if (!$offer->merchant->contract_cadre && !empty(array_intersect($offerCategories, $securityCategories))) {
            $this->throwException(new OfferValidationException($offer->id, self::class));
            return false;
        }

        return true;
    }
}
