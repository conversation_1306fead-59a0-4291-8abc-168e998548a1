<?php

namespace <PERSON><PERSON><PERSON>\Validator\Offer;

use <PERSON><PERSON><PERSON>\Exception\OfferValidationException;
use <PERSON><PERSON><PERSON>\Helper\CustomAttributes;
use <PERSON><PERSON><PERSON>\Helper\ExceptionTrait;
use stdClass;

class OfferCountryOfDeliveryValidator
{
    use ExceptionTrait;

    public function __invoke(stdClass $offer): bool
    {
        if (!property_exists($offer->attributes, CustomAttributes::IZBERG_ATTR_COUNTRY_OF_DELIVERY) ||
            empty(get_object_vars($offer->attributes)[CustomAttributes::IZBERG_ATTR_COUNTRY_OF_DELIVERY])
        ) {

            $this->throwException(new OfferValidationException($offer->id, self::class));
            return false;
        }

        return true;
    }
}
