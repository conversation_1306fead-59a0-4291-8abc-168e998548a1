<?php

namespace <PERSON><PERSON><PERSON>\Repository;

use DateTime;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\ManagerRegistry;
use <PERSON><PERSON><PERSON>\Entity\StatOffer;

class StatOfferRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, StatOffer::class);
    }

    public function deleteBeforeOneWeek(): void
    {

        $date = new DateTime('-1 week');
        $qb = $this->_em->createQueryBuilder();
        $query = $qb->delete('SyncBundle:StatOffer', 's')
            ->where('s.createdAt < :date')
            ->setParameters([
                "date" => $date,
            ])
            ->getQuery();
        $query->execute();
    }

    public function getMaxOfferSyncByIndexName(string $indexName): ?StatOffer
    {
        $sub = $this->_em->createQueryBuilder('sub')->select('max(s1.maxLastSync)')
            ->from("SyncBundle:StatOffer", "s1")
            ->where("s1.index= :index")
            ->setParameters([
                "index" => $indexName,
            ]);

        $qb = $this->_em->createQueryBuilder('s');
        $query = $qb->select('s')
            ->from(StatOffer::class, 's')
        ->where('s.index = :index')
            ->andWhere($qb->expr()->eq('s.maxLastSync', '('.$sub->getDQL()).')')
            ->setParameters([
                "index" => $indexName,
            ])
            ->getQuery();
        $stats =  $query->getResult();
        if(count($stats)>=1) {
            return $stats[0];
        }
        return null;
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function save(StatOffer $creditNote)
    {
        $this->_em->persist($creditNote);
        $this->_em->flush($creditNote);
    }

}