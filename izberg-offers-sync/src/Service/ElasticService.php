<?php

namespace <PERSON><PERSON><PERSON>\Service;

use Elasticsearch\Client;
use Elasticsearch\ClientBuilder;
use <PERSON><PERSON><PERSON>\Model\Elastic\BulkResult;
use <PERSON><PERSON><PERSON>\Model\Elastic\Document;
use <PERSON><PERSON><PERSON>\Model\Elastic\DocumentsList;
use Psr\Log\LoggerInterface;

class ElasticService
{
    private Client $client;

    public function __construct(array $indexHosts, bool $enableSsl, string $sslCertificatePath)
    {
        $clientBuilder = ClientBuilder::create()->setHosts($indexHosts);
        if ($enableSsl && !empty($sslCertificatePath)) {
            $clientBuilder->setSSLVerification($sslCertificatePath);
        }

        $this->client = $clientBuilder->build();
    }

    public function createIndexIfNotExists(string $indexName, ?string $mappingFilePath = null): bool
    {
        $params = [
            'index' => $indexName,
        ];

        if (!$this->client->indices()->exists($params)) {
            if ($mappingFilePath) {
                $params ['body'] = json_decode(file_get_contents($mappingFilePath), true);
            }

            $this->client->indices()->create($params);
            return true;
        }

        return false;
    }

    public function createIndexAliasesIfNotExists(string $indexName, array $aliases): bool
    {
        if (empty($aliases)) {
            return false;
        }
        $params = [
            'name' => implode(',', $aliases),
        ];
        if (!$this->client->indices()->existsAlias($params)) {
            $params = [
                'body' => [
                    'actions' => array_map(
                        function (string $alias) use ($indexName) {
                            return [
                                'add' => [
                                    'index' => $indexName,
                                    'alias' => $alias,
                                ],
                            ];
                        },
                        $aliases
                    ),
                ],
            ];

            $this->client->indices()->updateAliases($params);
            return true;
        }

        return false;
    }

    public function indexDocument(Document $document)
    {
        $this->client->index($document->getDocumentBody());
    }

    public function indexDocuments(DocumentsList $documentsList): BulkResult
    {
        return $this->analyseBulkResponse($this->client->bulk($documentsList->getDocumentsList()));
    }

    private function analyseBulkResponse(array $response): BulkResult
    {
        $succeededs = [];
        $errors = [];
        /** @var array $item */
        foreach ($response['items'] as $item) {
            /** @var array $update */
            $update = $item["index"];
            $id = (int)$update["_id"];
            if ($update["status"] === 400) {
                /** @var array $error */
                $error = $update["error"];
                $message = sprintf("%s [%s]", (string)$error["reason"], json_encode($error));
                $errors[$id] = $message;
            } else {
                $succeededs[] = $id;
            }
        }
        $result = new  BulkResult($succeededs, $errors);
        return $result;
    }

    public function logResult(LoggerInterface $logger, BulkResult $bulkResulk): void
    {

        $logger->info(
            sprintf(
                "[ES_MANAGER] - %d indexed [ids:%s], %d not indexed",
                count($bulkResulk->successId),
                implode(",", $bulkResulk->successId),
                count($bulkResulk->errors)
            )
        );
        /**
         * @var int $id
         * @var string $message
         */
        foreach ($bulkResulk->errors as $id => $message) {
            $logger->error(sprintf("[ES_MANAGER] - offer not indexed [id:%s]", $id), ["message" => $message]);
        }
    }
}
