<?php

namespace <PERSON><PERSON><PERSON>\Service;

use DateTimeImmutable;
use <PERSON><PERSON><PERSON>\Api\CategoryApi;
use <PERSON><PERSON><PERSON>\Api\MerchantApi;
use <PERSON><PERSON><PERSON>\Api\OfferApi;
use <PERSON><PERSON><PERSON>\Event\EndSyncEvent;
use <PERSON><PERSON><PERSON>\Event\IndexAliasesCreationEvent;
use <PERSON><PERSON><PERSON>\Event\IndexCreationEvent;
use <PERSON><PERSON><PERSON>\Event\OffersListSyncEvent;
use I<PERSON><PERSON>\Event\StartSyncEvent;
use <PERSON><PERSON><PERSON>\Helper\CustomAttributes;
use I<PERSON><PERSON>\Helper\ExceptionTrait;
use <PERSON><PERSON><PERSON>\Helper\ParameterBagTrait;
use I<PERSON><PERSON>\Repository\StatOfferRepository;
use <PERSON><PERSON><PERSON>\Utils\DateUtils;
use <PERSON><PERSON><PERSON>\Validator\OfferValidator;
use JsonMachine\Exception\InvalidArgumentException;
use JsonMachine\Items;
use stdClass;

class SyncOffersService
{
    use ExceptionTrait;
    use ParameterBagTrait;


    #################
    ### CONSTANTS
    #################
    private const PARAM_ES_INDEX_MAPPING = 'elastic_index_mapping';
    private const PARAM_ES_INDEXES_ALIASES = 'elastic_indexes_aliases';
    private const ES_INDEX_PREFIX = 'offers';

    ################
    ### SERVICES
    ################
    private OfferApi $offerApi;
    private MerchantApi $merchantApi;
    private CategoryApi $categoryApi;
    private OfferValidator $offerValidator;
    private StatOfferRepository $statOfferRepository;
    private SyncDateService $syncDateService;
    private int $bulkSize;
    private int $delayedTime;

    public function __construct(
        OfferApi            $offerApi,
        MerchantApi         $merchantApi,
        CategoryApi         $categoryApi,
        OfferValidator      $offerValidator,
        StatOfferRepository $statOfferRepository,
        SyncDateService     $syncDateService,
        int                 $bulkSize,
        int                 $delayedTime
    )
    {
        $this->offerApi = $offerApi;
        $this->merchantApi = $merchantApi;
        $this->categoryApi = $categoryApi;
        $this->offerValidator = $offerValidator;
        $this->statOfferRepository = $statOfferRepository;
        $this->bulkSize = $bulkSize;
        $this->syncDateService = $syncDateService;
        $this->delayedTime = $delayedTime;

    }

    ######################
    ### PUBLIC METHODS
    ######################
    public function syncOffers(string $marketplace)
    {
        $this->statOfferRepository->deleteBeforeOneWeek();
        # get sync parameters
        $indexMappingPath = $this->getParameter(self::PARAM_ES_INDEX_MAPPING);
        $indexesAliasesPath = $this->getParameter(self::PARAM_ES_INDEXES_ALIASES);

        # init izberg client configuration
        $this->initApiClientsConfig($marketplace);

        # dispatch start synchronization event
        $startDate = new DateTimeImmutable();
        $this->eventDispatcher->dispatch(new StartSyncEvent($startDate));

        $lastSyncData = $this->syncDateService->getLastSyncData($marketplace);


        $offersNb = [];
        $minLastSync = [];
        $maxLastSync = [];
        $sinceDate = $lastSyncData->getLastSyncDate();
        $beforeDate = $lastSyncData->getBeforeSyncDate($sinceDate, $this->delayedTime);
        if($beforeDate < $sinceDate){
            $sinceDate = $beforeDate;
        }
        foreach ($this->offerApi->getApplicationConfiguration()->getChannels() as $channelLanguage => $channelId) {
            # index creation if needed
            $indexName = sprintf('%s_%s_%s', self::ES_INDEX_PREFIX, $marketplace, $channelLanguage);

            $this->eventDispatcher->dispatch(new IndexCreationEvent($indexName, $indexMappingPath));
            $this->eventDispatcher->dispatch(new IndexAliasesCreationEvent($indexName, $indexesAliasesPath));



            if (!isset($offersNb[$indexName])) {
                $offersNb[$indexName] = 0;
                $minLastSync[$indexName] = PHP_INT_MAX;
                $maxLastSync[$indexName] = 0;
//                $maxOfferSyncDate = $this->syncDateService->getLastOfferSyncByIndex($indexName);
//                if ($maxOfferSyncDate instanceof DateTimeImmutable) {
//                    $sinceDate = $maxOfferSyncDate;
//                    $beforeDate = $lastSyncData->getBeforeSyncDate($sinceDate);
//                }
            }
            $filter = [
                "format" => "json",
                "modified_since" => DateUtils::getFormattedDate($sinceDate),
                "modified_before" => DateUtils::getFormattedDate($beforeDate)
            ];

            $channelChange = $this->offerApi->fetchChannelChanges($channelId, $filter);


            # offers synchronization
            try {
                $offersList = [];
                foreach (Items::fromIterable($channelChange, ['debug' => true]) as $cpt => $offer) {
                    $minLastSync[$indexName] = min($minLastSync[$indexName], $offer->last_channel_sync);
                    $maxLastSync[$indexName] = max($maxLastSync[$indexName], $offer->last_channel_sync);
                    # update offer data
                    $this->updateOfferData($offer);

                    # validate offer
                    if ($this->offerValidator->isValid($offer)) {
                        $offersList[] = $offer;
                    }

                    if (count($offersList) === $this->bulkSize) {
                        $offersNb[$indexName] += count($offersList);
                        # dispatch offers list
                        $this->eventDispatcher->dispatch(new OffersListSyncEvent($indexName, $offersList));
                        # reset list
                        $offersList = [];
                        gc_collect_cycles();
                    }
                }

                if (count($offersList) > 0) {
                    $offersNb[$indexName] += count($offersList);
                    # dispatch last offers list package
                    $this->eventDispatcher->dispatch(new OffersListSyncEvent($indexName, $offersList));
                }
            } catch (InvalidArgumentException $e) {
                $this->throwException($e);
            }
        }

        //$this->syncDateService->saveStat($offersNb,$sinceDate, $beforeDate, $minLastSync, $maxLastSync);
        # update last sync date
        $this->syncDateService->updateLastSyncData($marketplace, $beforeDate);

        # dispatch end synchronization event
        $this->eventDispatcher->dispatch(new EndSyncEvent($startDate, new DateTimeImmutable()));
    }

    #######################
    ### UTILITY METHODS
    #######################
    private function initApiClientsConfig(string $marketplace)
    {
        $this->offerApi->setApplicationConfiguration($marketplace);
        $this->merchantApi->setApplicationConfiguration($marketplace);
        $this->categoryApi->setApplicationConfiguration($marketplace);
    }

    private function updateOfferData(stdClass $offer)
    {
        // update offer merchant
        $offer->merchant->adapted_company = $this->merchantApi->fetchMerchantAdaptedCompany($offer->merchant->id);
        $offer->merchant->rating = $this->merchantApi->fetchMerchantRating($offer->merchant->id);
        $offer->merchant->branches = $this->merchantApi->fetchMerchantBranches($offer->merchant->id);
        $offer->merchant->contract_cadre = $this->merchantApi->fetchMerchantContractCadre($offer->merchant->id);

        // update custom attributes
        if (!isset($offer->attributes)) {
            $offer->attributes = new stdClass();
        }

        if (property_exists($offer->attributes, CustomAttributes::IZBERG_ATTR_PRICE_ON_QUOTATION) &&
            get_object_vars($offer->attributes)[CustomAttributes::IZBERG_ATTR_PRICE_ON_QUOTATION] === 'Yes'
        ) {
            $offer->price = 100000;
            $offer->price_with_vat = 100000;
            $offer->price_without_vat = 100000;
        }

        // update product
        if ($offer->product && $offer->product->application_categories_dict) {
            foreach ($offer->product->application_categories_dict as $category) {
                $category->keywords = $this->categoryApi->fetchCategoryKeywords($category->id);
            }
        }
    }
}
