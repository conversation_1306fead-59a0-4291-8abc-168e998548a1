<?php

namespace <PERSON><PERSON><PERSON>\Service;

use DateInterval;
use DateTime;
use DateTimeImmutable;
use <PERSON><PERSON><PERSON>\Entity\StatOffer;
use <PERSON><PERSON><PERSON>\Helper\ParameterBagTrait;
use <PERSON><PERSON><PERSON>\Model\SyncData;
use <PERSON><PERSON><PERSON>\Repository\StatOfferRepository;

class SyncDateService
{
    use ParameterBagTrait;

    private StatOfferRepository $statOfferRepository;

    public function __construct(StatOfferRepository $statOfferRepository)
    {
        $this->statOfferRepository = $statOfferRepository;
    }

    public function getLastOfferSyncByIndex(string $indexName): ?DateTimeImmutable
    {
        $statOffer =  $this->statOfferRepository->getMaxOfferSyncByIndexName($indexName);
        if(!$statOffer instanceof StatOffer){
          return null;
        }
        $dateTime = $statOffer->getMaxLastSync();
        return DateTimeImmutable::createFromMutable($dateTime);
    }


    public function getLastSyncData(string $marketplace): SyncData
    {
        $lastSyncFilePath = sprintf('%s_%s.json', $this->getParameter('last_sync_file_path'), $marketplace);
        $defaultInitDate = (string)$this->getParameter('last_sync_default_init_date');
        $defaultIncrement = (int)$this->getParameter('last_sync_default_increment');

        if (!file_exists($lastSyncFilePath)) {
            return ((new SyncData())
                ->setLastSyncDate(new DateTimeImmutable($defaultInitDate))
                ->setIncrement($defaultIncrement)
            );
        }

        $syncData = json_decode(file_get_contents($lastSyncFilePath));
        return ((new SyncData())
            ->setLastSyncDate(new DateTimeImmutable($syncData->last_sync_date))
            ->setIncrement($syncData->increment)
        );
    }

    public function updateLastSyncData(string $marketplace, DateTimeImmutable $beforeDate)
    {
        $syncData = $this->getLastSyncData($marketplace);
        $lastSyncFilePath = sprintf('%s_%s.json', $this->getParameter('last_sync_file_path'), $marketplace);

        $syncData->setLastSyncDate(
            $beforeDate
        );

        file_put_contents($lastSyncFilePath, json_encode($syncData));
    }

    public function saveStat(array $nbOffers, DateTimeImmutable $sinceDate,DateTimeImmutable $beforeDate, array $minLastSync, array $maxLastSync)
    {

        $sinceDateTmp = new \DateTime();
        $sinceDateTmp->setTimestamp($sinceDate->getTimestamp());
        $beforeDateTmp = new \DateTime();
        $beforeDateTmp->setTimestamp($beforeDate->getTimestamp());
        foreach ($nbOffers as $index => $count) {
            $stat = (new StatOffer())
                ->setIndex($index)
                ->setCount($count)
                ->setStartDate($sinceDateTmp)
                ->setEndDate($beforeDateTmp);

            if ($count !== 0) {
                $stat->setMinLastSync($this->getDateFromTimeStamp($minLastSync[$index]));
                // TO Avoid forever loop on last second
                $maxLastSync = $maxLastSync[$index];
                if($maxLastSync===$sinceDate->getTimestamp()){
                    $maxLastSync+=1;
                }
                $stat->setMaxLastSync($this->getDateFromTimeStamp($maxLastSync));
            }

            $this->statOfferRepository->save($stat);
        }
    }

    private function getDateFromTimeStamp(int $lastChannelSync): DateTime
    {
        return (new DateTime())->setTimestamp($lastChannelSync);
    }
}