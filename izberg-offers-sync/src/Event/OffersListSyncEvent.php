<?php

namespace <PERSON><PERSON>berg\Event;

use stdClass;
use Symfony\Contracts\EventDispatcher\Event;

class OffersListSyncEvent extends Event
{
    private string $indexName;
    private array $offersList;

    public function __construct(string $indexName, array $offersList)
    {
        $this->indexName = $indexName;
        $this->offersList = $offersList;
    }

    public function getIndexName(): string
    {
        return $this->indexName;
    }

    public function getOffersList(): array
    {
        return $this->offersList;
    }

    public function getOffersIds(): array
    {
        return array_map(
            function (stdClass $offer) {
                return $offer->id.":".$offer->status;
            },
            $this->getOffersList()
        );
    }
}
