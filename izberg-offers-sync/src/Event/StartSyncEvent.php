<?php

namespace <PERSON><PERSON><PERSON>\Event;

use DateTimeImmutable;
use Symfony\Contracts\EventDispatcher\Event;

class StartSyncEvent extends Event
{
    private const DEFAULT_DATE_FORMAT = 'Y-m-d H:i:s';

    private DateTimeImmutable $startDate;

    public function __construct(DateTimeImmutable $startDate)
    {
        $this->startDate = $startDate;
    }

    public function getFormattedStartDate(): string
    {
        return $this->startDate->format(self::DEFAULT_DATE_FORMAT);
    }
}
