<?php

namespace I<PERSON>berg\Event;

use DateTimeImmutable;
use Symfony\Contracts\EventDispatcher\Event;

class EndSyncEvent extends Event
{
    private const DEFAULT_DATE_FORMAT = 'Y-m-d H:i:s';

    private DateTimeImmutable $startDate;
    private DateTimeImmutable $endDate;

    public function __construct(DateTimeImmutable $startDate, DateTimeImmutable $endDate)
    {
        $this->startDate = $startDate;
        $this->endDate = $endDate;
    }

    public function getStartDate(): DateTimeImmutable
    {
        return $this->startDate;
    }

    public function getEndDate(): DateTimeImmutable
    {
        return $this->endDate;
    }

    public function getFormattedStartDate(): string
    {
        return $this->startDate->format(self::DEFAULT_DATE_FORMAT);
    }

    public function getFormattedEndDate(): string
    {
        return $this->endDate->format(self::DEFAULT_DATE_FORMAT);
    }
}
