<?php

namespace <PERSON><PERSON><PERSON>\Event;

use Symfony\Contracts\EventDispatcher\Event;

class IndexCreationEvent extends Event
{
    private string $indexName;
    private string $indexMappingPath;

    public function __construct(string $indexName, string $indexMappingPath)
    {
        $this->indexName = $indexName;
        $this->indexMappingPath = $indexMappingPath;
    }

    public function getIndexName(): string
    {
        return $this->indexName;
    }

    public function getIndexMappingPath(): string
    {
        return $this->indexMappingPath;
    }
}
