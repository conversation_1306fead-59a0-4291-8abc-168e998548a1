<?php

namespace <PERSON><PERSON><PERSON>\Event;

use Symfony\Contracts\EventDispatcher\Event;

class IndexAliasesCreationEvent extends Event
{
    private string $indexName;
    private string $indexesAliasesPath;

    public function __construct(string $indexName, string $indexesAliasesPath)
    {
        $this->indexName = $indexName;
        $this->indexesAliasesPath = $indexesAliasesPath;
    }

    public function getIndexName(): string
    {
        return $this->indexName;
    }

    public function getIndexesAliasesPath(): string
    {
        return $this->indexesAliasesPath;
    }
}
