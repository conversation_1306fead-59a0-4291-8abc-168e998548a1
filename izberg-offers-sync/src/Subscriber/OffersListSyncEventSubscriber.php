<?php

namespace <PERSON><PERSON><PERSON>\Subscriber;

use <PERSON><PERSON><PERSON>\Event\OffersListSyncEvent;
use <PERSON><PERSON><PERSON>\Mapper\OfferMessageMapper;
use Symfony\Component\Messenger\MessageBusInterface;

class OffersListSyncEventSubscriber extends AbstractSubscriber
{
    private MessageBusInterface $messageBus;
    private OfferMessageMapper $offerMessageMapper;

    public function __construct(
        MessageBusInterface $messageBus,
        OfferMessageMapper  $offerMessageMapper
    )
    {
        $this->messageBus = $messageBus;
        $this->offerMessageMapper = $offerMessageMapper;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            OffersListSyncEvent::class => 'onOffersListSynchronization'
        ];
    }

    public function onOffersListSynchronization(OffersListSyncEvent $event)
    {
        // dispatch offers to rabbitMq queue
        $offerMessage = $this->offerMessageMapper->mapFromOffersListSyncEvent($event);
        $this->messageBus->dispatch($offerMessage);

        // logs
        $this->getLogger()->info(
            sprintf(
                "[ES_MANAGER] - %d dispatched [ids:%s]",
                count($event->getOffersIds()),
                implode(",", ($event->getOffersIds())
                ))
        );

    }
}
