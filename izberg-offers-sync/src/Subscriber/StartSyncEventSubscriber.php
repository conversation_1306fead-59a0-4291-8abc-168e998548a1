<?php

namespace <PERSON><PERSON><PERSON>\Subscriber;

use <PERSON><PERSON><PERSON>\Event\StartSyncEvent;

class StartSyncEventSubscriber extends AbstractSubscriber
{
    public static function getSubscribedEvents(): array
    {
        return [
            StartSyncEvent::class => 'onStartSynchronization'
        ];
    }

    public function onStartSynchronization(StartSyncEvent $event)
    {
        $msg = sprintf("Izberg offer synchronization starts at : %s", $event->getFormattedStartDate());
        $this->getLogger()->info($msg);
    }
}
