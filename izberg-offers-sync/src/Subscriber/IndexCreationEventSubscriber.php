<?php

namespace <PERSON><PERSON><PERSON>\Subscriber;

use <PERSON><PERSON><PERSON>\Event\IndexCreationEvent;
use <PERSON><PERSON><PERSON>\Service\ElasticService;

class IndexCreationEventSubscriber extends AbstractSubscriber
{
    private ElasticService $elasticService;

    public function __construct(ElasticService $elasticService)
    {
        $this->elasticService = $elasticService;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            IndexCreationEvent::class => 'onIndexCreation'
        ];
    }

    public function onIndexCreation(IndexCreationEvent $event)
    {
        if ($this->elasticService->createIndexIfNotExists($event->getIndexName(), $event->getIndexMappingPath())) {
            $this->logger->info(sprintf("Index : [%s] created successfully !", $event->getIndexName()));
        } else {
            $this->logger->info(sprintf("Index : [%s] already exists !", $event->getIndexName()));
        }
    }
}
