<?php

namespace <PERSON><PERSON><PERSON>\Subscriber;

use <PERSON><PERSON><PERSON>\Event\IndexAliasesCreationEvent;
use <PERSON><PERSON><PERSON>\Service\ElasticService;

class IndexAliasesCreationEventSubscriber extends AbstractSubscriber
{
    private ElasticService $elasticService;

    public function __construct(ElasticService $elasticService)
    {
        $this->elasticService = $elasticService;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            IndexAliasesCreationEvent::class => 'onIndexAliasesCreation',
        ];
    }

    public function onIndexAliasesCreation(IndexAliasesCreationEvent $event)
    {
        $indexesAliases = json_decode(file_get_contents($event->getIndexesAliasesPath()), true);
        $aliases = $indexesAliases[$event->getIndexName()] ?? [];

        if ($this->elasticService->createIndexAliasesIfNotExists($event->getIndexName(), $aliases)) {
            $this->logger->info(sprintf("Index Aliases: [%s -> %s] created successfully !",
                $event->getIndexName(),
                implode(', ', $aliases)
            ));
        } else {
            $this->logger->info(sprintf("Index Aliases: [%s -> %s] already exists !",
                $event->getIndexName(),
                implode(', ', $aliases)
            ));
        }
    }
}
