<?php

namespace <PERSON><PERSON><PERSON>\Subscriber;

use <PERSON><PERSON><PERSON>\Event\ExceptionEvent;
use <PERSON><PERSON><PERSON>\Exception\OfferValidationException;

class ExceptionEventSubscriber extends AbstractSubscriber
{
    public static function getSubscribedEvents(): array
    {
        return [
            ExceptionEvent::class => 'onExceptionThrow'
        ];
    }

    public function onExceptionThrow(ExceptionEvent $event)
    {
        $exception = $event->getException();

        if ($exception instanceof OfferValidationException) {
            $this->getLogger()->error($exception->getMessage());
        } else {
            $this->getLogger()->error($exception->getMessage(), [
                'stack_trace' => $exception->getTraceAsString()
            ]);
        }


    }
}
