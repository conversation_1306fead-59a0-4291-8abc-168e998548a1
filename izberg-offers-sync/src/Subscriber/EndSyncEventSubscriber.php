<?php

namespace <PERSON><PERSON><PERSON>\Subscriber;

use <PERSON><PERSON><PERSON>\Event\EndSyncEvent;

class EndSyncEventSubscriber extends AbstractSubscriber
{
    public static function getSubscribedEvents(): array
    {
        return [
            EndSyncEvent::class => 'onEndSynchronization'
        ];
    }

    public function onEndSynchronization(EndSyncEvent $event)
    {
        $interval = $event->getEndDate()->diff($event->getStartDate());

        $msg = sprintf("<PERSON><PERSON>berg offer synchronization finish ! %s Sync starts : %20s %s Sync ends : %22s %s Sync in : %6s days, %s hours, %s mins, %s secs",
            PHP_EOL,
            $event->getFormattedStartDate(),
            PHP_EOL,
            $event->getFormattedEndDate(),
            PHP_EOL,
            $interval->d,
            $interval->h,
            $interval->i,
            $interval->f
        );
        $this->getLogger()->info($msg);
    }
}
