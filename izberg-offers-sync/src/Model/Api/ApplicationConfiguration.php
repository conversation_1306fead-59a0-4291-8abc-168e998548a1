<?php

namespace <PERSON><PERSON>berg\Model\Api;

class ApplicationConfiguration
{
    private int $applicationId = 0;
    private string $accessToken = '';
    private array $channels = []; # [$channelLanguage => $channelId]

    public function getApplicationId(): int
    {
        return $this->applicationId;
    }

    public function setApplicationId(int $applicationId): self
    {
        $this->applicationId = $applicationId;
        return $this;
    }

    public function getAccessToken(): string
    {
        return $this->accessToken;
    }

    public function setAccessToken(string $accessToken): self
    {
        $this->accessToken = $accessToken;
        return $this;
    }

    public function getChannels(): array
    {
        return $this->channels;
    }

    public function setChannels(array $channels): self
    {
        $this->channels = $channels;
        return $this;
    }
}
