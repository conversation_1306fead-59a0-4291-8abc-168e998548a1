<?php

namespace <PERSON><PERSON><PERSON>\Model\Messenger;

use <PERSON><PERSON><PERSON>\Model\Elastic\DocumentsList;

class OfferMessage
{
    private string $offersIndexName;
    private DocumentsList $offersDocumentsList;

    public function __construct(string $offersIndexName, DocumentsList $offersDocumentsList)
    {
        $this->offersIndexName = $offersIndexName;
        $this->offersDocumentsList = $offersDocumentsList;
    }

    public function getOffersIndexName(): string
    {
        return $this->offersIndexName;
    }

    public function getOffersDocumentsList(): DocumentsList
    {
        return $this->offersDocumentsList;
    }
}
