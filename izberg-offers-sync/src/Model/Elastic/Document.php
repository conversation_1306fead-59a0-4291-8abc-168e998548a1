<?php

namespace <PERSON><PERSON>berg\Model\Elastic;

class Document
{
    private string $documentIndexName;
    private string $documentId;
    private array $documentContent;

    public function setDocumentIndexName(string $documentIndexName): self
    {
        $this->documentIndexName = $documentIndexName;
        return $this;
    }

    public function setDocumentId(string $documentId): self
    {
        $this->documentId = $documentId;
        return $this;
    }

    public function setDocumentContent(array $documentContent): self
    {
        $this->documentContent = $documentContent;
        return $this;
    }


    public function getDocumentBody(): array
    {
        return [
            [
                'index' => [
                    '_type' => '_doc',
                    '_index' => $this->documentIndexName,
                    '_id' => $this->documentId
                ]
            ],
            $this->documentContent
        ];
    }
}
