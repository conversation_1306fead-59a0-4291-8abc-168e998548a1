<?php

namespace <PERSON><PERSON>berg\Model\Elastic;

class DocumentsList
{
    private array $documentsList;

    public function __construct()
    {
        $this->initDocumentsList();
    }


    public function getDocumentsList(): array
    {
        return $this->documentsList;
    }

    public function getDocumentsListIds(): array
    {
        return array_map(
            function (array $documentBody) {
                return $documentBody['index']['_id'];
            },
            array_filter($this->documentsList['body'], function($body) { return isset($body['index']); })
        );
    }

    public function addDocument(Document $document): self
    {
        $this->documentsList['body'] = array_merge($this->documentsList['body'], $document->getDocumentBody());
        return $this;
    }

    private function initDocumentsList()
    {
        $this->documentsList = [
            'body' => []
        ];
    }
}
