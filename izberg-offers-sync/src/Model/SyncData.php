<?php

namespace <PERSON><PERSON><PERSON>\Model;

use DateInterval;
use DateTime;
use DateTimeImmutable;
use <PERSON><PERSON><PERSON>\Utils\DateUtils;
use JsonSerializable;

class SyncData implements JsonSerializable
{
    private const DEFAULT_DATE_FORMAT = DateTime::ISO8601;

    private DateTimeImmutable $lastSyncDate;
    private int $increment;

    ### GETTERS AND SETTERS ###
    public function getLastSyncDate(): DateTimeImmutable
    {
        return $this->lastSyncDate;
    }

    public function setLastSyncDate(DateTimeImmutable $lastSyncDate): self
    {
        $this->lastSyncDate = $lastSyncDate;
        return $this;
    }

    public function getIncrement(): int
    {
        return $this->increment;
    }

    public function setIncrement(int $increment): self
    {
        $this->increment = $increment;
        return $this;
    }

    ### UTILITY METHODS ###
    public function getFormattedSinceSyncDate(): string
    {

        return  DateUtils::getFormattedDate($this->lastSyncDate);
    }

    public function getBeforeSyncDate(DateTimeImmutable $date, int $delay): DateTimeImmutable|bool
    {
        $dateNow = new DateTimeImmutable();
        $dateMax  = $date
            ->add(new DateInterval(sprintf('P%sD', $this->increment)));
        if($dateMax> $dateNow){
            $dateMax = $dateNow->sub(DateInterval::createFromDateString($delay.' seconds'));
        }
        return $dateMax;
    }

    public function jsonSerialize(): array
    {
       return [
           'last_sync_date' => $this->getFormattedSinceSyncDate(),
           'increment' => $this->getIncrement()
       ];
    }
}
