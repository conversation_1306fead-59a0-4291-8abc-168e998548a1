<?php

namespace <PERSON><PERSON><PERSON>\Handler;

use <PERSON><PERSON><PERSON>\Model\Messenger\OfferMessage;
use <PERSON><PERSON><PERSON>\Service\ElasticService;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;

class OfferMessageHandler implements MessageHandlerInterface
{
    private LoggerInterface $logger;
    private ElasticService $elasticService;

    public function __construct(
        LoggerInterface $logger,
        ElasticService  $elasticService
    )
    {
        $this->logger = $logger;
        $this->elasticService = $elasticService;
    }

    public function __invoke(OfferMessage $message)
    {
        $result = $this->elasticService->indexDocuments($message->getOffersDocumentsList());
        $this->elasticService->logResult($this->logger, $result);
    }
}
