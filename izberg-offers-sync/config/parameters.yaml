parameters:
    ### APP PARAMS ###
    cache_directory: '%kernel.project_dir%/var/cache/'
    cache_expiration: 3600
    http_timeout: 200
    last_sync_file_path: '%kernel.project_dir%/last_sync'
    last_sync_default_init_date: '2024-03-23'
    last_sync_default_increment: 10

    izberg_security_categories: [ ]
    elastic_index_mapping: '%kernel.project_dir%/config/elastic_mappings/index_mapping.json'
    elastic_indexes_aliases: '%kernel.project_dir%/config/elastic_mappings/indexes_aliases.json'

    ### IZBERG API PARAMS ###
    api_base_url: '%env(string:API_BASE_URL)%'
    api_channel_base_url: '%env(string:API_CHANNEL_BASE_URL)%'

    api_merchant_base_url: '%api_base_url%merchant/'
    api_category_base_url: '%api_base_url%application_category/'

    ### MARKETPLACES CONFIGS PARAMS
    france.client.config:
        application_id: '%env(int:FRANCE_APPLICATION_ID)%'
        access_token: '%env(string:FRANCE_ACCESS_TOKEN)%'
        channels: '%env(json:FRANCE_CHANNELS)%'

    germany.client.config:
        application_id: '%env(int:GERMANY_APPLICATION_ID)%'
        access_token: '%env(string:GERMANY_ACCESS_TOKEN)%'
        channels: '%env(json:GERMANY_CHANNELS)%'

    belgium.client.config:
        application_id: '%env(int:BELGIUM_APPLICATION_ID)%'
        access_token: '%env(string:BELGIUM_ACCESS_TOKEN)%'
        channels: '%env(json:BELGIUM_CHANNELS)%'
