monolog:
    channels: ["messenger"]
    handlers:
        main:
            type: stream
            path: "%env(APP_LOG_DIR)%/%kernel.environment%.log"
            level: info
            formatter: monolog.formatter.json
            channels: ["!messenger"]
        messenger:
            type: stream
            level: error
            path: "%env(APP_LOG_DIR)%/%kernel.environment%.log"
            formatter: monolog.formatter.json
            channels: ["messenger"]
        console:
            type: console
            process_psr_3_messages: false
            channels: ["!event", "!doctrine"]
