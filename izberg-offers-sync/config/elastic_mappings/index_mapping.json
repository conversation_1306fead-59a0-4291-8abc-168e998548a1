{"settings": {"analysis": {"filter": {"autocomplete_filter": {"type": "edge_ngram", "min_gram": 2, "max_gram": 20}, "length_2_char": {"type": "length", "min": 2}}, "analyzer": {"autocomplete": {"type": "custom", "tokenizer": "standard", "char_filter": ["html_strip"], "filter": ["lowercase", "asciifolding", "autocomplete_filter"]}, "custom_analyzer": {"type": "custom", "tokenizer": "standard", "char_filter": ["html_strip"], "filter": ["lowercase", "asciifolding"]}, "custom_analyzer_with_min": {"filter": ["lowercase", "asciifolding", "length_2_char"], "char_filter": ["html_strip"], "type": "custom", "tokenizer": "standard"}, "separator_analyzer": {"tokenizer": "separator_tokenizer"}, "path_analyser": {"tokenizer": "path_tokenizer"}}, "tokenizer": {"separator_tokenizer": {"type": "simple_pattern_split", "pattern": ","}, "path_tokenizer": {"type": "path_hierarchy"}}, "normalizer": {"normalizer_lowercase": {"type": "custom", "filter": ["lowercase"]}}}}, "mappings": {"properties": {"name": {"type": "text", "analyzer": "autocomplete", "search_analyzer": "custom_analyzer_with_min"}, "description": {"type": "text", "analyzer": "custom_analyzer_with_min", "index_options": "docs"}, "availability_localized": {"type": "keyword"}, "merchant.name": {"type": "keyword", "fields": {"text": {"type": "text", "analyzer": "autocomplete", "search_analyzer": "custom_analyzer_with_min"}, "normalize": {"type": "keyword", "normalizer": "normalizer_lowercase"}}}, "product.name": {"type": "keyword"}, "product.gtin": {"type": "keyword"}, "attributes.0100_country_of_delivery": {"type": "keyword"}, "attributes.0110_price_on_quotation": {"type": "text", "fields": {"keyword": {"type": "keyword"}}}, "country_price": {"type": "float"}, "previous_price": {"type": "float"}, "previous_price_with_vat": {"type": "float"}, "previous_price_without_vat": {"type": "float"}, "price": {"type": "float"}, "price_with_vat": {"type": "float"}, "price_without_vat": {"type": "float"}, "attributes.1230_inclusive_branch": {"type": "text", "analyzer": "separator_analyzer", "fields": {"keyword": {"type": "keyword"}}}, "attributes.1231_inclusive_organization": {"type": "text", "analyzer": "separator_analyzer", "fields": {"keyword": {"type": "keyword"}}, "index_prefixes": {"min_chars": 3, "max_chars": 19}}, "attributes.1232_inclusive_site": {"type": "text", "analyzer": "separator_analyzer", "fields": {"keyword": {"type": "keyword"}}}, "attributes.1233_inclusive_invoice_entity": {"type": "text", "analyzer": "separator_analyzer", "fields": {"keyword": {"type": "keyword"}}}, "attributes.1240_exclusive_branch": {"type": "text", "analyzer": "separator_analyzer", "fields": {"keyword": {"type": "keyword"}}}, "attributes.1241_exclusive_organization": {"type": "text", "analyzer": "separator_analyzer", "fields": {"keyword": {"type": "keyword"}}, "index_prefixes": {"min_chars": 3, "max_chars": 19}}, "attributes.1242_exclusive_site": {"type": "text", "analyzer": "separator_analyzer", "fields": {"keyword": {"type": "keyword"}}}, "attributes.1243_exclusive_invoice_entity": {"type": "text", "analyzer": "separator_analyzer", "fields": {"keyword": {"type": "keyword"}}}, "attributes.0200_threshold_1": {"type": "text"}, "attributes.0220_threshold_2": {"type": "text"}, "attributes.0240_threshold_3": {"type": "text"}, "attributes.0260_threshold_4": {"type": "text"}}}}