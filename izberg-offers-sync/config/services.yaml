imports:
    - { resource: parameters.yaml }

services:
    ### DEFAULTS CONFIGS ###
    _defaults:
        autowire: true
        autoconfigure: true

    _instanceof:
        <PERSON><PERSON><PERSON>\Api\AbstractApi:
            calls:
                -   setParameterBag: [ '@parameter_bag' ]
                -   setApplicationConfigurationMapper: [ '@I<PERSON><PERSON>\Mapper\ApplicationConfigurationMapper' ]
                -   setEventDispatcher: [ '@event_dispatcher' ]
                -   setCache: [ '@Izberg\Contract\CacheInterface' ]
                -   setCacheExpiration: [ '%cache_expiration%' ]
                -   setHttpTimeout: [ '%http_timeout%' ]
                -   setBaseUrl: [ '%api_base_url%' ]
                -   setChannelBaseUrl: [ '%api_channel_base_url%' ]

        I<PERSON>berg\Subscriber\AbstractSubscriber:
            calls:
                -   setLogger: [ '@monolog.logger' ]

    ### NAMESPACES CONFIGS ###
    Izberg\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    Izberg\Validator\Offer\:
        resource: '../src/Validator/Offer'
        tags: [ 'offer.validator' ]

    ### HELPERS CONFIGS ###
    Izberg\Helper\ExceptionTrait:
        calls:
            -   setEventDispatcher: [ '@event_dispatcher' ]

    Izberg\Helper\ParameterBagTrait:
        calls:
            -   setParameterBag: [ '@parameter_bag' ]

    ### SERVICES CONFIGS ###
    Izberg\Mapper\ApplicationConfigurationMapper: ~

    Izberg\Contract\CacheInterface:
        class: Izberg\Service\CacheService
        arguments:
            $cacheDirectory: '%cache_directory%'
            $cacheExpiration: '%cache_expiration%'

    Izberg\Validator\OfferValidator:
        arguments:
            $validators: !tagged_iterator { tag: offer.validator }

    Izberg\Service\ElasticService:
        arguments:
            $indexHosts: '%env(json:ELASTIC_HOSTS)%'
            $enableSsl: '%env(bool:ELASTIC_ENABLE_SSL)%'
            $sslCertificatePath: '%env(string:ELASTIC_SSL_CERTIFICATE_PATH)%'

    Izberg\Service\SyncOffersService:
        arguments:
            $bulkSize: '%env(int:BULK_SIZE)%'
            $delayedTime: '%env(int:DELAYED_TIME)%'

framework:
    messenger:
        # Uncomment this (and the failed transport below) to send failed messages to this transport for later handling.
        # failure_transport: failed

        transports:
            # https://symfony.com/doc/current/messenger.html#transport-configuration
            async: '%env(MESSENGER_TRANSPORT_DSN)%'
            # failed: 'doctrine://default?queue_name=failed'
            # sync: 'sync://'

        routing:
            'Izberg\Model\Messenger\OfferMessage': async

monolog:
    handlers:
        main:
            type: stream
            path: "php://stderr"
            level: debug
            channels: ["!event"]