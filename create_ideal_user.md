# Complete Guide: Creating an Ideal User in Click & Buy System

## Overview

This guide provides a comprehensive, step-by-step process for creating a new user in the Click & Buy system, including all required API calls, database operations, and validation steps.

## Prerequisites

- Access to Click & Buy container: `docker exec -it click-and-buy bash`
- Database access (MariaDB)
- Ideal API credentials configured
- Izberg API access configured

## Complete User Creation Process

### Step 1: Verify User Data from Ideal API

**Purpose**: Ensure the user exists in Ideal and retrieve their complete profile.

#### 1.1 Retrieve User from Ideal API

```bash
# Method 1: Full sync (if user is new)
php bin/console open:buyer:retrieve

# Method 2: Single user retrieval (if implemented)
php bin/console open:buyer:retrieve-by-igg "USER_IGG"
```

#### 1.2 Verify User in DistantUser Table

```sql
-- Check if user exists in distant_user table
SELECT * FROM distant_user WHERE igg = 'USER_IGG';

-- Example result should include:
-- igg, first_name, last_name, email, organization, manager_igg, etc.
```

**Required Fields Validation**:
- `igg` (MainEmployeeId) - Must not be null
- `first_name` - Must not be null  
- `last_name` - Must not be null
- `email` - Must be valid email format
- `preferred_language` - Defaults to "EN" if null

### Step 2: Create User Identity in Izberg System

**Purpose**: Create the user identity in the Izberg identity management system.

#### 2.1 Prepare User Identity Data

```php
// Data structure for Izberg UserIdentity creation
$merchantUserData = [
    'email' => $distantUser->getEmail(),
    'first_name' => $distantUser->getFirstName(),
    'last_name' => $distantUser->getLastName(),
    'domain_id' => $izbergConfiguration->getDomainId(),
    'merchant_scopes' => [
        $merchantId => ["*" => "admin"]
    ]
];
```

#### 2.2 API Call to Create User Identity

```php
// Using UserIdentityApi
$userIdentity = $userIdentityApi->createMerchantUser($merchantUserData);

// Returns UserIdentity object with:
// - id (user_identity_id)
// - email
// - first_name, last_name
// - domain_id
// - merchant_scopes
```

**API Endpoint**: `POST {IZBERG_IDENTITY_URL}/user/`

**Headers**:
```
Content-Type: application/json
Authorization: Bearer {IZBERG_ACCESS_TOKEN}
```

### Step 3: Create User in Izberg Marketplace

**Purpose**: Create the marketplace user profile linked to the identity.

#### 3.1 Prepare Marketplace User Data

```php
$izbergUserData = [
    'email' => $distantUser->getEmail(),
    'first_name' => $distantUser->getFirstName(),
    'last_name' => $distantUser->getLastName(),
    'username' => $distantUser->getIgg(), // Use IGG as username
    'user_identity_id' => $userIdentity->getId(),
    'language' => $this->mapLanguage($distantUser->getPreferredLanguage()),
    'is_active' => true
];
```

#### 3.2 API Call to Create Marketplace User

```php
// Using Izberg UserApi
$izbergUser = $userApi->createUser($izbergUserData);

// Returns IzbergUser object with:
// - id (izberg_user_id)
// - email, username
// - user_identity_id
// - language, is_active
```

**API Endpoint**: `POST {IZBERG_API_URL}/user/`

### Step 4: Create Local User Entity

**Purpose**: Create the local User entity in the Click & Buy database.

#### 4.1 Prepare Local User Data

```php
$user = new User();
$user->setUsername($distantUser->getIgg())
     ->setEmail($distantUser->getEmail())
     ->setFirstname($distantUser->getFirstName())
     ->setLastname($distantUser->getLastName())
     ->setRoles(['ROLE_BUYER'])
     ->setEnabled(true)
     ->setUserIdentityId($userIdentity->getId())
     ->setIzbergUserId($izbergUser->getId())
     ->setPreferredLanguage($distantUser->getPreferredLanguage() ?? 'EN');
```

#### 4.2 Set Additional User Properties

```php
// Address information
if ($distantUser->getSiteName()) {
    $address = new Address();
    $address->setName($distantUser->getSiteName())
           ->setStreet($distantUser->getSiteStreet())
           ->setStreet2($distantUser->getSiteStreet2())
           ->setZipcode($distantUser->getSiteZipcode())
           ->setLocality($distantUser->getSiteLocality())
           ->setCountry($distantUser->getSiteCountry());
    
    $user->addAddress($address);
}

// Organization and cost center
$user->setOrganization($distantUser->getOrganization())
     ->setCostCenter($distantUser->getCostCenter())
     ->setTelephone($distantUser->getTelephone());
```

#### 4.3 Database Persistence

```sql
-- The User entity will be persisted to the 'user' table
INSERT INTO user (
    username, email, firstname, lastname, roles, enabled,
    user_identity_id, izberg_user_id, preferred_language,
    organization, cost_center, telephone, created_at, updated_at
) VALUES (
    'USER_IGG', '<EMAIL>', 'John', 'Doe', 
    '["ROLE_BUYER"]', 1, 'identity_id', 'izberg_id', 'EN',
    'Organization Name', 'Cost Center', '+33123456789',
    NOW(), NOW()
);
```

### Step 5: Establish Manager Hierarchy

**Purpose**: Create manager-subordinate relationships if applicable.

#### 5.1 Find Manager User

```php
if ($distantUser->getManagerIgg()) {
    $manager = $userRepository->findOneBy(['username' => $distantUser->getManagerIgg()]);
    
    if ($manager) {
        // Create relationship
        $relationship = new UserToUserRelationship();
        $relationship->setParentUser($manager)
                    ->setChildUser($user)
                    ->setIsDelegate(false)
                    ->setCreatedAt(new \DateTime());
        
        $entityManager->persist($relationship);
    }
}
```

#### 5.2 Database Structure

```sql
-- UserToUserRelationship table
INSERT INTO user_to_user_relationship (
    parent_user_id, child_user_id, is_delegate, created_at
) VALUES (
    manager_user_id, new_user_id, 0, NOW()
);
```

### Step 6: Set Permissions and Roles

**Purpose**: Configure user permissions in both Izberg and local systems.

#### 6.1 Izberg Permission Setup

```php
// Set merchant permissions
$userIdentityApi->patchPermission($merchantId, $userIdentity->getId());

// This grants admin access to the merchant scope
```

#### 6.2 Local Role Assignment

```php
// Roles are already set in Step 4.1
// Default: ['ROLE_BUYER']
// Additional roles can be added based on business logic
```

### Step 7: Validation and Testing

**Purpose**: Verify the user was created correctly and can access the system.

#### 7.1 Database Verification

```sql
-- Verify user creation
SELECT u.*, ui.email as identity_email 
FROM user u 
LEFT JOIN user_identity ui ON u.user_identity_id = ui.id 
WHERE u.username = 'USER_IGG';

-- Verify relationships
SELECT p.username as manager, c.username as subordinate 
FROM user_to_user_relationship r
JOIN user p ON r.parent_user_id = p.id
JOIN user c ON r.child_user_id = c.id
WHERE c.username = 'USER_IGG';

-- Verify address
SELECT a.* FROM address a 
JOIN user_address ua ON a.id = ua.address_id
JOIN user u ON ua.user_id = u.id
WHERE u.username = 'USER_IGG';
```

#### 7.2 API Verification

```php
// Test Izberg user retrieval
$retrievedUser = $userApi->getUser($izbergUser->getId());

// Test identity retrieval
$retrievedIdentity = $userIdentityApi->findUser(
    $domainId, 
    $distantUser->getEmail()
);
```

#### 7.3 Authentication Test

```bash
# Test user login (if authentication is configured)
curl -X POST "https://your-domain.com/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "USER_IGG", "password": "test_password"}'
```

## Complete Implementation Example

### Service Method Implementation

```php
<?php
// marketplace/src/AppBundle/Services/UserCreationService.php

namespace AppBundle\Services;

use AppBundle\Entity\User;
use AppBundle\Entity\Address;
use AppBundle\Entity\UserToUserRelationship;
use AppBundle\Entity\DistantUser;
use AppBundle\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Open\IzbergBundle\Api\UserApi;
use Open\IzbergBundle\Api\UserIdentityApi;

class UserCreationService
{
    private EntityManagerInterface $entityManager;
    private UserRepository $userRepository;
    private UserApi $userApi;
    private UserIdentityApi $userIdentityApi;
    private int $merchantId;
    private string $domainId;

    public function createUserFromDistantUser(DistantUser $distantUser): User
    {
        // Step 1: Validate required data
        $this->validateDistantUser($distantUser);
        
        // Step 2: Check if user already exists
        $existingUser = $this->userRepository->findOneBy([
            'username' => $distantUser->getIgg()
        ]);
        
        if ($existingUser) {
            return $this->updateExistingUser($existingUser, $distantUser);
        }
        
        // Step 3: Create user identity
        $userIdentity = $this->createUserIdentity($distantUser);
        
        // Step 4: Create Izberg marketplace user
        $izbergUser = $this->createIzbergUser($distantUser, $userIdentity);
        
        // Step 5: Create local user entity
        $user = $this->createLocalUser($distantUser, $userIdentity, $izbergUser);
        
        // Step 6: Set up manager relationship
        $this->setupManagerRelationship($user, $distantUser);
        
        // Step 7: Persist all changes
        $this->entityManager->flush();
        
        return $user;
    }
    
    private function validateDistantUser(DistantUser $distantUser): void
    {
        if (empty($distantUser->getIgg())) {
            throw new \InvalidArgumentException('IGG is required');
        }
        
        if (empty($distantUser->getFirstName()) || empty($distantUser->getLastName())) {
            throw new \InvalidArgumentException('First name and last name are required');
        }
        
        if (empty($distantUser->getEmail()) || !filter_var($distantUser->getEmail(), FILTER_VALIDATE_EMAIL)) {
            throw new \InvalidArgumentException('Valid email is required');
        }
    }
    
    // ... implement other private methods
}
```

### Console Command for Manual User Creation

```php
<?php
// marketplace/src/AppBundle/Command/CreateUserCommand.php

namespace AppBundle\Command;

use AppBundle\Services\UserCreationService;
use AppBundle\Repository\DistantUserRepository;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class CreateUserCommand extends Command
{
    private UserCreationService $userCreationService;
    private DistantUserRepository $distantUserRepository;

    protected function configure()
    {
        $this
            ->setName('open:user:create-from-ideal')
            ->setDescription('Create a user from Ideal data by IGG')
            ->addArgument('igg', InputArgument::REQUIRED, 'The user IGG');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $igg = $input->getArgument('igg');
        
        // Find distant user
        $distantUser = $this->distantUserRepository->findOneBy(['igg' => $igg]);
        
        if (!$distantUser) {
            $output->writeln("<error>DistantUser with IGG {$igg} not found. Run retrieve command first.</error>");
            return 1;
        }
        
        try {
            $user = $this->userCreationService->createUserFromDistantUser($distantUser);
            $output->writeln("<info>User created successfully:</info>");
            $output->writeln("ID: " . $user->getId());
            $output->writeln("Username: " . $user->getUsername());
            $output->writeln("Email: " . $user->getEmail());
            return 0;
        } catch (\Exception $e) {
            $output->writeln("<error>Error creating user: " . $e->getMessage() . "</error>");
            return 1;
        }
    }
}
```

## Usage Examples

```bash
# Complete user creation process
docker exec -it click-and-buy bash

# 1. Ensure user data is available
php bin/console open:buyer:retrieve

# 2. Create specific user
php bin/console open:user:create-from-ideal "12345"

# 3. Verify creation
php bin/console doctrine:query:sql "SELECT * FROM user WHERE username = '12345'"
```

## Error Handling and Troubleshooting

### Common Issues

1. **User Identity Creation Fails**
   - Check Izberg API credentials
   - Verify domain_id configuration
   - Ensure email is unique

2. **Marketplace User Creation Fails**
   - Verify user_identity_id is valid
   - Check username uniqueness
   - Validate language code

3. **Manager Relationship Issues**
   - Ensure manager exists in system
   - Check manager IGG validity
   - Verify relationship constraints

### Rollback Procedures

```sql
-- Rollback user creation (in case of errors)
DELETE FROM user_to_user_relationship WHERE child_user_id = user_id;
DELETE FROM user_address WHERE user_id = user_id;
DELETE FROM user WHERE username = 'USER_IGG';

-- Note: Izberg API calls may need manual cleanup
```

## Security Considerations

1. **Data Validation**: Always validate input data
2. **Permission Checks**: Verify user has permission to create users
3. **Audit Logging**: Log all user creation activities
4. **Error Handling**: Don't expose sensitive information in error messages
5. **Transaction Management**: Use database transactions for consistency

## Advanced Configuration

### Language Mapping

```php
private function mapLanguage(?string $idealLanguage): string
{
    $languageMap = [
        'French' => 'fr',
        'English' => 'en',
        'Dutch' => 'nl',
        'German' => 'de'
    ];

    return $languageMap[$idealLanguage] ?? 'en';
}
```

### Address Normalization

```php
private function normalizeAddress(DistantUser $distantUser): ?Address
{
    if (!$distantUser->getSiteName()) {
        return null;
    }

    $address = new Address();
    $address->setName(trim($distantUser->getSiteName()))
           ->setStreet(trim($distantUser->getSiteStreet() ?? ''))
           ->setStreet2(trim($distantUser->getSiteStreet2() ?? ''))
           ->setZipcode(trim($distantUser->getSiteZipcode() ?? ''))
           ->setLocality(trim($distantUser->getSiteLocality() ?? ''))
           ->setCountry(trim($distantUser->getSiteCountry() ?? ''));

    return $address;
}
```

### Batch User Creation

```php
public function createUsersFromDistantUsers(array $distantUsers): array
{
    $results = [];
    $this->entityManager->beginTransaction();

    try {
        foreach ($distantUsers as $distantUser) {
            try {
                $user = $this->createUserFromDistantUser($distantUser);
                $results[] = ['success' => true, 'user' => $user, 'igg' => $distantUser->getIgg()];
            } catch (\Exception $e) {
                $results[] = ['success' => false, 'error' => $e->getMessage(), 'igg' => $distantUser->getIgg()];
            }
        }

        $this->entityManager->commit();
    } catch (\Exception $e) {
        $this->entityManager->rollback();
        throw $e;
    }

    return $results;
}
```

## Monitoring and Metrics

### Key Metrics to Track

1. **User Creation Success Rate**
2. **API Response Times** (Ideal, Izberg)
3. **Error Rates by Type**
4. **Manager Relationship Success Rate**
5. **Data Quality Issues**

### Logging Configuration

```yaml
# config/packages/monolog.yaml
monolog:
    channels: ['user_creation']
    handlers:
        user_creation:
            type: stream
            path: '%kernel.logs_dir%/user_creation.log'
            level: info
            channels: ['user_creation']
```

### Sample Logging Implementation

```php
use Psr\Log\LoggerInterface;

class UserCreationService
{
    private LoggerInterface $logger;

    public function createUserFromDistantUser(DistantUser $distantUser): User
    {
        $this->logger->info('Starting user creation', [
            'igg' => $distantUser->getIgg(),
            'email' => $distantUser->getEmail()
        ]);

        try {
            // ... creation logic

            $this->logger->info('User created successfully', [
                'igg' => $distantUser->getIgg(),
                'user_id' => $user->getId()
            ]);

            return $user;
        } catch (\Exception $e) {
            $this->logger->error('User creation failed', [
                'igg' => $distantUser->getIgg(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }
}
```

## Production Deployment Checklist

### Pre-Deployment

- [ ] Verify all API credentials are configured
- [ ] Test database connectivity
- [ ] Validate Ideal API access
- [ ] Confirm Izberg API integration
- [ ] Review error handling and logging
- [ ] Test rollback procedures

### Post-Deployment

- [ ] Monitor user creation success rates
- [ ] Verify manager relationships are created correctly
- [ ] Check address data quality
- [ ] Validate authentication flows
- [ ] Monitor API performance
- [ ] Review error logs

### Maintenance Tasks

- [ ] Regular cleanup of failed creation attempts
- [ ] Monitor and rotate logs
- [ ] Update API credentials as needed
- [ ] Review and update language mappings
- [ ] Audit user permissions periodically

## API Reference Quick Guide

### Ideal API Endpoints

```
Authentication: POST /connect/token
User Retrieval: GET /api/Resource/Directory_User?api-version=1.0
```

### Izberg API Endpoints

```
User Identity: POST {IDENTITY_URL}/user/
Marketplace User: POST {API_URL}/user/
User Permissions: PATCH {IDENTITY_URL}/user/{id}/
```

### Database Tables

```
- user (main user entity)
- distant_user (Ideal API data)
- user_to_user_relationship (manager hierarchy)
- address (user addresses)
- user_address (user-address relationships)
```

This comprehensive guide provides everything needed to successfully create users in the Click & Buy system from Ideal API data.
